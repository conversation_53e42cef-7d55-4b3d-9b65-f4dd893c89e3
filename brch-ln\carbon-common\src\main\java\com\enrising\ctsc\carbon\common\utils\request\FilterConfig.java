package com.enrising.ctsc.carbon.common.utils.request;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FilterConfig {

    @Bean
    public FilterRegistrationBean<RequestCachingFilter> loggingFilter(){
        FilterRegistrationBean<RequestCachingFilter> registrationBean = new FilterRegistrationBean<>(new RequestCachingFilter());
        // 适用于所有URL
        registrationBean.addUrlPatterns("/*");
        return registrationBean;
    }
}
