package com.enrising.ctsc.discharge.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.enrising.ctsc.discharge.api.entity.EnergyCalculatePrice;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.discharge.service.EnergyCalculatePriceService;
import lombok.AllArgsConstructor;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.WebRequest;

import javax.validation.Valid;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
* 能源单价表接口
*
* @<NAME_EMAIL>
* @since 2024-09-17
*/
@RestController
@RequestMapping("/discharge/energyPrice")
@AllArgsConstructor
public class EnergyCalculatePriceController {
    private final EnergyCalculatePriceService energyCalculatePriceService;

    @InitBinder
    public void initBinder(WebDataBinder binder, WebRequest request) {
        //转换日期
        DateFormat dateFormat=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
    }

    /**
     * 详情
     * @param query 查询参数
     * @return 结果
     */
    @GetMapping("/detail")
    public R<EnergyCalculatePrice> get(EnergyCalculatePrice query){
        return R.success(energyCalculatePriceService.getLastOne(query), "查询成功");
    }

    /**
     * 保存
     * @param energyCalculatePrice 数据
     * @return 结果
     */
    @PostMapping
    public R save(@RequestBody @Valid EnergyCalculatePrice energyCalculatePrice){
        if (ObjectUtil.isEmpty(energyCalculatePrice.getGasolinePriceFive())) {
            energyCalculatePrice.setGasolinePriceFive(energyCalculatePrice.getGasolinePriceTwo());
        }
        if (ObjectUtil.isEmpty(energyCalculatePrice.getGasolinePriceEight())) {
            energyCalculatePrice.setGasolinePriceEight(energyCalculatePrice.getGasolinePriceTwo());
        }
        EnergyCalculatePrice old = energyCalculatePriceService.getOne(new LambdaQueryWrapper<EnergyCalculatePrice>()
                .eq(EnergyCalculatePrice::getReportTime, energyCalculatePrice.getReportTime()));
        if (ObjectUtil.isEmpty(old)) {
            return R.success(energyCalculatePriceService.save(energyCalculatePrice), "操作成功");
        } else {
            energyCalculatePrice.setId(old.getId());
            return R.success(energyCalculatePriceService.updateById(energyCalculatePrice), "操作成功");
        }
    }

    /**
     * 修改
     * @param energyCalculatePrice 数据
     * @return 结果
     */
    @PutMapping
    public R update(@RequestBody @Valid EnergyCalculatePrice energyCalculatePrice){
        if (ObjectUtil.isEmpty(energyCalculatePrice.getGasolinePriceFive())) {
            energyCalculatePrice.setGasolinePriceFive(energyCalculatePrice.getGasolinePriceTwo());
        }
        if (ObjectUtil.isEmpty(energyCalculatePrice.getGasolinePriceEight())) {
            energyCalculatePrice.setGasolinePriceEight(energyCalculatePrice.getGasolinePriceTwo());
        }
        return R.success(energyCalculatePriceService.updateById(energyCalculatePrice), "操作成功");
    }

    /**
     * 删除
     * @param id 数据ID
     * @return 结果
     */
    @DeleteMapping(value = "/{id}")
    public R delete(@PathVariable Long id){
        return R.success(energyCalculatePriceService.removeById(id), "操作成功");
    }

}
