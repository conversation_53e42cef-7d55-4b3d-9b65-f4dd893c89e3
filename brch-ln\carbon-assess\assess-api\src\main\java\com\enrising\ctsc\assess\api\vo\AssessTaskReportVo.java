package com.enrising.ctsc.assess.api.vo;

import com.enrising.ctsc.assess.api.entity.Attachment;
import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 考核任务上报表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
public class AssessTaskReportVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date updateTime;

    /**
     * 考核任务id
     */
    private Long templateId;

    /**
     * 考核任务id
     */
    private Long templateObjectId;

    /**
     * 二级指标id
     */
    private Long secondaryTargetId;

    /**
     * 上报名称
     */
    private String reportName;

    /**
     * 上报说明
     */
    private String reportDescription;

    /**
     * 考核评分
     */
    private Double assessScore;

    /**
     * 考核评价
     */
    private String assessEvaluate;

    /**
     * 按规则得分
     */
    private Double ruleScore;

    /**
     * 考评人
     */
    private Long assesser;

    /**
     * 考评人姓名
     */
    private String assesserName;

    /**
     * 考评时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date assessTime;

    /**
     * 市州公司\部门左侧列表，显示好多未处理个数
     */
    private Integer undoTaskNum;

    /**
     * 公司id
     */
    private Long companyId;


    /**
     * 相关资料
     */
    private List<Attachment> attachmentList;

    /**
     * 上报状态
     */
    private String reportStatus;

    /**
     * 下标 一、二
     */
    private String index;

    /**
     * 下标 1、2
     */
    private Integer num;

    /**
     * 指标周期
     */
    private String targetPeriod;

    /**
     * 报告类型 1-1份报告 2-3份报告 3-4份报告  4-12份报告
     */
    private String reportType;

    /**
     * 上报时间
     */
    private Integer reportTime;

    /**
     * 上报人
     */
    private String reportBy;


    /**
     * 考评人
     */
    private String assessBy;

    /**
     * 上报时间
     */
    private Date reportDate;

    /**
     * 是否到了上报时间
     */
    private boolean report;
}
