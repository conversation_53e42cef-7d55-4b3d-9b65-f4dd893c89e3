package com.enrising.ctsc.assess.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
* 指标状态
*
* <AUTHOR> <EMAIL>
* @since 1.0.0 2022-12-15
*/
@Getter
@AllArgsConstructor
public enum ReportTimeType {
	/***/
	YEAR("1", 1,"年上报","年"),
	YEAR_MONTH("2", 3,"季度上报-按月上报","月"),
	QUARTER("3", 4,"季度上报","季度"),
	MONTH("4", 12,"月上报","月"),
	;
	private final String type;
	private final Integer reportNum;
	private final String name;
	private final String unitOfTime;

}
