package com.enrising.ctsc.carbon.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.zhxu.bs.BeanSearcher;
import cn.zhxu.bs.FieldOps;
import cn.zhxu.bs.util.MapUtils;
import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.enrising.ctsc.carbon.common.entity.Role;
import com.enrising.ctsc.carbon.common.entity.User;
import com.enrising.ctsc.carbon.common.entity.UserRoles;
import com.enrising.ctsc.carbon.common.enums.SysRoleEnums;
import com.enrising.ctsc.carbon.common.vo.IdNameVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Token 工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class JwtUtils {

    public static User getUser() {
        String token = AuthorizationUtil.getAuthorizationHeader();
        User user = new User();
        try {
            DecodedJWT jwt = JWT.decode(token);
            Map<String, Claim> claims = jwt.getClaims();
            Map<String, Object> uMap = claimsToMap(claims);
            user.setId(Long.valueOf(uMap.get("userId").toString()));
            user.setLoginId(uMap.get("loginId").toString());
            //用户名称
            user.setUserName(uMap.get("userName").toString());
            user.setHrLoginId(uMap.get("hrLoginId").toString());
            //设置perms
            if (uMap.get("perms") != null && StringUtils.isNotEmpty(uMap.get("perms").toString())) {
                Set<String> perms = new HashSet<>(Arrays.asList(uMap.get("perms").toString().split(",")));
                user.setPerms(perms);
            }
            //设置公司和部门
            if (uMap.get("departments") != null && StringUtils.isNotEmpty(uMap.get("departments").toString())) {
                List<IdNameVo> list = getIdNameListFromString(uMap.get("departments").toString());
                user.setDepartments(list);
                if (CollUtil.isNotEmpty(list)) {
                    user.setDepartmentNo(Long.valueOf(list.get(0).getId()));
                }
            }

            if (uMap.get("companies") != null && StringUtils.isNotEmpty(uMap.get("companies").toString())) {
                List<IdNameVo> list = getIdNameListFromString(uMap.get("companies").toString());
                user.setCompanies(list);
            }
            //电话号码
            if (uMap.get("phone") != null && StringUtils.isNotEmpty(uMap.get("phone").toString())) {
                user.setPhone(uMap.get("phone").toString());
            }
            // 设置角色列表
            BeanSearcher beanSearcher = SpringUtils.getBean(BeanSearcher.class);
            user.setRoleIds(beanSearcher.searchList(UserRoles.class, MapUtils.builder()
                                    .field(UserRoles::getUserId, user.getId()).op(FieldOps.Equal)
                                    .build()
                            ).stream().map(UserRoles::getRoleId)
                            .collect(Collectors.toList())
            );
        } catch (Exception e) {
            user = null;
            log.error("token解析失败:{}", token, e);
        }
        log.info("GetUser:{}", user);
        return user;
    }

    public static List<IdNameVo> getIdNameListFromString(String idNameString) {
        List<IdNameVo> list = new ArrayList<IdNameVo>();
        String[] nodes = idNameString.split(";");
        for (String node : nodes) {
            IdNameVo vo = new IdNameVo();
            vo.setId(StringUtils.substringBefore(node, ","));
            vo.setName(StringUtils.substringAfter(node, ","));
            list.add(vo);
        }
        return list;
    }

    public static Map<String, Object> claimsToMap(Map<String, Claim> claims) {
        if (claims == null) {
            return null;
        }

        Map<String, Object> map = new HashMap<String, Object>();

        for (String key : claims.keySet()) {
            map.put(key, claims.get(key).asString());
        }

        return map;
    }

    public static Long getCurrentUserCompanyId() {
        User user = getUser();
        return getCurrentUserCompanyId(user);
    }

    public static Long getCurrentUserCompanyId(User user) {
        if (ObjectUtil.isEmpty(user)) {
            return null;
        }
        if (CollectionUtil.isNotEmpty(user.getCompanies())) {
            return Long.parseLong(user.getCompanies().get(0).getId());
        } else if (CollectionUtil.isNotEmpty(user.getDepartments())) {
            return Long.parseLong(user.getDepartments().get(0).getId().substring(0, 4) + "000000");
        }
        return null;
    }

    /**
     * 获取用户最大权限级别
     * @param user 用户
     * @return 用户最大权限级别
     */
    public static int getUserMaxRoleLevel(User user) {
        if (ObjectUtil.isEmpty(user)) {
            user = getUser();
        }
        List<Long> roleIds = user.getRoleIds();
        log.info("roles:{}", roleIds);
        if (CollUtil.isEmpty(roleIds)) {
            return SysRoleEnums.ROLE_ORDINARY.getLevel();
        }

        // 根据角色ID查询角色代码
        List<String> roleCodes = getRoleCodesByRoleIds(roleIds);

        // 使用SysRoleEnums获取最大角色等级
        return SysRoleEnums.getMaxRoleLevel(roleCodes);
    }

    /**
     * 根据角色ID列表获取角色代码列表
     * @param roleIds 角色ID列表
     * @return 角色代码列表
     */
    private static List<String> getRoleCodesByRoleIds(List<Long> roleIds) {
        if (CollUtil.isEmpty(roleIds)) {
            return new ArrayList<>();
        }

        try {
            BeanSearcher beanSearcher = SpringUtils.getBean(BeanSearcher.class);
            List<String> roleCodes = new ArrayList<>();

            for (Long roleId : roleIds) {
                List<Role> roles = beanSearcher.searchList(Role.class, MapUtils.builder()
                        .field(Role::getId, roleId).op(FieldOps.Equal)
                        .build());

                if (CollUtil.isNotEmpty(roles)) {
                    Role role = roles.get(0);
                    if (StringUtils.isNotEmpty(role.getCode())) {
                        roleCodes.add(role.getCode());
                    }
                }
            }

            return roleCodes;
        } catch (Exception e) {
            log.error("获取角色代码失败", e);
            return new ArrayList<>();
        }
    }
}
