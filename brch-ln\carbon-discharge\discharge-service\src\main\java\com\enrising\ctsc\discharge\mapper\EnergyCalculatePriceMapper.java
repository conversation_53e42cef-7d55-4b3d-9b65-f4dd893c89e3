package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.enrising.ctsc.discharge.api.entity.EnergyCalculatePrice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 能源单价表
 *
 * <AUTHOR>
 * @since 2024-09-17
 */
@Mapper
public interface EnergyCalculatePriceMapper extends BaseMapper<EnergyCalculatePrice> {
    EnergyCalculatePrice getLastOne(@Param("query") EnergyCalculatePrice query);
}