package com.enrising.ctsc.assess.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.assess.api.bo.AssessTargetBo;
import com.enrising.ctsc.assess.api.entity.AssessTarget;
import com.enrising.ctsc.assess.api.query.AssessTargetQuery;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.assess.api.vo.AssessTargetVo;

/**
 * 考核指标
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
public interface AssessTargetService extends IService<AssessTarget> {

	/**
	 * 分页查询
	 *
	 * @param page  分页
	 * @param query 参数
	 * @return 列表
	 */
	TableDataInfo<AssessTargetVo> findList(Page<AssessTargetVo> page, AssessTargetQuery query);

	/**
	 * 详情
	 *
	 * @param query 参数
	 * @return 详情
	 */
	AssessTargetVo detail(AssessTargetQuery query);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	void add(AssessTargetBo bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	void edit(AssessTargetBo bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	void del(Long id);

}
