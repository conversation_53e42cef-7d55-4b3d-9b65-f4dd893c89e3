# 碳排放系统附件功能使用说明

## 概述

基于现有的 `attachments` 表和 `Attachments` 实体类，使用 MyBatis-Plus 重构实现了完整的附件管理功能。

## 核心组件

### 1. 实体类

- `Attachments.java` - 附件实体，已添加 MyBatis-Plus 注解

### 2. 数据访问层

- `AttachmentsMapper.java` - Mapper 接口
- `AttachmentsMapper.xml` - SQL 映射文件

### 3. 服务层

- `AttachmentsService.java` - 服务接口
- `AttachmentsServiceImpl.java` - 服务实现

### 4. 配置类

- `MyBatisPlusConfig.java` - MyBatis-Plus 配置，包含自动填充

### 5. 工具类

- `AttachmentUtil.java` - 附件操作工具类

### 6. 重构的服务

- `UploadServiceImpl.java` - 上传服务，已重构使用新的附件管理

## 功能特性

### 1. 自动填充

- 创建时间、更新时间自动填充
- 创建人、年份自动填充
- 支持自定义用户信息集成

### 2. 逻辑删除

- 通过 `del_flag` 字段实现逻辑删除
- MyBatis-Plus 自动处理查询过滤

### 3. 分页支持

- 内置分页插件，支持分页查询

### 4. 事务管理

- 关键操作支持事务回滚

## 使用示例

### 1. 保存附件信息

```java
@Autowired
private AttachmentsService attachmentsService;

// 方式一：直接使用服务
public void saveAttachment(MultipartFile file, String url, String bucketName, String objectName) {
    Attachments attachments = new Attachments();
    attachments.setBusiId(businessId);
    attachments.setBusiAlias("carbon_report");
    attachments.setBusiAliasName("碳排放报告");
    attachments.setCategoryCode("file");
    attachments.setCategoryName("文件");
    attachments.setFileName(file.getOriginalFilename());
    attachments.setFileType(file.getContentType());
    attachments.setFileSize(file.getSize());
    attachments.setUrl(url);
    attachments.setBucketName(bucketName);
    attachments.setObjectName(objectName);
    attachments.setSaveType("MINIO");
    attachments.setDelFlag("0");
    
    boolean saved = attachmentsService.saveAttachment(attachments);
}

// 方式二：使用工具类（推荐）
public void saveAttachmentWithUtil(MultipartFile file, String url, String bucketName, String objectName) {
    Attachments attachments = AttachmentUtil.createAttachment(
        file, 
        businessId,
        AttachmentUtil.BusiAlias.CARBON_REPORT, 
        "碳排放报告",
        AttachmentUtil.CategoryCode.FILE,
        "文件",
        url,
        bucketName,
        objectName
    );
    
    boolean saved = attachmentsService.saveAttachment(attachments);
}
```

### 2. 查询附件

```java
// 根据业务ID和业务别名查询
List<Attachments> attachments = attachmentsService.getByBusiIdAndAlias(
    businessId, 
    AttachmentUtil.BusiAlias.CARBON_REPORT
);

// 批量查询多个业务的附件
List<Long> businessIds = Arrays.asList(1L, 2L, 3L);
List<Attachments> allAttachments = attachmentsService.getByBusiIds(businessIds);

// 使用工具类查询
AttachmentUtil attachmentUtil; // 注入工具类
List<Attachments> utilAttachments = attachmentUtil.getAttachmentsByBusiness(
    businessId, 
    AttachmentUtil.BusiAlias.CARBON_REPORT
);
```

### 3. 删除附件

```java
// 删除单个附件
boolean deleted = attachmentsService.deleteAttachment(attachmentId);

// 批量删除
List<Long> attachmentIds = Arrays.asList(1L, 2L, 3L);
boolean batchDeleted = attachmentsService.batchDeleteAttachments(attachmentIds);

// 使用工具类删除
AttachmentUtil attachmentUtil; // 注入工具类
boolean utilDeleted = attachmentUtil.deleteAttachment(attachmentId);
```

### 4. 分页查询

```java
// 使用 MyBatis-Plus 分页
@Autowired
private AttachmentsMapper attachmentsMapper;

public IPage<Attachments> getAttachmentsPage(int current, int size, Long busiId, String busiAlias) {
    Page<Attachments> page = new Page<>(current, size);
    LambdaQueryWrapper<Attachments> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(Attachments::getBusiId, busiId)
               .eq(Attachments::getBusiAlias, busiAlias)
               .eq(Attachments::getDelFlag, "0")
               .orderByDesc(Attachments::getCreateTime);
    
    return attachmentsMapper.selectPage(page, queryWrapper);
}
```

## 常用业务别名

工具类 `AttachmentUtil.BusiAlias` 提供了常用的业务别名常量：

- `CARBON_FILE` - 碳排放文件
- `CARBON_ILLUSTRATION` - 碳排放插图
- `CARBON_REPORT` - 碳排放报告
- `CARBON_CALCULATION` - 碳排放计算
- `CARBON_MONITORING` - 碳排放监测
- `CARBON_AUDIT` - 碳排放审核

## 常用分类代码

工具类 `AttachmentUtil.CategoryCode` 提供了常用的分类代码：

- `FILE` - 文件
- `IMAGE` - 图片
- `DOCUMENT` - 文档
- `VIDEO` - 视频

## 配置说明

### 1. 自动填充配置

在 `MyBatisPlusConfig.MyMetaObjectHandler` 中可以自定义填充逻辑：

```java
@Override
public void insertFill(MetaObject metaObject) {
    LocalDateTime now = LocalDateTime.now();
    
    // 获取当前登录用户（需要根据实际情况实现）
    // UserInfo currentUser = getCurrentUser();
    // Long userId = currentUser != null ? currentUser.getId() : 1L;
    // String userName = currentUser != null ? currentUser.getName() : "system";
    
    this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, now);
    this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, now);
    this.strictInsertFill(metaObject, "year", Integer.class, now.getYear());
    this.strictInsertFill(metaObject, "creatorId", Long.class, 1L);
    this.strictInsertFill(metaObject, "creatorName", String.class, "system");
}
```

### 2. 逻辑删除配置

实体类中已配置 `@TableLogic` 注解：

```java
@TableLogic
@TableField("del_flag")
private String delFlag;
```

MyBatis-Plus 会自动处理：

- 查询时自动过滤已删除数据
- 删除时自动转换为更新操作

## 扩展建议

### 1. 业务类型扩展

可以在 `AttachmentUtil.BusiAlias` 中添加新的业务类型：

```java
public static class BusiAlias {
    // ... 现有常量
    
    /** 新业务类型 */
    public static final String NEW_BUSINESS = "new_business";
}
```

### 2. 用户信息集成

在 `MyMetaObjectHandler` 中集成当前登录用户信息：

```java
// 获取当前用户的具体实现
private UserInfo getCurrentUser() {
    // 从 SecurityContext、Session 或其他方式获取当前用户
    return UserContextHolder.getCurrentUser();
}
```

### 3. 文件类型验证

可以在保存前添加文件类型验证：

```java
private void validateFileType(String fileName, String contentType) {
    // 实现文件类型验证逻辑
    List<String> allowedTypes = Arrays.asList("pdf", "doc", "docx", "jpg", "png");
    String extension = getFileExtension(fileName);
    if (!allowedTypes.contains(extension.toLowerCase())) {
        throw new BusinessException("不支持的文件类型：" + extension);
    }
}
```

## 注意事项

1. **数据库兼容性**：确保数据库表结构与实体类字段对应
2. **事务管理**：重要操作建议使用 `@Transactional` 注解
3. **异常处理**：适当处理数据库操作异常
4. **性能优化**：合理使用索引，避免大数据量查询
5. **安全考虑**：文件上传前进行安全检查

## 总结

通过这次重构，实现了：

1. **标准化**：使用 MyBatis-Plus 标准操作方式
2. **自动化**：自动填充时间、用户信息
3. **安全性**：逻辑删除保证数据安全
4. **便捷性**：工具类简化常用操作
5. **扩展性**：良好的架构便于功能扩展

现在您可以通过标准的 MyBatis-Plus 方式操作附件数据，同时保持了与原有接口的兼容性。 
