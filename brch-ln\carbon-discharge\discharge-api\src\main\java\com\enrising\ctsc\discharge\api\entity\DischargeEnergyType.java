package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放能源类型表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */

@Data
@TableName("discharge_energy_type")
public class DischargeEnergyType extends Model<DischargeEnergyType> {

	/**
	 * 主键id,采用雪花id
	 */
		@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 创建者id
	 */
		private Long createBy;

	/**
	 * 创建时间
	 */
		private Date createTime;

	/**
	 * 更新者id
	 */
		private Long updateBy;

	/**
	 * 更新时间
	 */
		private Date updateTime;

	/**
	 * 能源类型：1-水、2-电、3-气、4-油、5-热力
	 */
		private String energyType;

	/**
	 * 单位id：使用字典
	 */
		private String unit;

	/**
	 * 细类
	 */
		private String secondName;

	/**
	 * 删除标志：0-正常；1-删除
	 */
		private String delFlag;

	/**
	 * 启用状态
	 */
		private String status;

	/**
	 * 密度
	 */
		private BigDecimal density;

	/**
	 * 能源类型大小类
	 */
		private String sizeType;

}
