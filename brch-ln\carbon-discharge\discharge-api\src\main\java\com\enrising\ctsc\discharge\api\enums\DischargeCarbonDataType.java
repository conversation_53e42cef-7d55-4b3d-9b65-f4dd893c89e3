package com.enrising.ctsc.discharge.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
* 碳排放总量数据类型
*
* <AUTHOR>
* @since 1.0.0 2023-1-3
*/
@Getter
@AllArgsConstructor
public enum DischargeCarbonDataType {
	/***/
	TYPE_ALL("0", "全部"),
	CARBON_EMISSIONS("1", "碳排放量"),
	STAND_COAL_CONSUMPTION("2", "标准煤使用量"),
	POWER("3", "电"),
	OIL("4", "油"),
	GASOLINE("5", "汽油"),
	DIESEL("6", "柴油"),
	CRUDE("7", "原油"),
	FUEL("8", "燃料油"),
	KEROSENE("9", "煤油"),
	GAS("10", "气"),
	NG("11", "天然气"),
	LPG("12", "液化石油气"),
	WATER("13", "水"),
	THERMAL("14", "热力"),
	COAL("15", "煤炭"),
	OUTSOURCING_THERMAL_POWER("16", "外购火电"),
	OWN_GREEN_POWER("17", "自有新能源发电"),
	OUTSOURCING_THERMAL("18", "外购热力"),
	SOLID_LPG("19", "固定源—液化石油气"),
	;
	private final String value;
	private final String name;

}
