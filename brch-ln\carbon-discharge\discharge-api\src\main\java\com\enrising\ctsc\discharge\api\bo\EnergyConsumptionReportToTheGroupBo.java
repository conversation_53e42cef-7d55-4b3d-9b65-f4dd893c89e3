package com.enrising.ctsc.discharge.api.bo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 能耗上报到集团
 */
@Data
public class EnergyConsumptionReportToTheGroupBo {

	/**
	 * 上报时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD)
	private Date reportTime;

	/**
	 * 数据类型，0-原始数据，1-按规则计算数据
	 */
	private String countType;
}
