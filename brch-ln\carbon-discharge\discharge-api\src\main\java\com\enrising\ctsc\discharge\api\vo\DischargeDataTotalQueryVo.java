package com.enrising.ctsc.discharge.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 碳排放总量数据查询
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeDataTotalQueryVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据填报单位id
	 */
	private Long companyId;

	/**
	 * 数据填报单位名称
	 */
	private String companyName;

	/**
	 * 数据年份
	 */
	private String dataYear;

	/**
	 * 全年数据
	 */
	private BigDecimal dataAllYear;

	/**
	 * 数据列表
	 */
	private BigDecimal[] dataArray;

	/**
	 * 数据标题列表
	 */
	private String[] dataTitleArray;

	/**
	 * 1月数据
	 */
	private BigDecimal dataJan;

	/**
	 * 2月数据
	 */
	private BigDecimal dataFeb;
	/**
	 * 3月数据
	 */
	private BigDecimal dataMar;

	/**
	 * 4月数据
	 */
	private BigDecimal dataApr;
	/**
	 * 5月数据
	 */
	private BigDecimal dataMay;

	/**
	 * 6月数据
	 */
	private BigDecimal dataJun;
	/**
	 * 7月数据
	 */
	private BigDecimal dataJul;

	/**
	 * 8月数据
	 */
	private BigDecimal dataAug;
	/**
	 * 9月数据
	 */
	private BigDecimal dataSept;

	/**
	 * 10月数据
	 */
	private BigDecimal dataOct;
	/**
	 * 11月数据
	 */
	private BigDecimal dataNov;

	/**
	 * 12月数据
	 */
	private BigDecimal dataDec;

	/**
	 * 排名
	 */
	private Integer num = 0;

	/**
	 * 区划编码
	 */
	private String cityCode;
}