package com.enrising.ctsc.carbon.common.vo;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 页面上用户ID NAME字段展示用的VO
 * <AUTHOR>
 *
 */
@Setter
@Getter
public class IdNameVo implements Serializable{

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	private String id;
	private String name;

	@Override
	public boolean equals(Object object){
		if (object instanceof IdNameVo) {
			return ((IdNameVo) object).id.equals(this.id);
		}
		return false;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SIMPLE_STYLE);
	}
}
