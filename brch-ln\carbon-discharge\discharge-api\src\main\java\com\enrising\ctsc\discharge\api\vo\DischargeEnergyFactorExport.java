package com.enrising.ctsc.discharge.api.vo;

import com.github.liaochong.myexcel.core.annotation.ExcelColumn;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 碳排放能源转换因子表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeEnergyFactorExport implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 能源细类名称
	 */
	@ExcelColumn(order = 0, title = "类型")
	private String energySecondName;

	/**
	 * 数据有效期起始日期
	 */
	@ExcelColumn(order = 1, title = "数据有效期起始日期", format = "yyyy")
	private Date validityStart;

	/**
	 * 数据有效期结束日期
	 */
	@ExcelColumn(order = 2, title = "数据有效期结束日期", format = "yyyy")
	private Date validityEnd;

	/**
	 * 转换因子
	 */
	@ExcelColumn(order = 3, title = "转换因子")
	private String factorStr;

	/**
	 * 数据标准来源
	 */
	@ExcelColumn(order = 4, title = "数据标准来源")
	private String source;

}