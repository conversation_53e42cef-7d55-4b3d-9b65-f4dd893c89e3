package com.enrising.ctsc.discharge.api.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 碳排放画像--中部展示内容vo
 *
 * <AUTHOR>
 */
@Data
public class DischargeFossilVo {

	/**
	 *碳排放总量
	 */
	private BigDecimal carbonTotal;

	/**
	 *直接碳排放
	 */
	private BigDecimal directTotal;

	/**
	 *间接碳排放
	 */
	private BigDecimal indirectTotal;


	/**
	 *能源消耗总量
	 */
	private BigDecimal energyTotal;

	/**
	 * 化石能源消耗
	 */
	private BigDecimal fossilTotal;

	/**
	 * 非化石能源消耗
	 */
	private BigDecimal nonFossilTotal;

	/**
	 * 碳排放强度
	 */
	private BigDecimal carbonIntensity = new BigDecimal(0);

	/**
	 * 业务碳排放强度
	 */
	private BigDecimal busCarbonIntensity = new BigDecimal(0);
}
