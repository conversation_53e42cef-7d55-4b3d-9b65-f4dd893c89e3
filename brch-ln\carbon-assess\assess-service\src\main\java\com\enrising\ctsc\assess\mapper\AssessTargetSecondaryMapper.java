package com.enrising.ctsc.assess.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.assess.api.bo.AssessTemplateTargetObjectBo;
import com.enrising.ctsc.assess.api.entity.AssessTargetSecondary;
import com.enrising.ctsc.assess.api.query.AssessTargetSecondaryQuery;
import com.enrising.ctsc.assess.api.vo.AssessTargetSecondaryVo;
import com.enrising.ctsc.assess.api.vo.AssessTaskInfoVo;
import com.enrising.ctsc.assess.api.vo.CompanyCarbonVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 考核二级指标
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Mapper
public interface AssessTargetSecondaryMapper extends BaseMapper<AssessTargetSecondary> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
	IPage<AssessTargetSecondaryVo> findList(Page<AssessTargetSecondaryVo> page, @Param(Constants.WRAPPER) Wrapper<AssessTargetSecondaryQuery> wrapper);

	/**
	 * 列表查询
	 *
	 * @param wrapper 条件
	 * @return 列表
	 */
	List<AssessTargetSecondaryVo> findList(@Param(Constants.WRAPPER) Wrapper<AssessTargetSecondaryQuery> wrapper);

	/**
	 * 详情查询
	 *
	 * @param wrapper 条件
	 * @return 结果
	 */
	AssessTargetSecondaryVo detail(@Param(Constants.WRAPPER) Wrapper<AssessTargetSecondaryQuery> wrapper);

	/**
	 * 根据一级指标删除二级指标
	 *
	 * @param id 一级指标id
	 * @return 结果
	 */
	@Select("delete from assess_target_secondary where primary_target_id = #{id}")
	Integer delByPrimaryTargetId(@Param("id") Long id);

	/*
	 * 任务成效管理 -- 根据指标ids查询模板总分
	 * */
	double getTotalScoreByTargetIds(@Param("targetIds") List<Long> targetIds);

	/*
	 * 考核任务管理 -- 根据指标ids查询详情
	 * */
	AssessTaskInfoVo getInfoByTargetId(@Param("bo") AssessTemplateTargetObjectBo bo);

	/**
	 * 详情查询
	 *
	 * @param id 二级指标id
	 * @return 结果
	 */
	AssessTargetSecondaryVo getFullDetail(@Param("id") Long id);

	List<AssessTargetSecondaryVo> targetSecondaryList(@Param("query") AssessTargetSecondaryQuery query);

	Page<CompanyCarbonVo> getCompanyCarbonByPage(@Param("page") Page<CompanyCarbonVo> companyCarbonVoPage, @Param("query") AssessTargetSecondaryQuery query);
}