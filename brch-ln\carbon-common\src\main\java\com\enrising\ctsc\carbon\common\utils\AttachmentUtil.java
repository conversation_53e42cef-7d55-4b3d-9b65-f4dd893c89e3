package com.enrising.ctsc.carbon.common.utils;

import com.enrising.ctsc.carbon.common.entity.Attachments;
import com.enrising.ctsc.carbon.common.service.AttachmentsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 附件工具类
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Component
@RequiredArgsConstructor
public class AttachmentUtil {

    private final AttachmentsService attachmentsService;

    /**
     * 创建标准附件对象
     *
     * @param file          上传文件
     * @param busiId        业务ID
     * @param busiAlias     业务别名
     * @param busiAliasName 业务别名名称
     * @param categoryCode  分类代码
     * @param categoryName  分类名称
     * @param url           文件URL
     * @param bucketName    桶名称
     * @param objectName    对象名称
     * @return 附件对象
     */
    public static Attachments createAttachment(MultipartFile file, Long busiId, String busiAlias,
                                               String busiAliasName, String categoryCode, String categoryName,
                                               String url, String bucketName, String objectName) {
        Attachments attachments = new Attachments();
        attachments.setBusiId(busiId);
        attachments.setBusiAlias(busiAlias);
        attachments.setBusiAliasName(busiAliasName);
        attachments.setCategoryCode(categoryCode);
        attachments.setCategoryName(categoryName);
        attachments.setFileName(file.getOriginalFilename());
        attachments.setFileType(file.getContentType());
        attachments.setFileSize(file.getSize());
        attachments.setUrl(url);
        attachments.setBucketName(bucketName);
        attachments.setObjectName(objectName);
        attachments.setSaveType("MINIO");
        attachments.setDelFlag("0");
        return attachments;
    }

    /**
     * 根据业务ID和业务别名查询附件列表
     *
     * @param busiId    业务ID
     * @param busiAlias 业务别名
     * @return 附件列表
     */
    public List<Attachments> getAttachmentsByBusiness(Long busiId, String busiAlias) {
        return attachmentsService.getByBusiIdAndAlias(busiId, busiAlias);
    }

    /**
     * 根据业务ID列表查询附件
     *
     * @param busiIds 业务ID列表
     * @return 附件列表
     */
    public List<Attachments> getAttachmentsByBusinessIds(List<Long> busiIds) {
        return attachmentsService.getByBusiIds(busiIds);
    }

    /**
     * 删除附件
     *
     * @param id 附件ID
     * @return 删除结果
     */
    public boolean deleteAttachment(Long id) {
        return attachmentsService.deleteAttachment(id);
    }

    /**
     * 批量删除附件
     *
     * @param ids 附件ID列表
     * @return 删除结果
     */
    public boolean batchDeleteAttachments(List<Long> ids) {
        return attachmentsService.batchDeleteAttachments(ids);
    }

    /**
     * 常用业务别名常量
     */
    public static class BusiAlias {
        /**
         * 碳排放文件
         */
        public static final String CARBON_FILE = "carbon_file";
        /**
         * 碳排放插图
         */
        public static final String CARBON_ILLUSTRATION = "carbon_illustration";
        /**
         * 碳排放报告
         */
        public static final String CARBON_REPORT = "carbon_report";
        /**
         * 碳排放计算
         */
        public static final String CARBON_CALCULATION = "carbon_calculation";
        /**
         * 碳排放监测
         */
        public static final String CARBON_MONITORING = "carbon_monitoring";
        /**
         * 碳排放审核
         */
        public static final String CARBON_AUDIT = "carbon_audit";
    }

    /**
     * 常用分类代码常量
     */
    public static class CategoryCode {
        /**
         * 文件
         */
        public static final String FILE = "file";
        /**
         * 图片
         */
        public static final String IMAGE = "image";
        /**
         * 文档
         */
        public static final String DOCUMENT = "document";
        /**
         * 视频
         */
        public static final String VIDEO = "video";
    }
}
