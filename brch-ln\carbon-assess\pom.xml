<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>
	<groupId>com.enrising.ctsc</groupId>
	<artifactId>carbon-assess</artifactId>
	<version>1.0.0-SNAPSHOT</version>
	<packaging>pom</packaging>
	<description>碳考核模块</description>

	<modules>
		<module>assess-api</module>
		<module>assess-service</module>
	</modules>

	<properties>
		<maven.compiler.source>8</maven.compiler.source>
		<maven.compiler.target>8</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<spring.boot.version>2.4.4</spring.boot.version>
		<druid.version>1.1.24</druid.version>
		<fastjson.version>1.2.73</fastjson.version>
		<shiro.version>1.5.3</shiro.version>
		<thymeleaf.extras.shiro.version>2.0.0</thymeleaf.extras.shiro.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>${spring.boot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

</project>
