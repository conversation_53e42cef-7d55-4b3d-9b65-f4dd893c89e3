package com.enrising.ctsc.assess.api.vo;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.sccl.common.utils.MathUtils;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 碳排放排名
 *
 * <AUTHOR>
 * @since 1.0.0 2023-10-15
 */

@Data
public class CarbonRankingVo extends Model<CarbonRankingVo> {

	/**
	 * 填报单位id
	 */
	private Long deptId;

	/**
	 * 填报单位名称
	 */
	private String deptName;

	/**
	 * 填报单位城市编码
	 */
	private String cityCode;

	/**
	 * 汉字排名
	 */
	private String rankNum;

	/**
	 * 碳排放量（tCO2）
	 */
	private BigDecimal carbonEmissions = BigDecimal.valueOf(0);

	/**
	 * 去年碳排放量（tCO2）
	 */
	private BigDecimal carbonEmissionsLastYear = BigDecimal.valueOf(0);

	/**
	 * 能源消耗总量（tce）
	 */
	private BigDecimal energyConsumption = BigDecimal.valueOf(0);

	/**
	 * 电信业务总量（万元）
	 */
	private BigDecimal telecomBusinessTotal = BigDecimal.valueOf(0);

	/**
	 * 去年电信业务总量（万元）
	 */
	private BigDecimal telecomBusinessTotalLastYear = BigDecimal.valueOf(0);

	/**
	 * 业务流量总量（TB）
	 */
	private BigDecimal businessFlowTotal = BigDecimal.valueOf(0);

	/**
	 * 碳排强度（kgCO2/万元）
	 */
	private BigDecimal carbonStrength;

	/**
	 * 去年碳排强度（kgCO2/万元）
	 */
	private BigDecimal carbonStrengthLastYear;

	/**
	 * 业务碳排强度（kgCO2/TB）
	 */
	private BigDecimal carbonBusinessStrength;

	/**
	 * 碳排放额度使用率
	 */
	private String useRate;

	/**
	 * 碳排放量同比增幅
	 */
	private BigDecimal carbonEmissionsGrowthRate;

	/**
	 * 碳排强度同比降幅
	 */
	private BigDecimal carbonStrengthDecreaseRate;

	/**
	 * 电信业务总量同比增幅
	 */
	private Double telecomBusinessTotalGrowthRate;

	/**
	 * 背景状态
	 */
	private String state;

	public BigDecimal getCarbonStrength() {
		return MathUtils.division(carbonEmissions.multiply(BigDecimal.valueOf(1000)), telecomBusinessTotal);
	}

	public BigDecimal getCarbonStrengthLastYear() {
		return MathUtils.division(carbonEmissionsLastYear.multiply(BigDecimal.valueOf(1000)), telecomBusinessTotalLastYear);
	}

	public BigDecimal getCarbonBusinessStrength() {
		return MathUtils.division(carbonEmissions.multiply(BigDecimal.valueOf(1000)), businessFlowTotal);
	}

	public BigDecimal getCarbonEmissionsGrowthRate() {
		return new BigDecimal(MathUtils.getUpRate(carbonEmissions, carbonEmissionsLastYear));
	}

	public BigDecimal getCarbonStrengthDecreaseRate() {
		return new BigDecimal(MathUtils.getUpRate(getCarbonStrength(), getCarbonStrengthLastYear()));
	}

	public Double getTelecomBusinessTotalGrowthRate() {
		return new BigDecimal(MathUtils.getUpRate(telecomBusinessTotal, telecomBusinessTotalLastYear)).doubleValue();
	}

	public String getState() {
		if (getCarbonStrengthDecreaseRate().compareTo(new BigDecimal(-12)) < 0) {
			return "1";
//				} else if (useRate.compareTo(new BigDecimal(12)) <= 0) {
//					carbonRankingVo.setState("2");
		} else {
			return "3";
		}
	}
}