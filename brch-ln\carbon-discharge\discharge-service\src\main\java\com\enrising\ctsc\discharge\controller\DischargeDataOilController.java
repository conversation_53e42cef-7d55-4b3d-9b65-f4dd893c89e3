package com.enrising.ctsc.discharge.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.discharge.api.bo.DischargeDataOilBo;
import com.enrising.ctsc.discharge.api.vo.DischargeDataOilVo;
import com.enrising.ctsc.discharge.service.DischargeDataOilService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 碳排放数据填报表（油）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@RestController
@RequestMapping("/discharge/oil")

@AllArgsConstructor
public class DischargeDataOilController {
	private final DischargeDataOilService dischargeDataOilService;

	@PostMapping("/getOilListPage")
		public R<Page<DischargeDataOilVo>> getOilListPage(@RequestBody QueryPage<DischargeDataOilBo> queryPage) {
		return R.success(dischargeDataOilService.getOilListPage(queryPage));
	}

	@PostMapping("/getOilListToExcel")
		public R<List<DischargeDataOilVo>> getOilListToExcel(@RequestBody DischargeDataOilBo dischargeDataOilBo) {
		return R.success(dischargeDataOilService.getOilListToExcel(dischargeDataOilBo));
	}

	@GetMapping("/getDataList")
		public R<List<DischargeDataOilVo>> getDataList(Integer dataYear, Long companyId) {
		return R.success(dischargeDataOilService.getDataList(dataYear, companyId));
	}

	@GetMapping("/detail")
		public R<DischargeDataOilVo> get(Long id) {
		DischargeDataOilVo detail = dischargeDataOilService.detail(id);
		return R.success(detail, "查询成功");
	}

		@PostMapping(value = "/save")
		public R<String> save(@RequestBody DischargeDataOilBo bo) {
		String sRet = dischargeDataOilService.add(bo);
		if (StrUtil.isBlank(sRet)){
			return R.success("保存成功");
		}
		return R.failed(sRet);
	}

		@PostMapping(value = "/update")
		public R<String> update(@RequestBody DischargeDataOilBo bo) {
		dischargeDataOilService.edit(bo);
		return R.success("修改成功");
	}

		@PostMapping(value = "/delete/{id}")
		public R<String> delete(@PathVariable Long id) {
		dischargeDataOilService.del(id);
		return R.success("删除成功");
	}
}
