package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放能源转换系数表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */

@Data
@TableName("discharge_energy_coefficient")
public class DischargeEnergyCoefficient extends Model<DischargeEnergyCoefficient> {

	/**
	 * 主键id,采用雪花id
	 */
		@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 创建者id
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 更新者id
	 */
	private Long updateBy;

	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 能源类型id
	 */
	private Long energyTypeId;

	/**
	 * 数据有效期起始日期
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date validityStart;

	/**
	 * 数据有效期结束日期
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date validityEnd;

	/**
	 * 转换系数
	 */
	private BigDecimal coefficient;

	/**
	 * 数据标准来源
	 */
	private String source;

	/**
	 * 删除标志：0-正常；1-删除
	 */
	private String delFlag;

	/**
	 * 能源类型单位名称
	 */
	@Length(max = 32,message = "能源类型单位名称字符不能超过32")
	@NotBlank(message = "能源类型单位名称")
	private String energyTypeUnitName;
}
