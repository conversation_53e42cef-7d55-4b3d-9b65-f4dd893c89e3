/*
 * Copyright (c) 2021 ctsc-cloudx Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 *
 * 此类来自 https://gitee.com/geek_qi/cloud-platform/blob/master/ace-common/src/main/java/com/github/wxiaoqi/security/common/vo/TreeNode.java
 * @ Apache-2.0
 */

package com.enrising.ctsc.discharge.api.bo;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-5-25
 */
@Data
public class TreeNode {

	protected Long id;
	protected Long parentId;
	protected Boolean hasChildren;
	protected List<TreeNode> children = Lists.newArrayList();
	public void add(TreeNode node) {
		children.add(node);
	}

}
