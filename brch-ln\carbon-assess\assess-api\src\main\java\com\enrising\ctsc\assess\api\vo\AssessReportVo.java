package com.enrising.ctsc.assess.api.vo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 考核报告
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
public class AssessReportVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date updateTime;

    /**
     * 考核任务id
     */
    private Long templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 报告名称
     */
    private String reportName = "——";

    /**
     * 报告内容
     */
    private String content;

    /**
     * 意见建议
     */
    private String suggestion;

    /**
     * 是否通知公告
     */
    private String isNotification;

    /**
     * 生成状态
     */
    private String generateStatus;

    /**
     * 报告落款人
     */
    private String reporter;


    /**
     * 报告时间
     */
    private String reportTime;

}
