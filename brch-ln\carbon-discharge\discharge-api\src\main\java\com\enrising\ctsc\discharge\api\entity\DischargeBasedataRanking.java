package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 集团排名情况
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-10-13
 */

@Data
@TableName("discharge_basedata_ranking")
public class DischargeBasedataRanking extends Model<DischargeBasedataRanking> {

	/**
	 * 主键id,采用雪花id
	 */
		@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 创建者id
	 */
		@TableField(fill = FieldFill.INSERT)
	private Long createBy;

	/**
	 * 创建时间
	 */
		@TableField(fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新者id
	 */
		@TableField(fill = FieldFill.UPDATE)
	private Long updateBy;

	/**
	 * 更新时间
	 */
		@TableField(fill = FieldFill.UPDATE)
	private Date updateTime;

	/**
	 * 时间周期-年
	 */
		private String timePeriodYear;

	/**
	 * 季度
	 */
		private String timePeriodQuarter;

	/**
	 * 碳排放量同比增幅
	 */
		private Double carbonQuantityAmplify;

	/**
	 * 排名
	 */
		private String carbonQuantityAmplifyRanking;

	/**
	 * 碳排放强度同比降幅
	 */
		private Double carbonStrengthReduction;

	/**
	 * 排名
	 */
		private String carbonStrengthReductionRanking;

	/**
	 * 电信业务总量同比增幅
	 */
		private Double carbonBusinessGrowth;

	/**
	 * 排名
	 */
		private String carbonBusinessGrowthRanking;

	/**
	 * 展示状态
	 */
		private String displayState;

}
