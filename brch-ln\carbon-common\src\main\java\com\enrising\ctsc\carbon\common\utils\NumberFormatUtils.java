package com.enrising.ctsc.carbon.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class NumberFormatUtils {
    private static char[] cnArr = new char[]{'一', '二', '三', '四', '五', '六', '七', '八', '九'};

	private static char[] chArr = new char [] {'十','百','千','万','亿'};
    /**
     * 将数字转换为中文数字， 这里只写到了万
     *
     * @param arabicNum 阿拉伯数字
     * @return 中文数字
     */
    public static String arabicNumToChineseNum(int arabicNum) {
        String si = String.valueOf(arabicNum);
        String sd = "";
        if (si.length() == 1) {
            if (arabicNum == 0) {
                return sd;
            }
            sd += cnArr[arabicNum - 1];
            return sd;
        } else if (si.length() == 2) {
            if (si.substring(0, 1).equals("1")) {
                sd += "十";
                if (arabicNum % 10 == 0) {
                    return sd;
                }
            } else {
                sd += (cnArr[arabicNum / 10 - 1] + "十");
            }
            sd += arabicNumToChineseNum(arabicNum % 10);
        } else if (si.length() == 3) {
            sd += (cnArr[arabicNum / 100 - 1] + "百");
            if (String.valueOf(arabicNum % 100).length() < 2) {
                if (arabicNum % 100 == 0) {
                    return sd;
                }
                sd += "零";
            }
            sd += arabicNumToChineseNum(arabicNum % 100);
        } else if (si.length() == 4) {
            sd += (cnArr[arabicNum / 1000 - 1] + "千");
            if (String.valueOf(arabicNum % 1000).length() < 3) {
                if (arabicNum % 1000 == 0) {
                    return sd;
                }
                sd += "零";
            }
            sd += arabicNumToChineseNum(arabicNum % 1000);
        } else if (si.length() == 5) {
            sd += (cnArr[arabicNum / 10000 - 1] + "万");
            if (String.valueOf(arabicNum % 10000).length() < 4) {
                if (arabicNum % 10000 == 0) {
                    return sd;
                }
                sd += "零";
            }
            sd += arabicNumToChineseNum(arabicNum % 10000);
        }
        return sd;
    }

	/**
	 * 将汉字中的数字转换为阿拉伯数字
	 * @param chineseNum
	 * @return
	 */
	public static int chineseNumToArabicNum(String chineseNum) {
		int result = 0;
		int temp = 1;//存放一个单位的数字如：十万
		int count = 0;//判断是否有chArr
		for (int i = 0; i < chineseNum.length(); i++) {
			boolean b = true;//判断是否是chArr
			char c = chineseNum.charAt(i);
			for (int j = 0; j < cnArr.length; j++) {//非单位，即数字
				if (c == cnArr[j]) {
					if(0 != count){//添加下一个单位之前，先把上一个单位值添加到结果中
						result += temp;
						temp = 1;
						count = 0;
					}
					// 下标+1，就是对应的值
					temp = j + 1;
					b = false;
					break;
				}
			}
			if(b){//单位{'十','百','千','万','亿'}
				for (int j = 0; j < chArr.length; j++) {
					if (c == chArr[j]) {
						switch (j) {
							case 0:
								temp *= 10;
								break;
							case 1:
								temp *= 100;
								break;
							case 2:
								temp *= 1000;
								break;
							case 3:
								temp *= 10000;
								break;
							case 4:
								temp *= 100000000;
								break;
							default:
								break;
						}
						count++;
					}
				}
			}
			if (i == chineseNum.length() - 1) {//遍历到最后一个字符
				result += temp;
			}
		}
		return result;
	}

	public static BigDecimal formatValue(BigDecimal value){
		return value.divide(BigDecimal.valueOf(1000),2, RoundingMode.HALF_UP);
	}

	public static BigDecimal formatWanValue(BigDecimal value){
		return value.divide(BigDecimal.valueOf(10000),2, RoundingMode.HALF_UP);
	}
}
