package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.entity.DischargeJituanEnergySyncLog;
import com.enrising.ctsc.discharge.api.query.DischargeJituanEnergySyncLogQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeJituanEnergySyncLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* 集团能耗同步日志
*
* <AUTHOR> <EMAIL>
* @since 1.0.0 2024-01-03
*/
@Mapper
public interface DischargeJituanEnergySyncLogMapper extends BaseMapper<DischargeJituanEnergySyncLog> {
    /**
     * 列表分页查询
     *
     * @param page    分页数据
     * @param query 条件
     * @return 列表
     */
    IPage<DischargeJituanEnergySyncLogVo> findList(Page<DischargeJituanEnergySyncLogVo> page,
                                                   @Param("query") DischargeJituanEnergySyncLogQuery query);
}
