package com.enrising.ctsc.assess.api.bo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 考核模板
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
public class AssessTemplateBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 公司id
     */
    private Long deptId;

    /**
     * 任务周期
     */
    private String period;

    /**
     * 任务开始时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD)
    private Date templateStartTime;

    /**
     * 任务结束时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD)
    private Date templateEndTime;

    /**
     * 查询用任务开始时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD)
    private Date queryStartTime;

    /**
     * 查询用任务结束时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD)
    private Date queryEndTime;

    /**
     * 考核预警值
     */
    private Integer warningValue;

    /**
     * 模板内容
     */
    private String content;

    /**
     * 下发状态
     */
    private String sendStatus;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 创建时间区间 [开始时间，结束时间]
     */

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date[] createDateTimeArea;

    /**
     * 任务时间 [开始时间，结束时间]
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD)
    private Date[] templateTimeArea;

    /**
     * 考核指标列表
     */
    private List<AssessTemplateTargetBo> assessTemplateTargetBoList;

    /**
     * 任务管理--市州/部门列表时间查询条件
     */
    private String year;

    /**
     * 生成状态
     */
    private String generateStatus;

    /**
     * 查询类型 1--市州/部门的所有 2--首页 待考核的任务
     */
    private String queryTpe;

}
