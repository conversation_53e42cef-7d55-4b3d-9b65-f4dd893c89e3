package com.enrising.ctsc.assess.api.utils.constant;

import cn.hutool.core.map.MapUtil;

import java.util.Map;

/**
 * 考核常用变量
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-1-30
 */
public interface AssessConstant {

	/**
	 * 用于数字月份转汉子
	 */
	Map<Object, Object> MONTH_NUM_TO_TEXT = MapUtil.of(new Object[][]{
			{1, "一"},
			{2, "二"},
			{3, "三"},
			{4, "四"},
			{5, "五"},
			{6, "六"},
			{7, "七"},
			{8, "八"},
			{9, "九"},
			{10, "十"},
			{11, "十一"},
			{12, "十二"},
	});

}
