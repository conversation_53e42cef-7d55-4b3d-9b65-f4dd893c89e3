package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.enrising.ctsc.discharge.api.bo.DischargeDataEnergyBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergyUpdateRecord;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyUpdateLogVo;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyUpdateRecordVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 碳排放数据填报表（能源）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Mapper
public interface DischargeDataEnergyUpdateRecordMapper extends BaseMapper<DischargeDataEnergyUpdateRecord> {


	List<DischargeDataEnergyUpdateRecordVo> getUpdateRecordList(@Param("bo") DischargeDataEnergyBo bo);

	List<DischargeDataEnergyUpdateLogVo> getDataUpdateList(@Param("bo") DischargeDataEnergyBo bo);
}