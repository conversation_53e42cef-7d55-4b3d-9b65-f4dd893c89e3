package com.enrising.ctsc.carbon.common.utils.pdf;

import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartUtilities;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.block.BlockBorder;
import org.jfree.chart.labels.StandardPieSectionLabelGenerator;
import org.jfree.chart.plot.PiePlot;
import org.jfree.data.general.DefaultPieDataset;
import org.jfree.data.general.PieDataset;
import org.jfree.ui.RectangleEdge;

import java.awt.*;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Paths;

;


/**
 * <p><br/>
 * <a href="https://blog.csdn.net/wfzlm/article/details/109245248">java 生成柱状图、饼状图等图片保存至word文档</a>
 * <a href="https://blog.csdn.net/qq_36846058/article/details/89203067">JfreeChart生成饼图，百分比在饼里，文字居右等</a>
 * <AUTHOR>
 */

public class PieChartToWord {
	private static final String TEMP_DIR = System.getProperty("java.io.tmpdir");
	private static final String FILE_SEPARATOR = FileSystems.getDefault().getSeparator();
	private static final String CHART_PATH = TEMP_DIR.endsWith(FILE_SEPARATOR) ? TEMP_DIR : TEMP_DIR + FILE_SEPARATOR;
	private static final String DEFAULT_PNG_NAME = "饼图.png";

	public static void appendPicture( XWPFDocument doc) throws IOException, InvalidFormatException {
		// 创建一个新的Word文档
//        XWPFDocument doc = new XWPFDocument(Files.newInputStream(Paths.get(docPath)));
		// 创建一个段落
		XWPFParagraph para = doc.createParagraph();
		//---段落的对齐方式 1左 2中 3右 4往上 左 不可写0和负数
		para.setFontAlignment(2);
		// 创建一个图片
		String filename = CHART_PATH + DEFAULT_PNG_NAME;
		System.out.println("生成路径:" + filename);
		XWPFRun run = para.createRun();
		int format = XWPFDocument.PICTURE_TYPE_PNG;
		run.addPicture(Files.newInputStream(Paths.get(filename)), format, filename, Units.toEMU(200), Units.toEMU(200));
		// 删除统计图片
		boolean delete = new File(filename).delete();
		System.out.println("删除临时统计图片:" + (delete ? "成功" : "失败"));
	}

	/**
	 * 生成饼状图
	 */
	public static void makePieChart(double[] data,String[] keys) {
		createValidityComparePimChar(getDataPieSetByUtil(data, keys), DEFAULT_PNG_NAME, keys);
	}

	/**
	 * 饼状图 数据集
	 *
	 * @param data            数据集
	 * @param dataDescription 描述
	 * @return 数据集
	 */
	public static PieDataset getDataPieSetByUtil(double[] data, String[] dataDescription) {
		if (data != null && dataDescription != null) {
			if (data.length == dataDescription.length) {
				DefaultPieDataset dataset = new DefaultPieDataset();
				for (int i = 0; i < data.length; i++) {
					dataset.setValue(dataDescription[i], data[i]);
				}
				return dataset;
			}

		}

		return null;
	}

	/**
	 * 饼状图
	 *
	 * @param dataset    数据集
	 * @param charName   生成图的名字
	 * @param pieKeys    分饼的名字集
	 */
	public static void createValidityComparePimChar(PieDataset dataset,  String charName, String[] pieKeys) {
		JFreeChart chart = ChartFactory.createPieChart(null, dataset, true, true, false);
		// 使下说明标签字体清晰,去锯齿类似于的效果
		chart.getRenderingHints().put(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_OFF);
		chart.setTextAntiAlias(true);
		// 图片背景色
		chart.setBackgroundPaint(Color.white);
		chart.getLegend().setItemFont(new Font("宋体", Font.BOLD, 25));

		chart.getLegend().setBackgroundPaint(Color.WHITE);
		chart.getLegend().setFrame(new BlockBorder(Color.WHITE));
		// 底部字体颜色
		PiePlot plot = (PiePlot) chart.getPlot();
		// 指定饼图轮廓线的颜色
		plot.setBaseSectionOutlinePaint(Color.WHITE);
		plot.setBaseSectionPaint(Color.WHITE);
		for (String pieKey : pieKeys) {
			plot.setSectionOutlineStroke(pieKey, new BasicStroke(5.0f));
		}
		// 设置无数据时的信息
		plot.setNoDataMessage("无对应的数据，请重新查询。");

		//饼图背景色
		plot.setBackgroundPaint(Color.WHITE);

		// 设置绘图面板外边的填充颜色
		plot.setOutlinePaint(Color.WHITE);
		// 设置绘图面板阴影的填充颜色
		plot.setShadowPaint(Color.WHITE);

		//设置每块饼和数据之间的线
		plot.setLabelLinksVisible(true);
		chart.setBackgroundPaint(new Color(253, 253, 253));
		//是否显示分界线
		plot.setSectionOutlinesVisible(true);
		plot.setSectionOutlinePaint(pieKeys[0], Color.WHITE);
		//设置简单标签
		plot.setSimpleLabels(true);

		//设置标签最长宽度
		plot.setMaximumLabelWidth(0.25D);
		// 设置分类标签与图的连接线边距
		plot.setLabelLinkMargin(0.05D);
		// 忽略无值的分类
		plot.setIgnoreZeroValues(true);
		// 设置饼与边框的距离
		plot.setLabelGap(0.00D);

		//下面设置的是:饼图里面的百分比去除标签边框,只显示百分比的数据 (4个都要设置,缺一不可)
		// (1)自定义标签产生器,设置绘图面板外边的填充颜色
		plot.setLabelOutlinePaint(null);
		//自定义标签产生器, 设置绘图面板阴影的填充颜色
		plot.setLabelShadowPaint(null);
		//(2) 自定义标签产生器,设置绘图面板外边的填充颜色
		plot.setLabelOutlineStroke(null);
		//自定义标签产生器,背景色
		plot.setLabelBackgroundPaint(null);
		//上面设置的是:饼图里面的百分比去除标签边框,只显示百分比的数据

		// 设置无数据时的信息显示颜色
		plot.setNoDataMessagePaint(Color.red);

		// 图片中显示百分比:自定义方式，{0} 表示选项， {1} 表示数值， {2} 表示所占比例 ,小数点后两位
		plot.setLabelGenerator(new StandardPieSectionLabelGenerator(
				"{0}({1})")
		);
		// 图例显示百分比:自定义方式， {0} 表示选项， {1} 表示数值， {2} 表示所占比例
		plot.setLegendLabelGenerator(new StandardPieSectionLabelGenerator("{0}"));

		plot.setLabelFont(new Font("SansSerif", Font.PLAIN, 16));
		// 设置标签字体颜色
		plot.setLabelPaint(Color.BLACK);
		// 指定图片的透明度(0.0-1.0)
		plot.setForegroundAlpha(1f);
		// 指定显示的饼图上圆形(false)还椭圆形(true)
		plot.setCircular(true, true);
		// 设置第一个 饼块section 的开始位置，默认是12点钟方向
		plot.setStartAngle(90);
		// 设置阴影
		plot.setShadowXOffset(0);
		plot.setShadowYOffset(0);
		// // 设置分饼颜色
		//饼图文字居下显示
		chart.getLegend().setPosition(RectangleEdge.RIGHT);
		if(pieKeys.length == 2){
			plot.setSectionPaint(pieKeys[0], new Color(91, 155, 213));
			plot.setSectionPaint(pieKeys[1], new Color(237, 125, 49));
		} else {
			plot.setSectionPaint(pieKeys[0], new Color(91, 155, 213));
			plot.setSectionPaint(pieKeys[1], new Color(237, 125, 49));
			plot.setSectionPaint(pieKeys[2], new Color(192, 192, 192));
			plot.setSectionPaint(pieKeys[3], new Color(255, 215, 0));
		}
		FileOutputStream fosJpg = null;
		try {
			String chartName = CHART_PATH + charName;
			fosJpg = new FileOutputStream(chartName);
			// 高宽的设置影响椭圆饼图的形状
			ChartUtilities.writeChartAsPNG(fosJpg, chart, 1000, 1000);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (fosJpg != null) {
					fosJpg.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
}
