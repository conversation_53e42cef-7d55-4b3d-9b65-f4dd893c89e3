package com.sccl.modules.minio.util;


import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.sccl.modules.minio.config.MinioConfig;
import com.sccl.modules.minio.vo.MinioObjectVO;
import com.sccl.common.utils.DateUtils;
import com.sccl.exception.BusinessException;
import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.Bucket;
import io.minio.messages.DeleteError;
import io.minio.messages.DeleteObject;
import io.minio.messages.Item;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @date 2022/9/27
 * @description minio工具类
 */
@Slf4j
@Component
public class MinioUtil {


    private final MinioClient minioClient;

    private final MinioConfig minioConfig;



    MinioUtil(MinioClient minioClient, MinioConfig minioConfig) {
        this.minioConfig = minioConfig;
        this.minioClient = minioClient;
    }



    @SneakyThrows
    public String getViewUrl(String obj) {
        String bucketName = minioConfig.getBucketName();
        int beginIndex = bucketName.length() + 1;
        log.info("bucketName:{},obj:{}", bucketName, obj);
        //默认过期时间为1小时，可以自己自定义传入
        String sharedUrl = minioClient.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                        .bucket(bucketName)
                        .expiry(60, TimeUnit.MINUTES)
                        .object(obj)
                        .method(Method.GET)
                        .build()
        );
        log.info("临时授权:{}", sharedUrl);
        return sharedUrl;
    }

    @SneakyThrows
    public String getViewUrl(String bucketName, String objectName) {
        //默认过期时间为1小时，可以自己自定义传入
        String sharedUrl = minioClient.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                        .bucket(bucketName)
                        .expiry(60, TimeUnit.MINUTES)
                        .object(objectName)
                        .method(Method.GET)
                        .build()
        );
        // 通过/ln-nh-oss截取字符串，去掉原始的IP部分，拼接新的IP
        int bucketIndex = sharedUrl.indexOf(bucketName);
        if (bucketIndex != -1) {
            sharedUrl = sharedUrl.substring(bucketIndex);
        }
        log.info("临时授权:{}", sharedUrl);
        return sharedUrl;
    }

    /**
     * 检查存储桶是否存在
     *
     * @param bucketName 存储桶名称
     */
    @SneakyThrows
    public boolean bucketExists(String bucketName) {
        return minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
    }

    /**
     * 创建存储桶
     *
     * @param bucketName 存储桶名称
     */
    @SneakyThrows
    public void makeBucket(String bucketName) {
        boolean flag = bucketExists(bucketName);
        if (!flag) {
            log.info("创建存储桶：{}", bucketName);
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
        }
    }

    /**
     * 列出所有存储桶名称
     */
    @SneakyThrows
    public List<String> listBucketNames() {
        List<Bucket> bucketList = listBuckets();
        List<String> bucketListName = new ArrayList<>();
        for (Bucket bucket : bucketList) {
            bucketListName.add(bucket.name());
        }
        return bucketListName;
    }

    /**
     * 列出所有存储桶
     */
    @SneakyThrows
    private List<Bucket> listBuckets() {
        return minioClient.listBuckets();
    }

    /**
     * 删除存储桶
     *
     * @param bucketName 存储桶名称
     */
    @SneakyThrows
    public boolean removeBucket(String bucketName) {
        boolean flag = bucketExists(bucketName);
        if (flag) {
            Iterable<Result<Item>> myObjects = listObjects(bucketName);
            for (Result<Item> result : myObjects) {
                Item item = result.get();
                // 有对象文件，则删除失败
                if (item.size() > 0) {
                    throw new BusinessException("存储桶不为空，不能删除");
                }
            }
            // 删除存储桶，注意，只有存储桶为空时才能删除成功。
            minioClient.removeBucket(RemoveBucketArgs.builder().bucket(bucketName).build());
            flag = bucketExists(bucketName);
            return !flag;
        }
        log.info("桶不存在{}", bucketName);
        return false;
    }

    /**
     * 列出存储桶中的所有对象名称
     *
     * @param bucketName 存储桶名称
     */
    @SneakyThrows
    public List<String> listObjectNames(String bucketName) {
        List<String> listObjectNames = new ArrayList<>();
        boolean flag = bucketExists(bucketName);
        if (flag) {
            Iterable<Result<Item>> myObjects = listObjects(bucketName);
            for (Result<Item> result : myObjects) {
                Item item = result.get();
                listObjectNames.add(item.objectName());
            }
        } else {
            listObjectNames.add("存储桶不存在");
        }
        return listObjectNames;
    }

    /**
     * 列出存储桶中的所有对象
     *
     * @param bucketName 存储桶名称
     */
    @SneakyThrows
    public Iterable<Result<Item>> listObjects(String bucketName) {
        boolean flag = bucketExists(bucketName);
        if (flag) {
            return minioClient.listObjects(ListObjectsArgs.builder().bucket(bucketName).build());
        }
        return null;
    }

    /**
     * 文件上传
     *
     * @param file       上传的文件
     */
    @SneakyThrows
    public MinioObjectVO putObject(MultipartFile file) {
        String bucketName = minioConfig.getBucketName();
        InputStream inputStream = new ByteArrayInputStream(file.getBytes());
        if (StrUtil.isBlank(bucketName)) {
            log.info("bucketName 不能为空");
            throw new BusinessException("bucketName 不能为空");
        }
        try {
            this.makeBucket(bucketName);
            String originalFilename = file.getOriginalFilename();
            if (StrUtil.isBlank(originalFilename)) {
                throw new BusinessException("文件名不能为空");
            }

            String fileName = DateUtils.datePath() + UUID.randomUUID() + "." + FileUtil.getSuffix(originalFilename);
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(fileName)
                            .stream(inputStream, file.getSize(), -1)
                            .contentType(file.getContentType())
                            .build()
            );
            String url = minioConfig.getEndPoint() + "/" + bucketName + "/" + fileName;
            MinioObjectVO minioObjectVO = new MinioObjectVO();
            minioObjectVO.setUrl(url);
            minioObjectVO.setObjectName(fileName);
            return minioObjectVO;
        } catch (Exception e) {
            log.error("上传失败", e);
            return new MinioObjectVO();
        }

    }

    /**
     * 文件上传
     *
     * @param bucketName 桶名称
     * @param file       上传的文件
     */
    @SneakyThrows
    public MinioObjectVO putObject(String bucketName, MultipartFile file) {
        InputStream inputStream = new ByteArrayInputStream(file.getBytes());
        if (StrUtil.isBlank(bucketName)) {
            log.info("bucketName 不能为空");
            throw new BusinessException("bucketName is null");
        }
        try {
            this.makeBucket(bucketName);
            String originalFilename = file.getOriginalFilename();
            if (StrUtil.isBlank(originalFilename)) {
                throw new BusinessException("文件名不能为空");
            }

            String fileName = DateUtils.datePath() + UUID.randomUUID() + "." + FileUtil.getSuffix(originalFilename);
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(fileName)
                            .stream(inputStream, file.getSize(), -1)
                            .contentType(file.getContentType())
                            .build()
            );
            String url = minioConfig.getEndPoint() + "/" + bucketName + "/" + fileName;
            MinioObjectVO minioObjectVO = new MinioObjectVO();
            minioObjectVO.setUrl(url);
            minioObjectVO.setObjectName(fileName);
            return minioObjectVO;
        } catch (Exception e) {
            log.error("上传失败", e);
            return new MinioObjectVO();
        }

    }

    /**
     * 文件上传
     * @param bucketName  桶名称
     * @param inputStream 文件流
     * @param fileSuffix  文件后缀
     * @param contentType 文件类型
     * @return url地址
     */
    @SneakyThrows
    public MinioObjectVO putObject(String bucketName, InputStream inputStream, String fileSuffix, String contentType) {
        try {
            this.makeBucket(bucketName);

            String fileName = DateUtils.datePath() + UUID.randomUUID() + fileSuffix;
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(fileName)
                            .stream(inputStream, inputStream.available(), -1)
                            .contentType(contentType)
                            .build()
            );
            String url =  minioConfig.getEndPoint() + "/" + bucketName + "/" + fileName;
            MinioObjectVO minioObjectVO = new MinioObjectVO();
            minioObjectVO.setUrl(url);
            minioObjectVO.setObjectName(fileName);
            return minioObjectVO;
        } catch (Exception e) {
            log.error("上传失败", e);
            return null;
        }

    }

    /**
     * 删除一个对象
     *
     * @param bucketName 存储桶名称
     * @param objectName 存储桶里的对象名称
     */
    @SneakyThrows
    public boolean removeObject(String bucketName, String objectName) {
        boolean flag = bucketExists(bucketName);
        if (flag) {
            minioClient.removeObject(RemoveObjectArgs.builder().bucket(bucketName).object(objectName).build());
            return true;
        }
        return false;
    }

    /**
     * 以流的形式获取一个文件对象
     *
     * @param bucketName 存储桶名称
     * @param objectName 存储桶里的对象名称
     */
    @SneakyThrows
    public InputStream getObject(String bucketName, String objectName) {
        boolean flag = bucketExists(bucketName);
        if (flag) {
            StatObjectResponse statObject = statObject(bucketName, objectName);
            if (statObject != null && statObject.size() > 0) {
                return minioClient.getObject(GetObjectArgs.builder().bucket(bucketName).object(objectName).build());
            }
        }
        return null;
    }

    /**
     * 获取对象的元数据
     *
     * @param bucketName 存储桶名称
     * @param objectName 存储桶里的对象名称
     */
    @SneakyThrows
    public StatObjectResponse statObject(String bucketName, String objectName) {
        boolean flag = bucketExists(bucketName);
        if (flag) {
            return minioClient.statObject(
                    StatObjectArgs.builder().bucket(bucketName).object(objectName).build());
        }
        return null;
    }

    /**
     * 删除指定桶的多个文件对象,返回删除错误的对象列表，全部删除成功，返回空列表
     *
     * @param bucketName  存储桶名称
     * @param objectNames 含有要删除的多个object名称的迭代器对象
     */
    @SneakyThrows
    public boolean removeObject(String bucketName, List<String> objectNames) {
        boolean flag = bucketExists(bucketName);
        if (flag) {
            List<DeleteObject> objects = new LinkedList<>();
            for (String objectName : objectNames) {
                objects.add(new DeleteObject(objectName));
            }
            Iterable<Result<DeleteError>> results =
                    minioClient.removeObjects(
                            RemoveObjectsArgs.builder().bucket(bucketName).objects(objects).build());
            for (Result<DeleteError> result : results) {
                DeleteError error = result.get();
                log.info("Error in deleting object {}: {}", error.objectName(), error.message());
            }
        }
        return true;
    }

    /**
     * 以流的形式获取一个文件对象（断点下载）
     *
     * @param bucketName 存储桶名称
     * @param objectName 存储桶里的对象名称
     * @param offset     起始字节的位置
     * @param length     要读取的长度 (可选，如果无值则代表读到文件结尾)
     */
    @SneakyThrows
    public InputStream getObject(String bucketName, String objectName, long offset, Long length) {
        boolean flag = bucketExists(bucketName);
        if (flag) {
            StatObjectResponse statObject = statObject(bucketName, objectName);
            if (statObject != null && statObject.size() > 0) {
                return minioClient.getObject(
                        GetObjectArgs.builder()
                                .bucket(bucketName)
                                .object(objectName)
                                .offset(offset)
                                .length(length)
                                .build());
            }
        }
        return null;
    }

}
