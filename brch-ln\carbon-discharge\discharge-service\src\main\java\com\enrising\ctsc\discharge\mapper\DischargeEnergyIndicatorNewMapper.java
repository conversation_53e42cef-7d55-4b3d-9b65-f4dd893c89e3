package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyIndicatorNew;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyIndicatorQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorNewVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 碳排放能源指标表
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-23
 */
@Mapper
public interface DischargeEnergyIndicatorNewMapper extends BaseMapper<DischargeEnergyIndicatorNew> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
	IPage<DischargeEnergyIndicatorNewVo> findList(Page<DischargeEnergyIndicatorNewVo> page, @Param(Constants.WRAPPER) Wrapper<DischargeEnergyIndicatorQuery> wrapper);

	/**
	 * 列表查询
	 *
	 * @param wrapper 条件
	 * @return 列表
	 */
	List<DischargeEnergyIndicatorNewVo> findList(@Param(Constants.WRAPPER) Wrapper<DischargeEnergyIndicatorQuery> wrapper);

	/**
	 * 详情查询
	 *
	 * @param wrapper 条件
	 * @return 结果
	 */
	DischargeEnergyIndicatorNewVo detail(@Param(Constants.WRAPPER) Wrapper<DischargeEnergyIndicatorQuery> wrapper);

	/**
	 * 列表查询
	 *
	 * @return 列表
	 */
	List<DischargeEnergyIndicatorNewVo> getIndicatorList();

	/**
	 * 列表查询
	 *
	 * @param parentId 父节点id
	 * @param status 状态
	 * @return 列表
	 */
	List<DischargeEnergyIndicatorNewVo> getIndicatorTreeList(@Param("parentId") Long parentId,
															 @Param("status") String status,
															 @Param("indicatorName") String indicatorName);
}