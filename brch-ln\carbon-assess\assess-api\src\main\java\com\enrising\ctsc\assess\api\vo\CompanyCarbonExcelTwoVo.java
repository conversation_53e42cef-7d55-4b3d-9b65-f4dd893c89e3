package com.enrising.ctsc.assess.api.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CompanyCarbonExcelTwoVo {

    /*
     * 公司id
     * */
    private Long companyId;

    /**
     * 公司名称
     */
    @Excel(name = "部门名称", width = 20, orderNum = "0")
    private String companyName;

    /**
     * 去年业务碳排放量（吨）
     */
    @Excel(name = "去年碳排放量（吨）", groupName = "综合能耗（碳排放总量）增幅", width = 20, orderNum = "1")
    private BigDecimal preCarbonTotal;

    /**
     * 今年业务碳排放量（吨）
     */
    @Excel(name = "今年碳排放量（吨）", groupName = "综合能耗（碳排放总量）增幅", width = 20, orderNum = "2")
    private BigDecimal nowCarbonTotal;

    /**
     * 下降率
     */
    @Excel(name = "下降率", groupName = "综合能耗（碳排放总量）增幅", width = 20, orderNum = "3")
    private String downRate;

}
