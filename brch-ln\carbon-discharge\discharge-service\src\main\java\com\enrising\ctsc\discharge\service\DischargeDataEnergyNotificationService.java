package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.discharge.api.bo.DischargeDataEnergyNotificationBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergyNotification;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyNotificationVo;

import java.util.List;

/**
 * 碳排放数据填报（能源）提醒
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
public interface DischargeDataEnergyNotificationService extends IService<DischargeDataEnergyNotification> {

	/**
	 * 提醒次数查询
	 *
	 * @param dischargeDataEnergyNotificationBo 查询参数
	 * @return 提醒次数
	 */
	List<DischargeDataEnergyNotificationVo> getRemindCount(DischargeDataEnergyNotificationBo dischargeDataEnergyNotificationBo);

	/**
	 * 部门提醒次数查询
	 *
	 * @param dischargeDataEnergyNotificationBo 查询参数
	 * @return 部门提醒次数
	 */
	DischargeDataEnergyNotification getCompanyRemind(DischargeDataEnergyNotificationBo dischargeDataEnergyNotificationBo);

	/**
	 * 清除部门提醒
	 *
	 * @param dischargeDataEnergyNotificationBo 参数
	 * @return true/false
	 */
	boolean clearCompanyRemind(DischargeDataEnergyNotificationBo dischargeDataEnergyNotificationBo);
}