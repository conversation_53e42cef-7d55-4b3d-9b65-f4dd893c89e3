package com.enrising.ctsc.discharge.api.query;

import com.enrising.ctsc.discharge.api.entity.DischargeCheck;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 碳排放核查查询
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DischargeCheckQuery extends DischargeCheck {

	/*
	* 关键字
	* */
	private String keyword;

	/*
	 * 任务时间
	 * */
	private List<String> searchTimeArea;
}
