
package com.enrising.ctsc.carbon.common.utils;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser.Feature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.util.JSONPObject;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.TimeZone;

/**
 * 简单封装Jackson，实现JSON String<->Java Object的Mapper.
 *
 * <AUTHOR>
 * @Date 2019/3/11 11:26
 */
public class JsonMapper extends ObjectMapper {

    private static final long serialVersionUID = 1L;

    private final static Logger LOGGER = LoggerFactory.getLogger(JsonMapper.class);

    private static JsonMapper mapper;

    public JsonMapper() {
        this(Include.NON_EMPTY);
    }

    public JsonMapper(Include include) {
        // 设置输出时包含属性的风格
        if (include != null) {
            this.setSerializationInclusion(include);
        }
        // 设置输入时忽略在JSON字符串中存在但Java对象实际没有的属性
        this.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        // 空值处理为空串
        this.getSerializerProvider().setNullValueSerializer(
                new JsonSerializer<Object>() {
                    @Override
                    public void serialize(Object value, JsonGenerator jgen,
                                          SerializerProvider provider) throws IOException,
                            JsonProcessingException {
                        jgen.writeString("");
                    }
                });
        // 进行HTML解码。
        this.registerModule(new SimpleModule().addSerializer(String.class,
                new JsonSerializer<String>() {
                    @Override
                    public void serialize(String value, JsonGenerator jgen,
                                          SerializerProvider provider) throws IOException,
                            JsonProcessingException {
                        jgen.writeString(StringEscapeUtils.unescapeHtml4(value));
                    }
                }));
        // 设置时区
        this.setTimeZone(TimeZone.getDefault());
    }

    /**
     * 创建只输出非Null且非Empty(如List.isEmpty)的属性到Json字符串的Mapper,建议在外部接口中使用.
     *
     * @return com.sccl.framework.utils.JsonMapper
     */
    public static JsonMapper getInstance() {
        if (mapper == null) {
            mapper = new JsonMapper().enableSimple();
        }
        return mapper;
    }

    /**
     * 创建只输出初始值被改变的属性到Json字符串的Mapper, 最节约的存储方式，建议在内部接口中使用。
     *
     * @return com.sccl.framework.utils.JsonMapper
     */
    public static JsonMapper nonDefaultMapper() {
        if (mapper == null) {
            mapper = new JsonMapper(Include.NON_DEFAULT);
        }
        return mapper;
    }

    /**
     * 将对象转换为json字符串
     *
     * @param object 可以是POJO，也可以是Collection或数组。 如果对象为Null, 返回"null". 如果集合为空集合, 返回"[]"
     * @return java.lang.String
     */
    public String toJson(Object object) {
        try {
            return this.writeValueAsString(object);
        } catch (IOException e) {
            if (LOGGER.isWarnEnabled()) {
                LOGGER.warn("write to json string error:" + object, e);
            }
            return null;
        }
    }


    /**
     * 反序列化POJO或简单Collection如List<String>
     * 如果JSON字符串为Null或"null"字符串, 返回Null. 如果JSON字符串为"[]", 返回空集合.
     * 如需反序列化复杂Collection如List<MyBean>, 请使用fromJson(String,JavaType)
     *
     * @param jsonString json字符串
     * @param clazz      类型
     * @return T
     */
    public <T> T fromJson(String jsonString, Class<T> clazz) {
        if (StringUtils.isEmpty(jsonString)) {
            return null;
        }
        try {
            return this.readValue(jsonString, clazz);
        } catch (IOException e) {
            if (LOGGER.isWarnEnabled()) {
                LOGGER.warn("parse json string error:" + jsonString, e);
            }
            return null;
        }
    }

    /**
     * 反序列化复杂Collection如List<Bean>, 先使用函數createCollectionType构造类型,然后调用本函数.
     *
     * @param jsonString json字符串
     * @param javaType   类型
     * @return T
     */
    @SuppressWarnings("unchecked")
    public <T> T fromJson(String jsonString, JavaType javaType) {
        if (StringUtils.isEmpty(jsonString)) {
            return null;
        }
        try {
            return (T) this.readValue(jsonString, javaType);
        } catch (IOException e) {
            if (LOGGER.isWarnEnabled()) {
                LOGGER.warn("parse json string error:" + jsonString, e);
            }
            return null;
        }
    }


    /**
     * 構造泛型的Collection Type如:
     * ArrayList<MyBean> 调用constructCollectionType(ArrayList.class,MyBean.class)
     * HashMap<String,MyBean>, 则调用(HashMap.class,String.class, MyBean.class)
     *
     * @param collectionClass
     * @param elementClasses
     * @return com.fasterxml.jackson.databind.JavaType
     */
    public JavaType createCollectionType(Class<?> collectionClass,
                                         Class<?>... elementClasses) {
        return this.getTypeFactory().constructParametricType(collectionClass,
                elementClasses);
    }

    /**
     *
     */
    /**
     * 当JSON里只含有Bean的部分属性时，更新一个已存在Bean，只覆盖该部分的属性.
     *
     * @param jsonString json字符串
     * @param object     对象
     * @return T
     */
    @SuppressWarnings("unchecked")
    public <T> T update(String jsonString, T object) {
        try {
            return (T) this.readerForUpdating(object).readValue(jsonString);
        } catch (JsonProcessingException e) {
            if (LOGGER.isWarnEnabled()) {
                LOGGER.warn("update json string:" + jsonString + " to object:"
                        + object + " error.", e);
            }
        } catch (IOException e) {
            if (LOGGER.isWarnEnabled()) {
                LOGGER.warn("update json string:" + jsonString + " to object:"
                        + object + " error.", e);
            }
        }
        return null;
    }

    /**
     * 输出JSONP格式数据
     *
     * @param functionName
     * @param object
     * @return java.lang.String
     */
    public String toJsonP(String functionName, Object object) {
        return toJson(new JSONPObject(functionName, object));
    }

    /**
     * 设定是否使用Enum的toString函数来读写Enum，为false时使用Enum的name()函数来读写Enum,默认为false
     * 注意本函数一定要在Mapper创建后，所有的读写动作之前调用
     *
     * @return com.sccl.framework.utils.JsonMapper
     */
    public JsonMapper enableEnumUseToString() {
        this.enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING);
        this.enable(DeserializationFeature.READ_ENUMS_USING_TO_STRING);
        return this;
    }



    /**
     * 允许单引号 允许不带引号的字段名称
     *
     * @return JsonMapper
     */
    public JsonMapper enableSimple() {
        this.configure(Feature.ALLOW_SINGLE_QUOTES, true);
        this.configure(Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        return this;
    }

    /**
     * 取出Mapper做进一步的设置或使用其他序列化API.
     *
     * @return ObjectMapper
     */
    public ObjectMapper getMapper() {
        return this;
    }

    /**
     * 对象转换为JSON字符串
     *
     * @param object
     * @return String
     */
    public static String toJsonString(Object object) {
        return JsonMapper.getInstance().toJson(object);
    }

    /**
     * JSON字符串转换为对象
     *
     * @param jsonString json 字符串
     * @param clazz      类型
     * @return T
     */
    public static <T> T fromJsonString(String jsonString, Class<T> clazz) {
        return JsonMapper.getInstance().fromJson(jsonString, clazz);
    }

    /**
     * 将obj对象转换成 class类型的对象
     *
     * @param obj   对象
     * @param clazz 类型
     * @return T
     */
    public static <T> T parseObject(Object obj, Class<T> clazz) {
        return JSON.parseObject(JSON.toJSONString(obj), clazz);
    }

}
