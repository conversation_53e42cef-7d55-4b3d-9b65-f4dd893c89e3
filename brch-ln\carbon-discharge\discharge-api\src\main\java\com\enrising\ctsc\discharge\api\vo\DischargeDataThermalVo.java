package com.enrising.ctsc.discharge.api.vo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放数据填报表（热）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeDataThermalVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
	private Long id;

	/**
	 * 创建者id
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date createTime;

	/**
	 * 更新者id
	 */
	private Long updateBy;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date updateTime;

	/**
	 * 填报单位id
	 */
	private Long companyId;

	/**
	 * 填报时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date reportTime;

	/**
	 * 热力总量
	 */
	private BigDecimal thermal = BigDecimal.ZERO;

	/**
	 * 数据月份
	 */
	private String dataMonth;

	/**
	 * 数据年份
	 */
	private String dataYear;

	/**
	 * 碳排放量
	 */
	private BigDecimal carbonEmissions = BigDecimal.ZERO;

	/**
	 * 能源消耗总量
	 */
	private BigDecimal energyConsumption = BigDecimal.ZERO;

	/**
	 * 热量碳排数据
	 */
	private BigDecimal carbonThermal = BigDecimal.ZERO;
}
