package com.enrising.ctsc.assess.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 考核模板
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-07
 */

@Data
@TableName("assess_template")
public class AssessTemplate extends Model<AssessTemplate> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建者id
     */
    private Long createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    /**
     * 更新者id
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date updateTime;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 任务周期
     */
    private String period;

    /**
     * 任务开始时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date templateStartTime;

    /**
     * 任务结束时间
     */
    private Date templateEndTime;

    /**
     * 考核预警值
     */
    private Integer warningValue;

    /**
     * 模板内容
     */
    private String content;

    /**
     * 下发状态
     */
    private String sendStatus;

    /**
     * 任务下发时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date sendTime;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
