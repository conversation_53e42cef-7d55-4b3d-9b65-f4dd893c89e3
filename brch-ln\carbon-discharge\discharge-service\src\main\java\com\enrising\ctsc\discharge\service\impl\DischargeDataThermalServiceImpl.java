package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.carbon.common.entity.User;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.JwtUtils;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.discharge.api.bo.DischargeDataThermalBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataThermal;
import com.enrising.ctsc.discharge.api.enums.EnergyType;
import com.enrising.ctsc.discharge.api.vo.DischargeDataThermalVo;
import com.enrising.ctsc.discharge.mapper.DischargeDataThermalMapper;
import com.enrising.ctsc.discharge.service.DischargeDataThermalService;
import com.enrising.ctsc.discharge.service.DischargeEnergyCoefficientService;
import com.enrising.ctsc.discharge.service.DischargeEnergyFactorService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 碳排放数据填报表（热）
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Service
@AllArgsConstructor
public class DischargeDataThermalServiceImpl extends ServiceImpl<DischargeDataThermalMapper, DischargeDataThermal> implements DischargeDataThermalService {

	private final DischargeEnergyCoefficientService dischargeEnergyCoefficientService;

	private final DischargeEnergyFactorService dischargeEnergyFactorService;

//	private final RemoteUserService remoteUserService;

	@Override
	public Page<DischargeDataThermalVo> getThermalListPage(QueryPage<DischargeDataThermalBo> queryPage) {
		LambdaQueryWrapper<DischargeDataThermal> qw = Wrappers.lambdaQuery();
		DischargeDataThermalBo dischargeDataThermalBo = queryPage.getModel();
		dischargeDataThermalBo.setSize(queryPage.getSize());
		dischargeDataThermalBo.setOffset((queryPage.getCurrent() - 1 ) * queryPage.getSize());
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			Page<DischargeDataThermal> dischargeDataThermalPage = new Page<>(queryPage.getCurrent(),
					queryPage.getSize(), true);
			Page<DischargeDataThermalVo> dischargeDataThermalVoPage = new Page<>();
			BeanUtils.copyProperties(dischargeDataThermalPage, dischargeDataThermalVoPage);
			if (ObjectUtil.isNotEmpty(dischargeDataThermalBo)) {
				if (ObjectUtil.isEmpty(dischargeDataThermalBo.getCompanyId())) {
					dischargeDataThermalBo.setCompanyId(0L);
				}
				if (ObjectUtil.isNotEmpty(dischargeDataThermalBo.getReportTime())) {
					//查询条件 填报时间
					qw.eq(DischargeDataThermal::getReportTime, dischargeDataThermalBo.getReportTime());
					dischargeDataThermalBo.setQueryStartTime(DateUtil.beginOfMonth(dischargeDataThermalBo.getReportTime()));
					dischargeDataThermalBo.setQueryEndTime(DateUtil.endOfMonth(dischargeDataThermalBo.getReportTime()));
				} else {
					//查询条件 数据年份
					String startYear = dischargeDataThermalBo.getDataYear() + "-01-01 0:00:00";
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
					dateStart = sdf.parse(startYear);
					dateEnd = DateUtil.endOfYear(dateStart);
					dischargeDataThermalBo.setQueryStartTime(dateStart);
					dischargeDataThermalBo.setQueryEndTime(dateEnd);
					qw.ge(DischargeDataThermal::getReportTime, dateStart)
							.le(DischargeDataThermal::getReportTime, dateEnd);
				}
				//查询条件，公司
				qw.eq(dischargeDataThermalBo.getCompanyId() != 0, DischargeDataThermal::getCompanyId,
						dischargeDataThermalBo.getCompanyId());
			}
			dischargeDataThermalVoPage.setTotal(this.count(qw));
			if (ObjectUtil.isEmpty(dischargeDataThermalBo) || ObjectUtil.isEmpty(dischargeDataThermalBo.getCompanyId()) ||
					dischargeDataThermalBo.getCompanyId().equals(0L)) {
				dischargeDataThermalVoPage.setRecords(baseMapper.countCompanyData(dischargeDataThermalBo));
			} else {
				dischargeDataThermalVoPage.setRecords(baseMapper.getCompanyDataList(dischargeDataThermalBo));
			}
			return dischargeDataThermalVoPage;
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public List<DischargeDataThermalVo> getThermalListToExcel(DischargeDataThermalBo dischargeDataThermalBo) {
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			if (ObjectUtil.isNotEmpty(dischargeDataThermalBo)) {
				if (ObjectUtil.isEmpty(dischargeDataThermalBo.getCompanyId())) {
					dischargeDataThermalBo.setCompanyId(0L);
				}
				if (ObjectUtil.isNotEmpty(dischargeDataThermalBo.getReportTime())) {
					dischargeDataThermalBo.setQueryStartTime(DateUtil.beginOfMonth(dischargeDataThermalBo.getReportTime()));
					dischargeDataThermalBo.setQueryEndTime(DateUtil.endOfMonth(dischargeDataThermalBo.getReportTime()));
				} else {
					//查询条件 数据年份
					String startYear = dischargeDataThermalBo.getDataYear() + "-01-01 0:00:00";
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
					dateStart = sdf.parse(startYear);
					dateEnd = DateUtil.endOfYear(dateStart);
					dischargeDataThermalBo.setQueryStartTime(dateStart);
					dischargeDataThermalBo.setQueryEndTime(dateEnd);
				}
			}
			return baseMapper.countCompanyData(dischargeDataThermalBo);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public List<DischargeDataThermalVo> getDataList(Integer dataYear, Long companyId) {
		if (ObjectUtil.isEmpty(dataYear)) {
			throw new BusinessException("数据年份不能为空！");
		}
		if (ObjectUtil.isEmpty(companyId)) {
//			companyId = remoteUserService.getCityDeptId();
			companyId = JwtUtils.getCurrentUserCompanyId();
		}
		String startYear = dataYear + "-01-01 0:00:00";
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			dateStart = sdf.parse(startYear);
			dateEnd = DateUtil.endOfYear(dateStart);
			return this.countCompanyData(dateStart, dateEnd, companyId);

		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}


	@Override
	public List<DischargeDataThermalVo> getDataListByDate(Date dateStart, Date dateEnd, Long companyId) {
		if (ObjectUtil.isEmpty(companyId)) {
//			companyId = remoteUserService.getCityDeptId();
			companyId = JwtUtils.getCurrentUserCompanyId();
		}
		return this.countCompanyData(dateStart, dateEnd, companyId);
	}

	@Override
	public DischargeDataThermalVo detail(Long id) {
		if (ObjectUtil.isEmpty(id)) {
			throw new BusinessException("查询参数不能为空");
		}
		DischargeDataThermal dischargeDataThermal = baseMapper.selectById(id);
		if (ObjectUtil.isEmpty(dischargeDataThermal)) {
			return null;
		}
		DischargeDataThermalVo dischargeDataThermalVo = new DischargeDataThermalVo();
		BeanUtils.copyProperties(dischargeDataThermal ,dischargeDataThermalVo);
		Calendar cal = Calendar.getInstance();
		cal.setTime(dischargeDataThermalVo.getReportTime());
		Integer year=cal.get(Calendar.YEAR);//获取年
		Integer month = cal.get(Calendar.MONTH) + 1;//获取月（月份从0开始，需要加一）
		dischargeDataThermalVo.setDataMonth(month + "月");
		dischargeDataThermalVo.setDataYear(year + "年");
		dischargeDataThermalVo.setCarbonEmissions(dischargeDataThermalVo.getThermal().
				multiply(dischargeEnergyFactorService.getFactorByTime(EnergyType.THERMAL.getId(),
						dischargeDataThermalVo.getReportTime())).divide(new BigDecimal(1000)).
				setScale(4, RoundingMode.HALF_UP));
		dischargeDataThermalVo.setEnergyConsumption(dischargeDataThermalVo.getThermal().
				multiply(dischargeEnergyCoefficientService.getCoefficientByTime(EnergyType.THERMAL.getId(),
						dischargeDataThermalVo.getReportTime())).divide(new BigDecimal(1000)).setScale(4,
						RoundingMode.HALF_UP));
		return  dischargeDataThermalVo;
	}

	@Override
	public String add(DischargeDataThermalBo bo) {
		DischargeDataThermal entity = new DischargeDataThermal();
		BeanUtils.copyProperties(bo, entity);
//		entity.setCompanyId(remoteUserService.getCityDeptId());
		if (ObjectUtil.isEmpty(entity.getCompanyId())) {
			entity.setCompanyId(JwtUtils.getCurrentUserCompanyId());
		}
		//查询已有数据是否重复
		List<DischargeDataThermal> dischargeDataThermalList = list(
				new LambdaQueryWrapper<DischargeDataThermal>()
						.eq(DischargeDataThermal::getCompanyId, entity.getCompanyId())
						.eq(DischargeDataThermal::getReportTime, entity.getReportTime())
						.select(DischargeDataThermal::getId));
		if (CollectionUtil.isNotEmpty(dischargeDataThermalList) && dischargeDataThermalList.size() > 0) {
			return "所选月份数据已填报";
		}
		if (baseMapper.insert(entity) == 1) {
			return "";
		} else {
			return "保存失败";
		}
	}

	@Override
	public void edit(DischargeDataThermalBo bo) {
		DischargeDataThermal entity = new DischargeDataThermal();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.updateById(entity);
	}

	@Override
	public void del(Long id) {
		baseMapper.deleteById(id);
	}

	@Override
	public List<DischargeDataThermalVo> countCompanyData(Date dateStart, Date dateEnd, Long companyId) {
		DischargeDataThermalBo queryBo = new DischargeDataThermalBo();
		queryBo.setCompanyId(companyId);
		queryBo.setQueryStartTime(dateStart);
		queryBo.setQueryEndTime(dateEnd);
		if (ObjectUtil.isEmpty(companyId) || companyId.equals(0L)) {
			return baseMapper.countCompanyData(queryBo);
		} else {
			return baseMapper.getCompanyDataList(queryBo);
		}
	}
}
