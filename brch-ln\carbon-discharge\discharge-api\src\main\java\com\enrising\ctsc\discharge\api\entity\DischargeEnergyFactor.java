package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放能源转换因子表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@TableName("discharge_energy_factor")
public class DischargeEnergyFactor extends Model<DischargeEnergyFactor> {

    /**
     * 主键id,采用雪花id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建者id
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者id
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 能源类型id
     */
    private Long energyTypeId;

    /**
     * 数据有效期起始日期
     */
    private Date validityStart;

    /**
     * 数据有效期结束日期
     */
    private Date validityEnd;

    /**
     * 转换因子
     */
    private BigDecimal factor;

    /**
     * 数据标准来源
     */
    private String source;

    /**
     * 删除标志：0-正常；1-删除
     */
    private String delFlag;

    /**
     * 低位发热量
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal netCalorificPower;

    /**
     * 单位热值含碳量
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal carbonPerUnitCalorificValue;

    /**
     * 碳氧化率
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal carbonOxidationRate;
}
