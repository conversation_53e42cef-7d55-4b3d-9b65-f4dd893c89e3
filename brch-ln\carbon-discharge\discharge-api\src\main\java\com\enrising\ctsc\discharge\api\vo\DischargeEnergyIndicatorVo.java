package com.enrising.ctsc.discharge.api.vo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放能源指标表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeEnergyIndicatorVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
	private Long id;

	/**
	 * 创建者id
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date createTime;

	/**
	 * 更新者id
	 */
	private Long updateBy;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date updateTime;

	/**
	 * 能源指标类型名称
	 */
	private String indicatorName;

	/**
	 * 单位id
	 */
	private String unit;

	/**
	 * 状态，1-启用，2-禁用
	 */
	private String status;

	/**
	 * 父指标id
	 */
	private Long parentId;

	/**
	 * 排序值
	 */
	private Integer sort;

	/**
	 * 单位名称
	 */
	private String unitName;

	/**
	 * 单位描述
	 */
	private String unitDescription;

	/**
	 * 集团数据
	 */
		private BigDecimal groupData;

	/**
	 * 股份数据
	 */
		private BigDecimal stockData;

	/**
	 * 大型数据中心数据
	 */
		private BigDecimal largeData;

	/**
	 * 中小型数据中心数据
	 */
		private BigDecimal mediumData;

	/**
	 * 移动业务数据
	 */
		private BigDecimal mobileData;

	/**
	 * 填报时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
		private Date reportTime;

	/**
	 * 填报单位id
	 */
		private Long companyId;

	/**
	 * 上报标志
	 */
		private String reportFlag;
}
