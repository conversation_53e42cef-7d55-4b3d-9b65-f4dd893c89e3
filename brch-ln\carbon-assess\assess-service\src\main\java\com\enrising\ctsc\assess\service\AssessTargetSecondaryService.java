package com.enrising.ctsc.assess.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.assess.api.bo.AssessTargetSecondaryBo;
import com.enrising.ctsc.assess.api.entity.AssessTargetSecondary;
import com.enrising.ctsc.assess.api.query.AssessTargetSecondaryQuery;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.assess.api.vo.AssessTargetSecondaryVo;
import com.enrising.ctsc.assess.api.vo.CompanyCarbonVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 考核二级指标
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
public interface AssessTargetSecondaryService extends IService<AssessTargetSecondary> {

	/**
	 * 分页查询
	 *
	 * @param page  分页
	 * @param query 参数
	 * @return 列表
	 */
	TableDataInfo<AssessTargetSecondaryVo> findList(Page<AssessTargetSecondaryVo> page, AssessTargetSecondaryQuery query);

	/*
	 * 查询指标下各个公司的碳排放量
	 * */
	Page<CompanyCarbonVo> getCompanyCarbonByPage(QueryPage<AssessTargetSecondaryQuery> queryPage);

	/*
	 * 查询指标下某个公司的每月碳排放量
	 * */
	List<CompanyCarbonVo> getMonthCarbonByPage(AssessTargetSecondaryQuery query);

	/**
	 * 列表
	 * @param query 参数
	 * @return 列表
	 */
	List<AssessTargetSecondaryVo> targetSecondaryList(AssessTargetSecondaryQuery query);

	/**
	 * 详情
	 *
	 * @param query 参数
	 * @return 详情
	 */
	AssessTargetSecondaryVo detail(AssessTargetSecondaryQuery query);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	void add(AssessTargetSecondaryBo bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	void edit(AssessTargetSecondaryBo bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	void del(Long id);

	/**
	 * 完全详情
	 *
	 * @param query 参数
	 * @return 详情
	 */
	AssessTargetSecondaryVo getFullDetail(AssessTargetSecondaryQuery query);

	/**
	 * 导出指标下各个公司的碳排放量
	 * @param  query 参数
	 */
	void downloadExcel(HttpServletRequest request, HttpServletResponse response, QueryPage<AssessTargetSecondaryQuery> query);
}
