package com.sccl.modules.minio.controller;

import com.sccl.modules.minio.config.MinioConfig;
import com.sccl.modules.minio.service.FileMigrationService;
import com.sccl.modules.minio.util.MinioUtil;
import com.sccl.framework.web.domain.AjaxResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件管理
 */
@Slf4j
@RestController
@RequestMapping("/file")
@AllArgsConstructor(onConstructor = @__(@Autowired))
public class MinioController {

    private final MinioUtil minioUtil;

    private final FileMigrationService fileMigrationService;

    /**
     * 上传图片，成功返回文件url
     */
    @PostMapping("/uploadImage")
    public AjaxResult uploadImage(@RequestParam(name = "file") MultipartFile file) {
        String url = minioUtil.putObject(file).getUrl();
        log.info("上传图片成功，url={}", url);
        return AjaxResult.success(url);
    }

    /**
     * 获取预览url
     */
    @GetMapping("/getPreviewUrl/{bucketName}")
    public AjaxResult getPreviewUrl(@PathVariable String bucketName, String objectName) {
        String viewUrl = minioUtil.getViewUrl(bucketName, objectName);
        return AjaxResult.success().put("url", viewUrl);
    }

    /**
     * 列出所有桶
     */
    @GetMapping("/listBuckets")
    public AjaxResult listBuckets() {
        return AjaxResult.success(minioUtil.listBucketNames());
    }

    /**
     * 删除存储桶
     */
    @DeleteMapping("/deleteBucket")
    public AjaxResult deleteBucket(String bucketName) {
        boolean removeBucket = minioUtil.removeBucket(bucketName);
        log.info("删除结果：{}", removeBucket);
        return AjaxResult.success(removeBucket);
    }

    /**
     * 删除缓存桶名key
     */
    @DeleteMapping("/deleteBucketKey")
    public AjaxResult deleteBucketKey() {
        fileMigrationService.deleteBucketKey();
        return AjaxResult.success("删除完成");
    }

    /**
     * 同步文件到Minio
     */
    @PostMapping("/syncFile")
    public AjaxResult syncFile() {
        boolean isSuccess = fileMigrationService.syncFile();
        log.info("同步文件结果：{}", isSuccess);
        return AjaxResult.success(isSuccess);
    }

    /**
     * 终止迁移
     */
    @GetMapping("/stopSync")
    public AjaxResult stopSync() {
        fileMigrationService.stopSync();
        return AjaxResult.success("终止成功");
    }


}
