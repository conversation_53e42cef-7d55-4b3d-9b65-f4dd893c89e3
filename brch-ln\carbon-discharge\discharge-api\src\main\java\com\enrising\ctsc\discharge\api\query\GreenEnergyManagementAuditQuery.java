package com.enrising.ctsc.discharge.api.query;

import cn.hutool.core.util.StrUtil;
import cn.zhxu.bs.bean.DbField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 绿电管理审核查询
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2024-09-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GreenEnergyManagementAuditQuery {
    private Long id;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 能源类型
     */
    private String energyType;

    private Date submitTime;

    /**
     * 所属分公司
     */
    private Long companies;

    /**
     * 所属分公司名称
     */
    private String companiesName;

    /**
     * 所属部门
     */
    private Long companyBranch;

    /**
     * 所属部门名称
     */
    private String companyBranchName;

    private List<String> auditResultList;

    public String getKeyword() {
        if (StrUtil.isNotBlank(keyword)) {
            return StrUtil.format("%{}%", keyword);
        }
        return keyword;
    }
}
