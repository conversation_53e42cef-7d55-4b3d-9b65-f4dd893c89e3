package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.bo.DischargeMonitorSettingBo;
import com.enrising.ctsc.discharge.api.entity.DischargeMonitorSetting;
import com.enrising.ctsc.discharge.api.query.AssessTargetSecondaryQuery;
import com.enrising.ctsc.discharge.api.query.DischargeMonitorSettingQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeEmissionTrendVo;
import com.enrising.ctsc.discharge.api.vo.DischargeMonitorSettingVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 碳排放监测设置表
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Mapper
public interface DischargeMonitorSettingMapper extends BaseMapper<DischargeMonitorSetting> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
	IPage<DischargeMonitorSettingVo> findList(Page<DischargeMonitorSettingVo> page, @Param(Constants.WRAPPER) Wrapper<DischargeMonitorSettingQuery> wrapper);

	/**
	 * 获取监测设置分页列表
	 */
	IPage<DischargeMonitorSettingVo> getSettingList(@Param("page") Page<DischargeMonitorSettingVo> page, @Param("bo") DischargeMonitorSettingBo bo);

	/**
	 * 获取监测设置分页列表
	 */
	IPage<DischargeMonitorSettingVo> getRecordsList(@Param("page") Page<DischargeMonitorSettingVo> page, @Param("bo") DischargeMonitorSettingBo bo);

	/**
	 * 获取监测设置列表
	 */
	List<DischargeMonitorSettingVo> getRecordsList(@Param("bo") DischargeMonitorSettingBo bo);

	/**
	 * 列表查询
	 *
	 *
	 * @param wrapper 条件
	 * @return 列表
	 */
	List<DischargeMonitorSettingVo> findList(@Param(Constants.WRAPPER) Wrapper<DischargeMonitorSettingQuery> wrapper);

	/**
	 * 详情查询
	 *
	 * @param wrapper 条件
	 * @return 结果
	 */
	DischargeMonitorSettingVo detail(@Param(Constants.WRAPPER) Wrapper<DischargeMonitorSettingQuery> wrapper);

	/**
	 * 详情查询 -- 监测记录
	 *
	 * @return 结果
	 */
	DischargeMonitorSettingVo getRecordInfo(@Param("query") DischargeMonitorSettingQuery query);


	/**
	 * 	获取 今年单位电信业务总量碳排放
	 */
	List<DischargeMonitorSettingVo> getYearTotalCarbon(@Param("bo") DischargeMonitorSettingBo bo);

	/**
	 * 	获取 单位总量碳排放
	 */
	DischargeMonitorSettingVo getCarbonTotal(@Param("query") AssessTargetSecondaryQuery query);

	/**
	 * 	获取 单位化石总量碳排放
	 */
	DischargeMonitorSettingVo getFossilCarbonTotal(@Param("query") AssessTargetSecondaryQuery query);

	/**
	 * 	获取 业务能源数据
	 */
	List<DischargeEmissionTrendVo> getTelecomBusinessTotal(@Param("nowYear") String nowYear);

	/**
	 * 获取各个公司的能源消耗总量
	 */
	IPage<DischargeMonitorSettingVo> getEnergyCompanyList(@Param("page") Page<DischargeMonitorSettingVo> page, @Param("bo") DischargeMonitorSettingBo bo);


	/**
	 * 获取公司数量
	 */
	int getCompanyCount();
}