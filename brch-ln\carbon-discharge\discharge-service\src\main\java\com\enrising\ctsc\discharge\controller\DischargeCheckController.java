package com.enrising.ctsc.discharge.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.carbon.common.entity.User;
import com.enrising.ctsc.carbon.common.utils.JwtUtils;
import com.enrising.ctsc.discharge.api.bo.DischargeCheckBo;
import com.enrising.ctsc.discharge.api.query.DischargeCheckQuery;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.vo.DischargeCheckVo;
import com.enrising.ctsc.discharge.service.DischargeCheckService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 碳排放核查
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Slf4j
@RestController
@RequestMapping("/discharge/check")
@AllArgsConstructor
public class DischargeCheckController {
    private final DischargeCheckService dischargeCheckService;

    @GetMapping("/list")
    public TableDataInfo<DischargeCheckVo> page(Page<DischargeCheckVo> page, DischargeCheckQuery query) {
        log.info("page:{};{}", page.getCurrent(), page.getSize());
        log.info("query:{}", query);
        User user = JwtUtils.getUser();
        log.info("user:{}", user);
        return dischargeCheckService.findList(page, query);
    }

    @GetMapping("/detail")
    public R<DischargeCheckVo> get(DischargeCheckQuery query) {
        DischargeCheckVo detail = dischargeCheckService.detail(query);
        return R.success(detail, "查询成功");
    }

    @PostMapping(value = "/save")
    public R<String> save(@RequestBody DischargeCheckBo bo) {
        dischargeCheckService.add(bo);
        return R.success("保存成功");
    }

    @PostMapping(value = "/update")
    public R<String> update(@RequestBody DischargeCheckBo bo) {
        dischargeCheckService.edit(bo);
        return R.success("修改成功");
    }

    @PostMapping(value = "/delete/{id}")
    public R<String> delete(@PathVariable Long id) {
        dischargeCheckService.del(id);
        return R.success("删除成功");
    }
}
