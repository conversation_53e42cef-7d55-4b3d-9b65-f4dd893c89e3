package com.enrising.ctsc.discharge.controller;

import com.enrising.ctsc.discharge.api.bo.DischargeEnergyCoefficientBo;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyCoefficient;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyCoefficientQuery;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyCoefficientVo;
import com.enrising.ctsc.discharge.service.DischargeEnergyCoefficientService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 碳排放能源转换系数表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@RestController
@RequestMapping("/discharge/coefficient")
@AllArgsConstructor
public class DischargeEnergyCoefficientController {
	private final DischargeEnergyCoefficientService dischargeEnergyCoefficientService;

	@PostMapping("/list")
	public TableDataInfo<DischargeEnergyCoefficientVo> page(@RequestBody QueryPage<DischargeEnergyCoefficientQuery> page) {
		return dischargeEnergyCoefficientService.findList(page);
	}

	@PostMapping(value = "/selectTypeByDate")
	public R<List<DischargeEnergyCoefficient>> selectTypeByDate(@RequestBody DischargeEnergyCoefficientQuery query) {
		List<DischargeEnergyCoefficient> factors = dischargeEnergyCoefficientService.selectTypeByDate(query);
		return R.success(factors, factors.size() > 0 ? "有效期重合，添加后已有数据有效期将自动结束" : "有效期无重合");
	}


	@PostMapping(value = "/getListByTime")
	public R<List<DischargeEnergyCoefficient>> getListByTime(@RequestBody DischargeEnergyCoefficientQuery query) {
		return R.success(dischargeEnergyCoefficientService.getListByTime(query.getValidityStart()));
	}

	/**
	 * 导出excel
	 *
	 * @param query 查询条件
	 * @param response 响应
	 */
			@PostMapping(value = "/exportExcel")
	public void exportExcel(@RequestBody DischargeEnergyCoefficientQuery query, HttpServletResponse response) {
		dischargeEnergyCoefficientService.exportExcel(query, response);
	}

	@GetMapping("/detail")
	public R<DischargeEnergyCoefficientVo> get(DischargeEnergyCoefficientQuery query) {
		DischargeEnergyCoefficientVo detail = dischargeEnergyCoefficientService.detail(query);
		return R.success(detail, "查询成功");
	}

		@PostMapping(value = "/save")
	public R<String> save(@RequestBody @Valid DischargeEnergyCoefficientBo bo) {
		dischargeEnergyCoefficientService.add(bo);
		return R.success("保存成功");
	}

		@PostMapping(value = "/update")
	public R<String> update(@RequestBody @Valid DischargeEnergyCoefficientBo bo) {
		dischargeEnergyCoefficientService.edit(bo);
		return R.success("修改成功");
	}

		@PostMapping(value = "/delete/{id}")
	public R<String> delete(@PathVariable Long id) {
		dischargeEnergyCoefficientService.del(id);
		return R.success("删除成功");
	}
}
