package com.enrising.ctsc.discharge.api.bo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 碳排放核查
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeCheckBo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
		private Long id;


	/**
	 * 填报单位id
	 */
		private Long companyId;

	/**
	 * 核查单位名称
	 */
		private String checkCompany;

	/**
	 * 核查时间
	 */
		@JsonFormat(pattern = DateUtils.YYYY_MM_DD)
	private Date checkTime;

	/**
	 * 核查负责人
	 */
		private String checkDirector;

	/**
	 * 核查联系电话
	 */
		private String checkPhone;

	/**
	 * 说明
	 */
		private String remarks;

	/**
	 * 报告附件id
	 */
		private Long reportAttachmentId;

	/*
	* 文件ids
	* */
	private List<Long> fileIds;


}
