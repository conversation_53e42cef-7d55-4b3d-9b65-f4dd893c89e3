package com.enrising.ctsc.discharge.api.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 台账 返回结果
 *
 * <AUTHOR>
 * @date 2024-09-16
 */
@Data
public class AccountBaseResult {
    /**
     * 电表协议ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long ammeterid;
    /**
     * 项目名称
     */
    private String projectname;
    /**
     * 电表户号/协议编码
     */
    private String ammetercode;
    /**
     * 根据台账的录入日期来编号：如用电台账201201；用于为各次台账建立顺序关系，便于查询
     */
    private String accountno;
    /**
     * 起始日期-格式为: yyyyMMdd
     */
    private String startdate;
    /**
     * 截止日期-格式为: yyyyMMdd
     */
    private String enddate;
    /**
     * 上期峰值
     */
    private BigDecimal prevhighreadings;
    /**
     * 上期平值
     */
    private BigDecimal prevflatreadings;
    /**
     * 上期谷值
     */
    private BigDecimal prevlowreadings;
    /**
     * 上期止度
     */
    private BigDecimal prevtotalreadings;
    /**
     * 本期峰值
     */
    private BigDecimal curhighreadings;
    /**
     * 本期平值
     */
    private BigDecimal curflatreadings;
    /**
     * 本期谷值
     */
    private BigDecimal curlowreadings;
    /**
     * 本期止度
     */
    private BigDecimal curtotalreadings;
    /**
     * 电损
     */
    private BigDecimal transformerullage;
    /**
     * 用户输入的专票含税金额
     */
    private BigDecimal inputtaxticketmoney;
    /**
     * 专票税率
     */
    private BigDecimal taxrate;
    /**
     * 专票税额
     */
    private BigDecimal taxamount;
    /**
     * 用户输入的普票含税金额
     */
    private BigDecimal inputticketmoney;
    /**
     * 其它：记录发票、新安装、换表等一系列费用
     */
    private BigDecimal ullagemoney;
    /**
     * 总金额
     */
    private BigDecimal accountmoney;
    /**
     * 倍率
     */
    private BigDecimal magnification;
    /**
     * 用电量
     */
    private BigDecimal curusedreadings;
    /**
     * 总电量
     */
    private BigDecimal totalusedreadings;
    /**
     * 电价
     */
    private BigDecimal unitpirce;
    /** 用户输入的备注 */
    private String bz;
    /**
     * 电信分割比例
     */
    private BigDecimal percent;

    /**
     * 移动分割比例
     */
    private String mobileApportionmentratio;

    /**
     * 联通分割比例
     */
    private String unicomApportionmentratio;

    /**
     * 扩展分割比例
     */
    private String expandApportionmentratio;

    /**
     * 能源分割比例
     */
    private String energyApportionmentratio;
    /**
     * 错误提示
     */
    private String error;
/********************************************* 以上是导入台账顺序 ********************************************************************/

    /**
     * 备注
     */
    private String remark;
    /**
     * 类型描述字符串
     */
    private String categoryname;
    /**
     * 定额度数
     */
    private BigDecimal quotareadings;
    /**
     * 定额浮动比(%) , 公式 : (电量+电损-定额)/定额
     */
    private BigDecimal quotereadingsratio;
    /**
     * 支局
     */
    private String substation;
    /**
     * 录入人员ID
     */
    private Long inputerid;
    /**
     * 录入日期
     */
    private Date inputdate;
    /**
     * 最后编辑人
     */
    private Long lastediterid;
    /**
     * 最后编辑日期
     */
    private Date lasteditdate;
    /**
     * pcid
     */
    private Long pcid;
    /**
     * 用电类型
     */
    private String electrotypename;
    /**
     * 描述类型
     */
    private Integer category;
    /**
     * 所属分公司
     */
    private String company;
    /**
     * 责任中心
     */
    private String country;
    /**
     * 预估电费
     */
    private String predictionmoney;
    /**
     * 电表类型
     * 在power_category_type表中type_category="ammeterType"
     */
    private Integer ammetertype;
    /**
     * 0,老的电表协议1,新增的电表协议
     */
    private Integer isnew;
    /**
     * 1预交 2后交
     */
    private Integer paytype;
    /**
     * 0,正常,2手动修改上期止度,3手动修改上期峰谷平4,手工修改上期起始日期
     */
    private Integer opflag;
    /**
     * 翻表度数,即电表的最大度数
     */
    private BigDecimal maxdegree;
    /**
     * 业务字段，用于计算：本条台账信息是否使电表翻表
     */
    private Boolean ifMaxdegree = false;
    /**
     * 账有效性,填写后的台账<总费用不为0>标识为有效台账 : 有效 - 1 , 无效 - 0
     */
    private BigDecimal effective;
    /**
     * 状态1为正常;2流程中 3报账完成 4已生成归集单 5已退回
     */
    private Integer status;
    /** 台账状态名称 */
    private String statusName;
    /**
     * 台账关联的明细id
     */
    private Long pabriid;
    /**
     * 账单用，关联报账明细
     */
    private BigDecimal money;
    /**
     * 报账税额
     */
    private BigDecimal taxmoney;
    /**
     * 局站名称
     */
    private String stationName;
    /**
     * 1自有 2铁塔， 3自有预估 4铁塔预估
     */
    private Integer accounttype;
    /**
     * 电表类型
     */
    private Integer property;
    /**
     * 电表用途
     */
    private String ammeteruse;
    /** 电表用途名称 */
    private String ammeterusename;
    /**
     * 用电类型
     * 1	生产用电
     * 2	管理办公用电
     * 3	营业渠道用电
     * 4	其他用电
     */
    private Integer electrotype;
    /**
     * 5GR站址编码
     */
    private String stationcode5gr;
    /**
     * 5GR站址名称
     */
    private String stationname5gr;
    /**
     * 查询的时候判断是否有下一期正式数据
     */
    private Boolean ifNext;
    /**
     * 电表的父id
     */
    private Long ammparentid;
    /**
     * 台账的父id
     */
    private Long parentPcid;
    /**
     * 客户Id
     */
    private String customerId;
    /**
     * 客户Name
     */
    private String customerName;

    /**
     * 专票含税金额
     */
    private BigDecimal taxticketmoney;
    /**
     * 普票含税金额
     */
    private BigDecimal ticketmoney;
    /**
     * 所属分公司id
     */
    private Long companyId;
    /**
     * 责任中心id
     */
    private Long countryId;
    /** 不含税总金额 */
    private BigDecimal totalBHS;
    /** 普票税额（四川） */
    private BigDecimal tickettaxamount;
    /** RRU个数 */
    private BigDecimal rru;
    /** 是否供电局直供，0：否，1：是  改成对外结算类型 直供电、转供电
     * 在power_category_type表中type_category="directSupplyFlag" */
    private Integer directsupplyflag;
    /** 站址编码 */
    private String stationaddresscode;
    /** 预估台账类型 ：1预估 2挂账 3预付 */
    private Integer accountestype;
    /** 供电局电表编号 */
    private String supplybureauammetercode;
    /** 台账查询导出增加财辅报账单号（报账单），网管C网编号（基础数据），网管编号L2.1G（基础数据），网管编号L1.8G（基础数据），网管编号C+L800M（基础数据），合同对方（基础数据），报账人（报账单），台账状态，财辅账期（报账单）信息。 */
    private String nmccode;
    private String nml2100;
    private String nml1800;
    private String nmcl800m;
    private String contractothpart;
    private String billId;
    private String fillinname;
    private String budgetsetname;
    /** 对应资源系统局站编码或站址编码或房屋编码（当局站类型为‘生产用房-移动基站’且产权为‘租用’时，存放站址编码，当局站类型为‘非生产用房-管理用房’,'非生产用房-渠道用房'或’非生产用房-其他‘时存放房屋编码，其他时候为对应资源系统局站编码） */
    private String resstationcode;
    /** 台账类型 */
    private String type;
    /** 倍率在电表的基础信息中有，在台账中保存倍率是防止更换电表导致改变电表倍率的情况 */
    private BigDecimal multtimes;
    /** 事项名称 */
    private String note;
    /** 录入人名称 */
    private String inputname;
    /** 对外结算类型名称 */
    private String directsupplyflagname;
    /** 台账电表分割比例倍率不一致标准 1是一致 2是不一致 */
    private Integer magnificationerr;
    private Integer percenterr;

    /** 电表倍率 */
    private BigDecimal ammmulttimes;
    /** 电表分割比 */
    private BigDecimal ammpercent;
    /** 注意 */
    private String careful;
    /** 电表是否换标 */
    private Integer ischangeammeter;
    /** 原点表度数 */
    private BigDecimal oldbillpower;
    /** 归集单类型 */
    private String billtype;
    /** 辽宁铁塔包干截止时间 */
    private String lumpenddate;

    /** 报账状态 */
    private String expensesStatus;

    /**
     * 稽核后查询结果的唯一key
     */
    private String ctgKey;

    private String iftimeout;
    private String taremark;

    private Long oldammeterid;

    private Map<String,String> map;

    private Long toweraccountid;

    private List<AccountBaseResult> sonList = new ArrayList<>();

    private Integer pustatus;// 普服状态

    private String packagetype;
//    @Excel(name = "包干费用")
    private String fee;
//    @Excel(name = "详细地址")
    private String address;
//    @Excel(name = "付费方式")
    private String electrovalencenature;

    private  BigDecimal protocolprice;  //协议单价
    private BigDecimal unitpirceold;//上一次单价
    private BigDecimal curusedreadingsold;//上一次电量

    private BigDecimal totalusedreadingsold;//上一次实际电量
    private BigDecimal aveaccountmoneyold;//上一次日均电费
    private BigDecimal aveaccountmoney;//本次日均电费

    private Integer newAmmeter;//是否第一次报账 是否新电表 0 是 1 不是

    /**
     * 本期峰值 加减
     */
    private BigDecimal highreadings;
    /**
     * 本期平值 加减
     */
    private BigDecimal flatreadings;
    /**
     * 本期谷值 加减
     */
    private BigDecimal lowreadings;

    private String companyNameText;
}
