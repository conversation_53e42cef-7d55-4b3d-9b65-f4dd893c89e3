package com.enrising.ctsc.carbon.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 集团能耗同步日志
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2024-01-03
 */

@Data
@TableName("discharge_jituan_energy_sync_log")
public class DischargeJituanEnergySyncLog extends Model<DischargeJituanEnergySyncLog> {

	/**
	* id
	*/
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	* 上报人员
	*/
	private Long reportUser;

	/**
	* 上报部门
	*/
	private Long reportDept;

	/**
	* 上报数据
	*/
	private Date reportDate;

	/**
	* 上报时间
	*/
	private Date createTime;

}
