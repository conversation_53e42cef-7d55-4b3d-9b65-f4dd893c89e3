/*
 * Copyright (c) 2021 ctsc-cloudx Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.enrising.ctsc.carbon.common.constant;

/**
 * <AUTHOR>
 * @date 2021/3/1
 */
public interface CommonConstants {

	/**
	 * 字符串0
	 */
	String ZERO_STRING = "0";

	/**
	 * 数字0
	 */
	int ZERO_NUMBER = 0;

	/**
	 * 成功标记
	 */
	Integer SUCCESS = 0;

	/**
	 * 失败标记
	 */
	Integer FAIL = 1;

	/**
	 * 菜单树根节点
	 */
	Long MENU_TREE_ROOT_ID = -1L;

	String ID = "id";

	/**
	 * 通用字段-创建人ID
	 */
	String CREATE_BY = "createBy";

	String CREATOR_ID = "creatorId";
	/**
	 * 通用字段-创建人姓名
	 */
	String CREATOR_NAME = "creatorName";

	/**
	 * 通用字段-创建时间
	 */
	String CREATE_TIME = "createTime";

	/**
	 * 通用字段-删除标志
	 */
	String DEL_FLAG = "delFlag";
	String DELETE_FLAG = "deleteFlag";

	/**
	 * 通用字段-创建人ID
	 */
	String UPDATE_BY = "updateBy";

	/**
	 * 通用字段-创建人姓名
	 */
	String UPDATER_NAME = "updaterName";

	/**
	 * 通用字段-创建时间
	 */
	String UPDATE_TIME = "updateTime";
}
