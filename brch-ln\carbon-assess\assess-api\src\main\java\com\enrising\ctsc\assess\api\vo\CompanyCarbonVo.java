package com.enrising.ctsc.assess.api.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CompanyCarbonVo {

    /*
     * 公司id
     * */
    private Long companyId;

    /**
     * 公司名称
     */
    @Excel(name = "部门名称", width = 20)
    private String companyName;

    /**
     * 去年业务碳排放量（吨）
     */
    @Excel(name = "去年碳排放量（吨）", width = 20)
    private BigDecimal preCarbonTotal = new BigDecimal(0);

    /**
     * 今年业务碳排放量（吨）
     */
    @Excel(name = "今年碳排放量（吨）", width = 20)
    private BigDecimal nowCarbonTotal = new BigDecimal(0);

    /**
     * 下降率
     */
    @Excel(name = "下降率", width = 20)
    private String downRate;

    /*
     * 去年业务碳排放量（吨）(全公司)
     * */
    private BigDecimal preAllCompanyTotal = new BigDecimal(0);

    /*
     * 今年业务碳排放量（吨）(全公司)
     * */
    private BigDecimal nowAllCompanyTotal = new BigDecimal(0);

    /*
     * 下降率（全公司）
     * */
    private String allCompanyDownRate;

    /*
     * 月份
     * */
    private String month;

}
