<template>
    <Modal v-model="electricTypeModel" :width="screenWidth" :styles="{top: '20px'}" title="选择用电类型" @on-ok="onModalOK" @on-cancel="onModalCancel">
        <cl-table ref="electricType"
                  strip
                  :searchLayout="electricType.filter"
                  :columns="electricType.columns"
                  :data="electricType.data"
                  :loading="electricType.loading"
                  :showPage="false"
                  selectEnabled
                  select-multiple
                  :exportable="false"
                  @on-row-dblclick="chooseOk"
                  @on-query="query">
        </cl-table>
    </Modal>
</template>

<script>
    import {listElectricType} from '@/api/basedata/ammeter.js'

    export default {
        name: "selectElectricType",
        data() {
            return {
                electricTypeModel: false,
                screenWidth:500,
                electricType: {
                    loading: false,
                    filter: [
                        {
                            formItemType: 'input',
                            prop: 'typeName',
                            label: "用电类型",
                            width: 150,
                        }

                    ],
                    columns: [
                        {
                            title: '类型编码',
                            key: 'id',
                            align: 'center',
                            width:'120px'
                        },
                        {
                            title: '用电类型',
                            key: 'typeName',
                            align: 'center'
                        }],
                    data: [],
                    total: 0,
                    pageSize: 10
                }

            }
        },
        methods: {
            /**
             * 根据条件进行查询用电类型
             */
            initElectricType() {
                this.$refs.electricType.queryParams.typeName= null;
                this.query({
                    pageNum: 1,
                    pageSize: this.electricType.pageSize
                });
                this.electricTypeModel = true;
            },

            query(params) {
                listElectricType(params).then(res => {
                    this.electricType.total = res.data.total;
                    this.electricType.data = Object.assign([], res.data.rows)
                });
            },
            chooseOk(currentRow) {
                let rows = [];
                rows[0]=currentRow;
                this.$emit("listenToSetElectricType", rows);
                this.onModalCancel();
            },
            onModalOK() {
                let rows = this.$refs.electricType.getSelection();
                this.$emit("listenToSetElectricType", rows);
                this.onModalCancel();
            },
            onModalCancel() {
                this.electricTypeModel = false
            },
        }, mounted() {
            const that = this;
            window.onresize = () => {
                return (() => {
                    window.screenWidth = document.body.clientWidth;
                    that.screenWidth = window.screenWidth * 0.5;
                })();
            };
            window.onresize();
        }
    }
</script>

<style scoped>

</style>