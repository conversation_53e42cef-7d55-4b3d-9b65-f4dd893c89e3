package com.enrising.ctsc.assess.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 考核任务上报表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */

@Data
@TableName("assess_task_report")
public class AssessTaskReport extends Model<AssessTaskReport> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 考核任务id
     */
    private Long templateObjectId;

    /**
     * 上报名称
     */
    private String reportName;

    /**
     * 上报说明
     */
    private String reportDescription;

    /**
     * 考核评分
     */
    private Double assessScore;

    /**
     * 考核评价
     */
    private String assessEvaluate;

    /**
     * 按规则得分
     */
    private Double ruleScore;

    /**
     * 考评人
     */
    private Long assesser;

    /**
     * 考评时间
     */
    private Date assessTime;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 上报状态：0-未上报；1-已上报
     */
    private String reportStatus;


    /**
     * 报告类型 1-1份报告 2-3份报告 3-4份报告  4-12份报告
     */
    private String reportType;

    /**
     * 上报时间
     */
    private Integer reportTime;

    /**
     * 上报时间
     */
    private Date reportDate;


    @TableField(exist = false)
    private String reportBy;

    @TableField(exist = false)
    private String assessBy;
}
