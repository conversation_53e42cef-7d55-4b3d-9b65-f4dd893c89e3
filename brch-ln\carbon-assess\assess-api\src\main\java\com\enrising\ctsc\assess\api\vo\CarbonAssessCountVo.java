package com.enrising.ctsc.assess.api.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 碳考核统计工具类
 */
@Data
public class CarbonAssessCountVo {

    /**
     * 年份、月份、季度
     */
    private Integer node;

    /**
     * 当年、当季度、当月碳排放量
     */
    private BigDecimal thisYearCarbonCoal;

    /**
     * 去年、上季度、上月碳排放量
     */
    private BigDecimal lastYearCarbonCoal;

    /**
     * 当年、当季度、当月业务数据总量
     */
    private BigDecimal thisYearTelecomBusinessTotal;

    /**
     * 去年、上季度、上月业务数据总量
     */
    private BigDecimal lastYearTelecomBusinessTotal;
}
