<style lang="less">
// .accountbill .filter-divider {
//   margin: 0px;
//   text-align: center;
// }

// .accountbill .header-bar-show {
//   max-height: 300px;
//   padding-top: 14px;
//   overflow: inherit;
//   border-bottom: 1px solid #e8eaec;
// }

// .accountbill .header-bar-hide {
//   max-height: 0;
//   padding-top: 0;
//   overflow: hidden;
//   border-bottom: 0;
// }

.accountbill {
  height: 100%;
}
.accountbill .filter-divider {
  margin: 0px;
  text-align: center;
}

.accountbill .header-bar-show {
  max-height: 300px;
  /*padding-top: 14px;*/
  overflow: inherit;
  border-bottom: 1px solid #e8eaec;
}

.accountbill .header-bar-hide {
  max-height: 0;
  padding-top: 0;
  overflow: hidden;
  border-bottom: 0;
}

.accountbill .row {
  height: 30px;
  margin-bottom: -50px;
}

.form-line-height {
  margin-bottom: 10px;
}

.mytable .ivu-table-cell {
  padding-left: 1px;
  padding-right: 1px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-all;
  box-sizing: border-box;
}

#ipt_dep .ivu-icon:before {
  font-size: 30px;
}
.requireStar .ivu-form-item-label:before{
    content: '*';
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 12px;
    color: #ed4014;
}
</style>
<template>
  <div class="testaa1">
    <Spin size="large" fix v-if="exportloading"></Spin>
    
    <div class="accountbill">
      <Row :class="filterColl?'header-bar-show':'header-bar-hide'">
        <Form ref="formInline" :model="queryParams" :label-width="150" inline>
          <Row>
            <Col span="7">
              <FormItem label="电表编号:" prop="meterCode" class="form-line-height">
                <cl-input v-model="queryParams.meterCode" placeholder="请输入电表编号"
                          :style="formItemWidth"/>
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem label="项目名称:" prop="projectname" class="form-line-height">
                <cl-input v-model="queryParams.projectname" placeholder="请输入项目名称"
                          :style="formItemWidth"/>
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem label="局站名称:" prop="stationname" class="form-line-height">
                <cl-input v-model="queryParams.stationname" placeholder="请输入局站名称"
                          :style="formItemWidth"/>
              </FormItem>
            </Col>
          </Row>
          <Row> 
            <Col span="7">
              <FormItem label="所属分公司：" prop="company" class="form-line-height">
                <Select 
                v-model="queryParams.company" 
                @on-change="selectChange(queryParams.company)"
                :style="formItemWidth">
                  <Option value="" v-if="companies.length != 1">全部</Option>
                  <Option 
                  v-for="item in companies" 
                  :value="item.id" 
                  :key="item.id">{{ item.name }}
                  </Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem label="所属部门：" 
              prop="countryName" 
              v-if="isAdmin == true" 
              class="form-line-height">
                <Input 
                :clearable=true 
                icon="ios-archive" 
                v-model="queryParams.countryName"
                placeholder="点击图标选择" 
                @on-click="chooseResponseCenter()" 
                readonly
                :style="formItemWidth">
                </Input>
              </FormItem>
              <FormItem label="所属部门：" prop="country" v-if="isAdmin == false" class="form-line-height">
                <Select v-model="queryParams.country" :style="formItemWidth">
                  <Option value="">全部</Option>
                  <Option v-for="item in departments" :value="item.id" :key="item.id">{{ item.name }}
                  </Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem label="单据状态：" prop="billStatus" class="form-line-height">
                <Select v-model="queryParams.billStatus" :style="formItemWidth">
                  <Option value="">请选择</Option>
                  <Option value="0">草稿</Option>
                  <Option value="1">流程中</Option>
                  <Option value="2">已完成</Option>
                  <Option value="3">修改中</Option>
                  <Option value="4">修改流程中</Option>
                  <Option value="5">移除</Option>
                </Select>
              </FormItem>
            </Col>
            </Row>
          <div align="right">
            <Button type="success" icon="ios-search" @click="searchList"
              >查询</Button 
            >
            <Button type="info" icon="ios-redo" @click="onResetHandle()"
              >重置</Button
            >
          </div>
        </Form>
      </Row>
      <div class="filter-divider">
        <icon :type="filterColl?'md-arrow-dropup':'md-arrow-dropdown'" size="20"
              @click="filterColl=!filterColl" :color="filterColl?'#000':'#1ab394'"></icon>
      </div>
  </div>
  
  <div>
    <country-modal ref="countryModal" v-on:getDataFromModal="getDataFromModal"></country-modal>
    <upload-file-modal ref="uploadFileModal"></upload-file-modal>
  </div>
    <div>
      <Row>
        <Col span="12">
          <Page size="small" 
          :total="pageTotal" 
          :current="pageNum" 
          :page-size="pageSize" 
          show-elevator 
          show-sizer
          show-total
          placement="top" @on-change="handlePage" @on-page-size-change='handlePageSize'></Page>
        </Col>
        <Col span="12">
            <div align="right" class="account">
              <Button type="success"  @click="apply()">申请</Button>
              <Button type="success"  @click="modify()">修改</Button>
              <Button type="error" @click="remove()">删除</Button>
              <Button type="error" @click="deleteAll()">移除</Button>
                <Dropdown trigger="click" @on-click="exportCsv">
                    <Button type='default' style="margin-left: 5px" >导出
                        <Icon type='ios-arrow-down'></Icon>
                    </Button>
                    <DropdownMenu slot='list'>
                        <DropdownItem name="current">导出本页</DropdownItem>
                        <DropdownItem name="all">导出全部</DropdownItem>
                    </DropdownMenu>
                </Dropdown>
            </div>
          </Col>
        </Row>
        <Tabs v-model="tableName" type="card" @on-click="setvalue">
        <TabPane name="2" label="一站多表">
          <Table ref="listTable2"
             border :loading="listTb.loading"
             :columns="listTb.columns1"
             :data="insideData"
             class="mytable">
      </Table>
        </TabPane>
        <TabPane name="1" label="一表多站">
          <Table ref="listTable1"
             border :loading="listTb.loading"
             :columns="listTb.columns2"
             :data="insideData"
             class="mytable">
      </Table>
        </TabPane>
        <TabPane name="3"  label="单价合理性">
          <Table ref="listTable3"
             border :loading="listTb.loading"
             :columns="listTb.columns3"
             :data="insideData"
             class="mytable">
      </Table>
        </TabPane>
    </Tabs>
      <!-- <Table ref="listTable"
             border :loading="listTb.loading"
             :columns="listTb.columns"
             :data="insideData"
             class="mytable">
      </Table> -->
      
      <Spin size="large" fix v-if="spinShow"></Spin>
    </div>
    <cl-wf-btn ref="clwfbtn" :isStart="true" :params="workFlowParams" @on-ok="doWorkFlow" v-show="false"></cl-wf-btn>
    <!-- 查看流程 -->
    <Modal v-model="showWorkFlow" title="电表流程及审批意见跟踪表" :width="800">
        <WorkFlowInfoComponet :wfHisParams="hisParams" v-if="showWorkFlow"></WorkFlowInfoComponet>
    </Modal>
    <!-- 申请加入白名单（之前） 
        style="height: 50rem;"-->
    <Modal
      class="ModalA"
        v-model="showApplyModel"
        width="80%"
        title="申请加入白名单"
      > 
      <!-- <applyAddWhiteList :getQueryParams="queryParams" ref="applyAddWhiteList" :valueTL="ammeterTypeList"></applyAddWhiteList> -->
      <div slot="footer" style="text-align: center">
          <Button type="text" @click="cancelWL">取消</Button>
          <!-- <Button type="primary" @click="saveWL">保存</Button>
          <Button type="primary" @click="submitWL">提交</Button> 
          :loading="isLoading==0?loading:false" :disabled="isDisable"
          :loading="isLoading==1?loading:false" :disabled="isDisable"-->
          <Button type="success" @click="onModalOK(0)">保存</Button>
          <Button type="primary" @click="onModalOK1()">提交</Button>
        </div>
    </Modal>
    
    <applyAddWhiteList
     :getQueryParams="queryParams"
      ref="applyAddWhiteList"
      :valueTL="ammeterTypeList" 
      :isAddAndModify="isAddAndModify"
      @getAccountMessagesQ="getAccountMessagesQ"></applyAddWhiteList>
    <!-- 选择白名单类型 -->
    <Modal
        v-model="showApplyModelTL"
        width="65%"
        title="选择白名单类型"
      > 
      <Form :model="ammeterTypeList" ref="ammeterTypeList" :rules="ruleValidateTypeList" :label-width="80"
                class="margin-right-width" style="padding: 20px 20px 28px 20px;">
            <Row>
              <Col span="12">
                    <FormItem label="白名单类型：" :label-width="120" prop="typeList" :class="{requireStar:isRequireFlag}">
                        <!-- <cl-select v-model="ammeter.whiteListType" filterable
                                category="BUR_STAND_TYPE"
                                labelField="typeName" valueField="typeCode">
                        </cl-select> -->
                        <Select
                            @on-change="selectChangeTL"
                            ref="selects"
                            :multiple="false"
                            :clearable="true"
                            v-model="ammeterTypeList.typeList">
                            <!-- <Option value="">请选择</Option> -->
                            <Option value="1">一表多站</Option>
                            <Option value="2">一站多表</Option>
                            <Option value="3">单价合理性</Option>
                        </Select>
                    </FormItem>
                </Col>
            </Row>
      </Form>
      <div slot="footer" style="text-align: center">
          <Button type="text" @click="cancelWL">取消</Button>
          <Button type="primary" @click="nextStep()">下一步</Button>
        </div>
    </Modal>
    
    <whiteListView ref="whiteListView" />
</div>
</template> 
<script>
import whiteListView from '@/view/basedata/whitelist/whiteListView.vue';
  import {mapMutations} from "vuex";
  import {isInTodoList}from"@/api/alertcontrol/alertcontrol";
  import WorkFlowInfoComponet from '@/view/basic/system/workflow/workFlowInfoComponet'
  import { 
    whiteList,
    whiteListNotAvailable,
    whiteListDel, 
    whiteLists, 
    whiteInsert, 
    whiteUpdate, 
    whiteOneTableMultiStationListInfo, 
    whiteDetail,
    billStatusRemove, 
    listStation, 
    whiteGetFileBusiId,
    whiteGetFileListByFileBusiId } 
    from '@/api/account';
  import applyAddWhiteList from "./applyAddWhiteListd";
  import axios from "@/libs/api.request";
  import UploadFileModal from "./uploadFileModal";
  import CountryModal from "@/view/basedata/ammeter/countryModal";
  import {widthstyle} from "@/view/business/mssAccountbill/mssAccountbilldata";
  import {
    getClassification,
    getCountryByUserId,
    getCountrysdata,
    getUserByUserRole,
    getUserdata,
    editAmmeter,
    attchList,
  } from '@/api/basedata/ammeter.js'
  export default {
  name: "whiteList",
  components: {whiteListView, CountryModal, UploadFileModal, applyAddWhiteList, WorkFlowInfoComponet },
    data() {
            let renderW = (h, params) => {
              // console.log(params, "params55555555555555");
                let that = this;
                let text, type = "";
                let row = params.row;
                if (params.row.billStatus == 0 || params.row.billStatus == 5) {//草稿或者移除可以提交
                    text = "提交";
                    type = "primary";
                } else{
                  // if((params.row.meterCode != "") && (params.row.busiAlias != null)) {
                  //   text = "查看";
                  //   type = "success";
                  // }else{
                    text = "查看";
                    type = "success";
                  // }
                    
                }
                // if ((row.billStatus != 0 || row.billStatus != 5 || row.billStatus != "") && row.processinstId != null) {
                //     text = "查看";
                //     type = "success";
                // } else if (params.row.billStatus == 0 || params.row.billStatus == 5) {//草稿或者移除可以提交
                //     text = "提交";
                //     type = "primary";
                // } 
                // else if (params.row.billStatus == 1) {//草稿或者移除可以提交
                //     text = "查看";
                //     type = "success";
                // }
                if(type == ""){
                    return h("div", {}, text);
                }
                return h("Button", 
                {
                    props: {
                        type: type, size: "small"
                    }, 
                    on: {
                        click() {
                          // tijiao = 1;
                          if (params.row.billStatus == 0 || params.row.billStatus == 5) {
                                that.loading=true;
                                that.startFlow(params.row);
                            }
                            // else if ((row.billStatus!=0 || row.billStatus != 5 || row.billStatus != "") && row.procInstId != null) {
                            //     that.showFlow(params.row, params.row.processinstId);//查看流程图
                            // } 
                            // else if (params.row.billStatus == 1) {
                            //   that.showFlow(params.row, params.row.processinstId);
                            // }
                            else {
                              let p = {id : row.billId};

                              that.showFlow(params.row, params.row.procInstId);//查看流程图
                            }
                        }
                    }
                }, 
                text
                );
            };
      return {
        tijiao: 0,
        dataSC: [],
        tableName: "2",
        isRequireFlag:true,//局站是否必填
        ammeterTypeList: { 
          typeList: "1"
        },
        showApplyModelTL: false,
        selectV: [],
        spinShow: false,
        isAddAndModify: 0,
          loading: false,
        isLoading: false,
        isDisable: false,
        showApplyModel: false,  
        listTb: {
          loading: false,
          columns1: [
            { type: "selection", width: 30, align: "center", }, 
            // { title: "局站编码",key: "stationcode", minWidth:150, align: "center" ,},
            
            {
              title: '局站编码',
              width: 150,
              key: 'stationcode',
              render: (h, params) => {
                  return h('a', {
                      on: {
                          click: () => {
                              console.log(params, "单据编码 params555555555555");
                              this.busiViewClick(params.row)
                          }
                      }
                  }, params.row.stationcode)
              }
          },
            {title: '局站名称', key: 'stationname', align: 'center', minWidth: 60,},
            {title: '局站类型', key: 'stationtypeName', align: 'center', minWidth: 60,},
            {title: "所属分公司", key: "companyName", minWidth: 100, align: 'center'}, 
            {title: "所属部门", key: "countryName", minWidth: 110, align: 'center'},
            {title: "关联电表数量", key: "stationMetersCount", minWidth: 110, align: 'center'},
            {title: "单据状态", key: "billStatusName", width: 100, align: 'center'},
            {title: "创建时间", key: "createTime", width: 100,  isShow: true, align: 'center'
            },
            {
                title: "流程",
                fixed: 'right',
                key: "action",
                minWidth: 60,
                maxWidth:200,
                align: 'center',
                render: renderW
            },
          ],
          columns2: [ 
            { type: "selection", width: 30, align: "center", },
            
            // {
            // title: "电表/协议编号", key: "meterCode",  minWidth: 65, align: 'center'
            // },
            {
              title: '电表/协议编号',
              key: 'meterCode',
              width: 150,
              render: (h, params) => {
                  return h('a', {
                      on: {
                          click: () => {
                              console.log(params, "单据编码 params555555555555");
                              this.busiViewClick(params.row)
                          }
                      }
                  }, params.row.meterCode)
              }
          },
            {
                title: "项目名称", key: "projectname", minWidth: 90, align: 'center'
            }, 
            // {
            //     title: "关联局站", key: "stationName", minWidth: 100, align: 'center'
            // }, 
            {
                title: "所属分公司", key: "companyName", minWidth: 100, align: 'center'
            }, {
                title: "所属部门", key: "countryName", minWidth: 110, align: 'center'
            }, {
                title: "用电类型", key: "electrotypeName", minWidth: 100, className: "td-info", 
                isShow: true, align: 'center'
            }, 
            // {
            //     title: "对外结算类型", key: "dwjslx", minWidth: 100, className: "td-info", 
            //     isShow: true, align: 'center'
            // }, 
            //     {
            //     title: "产权归属", key: "propertyName", minWidth: 100, isShow: true, align: 'center'
            // }, {
            //     title: "状态", key: "statusName", width: 100,  isShow: true, align: 'center'
            // }, 
            {title: "关联局站数量", key: "stationTotal", minWidth: 110, align: 'center'},
            {title: "单据状态", key: "billStatusName", width: 100,  isShow: true, align: 'center'
            },
            {title: "创建时间", key: "createTime", width: 100,  isShow: true, align: 'center'
            },
            {
                title: "流程",
                fixed: 'right',
                key: "action",
                minWidth: 60,
                maxWidth:200,
                align: 'center',
                render: renderW
            },
            ],
          columns3: [ 
            { type: "selection", width: 30, align: "center", },
            
            // {
            // title: "电表/协议编号", key: "meterCode",  minWidth: 65, align: 'center'
            // },
            
            {
              title: '电表/协议编号',
              key: 'meterCode',
              width: 150,
              render: (h, params) => {
                  return h('a', {
                      on: {
                          click: () => {
                              console.log(params, "单据编码 params555555555555");
                              this.busiViewClick(params.row)
                          }
                      }
                  }, params.row.meterCode)
              }
          },
            {
                title: "项目名称", key: "projectname", minWidth: 90, align: 'center'
            }, 
            {
                title: "关联局站", key: "stationName", minWidth: 100, align: 'center'
            }, 
            {
                title: "所属分公司", key: "companyName", minWidth: 100, align: 'center'
            }, {
                title: "所属部门", key: "countryName", minWidth: 110, align: 'center'
            }, 
            // {
            //     title: "用电类型", key: "electrotypeName", minWidth: 100, className: "td-info", 
            //     isShow: true, align: 'center'
            // }, 
            {
                title: "对外结算类型", key: "directsupplyflagName", minWidth: 100, className: "td-info", 
                isShow: true, align: 'center'
            }, 
            //     {
            //     title: "产权归属", key: "propertyName", minWidth: 100, isShow: true, align: 'center'
            // }, {
            //     title: "状态", key: "statusName", width: 100,  isShow: true, align: 'center'
            // }, 
            {title: "单价", key: "price", minWidth: 110, align: 'center'},
            {title: "单据状态", key: "billStatusName", width: 100,  isShow: true, align: 'center'
            },
            {title: "创建时间", key: "createTime", width: 100,  isShow: true, align: 'center'
            },
              {
                  title: "流程",
                  fixed: 'right',
                  key: "action",
                  minWidth: 60,
                  maxWidth:200,
                  align: 'center',
                  render: renderW
              },
            ],
          data: []
        },
        insideData: [],
        pageTotal: 30,
        pageNum: 1,
        pageSize: 10,//当前页
        isAdmin: false,
        company: null,//用户默认公司
        country: null,//用户默认所属部门
        countryName: null,//用户默认所属部门
        exportloading: false,
        formItemWidth: widthstyle,
        queryParams: {
          type: "2",
          property: "",
          projectname: '',//项目名称
          meterCode: '',//电表编号
          stationname: '',//局站名称
          city: '',//地市
          district: '',//区县
          billStatus: '',//申请状态
          company: '',
          countryName: '',
          bi11statusName: '',
        },
        filterColl: true,
        companies: [],
        departments: [],
        multipleSelectionRow: [],
        showWorkFlow: false,
        workFlowParams: {},
        hisParams: {},
        exportColumns:[
            {title: '电表编号',key: 'ammetername'},
            {title: '项目名称',key: 'projectname'},
            {title: '关联局站名',key: 'stationName'},
            {title: '所属分公司',key: 'companyName'},
            {title: '所属部门', key: 'countryName'},
            {title: '状态',key: 'statusName'},
            {title: '单据状态',key: 'billStatusStr'},
            {title: '用电类型',key: 'electrotypename'},
            {title: '对外结算类型',key: 'directsupplyflagStr'},
            {title: '付费方式',key: 'paytypeStr'},
            {title: '电价性质',key: 'electrovalencenatureStr'},
            {title: '电表类型',key: 'ammetertypeStr'},
            {title: '产权归属',key: 'property'},
            // {title: '支局/分局',key: 'substation'},
            {title: '管理负责人',key: 'ammetermanager'},
            {title: '创建时间',key: 'createTime'},
            {title: '供电局电表户号或编号',key: 'supplybureauammetercode'},
            {title: '资源局站/房屋/站址编码：',key: 'stationaddresscode'},
            {title: '倍率：',key: 'magnification'},
            {title: '单价：',key: 'price'},
            {title: '分割比例：',key: 'percent'},
        ],
        export: {
          run: false, //是否正在执行导出
          data: "", //导出数据
          totalPage: 0, //一共多少页
          currentPage: 0, //当前多少页
          percent: 0,
          size: 10000000,
        },
        ruleValidateTypeList: {
          typeList: [
            // { required: true, message:"不能为空", trigger: 'change,blur' }
          ]
        },
        id1: "",
        ids: ",",
        valueTL: "",
      }
      
    },
    methods: {
      
      // isRowDisabled(row) {
      //   return true;
      // },
    ...mapMutations(["closeTag", "closeTagByName"]),
    busiViewClick(row) {
      console.log(row, "busiViewClick(row) {")
      let typeName = "";
      let id = "";
      if(this.tableName == 2) {
          typeName = "一站多表";
          id = row.id;
        }else if(this.tableName == 1) {
          typeName = "一表多站";
          id = row.billId;
        }else if(this.tableName == 3) {
          typeName = "单价合理性";
          id = row.billId;
        }else {
          typeName = "";
          id = "";
        }
      this.$refs.whiteListView.initAmmeter(id, typeName, "MODIFY");
    },
    getAccountMessagesQ() {
      this.getAccountMessages();
    },
    nextStep() {
      this.$refs.applyAddWhiteList.jz.insideData1 = [];
      this.$refs.applyAddWhiteList.jz.insideData2 = [];
      this.$refs.applyAddWhiteList.attachDataLa = [];
      this.$refs.applyAddWhiteList.showModel = true;
      // this.showApplyModel = true;
      // this.$router.push({
      //   path: 'applyAddWhiteListd', 
      //   query: {ammeterTypeList: this.ammeterTypeList},
      //   replace:true
      //             });
      // this.$router.push({
      //               name: "addAmmeter",
      //               query:{},
      //               replace:true
      //           })
      this.showApplyModelTL = false;
      this.$refs.applyAddWhiteList.ammeter.typeList = this.ammeterTypeList.typeList;
      whiteGetFileBusiId().then(res => {
        this.$refs.applyAddWhiteList.fileParam.busiId = res.data;
      })
    },
    setvalue(val) {
      this.queryParams.type = val;
      this.getAccountMessages();
    },
    selectChangeTL(val) {
      this.ammeterTypeList.typeList = val;
    },
    //查询局站
    queryStation(params2) {
                listStation(params2).then(res => {
                    this.station.total = res.data.total;
                    this.station.data = Object.assign([], res.data.rows)
                });
            },
    /*编辑*/
    editAmmeter() {
        if (this.multipleSelectionRow.length == 1) {
            let row = this.multipleSelectionRow[0];
            selectChangeAmmeter({id:row.id}).then(res => {
                //存在于代办中时，报出提示
                if (res.data.length > 0) {
                    this.$Modal.warning({title: "温馨提示",
                        content: "该电表已经存在换表电表【电表编号：" + res.data[0].ammetername + "，项目名称：" + res.data[0].projectname + "】,不允许再修改"
                    });
                } else {
                    isInTodoList(row.id, 1).then(res => {
                        //存在于代办中时，报出提示
                        let ownername = "";
                        if (res.data.length > 0) {
                            for (let i = 0; i < res.data.length; i++) {
                                ownername += res.data[i].ownername + ' ';
                            }
                            this.$Modal.warning({title: "温馨提示",
                                content: "该数据存在于" + ownername + "的流程代办中，处理后才可修改数据"
                            });
                        } else {
                            checkAcountByUpdate({id: row.id}).then(res => {
                                //修改数据前验证台账
                                if (res.data == -1) {
                                    this.$Modal.warning({title: "温馨提示", content: "该数据已填写台账或正在报账中，处理后才可修改数据"});
                                } else {
                                    this.closeTagByName({
                                        route: getHomeRoute(routers, "editAmmeter"),
                                    });
                                    this.$router.push({
                                        name: "editAmmeter",
                                        query: {id: row.id},
                                        replace: true
                                    })
                                }
                            })
                        }
                    }).catch(err => {
                        console.log(err);
                    });
                }
            });
        } else {
            this.$Message.info("请选择其中一行");
        }

    },
    beforeLoadData(data) {
      let cols=[],keys=[]
      for (let i = 0; i < this.exportColumns.length; i++) {
          cols.push(this.exportColumns[i].title)
          keys.push(this.exportColumns[i].key)
      }
      const params = {
        title: cols,
        key: keys,
        data: data,
        autoWidth: true,
        filename: '白名单数据导出'
      };
      this.queryParams.pageSize = this.pageSize;
      excel.export_array_to_excel(params);
      this.$Spin.hide();
      return
    },
    exportLoading(){
        this.$Spin.show({
            render: (h) => {
                return h('div', [
                    h('Progress', {
                        style: {
                            width: '800px'
                        },
                    }),
                    h('div', '导出中，请勿刷新页面......')
                ])
            }
        });
    },
    exportCsv(name) {
        // this.exportLoading();
        this.export.run = true;
        let params = {
        type: this.queryParams.type,
        size: this.pageSize, 
        current: this.pageNum
      };
      let url;
        if (name === 'current') {
            // this.beforeLoadData(this.setValueByForEach(this.ammeter.data))
            // return;
            params.isExportAll = false;
        } else if (name === 'all') {
            params.isExportAll = true;
        }
        if(this.tableName == "1") {
          url = "/stationReportWhitelist/exportOneWatchHasManyStationsExport"
        }else if(this.tableName == "2") {
          url = "/stationReportWhitelistBill/export"
        }else if(this.tableName == "3") {
          url = "/stationReportWhitelist/exportRationalityOfUnitPrice"
        }else{
          url = ""
        }
        let req = {
            url : url,
            method : "get",
            params : params
        };
        this.listTb.loading = true;
        axios.request(req).then(res => {
            console.log(res, "res");
            this.listTb.loading = false;
            if (res.data) {
                let array = res.data.rows;
                this.beforeLoadData(this.setValueByForEach(array));
            }
        }).catch(err => {
            console.log(err);
        });
    },
    setValueByForEach(array){
        array.forEach(function (item) {
            item.billStatus = btext("ammeterBillStatus", item.category,'typeCode','typeName');
            item.categoryStr = btext("ammeterCategory", item.category,'typeCode','typeName');
            item.packagetypeStr = btext("packageType", item.packagetype,'typeCode','typeName');
            item.payperiodStr = btext("payPeriod", item.payperiod,'typeCode','typeName');
            item.paytypeStr = btext("payType", item.paytype,'typeCode','typeName');
            item.electronatureStr = btext("electroNature", item.electronature,'typeCode','typeName');
            item.electrovalencenatureStr = btext("electrovalenceNature", item.electrovalencenature,'typeCode','typeName');
            item.electrotypeStr = btext("electroType", item.electrotype,'typeCode','typeName');
            item.statusStr = btext("status", item.status,'typeCode','typeName');
            item.propertyStr = btext("property", item.property,'typeCode','typeName');
            item.ammetertypeStr = btext("ammeterType", item.ammetertype,'typeCode','typeName');
            item.stationstatusStr = btext("stationStatus", item.stationstatus,'typeCode','typeName');
            item.stationtypeStr = btext("BUR_STAND_TYPE", item.stationtype,'typeCode','typeName');
            item.ammeteruseStr = btext("ammeterUse", item.ammeteruse,'typeCode','typeName');
            item.directsupplyflagStr = btext("directSupplyFlag", item.directsupplyflag,'typeCode','typeName');
            item.billStatusStr = btext("basicBillStatus", item.billStatus,'typeCode','typeName');
            item.supplybureauammetercode;
            item.magnification
        });
        return array;
    },
    //选择所属部门开始
    chooseResponseCenter() {
        if(this.queryParams.company == null || this.queryParams.company == "" ){
            this.$Message.info("请先选择分公司");return;
        }
        this.$refs.countryModal.choose(this.queryParams.company);//所属部门
    },
    getDataFromModal(data) {
        this.queryParams.country = data.id;
        this.queryParams.countryName = data.name;
        //选择所属部门结束
    },
    getUserData(){
        let that = this;
        getUserdata().then(res => {//当前登录用户所在公司和所属部门
            let companies = that.companies;
            if(res.data.companies != null && res.data.companies.length != 0){
                if(res.data.companies[0].id != "2600000000"){
                    companies = res.data.companies;;
                }
            }
            that.company = companies[0].id;
            that.queryParams.company = companies[0].id;

            let departments = that.departments;
            if(res.data.departments != null && res.data.departments.length != 0){
                if(res.data.companies[0].id != "2600000000"){
                    departments = res.data.departments;
                }
            }
            that.country = departments[0].id;
            that.countryName = departments[0].name;
            that.queryParams.country = Number(departments[0].id);
            that.queryParams.countryName = departments[0].name;
        });
    },
    showFlow(row, procInstId) {
              this.showWorkFlow = true;
              this.hisParams = {
                  busiId: this.tableName == "2"?row.id:this.tableName == "3"?row.billId:this.tableName == "1"?row.billId:"",
                  busiType: row.busiAlias,
                  procInstId: procInstId
              }
    },
    warn(){
        this.$Modal.warning({title:"温馨提示",content: "保存后的数据要提交审批才能生效！"});
    },
    doWorkFlow(data) { //流程回调
        console.log(data, "data777777777777777777");
        this.loading = false;
        // this.$refs.ammeterTable.query();
        // this.query(this.queryParams);
        this.closeTag({route: this.$route});
        if(data==0){
            this.warn();
        }
    },
    // query(params) {
    //     this.listTb.loading = true;
    //     listAmmeter(params).then(res => {
    //         this.listTb.loading = false;
    //         this.listTb.total = res.data.total
    //         this.listTb.data = Object.assign([], res.data.rows)
    //     });
    // },
      startFlowSubmit(row){
        var typeName = "";
        if(this.tableName == 2) {
          typeName = "一站多表";
        }else if(this.tableName == 1) {
          typeName = "一表多站";
        }else if(this.tableName == 3) {
          typeName = "单价合理性";
        }else {
          typeName = "";
        }
        row.typeName = typeName;
        // row.tableName = this.tableName;
                let busiAlias = "ADD_WHITELIST";
                let busiTitle = "新增电表("+row.typeName+")审批";
                if(row.billStatus === 3){
                    busiAlias = "MODIFY_WHITELIST";
                    busiTitle = "修改电表("+row.typeName+")审批";
                }
                // if(row.ischangeammeter == 1 && row.billStatus<2){
                //     busiAlias = "AMM_SWITCH_AMM";
                //     busiTitle = "电表换表("+row.projectname+")审批";
                // }
                console.log(row.typeName, "row.typeName");
                // let busiId = this.typeName == "1"?row.billId
                // :this.typeName == "2"?row.id
                // :this.typeName == "3"?row.billId
                // :"";
                // debugger;
                this.workFlowParams = {
                    busiId: this.tableName  == "1"?row.billId
                            :this.tableName  == "2"?row.id
                            :this.tableName  == "3"?row.billId
                            :"",
                    // busiId: row.id,
                    busiAlias: busiAlias,
                    busiTitle: busiTitle
                }
                console.log(this.workFlowParams, "this.workFlowParams");
                let that = this;
                this.$Modal.confirm({
                    title: '白名单提交流程',
                    content: '<p>是否提交白名单 (' + row.typeName + ') 到流程</p>',
                    onOk: () => {
                        that.loading = true;
                        setTimeout(function () {
                            that.$refs.clwfbtn.onClick();
                        }, 300);
                    },onCancel: () => {
                        that.loading = false;
                    }
                });
            },
      startFlow(row) {
        console.log(row, "row");
        var id;
        // if(this.tijiao == 1) {
           id= this.tableName == "1"?row.billId
          :this.tableName == "2"?row.id
          :this.tableName == "3"?row.billId
          :""
        // }else {
        //   id = row.id;
        // }
          let that = this;
          isInTodoList(id,1).then(res => {
              //存在于代办中时，报出提示
              let ownername = "";
              if (res.data.length > 0) {
                  for (let i = 0; i < res.data.length; i++) {
                      ownername += res.data[i].ownername + ' ';
                  }
                  that.$Modal.warning({title:"温馨提示",content: "该数据存在于" + ownername + "的流程代办中，处理后才可继续提交流程"});
                  that.loading = false;
              }else if(row.billStatus == 3 || row.billStatus == 4){
                  checkStartFlow({id:row.id}).then(res1 => {
                      /*提交流程验证用户是否有数据需要提交*/
                      that.loading = false;
                      if (res1.data.id == null || res1.data.id == undefined) {
                          that.$Modal.warning({title:"温馨提示",content: "您没有可提交的数据"});
                      }else{
                          that.startFlowSubmit(row);
                      }
                  });
              }else{
                  that.loading = false;
                  that.startFlowSubmit(row);
              }
          });
      },
      OK(type){
        let arr = [];
        // this.selectV = this.$refs.applyAddWhiteList.selectValue;
        console.log(this.selectV, "this.selectV33333333333333333333333333");
            // if (type == 1) {
            //   this.isLoading = 1;
            // } else {
            //   this.isLoading = 0;
            // }

            this.$refs.applyAddWhiteList.ammeter.typeList.forEach(i => {
              arr.push({
                "whitelistType": i
              })
            })
            console.log(arr, "arr66666666666666666666666666666666");
            // if (this.loading == true) {
            //   return;
            // }
            // this.loading = true;
            let flag = false;
            var a, b, c;
            // this.ammeter.electricTypes = this.electro.data;
            this.$refs.applyAddWhiteList.$refs.ammeter.validate((valid) => {
              if (valid) {
                flag = true;
              }
            });
            console.log(flag, "flag");
            if (flag) {
              if(this.isAddAndModify == 2) {//修改
                if(this.$refs.applyAddWhiteList.selectValue.length != 0) {
                  a = this.$refs.applyAddWhiteList.selectValue[0].ammetername;
                  b = this.$refs.applyAddWhiteList.selectValue[0].directsupplyflag;
                }else {
                  a = this.selectV[0].meterCode;
                  b = this.selectV[0].directsupplyflag;
                }
              }else if(this.isAddAndModify == 1) {//新增
                if(this.$refs.applyAddWhiteList.selectValue.length != 0) {
                  a = this.$refs.applyAddWhiteList.selectValue[0].ammetername;
                  b = this.$refs.applyAddWhiteList.selectValue[0].directsupplyflag;
                }else {
                  a = "";
                  b = "";
                }
              }//新增
              let data = 
                {"meterCode": a, //电表表号
                "dwjslx": b == 0?"直供电":b == 0?"转供电":"", //对外结算类型
                // "dxsfqz": "是", //电信是否起租
                "sftydxxg": "未上传附件；", //附件-文件地址，多个“，”隔开
                // "type": selectV.type, //0-白名单 1-一表对多站 2-一站对多表
                // "createTime": selectV.createTime, //录入时间
                "typeList": arr,
                "applyArgument": this.$refs.applyAddWhiteList.ammeter.applyReason,
                };
// {
// 	"city": "宜宾-测试-2",
// 	"district": "宜宾县",
// 	"meterCode": "666666",
// 	"payAccountNum": null,
// 	"dwjslx": "直供",
// 	"zgdxydj": 0,
// 	"xyCreateTime": null,
// 	"xyEndTime": null,
// 	"glzz": "2",
// 	"zzlx": "标准基站",
// 	"zbzzzbm": "511502908000001058",
// 	"zbzzzmc": "宜都花园",
// 	"dxsfqz": "是",
// 	"dbbzzzbm": "511502908000000198",
// 	"dbbzzzyzbzzzcf": "移动,电信,联通",
// 	"gxyysmc": "爱菲尔酒店",
// 	"dbbzzzmc": "四川省宜宾市翠屏区广场东路",
// 	"zzdz": "",
// 	"fj": "",
// 	"sftsfgs": "",
// 	"fgsshr": "",
// 	"sftssgs": "",
// 	"sgsshr": "",
// 	"sftsdx": "",
// 	"tsdxr": "",
// 	"tsdxsj": "",
// 	"dxjh": "",
// 	"jhsj": "",
// 	"sfdxqqxg": "",
// 	"sftydxxg": "未上传附件；",
// 	"xtjy": null,
// 	"type": 2,
// 	"createTime": "2024-04-22 14:20:12",
// 	"delFlag": null,
// 	"typeList": [
// 		{
// 			"whitelistType": "1"
// 		}
// 	]
// }
            console.log(data, "data");
             
            if(this.isAddAndModify == 1) {
              whiteInsert(data)
              .then(res => {
                // debugger
                console.log(res, "白名单新增res");
                // if(res.code == 0) {
                  
                // this.$Message.success(res.message);
                // this.loading = false;
                // this.id1 = res.data.id;
                // // }
                // else 
                if(res.data.code == 500) {
                this.loading = false;
                // this.$Message.error(res.data.msg);
                }else{
                this.$Message.success("保存成功");
                this.loading = false;
                this.id1 = res.data.id;
                }
                // else {
                // this.$Message.error("新增失败");
                // }
              //     if(res.data.code == 0) {
                // this.$Message.success("保存成功");
              // }else {
              // }
              })
            
              // .catch(err => {
                
              //   console.log(err, "err5555555555");
              // })
            }else if(this.isAddAndModify == 2) {
              data.id = this.ids;
                whiteUpdate(data)
                .then(res => {
                  this.loading = false;
                  // if(res.data.code == 0) {
                    console.log(res, "白名单修改res");
                    if(res.data.code == 500) {
                    this.loading = false;
                    // this.$Message.error(res.data.msg);
                    }else{
                    this.$Message.success("保存成功");
                    this.loading = false;
                    this.id1 = res.data.id;
                    }
                  // this.$Message.success("保存成功");
                  // this.id1 = res.data.id;
                  // }else {
                  //   this.$Message.error("修改失败");
                  // }
                  
              })
              // .catch(err => {
                
              //   console.log(err, "err5555555555");
              // })
              }
            } else {
              this.$Message.error("验证没通过");
              this.loading = false;
            }
          },

      onModalOK(type) {
        this.isDisable=true
        this.OK(type);
      },
      cancelWL() {
        this.showApplyModelTL = false;
      }, 
      saveWL() {
        this.showApplyModel = false;

      }, 
      submitWL() {
        this.showApplyModel = false;

      },
      searchList() {
      this.pageNum = 1;
      
      this.getAccountMessages();
    },
    onResetHandle() {
      this.queryParams= {
        projectname: '',//项目名称
        meterCode: '',//电表编号
        stationname: '',//局站名称
        city: '',//地市
        district: '',//区县
        billStatus: '1',//状态
        company: '',
        countryName: '',
        bi11statusName: '',
      };
    },
    //新增白名单
    apply() {
      this.isAddAndModify = 1;
      this.$refs.applyAddWhiteList.ammeter = {};
      console.log(this.queryParams, "queryParams");
      this.showApplyModelTL = true;
    },
    modify() {
      this.$refs.applyAddWhiteList.attachDataL.length = 0;
      this.isAddAndModify = 2;
      let dataXG;
      this.ammeterTypeList.typeList = this.tableName;
      this.$refs.applyAddWhiteList.ammeter = {};
      if(this.tableName == "1") {
        dataXG = this.$refs.listTable1.getSelection();
      }else if(this.tableName == "2") {
        dataXG = this.$refs.listTable2.getSelection();
      }else if(this.tableName == "3") {
        dataXG = this.$refs.listTable3.getSelection();
      }else {
        dataXG = [];
      }
      console.log(dataXG, "modify(dataXG) {");
      if(dataXG.length > 1) {
        this.errorTips("只能选择一条数据修改");
      }else if(dataXG.length == 0) {
        this.errorTips("请选择一条数据修改");
      }else {
      if(dataXG[0].billStatus == "0" || dataXG[0].billStatus == "5") {
        this.$refs.applyAddWhiteList.showModel = true;
          let billId = this.tableName == 1?dataXG[0].billId
            :this.tableName == 2?dataXG[0].id
            :this.tableName == 3?dataXG[0].billId
            :"";
        whiteDetail(billId).then(res => {
          console.log(res, "whiteDetail(billId) ");
          this.$refs.applyAddWhiteList.fileParam.busiId = res.data.fileBusiId;
          this.$refs.applyAddWhiteList.ammeter.applyReason = res.data.applyArgument;
          this.$refs.applyAddWhiteList.ammeter.stationcode = res.data.stationcode;
          this.$refs.applyAddWhiteList.ammeter.stationtypename = res.data.stationtypeName;
          this.$refs.applyAddWhiteList.ammeter.stationName = 
          // res.data.meterList[0].stationName;
          this.tableName == "2"?res.data.stationname:
          this.tableName == "1"?res.data.meterList[0].stationName:
          this.tableName == "3"?res.data.meterList[0].stationName:"";
          this.$refs.applyAddWhiteList.ammeter.typeList = res.data.whitelistType;
          this.$refs.applyAddWhiteList.ammeter.projectname = 
          // res.data.meterList[0].projectname;
          this.tableName == "2"?res.data.projectname:
          this.tableName == "1"?res.data.meterList[0].projectname:
          this.tableName == "3"?res.data.meterList[0].projectname:"-";
          this.$refs.applyAddWhiteList.ammeter.ammetername =  
          // res.data.meterList[0].ammetername;
          this.tableName == "2"?res.data.ammetername:
          this.tableName == "1"?res.data.meterList[0].ammetername:
          this.tableName == "3"?res.data.meterList[0].ammetername:"-";
          this.$refs.applyAddWhiteList.ammeter.ammeternameId =  
          // res.data.meterList[0].id;
          this.tableName == "1"?res.data.ammetername:
          this.tableName == "1"?res.data.meterList[0].id:
          this.tableName == "3"?res.data.meterList[0].id:"-";
          this.$refs.applyAddWhiteList.ammeter.companyName = 
          // res.data.companyName;
          this.tableName == "2"?res.data.companyName:
          this.tableName == "1"?res.data.meterList[0].companyName:
          this.tableName == "3"?res.data.meterList[0].companyName:"";
          this.$refs.applyAddWhiteList.ammeter.countryName = 
          this.tableName == "2"?res.data.countryName:
          this.tableName == "1"?res.data.meterList[0].countryName:
          this.tableName == "3"?res.data.meterList[0].countryName:"";
          // res.data.countryName;
          this.$refs.applyAddWhiteList.ammeter.electrotypename = 
          // res.data.meterList[0].electrotypeName;
          this.tableName == "2"?res.data.electrotypename:
          this.tableName == "1"?res.data.meterList[0].electrotypeName:
          this.tableName == "3"?res.data.meterList[0].electrotypeName:"-";
          this.$refs.applyAddWhiteList.ammeter.directsupplyflag = 
          // res.data.meterList[0].directsupplyflagName;
          this.tableName == "2"?res.data.directsupplyflagName:
          this.tableName == "1"?res.data.meterList[0].directsupplyflagName:
          this.tableName == "3"?res.data.meterList[0].directsupplyflagName:"-";
          this.$refs.applyAddWhiteList.ammeter.id = res.data.id;
          this.$refs.applyAddWhiteList.ammeter.billStatus = res.data.billStatus; 
          this.$refs.applyAddWhiteList.ammeter.procInstId = res.data.procInstId; 
          this.$refs.applyAddWhiteList.ammeter.price = 
          // res.data.meterList[0].price;
          this.tableName == "2"?res.data.price:
          this.tableName == "1"?res.data.meterList[0].price:
          this.tableName == "3"?res.data.meterList[0].price:"-";
          if(this.tableName == "1") {
            whiteOneTableMultiStationListInfo({stationCodes: res.data.meterList[0].stationCodes})
              .then(res => {
              res.data.forEach((item, index) => {
                item.index = index + 1;
                item.checked = true;
              })
          setTimeout(() => {
            this.$refs.applyAddWhiteList.$refs.listTable2.selectAll(true);
          }, 1000)
              this.$refs.applyAddWhiteList.jz.insideData2 = res.data;
          })
          }
          
          res.data.meterList.forEach((item, index) => {
            item.index = index + 1;
            item.checked = true;
          })
            res.data.meterList.forEach(item => {
                if(item.category == "1") {
                    item.ammetername1 = item.ammetername;
                }else {
                    item.ammetername1 = item.protocolname;
                } 
            })
            
          this.$refs.applyAddWhiteList.jz.insideData1 = res.data.meterList;
          setTimeout(() => {
            this.$refs.applyAddWhiteList.$refs.listTable1.selectAll(true);
          }, 1000)

          // 获取上传文件列表详情
          whiteGetFileListByFileBusiId(res.data.fileBusiId).then(res => {
              this.$refs.applyAddWhiteList.attachDataLa = res.data;
          });
        })
          this.ids = 
          this.tableName == "2"?dataXG[0].id:
          this.tableName == "1"?dataXG[0].billId:
          this.tableName == "3"?dataXG[0].billId:"";
    }else {
        this.errorTips("仅有单据状态为草稿或移除时才能修改");
      }



      }
      
    }, 
    
    //验证错误弹出提示框
    errorTips(str){
        this.$Notice.error({
            title: '提示',
            desc: str,
            duration: 10
        });
    },
    //删除白名单
    remove() {
      if(this.tableName == "1") {
        this.dataSC = this.$refs.listTable1.getSelection();
      }else if(this.tableName == "2") {
        this.dataSC = this.$refs.listTable2.getSelection();
      }else if(this.tableName == "3") {
        this.dataSC = this.$refs.listTable3.getSelection();
      }else {
        this.dataSC = [];
      }
      
      console.log(this.dataSC, "remove() {this.dataSC");
        
      if(this.dataSC.length > 1) {
        this.errorTips("只能选择一条数据删除");
      }else if(this.dataSC.length == 0) {
        this.errorTips("请选择一条数据删除");
      }else { 
      if(this.dataSC[0].billStatus == "0" || this.dataSC[0].billStatus == "5") {

        this.$Modal.confirm({
        title: "温馨提示",
        content: "<p>确定删除吗？</p>",
        onOk: () => {
          this.del1();
      
        },
        onCancel: () => {},
      });
       
      }else {
        this.errorTips("仅有单据状态为草稿或移除时才能删除");
      }
    }
    }, 
    del1() {
      let billId = this.tableName=="1"?this.dataSC[0].billId
        :this.tableName=="2"?this.dataSC[0].id
        :this.tableName=="3"?this.dataSC[0].billId
        :"";
      whiteListDel(billId)
      .then(res => {
      this.listTb.loading = false;
          if(res.data.code == 0) {
            this.$Message.success(res.data.msg);
            this.getAccountMessages();
          }else {
            this.$Message.error("操作失败");
          }
        })
    },
    //移除白名单
    deleteAll() {
      let dataYC;
      if(this.tableName == "1") {
        dataYC = this.$refs.listTable1.getSelection();
      }else if(this.tableName == "2") {
        dataYC = this.$refs.listTable2.getSelection();
      }else if(this.tableName == "3") {
        dataYC = this.$refs.listTable3.getSelection();
      }else {
        dataYC = [];
      }
      if(dataYC.length > 1) {
        this.errorTips("只能选择一条数据移除");
      }else if(dataYC.length == 0) {
        this.errorTips("请选择一条数据移除");
      }else { 
      if(dataYC[0].billStatus == "2") {

        this.$Modal.confirm({
        title: "温馨提示",
        content: "<p>是否确认将该电表移除出白名单？</p>",
        onOk: () => {
      this.listTb.loading = true;
      let billId2 = this.tableName=="1"?dataYC[0].billId
        :this.tableName=="2"?dataYC[0].id
        :this.tableName=="3"?dataYC[0].billId
        :"";
      whiteListNotAvailable(billId2)
      .then(res => {
      this.listTb.loading = false;
        console.log(res, "billStatusRemove res");
          if(res.data.code == 0) {
            this.$Message.success(res.data.msg);
            this.getAccountMessages();
          }else {
            this.$Message.error("操作失败");
          }
        })
        },
        onCancel: () => {},
      });
    }else {
        this.errorTips("仅有单据状态为已完成时才能移除");
      }
    }
      

    },
    //翻页
    handlePage(value) {
      this.pageNum = value;
      this.getAccountMessages();
    },
    //改变表格可显示数据数量
    handlePageSize(value) {
      this.pageSize = value;
      this.getAccountMessages();
    },
    selectChange() {
      let that = this;
      if (that.queryParams.company != undefined) {
        if (that.queryParams.company == "") {
          that.queryParams.country = "";
          that.queryParams.countryName = null;
        } else {
          getCountryByUserId(that.queryParams.company).then(res => {
            if (res.data.departments.length != 0) {
              that.queryParams.country = res.data.departments[0].id;
              that.queryParams.countryName = res.data.departments[0].name;
            }
          });
        }
      }
    },
    //白名单列表查询
    getAccountMessages() {
      this.listTb.loading = true;
        if(this.queryParams.countryName == ""){
            this.queryParams.country = "";
        }
      let params = {
        meterCode: this.queryParams.meterCode,
        projectname: this.queryParams.projectname,  
        stationname: this.queryParams.stationname,
        company: this.queryParams.company,
        country: this.queryParams.country,
        countryName: this.queryParams.countryName,
        billStatus: this.queryParams.billStatus,
        type: this.queryParams.type,
        size: this.pageSize, 
        current: this.pageNum
          };
        console.log(params, "getAccountMessages() {params");
        if(this.tableName == 2) {
          whiteList(params).then(res => {
          this.listTb.loading = false;
            console.log(res, "getAccountMessages() {res");
            res.data.rows.forEach(item => {
              if(item.property == 1) {
                item.propertyName = "自留";
              }else if(item.property == 2) {
                item.propertyName = "铁塔";
              }else{
                item.propertyName = "";
              }
            })
            this.pageTotal = res.data.total;
            this.insideData = res.data.rows;
          })
        }else if(this.tableName == 1 || this.tableName == 3) {
          whiteLists(params).then(res => {
          this.listTb.loading = false;
            console.log(res, "whiteLists(params).then(res => {() {res");
            res.data.rows.forEach(item => {
              if(item.property == 1) {
                item.propertyName = "自留";
              }else if(item.property == 2) {
                item.propertyName = "铁塔";
              }else{
                item.propertyName = "";
              }
            })
            this.pageTotal = res.data.total;
            this.insideData = res.data.rows;
          })
        }else{
            this.insideData = [];
        }
        
    },
    },
    mounted() {
      let that = this;
      getUserByUserRole().then(res => {//根据权限获取分公司
      that.companies = res.data.companies;
      if (res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true) {
        that.isAdmin = true;
      }
      getCountrysdata({orgCode: res.data.companies[0].id}).then(res => {//根据权限获取所属部门
        that.departments = res.data;
        that.getUserData();
        setTimeout(() => {
          this.getAccountMessages();
        },1000)
      });
    });
    
    },
  }
</script>
