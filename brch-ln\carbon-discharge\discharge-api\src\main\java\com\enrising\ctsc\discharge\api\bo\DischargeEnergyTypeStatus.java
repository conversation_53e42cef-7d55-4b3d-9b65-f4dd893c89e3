package com.enrising.ctsc.discharge.api.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 碳排放能源类型表
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-1-4
 */
@Data
public class DischargeEnergyTypeStatus implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
	@NotNull(message = "id不能为空")
		private Long id;

	/**
	 * 启用状态
	 */
	@NotBlank(message = "状态不能为空")
		private String status;

}
