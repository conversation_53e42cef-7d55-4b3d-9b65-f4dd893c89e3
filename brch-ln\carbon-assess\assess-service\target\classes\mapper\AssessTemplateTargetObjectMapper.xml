<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.assess.mapper.AssessTemplateTargetObjectMapper">
	<resultMap id="BaseResultMap" type="com.enrising.ctsc.assess.api.entity.AssessTemplateTargetObject">
		<result column="id" property="id" />
		<result column="template_target_id" property="templateTargetId" />
		<result column="company_id" property="companyId" />
		<result column="dept_id" property="deptId" />
		<result column="object_type" property="objectType" />
	</resultMap>
	<!-- 表字段 -->
	<sql id="baseColumns">
		t.id,
		t.template_target_id,
		t.company_id,
		t.dept_id,
		t.object_type
	</sql>
	<select id="getTaskInfoByOrgId" resultType="com.enrising.ctsc.assess.api.vo.AssessTemplateTargetObjectVo">
		SELECT
		    A.ID as template_object_id,
			A.template_target_id,
			A.object_type,
			A.dept_id,
			A.company_id,
			b.template_id,
			b.assess_method,
			CASE
			WHEN b.assess_method = '1' THEN
			'1'
			ELSE'3'
			END AS isReport,
			b.secondary_target_id,
			C.target_name,
			C.score,
		    c.formula,
		    c.assess_period,
		    d.target_type,
			d.target_category,
			COALESCE (G.assess_score, 0) as assess_score,
			e.org_name as company_name
		FROM assess_template_target_object A
		    LEFT JOIN assess_template_target b ON A.template_target_id = b.id
			LEFT JOIN assess_target_secondary C ON b.secondary_target_id = C.ID
		    LEFT JOIN assess_target d ON C.primary_target_id = d.id
			LEFT JOIN (
				SELECT
				n.template_object_id,
				COALESCE(sum(n.rule_score),0) as assess_score
				FROM assess_template_target_object M
				    LEFT JOIN assess_task_report n ON n.template_object_id = M.id
					LEFT JOIN assess_template_target P ON M.template_target_id = P.ID
				WHERE
					P.template_id = #{ bo.templateId }
					<if test="bo.companyId != null">
						AND M.company_id = #{bo.companyId}
					</if>
					and n.report_status = '1'
					GROUP BY n.template_object_id
			) G ON G.template_object_id = A.ID
			LEFT JOIN rmp.sys_organizations e on e.id = A.company_id
		WHERE
			b.template_id = #{ bo.templateId }
			<if test="bo.companyId != null">
				AND A.company_id = #{bo.companyId}
			</if>
			<if test="bo.targetCategory!=null and bo.targetCategory!=''">
				and d.target_category = #{bo.targetCategory}
			</if>
			<if test="bo.assessMethod!=null and bo.assessMethod!=''">
				and b.assess_method = #{bo.assessMethod}
			</if>
			<if test="bo.keyWord!=null and bo.keyWord!=''">
				and C.target_name like concat('%',#{bo.keyWord},'%')
			</if>
		ORDER By company_id,secondary_target_id
	</select>
	<select id="getFileByIds" resultType="com.enrising.ctsc.assess.api.entity.Attachment">
		select
		 *
		 from attachments
		 where
		 id in
		 <foreach collection="attachmentIds" open="(" separator="," item="id" close=")">
			 #{id}
		 </foreach>
	</select>
	<select id="getTaskInfoByTemplateId" resultType="com.enrising.ctsc.assess.api.vo.AssessTemplateTargetObjectVo">
		SELECT G
			.ID AS templateObjectId,
			b.ID AS secondaryTargetId,
			b.formula,
			A.target_category,
			A.target_type,
			b.target_name,
			b.score,
		    b.assess_period,
			C.assess_method,
			G.dept_id,
			G.company_id,
			G.object_type,
			G.template_id,
			CASE
			WHEN C.assess_method = '1' THEN
			'1'
			ELSE'3'
			END AS isReport
		FROM
			assess_target_secondary b
		LEFT JOIN assess_target A ON b.primary_target_id = A.
		ID LEFT JOIN assess_template_target C ON b.ID = C.secondary_target_id
		LEFT JOIN (
		SELECT M
		.ID,
		P.secondary_target_id,
		P.template_id,
		M.company_id,
		M.dept_id,
		M.object_type
		FROM
		assess_template_target_object
		M
		LEFT JOIN assess_template_target P ON M.template_target_id = P.ID
		WHERE
		P.template_id = #{ bo.templateId }
		<if test="bo.companyId != null">
			AND M.company_id = #{ bo.companyId }
		</if>
		<if test="bo.deptId != null">
			and M.dept_id = #{bo.deptId}
		</if>
		) G ON G.secondary_target_id = b.ID
		WHERE
		b.id IN (
		SELECT A.secondary_target_id
		FROM
			assess_template_target A
		    LEFT JOIN assess_template_target_object b ON A.id = b.template_target_id
		WHERE
		A.template_id = #{ bo.templateId }
		<if test="bo.companyId != null">
			AND b.company_id = #{ bo.companyId }
		</if>
		<if test="bo.deptId != null">
			and b.dept_id = #{bo.deptId}
		</if>
		)
		AND C.template_id = #{ bo.templateId }
		AND A.del_flag = '0'
		<if test="bo.targetCategory!=null and bo.targetCategory!=''">
			and a.target_category = #{bo.targetCategory}
		</if>
		<if test="bo.assessMethod!=null and bo.assessMethod!=''">
			and c.assess_method = #{bo.assessMethod}
		</if>
		<if test="bo.keyWord!=null and bo.keyWord!=''">
			and b.target_name like concat('%',#{bo.keyWord},'%')
			or A.target_type like concat('%',#{bo.keyWord},'%')
		</if>
		order by A.create_time desc
	</select>

	<select id="getAllTaskByTemplateId"
			resultType="com.enrising.ctsc.assess.api.vo.AssessTemplateTargetObjectVo">
		SELECT
			G.id as templateObjectId,
			b.id AS secondaryTargetId,
			b.formula,
			A.target_category,
			a.target_type,
			b.target_name,
			b.score,
			b.assess_period,
			C.assess_method,
			G.dept_id,
			G.company_id,
			G.object_type,
			G.template_id,
			case
			WHEN C.assess_method = '1' then '1'
			else '3'
			end as isReport
		FROM
		assess_target_secondary b
		LEFT JOIN assess_target a  ON b.primary_target_id = A.
		ID LEFT JOIN assess_template_target C ON b.ID = C.secondary_target_id
		LEFT JOIN (
			SELECT M
			.ID,
			p.secondary_target_id,
			p.template_id,
			m.company_id,
			M.dept_id,
			M.object_type
		FROM
			assess_template_target_object m
		LEFT JOIN assess_template_target p on m.template_target_id = p.id
			WHERE
			p.template_id = #{ bo.templateId }
		) G ON G.secondary_target_id = b.ID
		WHERE
		b.id IN ( SELECT
		              a.secondary_target_id
		          FROM assess_template_target A
		              LEFT JOIN assess_template_target_object b ON A.id = b.template_target_id
		WHERE a.template_id = #{ bo.templateId } )
		and c.template_id = #{ bo.templateId }
		AND A.del_flag = '0'
		<if test="bo.targetCategory!=null and bo.targetCategory!=''">
			and a.target_category = #{bo.targetCategory}
		</if>
		<if test="bo.assessMethod!=null and bo.assessMethod!=''">
			and c.assess_method = #{bo.assessMethod}
		</if>
		<if test="bo.keyWord!=null and bo.keyWord!=''">
			and b.target_name like concat('%',#{bo.keyWord},'%')
			or A.target_type like concat('%',#{bo.keyWord},'%')
		</if>
		order by A.create_time desc
	</select>

	<select id="getAssessScoreByTemplateId"
			resultType="com.enrising.ctsc.assess.api.vo.AssessTemplateTargetObjectVo">
		SELECT
			d.company_id,
		    d.assess_score,
			e.org_name as companyName
		FROM

		(
		  SELECT b.company_id,
			SUM(c.rule_score) AS assess_score
		FROM assess_template_target a
		 LEFT join assess_template_target_object b on  b.template_target_id = a.id
			LEFT join assess_task_report c ON b.id = c.template_object_id
		WHERE
			A.template_id = #{bo.templateId}
		<if test="bo.companyId!=null">
			and b.company_id = #{bo.companyId}
		</if>
		 GROUP BY	b.company_id
		) d
        LEFT JOIN rmp.sys_organizations e on d.company_id = e.id
        order by d.assess_score desc
	</select>
	<select id="getTemplateById" resultType="com.enrising.ctsc.assess.api.vo.AssessRankRateVo">
		SELECT
			 content,
			 period,
			template_name,
			template_start_time
		FROM assess_template
		where id = #{bo.templateId}
	</select>
	<select id="getRankRateInfoById" resultType="com.enrising.ctsc.assess.api.vo.RankRateInfoVo">
		SELECT
		   m.company_id,
			 m.dept_id,
			 m.target_name,
			 m.assess_score,
			 m.score,
			 m.secondary_target_id,
			 m.target_category,
			 m.assess_method,
			 m.formula,
			 n.org_name as deptName
		FROM (
			SELECT
			f.secondary_target_id,
			A.dept_id,
			a.company_id,
			b.target_name,
			b.score,
			d.target_category,
            t.assess_score,
            f.assess_method,
            b.formula
		FROM
			assess_template_target_object A
			LEFT JOIN assess_template_target f ON A.template_target_id = f.id
			LEFT JOIN assess_target_secondary b ON f.secondary_target_id = b.id
			LEFT JOIN assess_template C ON C.id = f.template_id
			LEFT JOIN assess_target d ON d.id = b.primary_target_id
            LEFT JOIN (
					SELECT
					f.secondary_target_id,
					COALESCE(SUM(e.rule_score), 0 ) AS assess_score
				FROM
					assess_template_target_object A
					LEFT JOIN assess_template_target f ON A.template_target_id = f.id
					LEFT JOIN assess_task_report e ON A.ID = e.template_object_id
				WHERE
					f.template_id = #{bo.templateId}
			    AND A.company_id = #{bo.companyId}
				GROUP BY
					f.secondary_target_id
			) t on t.secondary_target_id = f.secondary_target_id
		WHERE
			f.template_id = #{bo.templateId}
			AND A.company_id = #{bo.companyId}
		) m
		LEFT JOIN rmp.sys_organizations n on m.dept_id = n.id
	</select>
	<select id="getOrderByCompany" resultType="com.enrising.ctsc.assess.api.vo.AssessTaskReportVo">
		SELECT A
			.company_id,
			c.secondary_target_id,
		COALESCE(sum( b.rule_score ), 0 ) AS assess_score
			FROM assess_template_target_object A
			    LEFT JOIN assess_task_report b ON A.id = b.template_object_id
				LEFT JOIN assess_template_target C ON A.template_target_id = C.id
		WHERE
			template_id = #{ bo.templateId }
		AND  c.secondary_target_id in
			<foreach collection="bo.secondaryTargetIds" open="(" separator="," item="secondaryTargetId" close=")">
				#{secondaryTargetId }
			</foreach>
		GROUP BY
			c.secondary_target_id, A.company_id
	</select>
	<select id="getCompanyAssScoreList" resultType="com.enrising.ctsc.assess.api.vo.AssessProvinceComVo">
		SELECT
		    e.company_id as id,
			f.org_name as name,
			e.assess_score,
			e.template_score as taskTotal
		FROM  (
			SELECT
			b.company_id,
			COALESCE(sum(d.assess_score),0) as assess_score,
			COALESCE(sum(m.score),0) as template_score
		FROM assess_template_target a
		LEFT JOIN assess_template_target_object b on a.id = b.template_target_id
		LEFT JOIN  (
		   SELECT
		       id,
		       org_name as name
		   FROM
			   rmp.sys_organizations
		   WHERE
			   del_flag='0'
			 AND `status`='1'
			 AND org_type='1'
			 AND parent_company_no = '2600000000'
		) c on b.company_id = c.id
		left JOIN assess_task_report d on b.id = d.template_object_id
		LEFT JOIN assess_target_secondary m on a.secondary_target_id = m.id
		WHERE a.template_id = #{bo.templateId}
		GROUP  BY b.company_id
		) e
		LEFT JOIN rmp.sys_organizations f on e.company_id = f.id
	</select>
	<select id="getScoreByTemplateId" resultType="com.enrising.ctsc.assess.api.vo.AssessProvinceComVo">
			SELECT
			    m.template_id,
				m.template_score as taskTotal,
				n.assess_score
			FROM  (
			   SELECT
					a.template_id,
					COALESCE(sum(b.score),0) as template_score
					FROM assess_template_target a
					LEFT JOIN assess_target_secondary b on  a.secondary_target_id = b.id
					WHERE a.template_id = #{bo.templateId}
					GROUP BY template_id
			) m
			LEFT JOIN
			(
			SELECT
			 a.template_id,
			 COALESCE(sum(c.assess_score),0) as assess_score
			FROM assess_template_target  a
			LEFT JOIN assess_template_target_object b on a.id = b.template_target_id
			LEFT JOIN assess_task_report c on c.template_object_id = b.id
			WHERE a.template_id = #{bo.templateId}
			<if test="bo.companyId !=null">
				and b.company_id = #{bo.companyId}
			</if>
			GROUP BY a.template_id
			) n
			on m.template_id = n.template_id
	</select>
	<select id="getScoreListByTemplateId" resultType="com.enrising.ctsc.assess.api.vo.AssessScoreVo">
		SELECT
			COALESCE(sum( m.assess_score ), 0 ) AS assess_score,
			M.company_id
		FROM (

			SELECT
				COALESCE(sum(e.rule_score),0) as assess_score,
				b.secondary_target_id,
				d.company_id
			FROM assess_template a
			left JOIN assess_template_target b on a.id = b.template_id
			LEFT JOIN assess_template_target_object d on d.template_target_id = b.id
			LEFT JOIN assess_task_report e on e.template_object_id = d.id
			WHERE template_id = #{bo.templateId}
			<if test="bo.companyId!=null">
				and d.company_id = #{bo.companyId}
			</if>
			<if test="bo.targetIds !=null and bo.targetIds.size()>0">
				and b.secondary_target_id in
				<foreach collection="bo.targetIds" open="(" separator="," item="targetId" close=")">
					#{targetId}
				</foreach>
			</if>
			GROUP BY d.company_id , b.secondary_target_id
			ORDER BY assess_score desc
		) m
		GROUP BY m.company_id
		ORDER BY assess_score desc
	</select>
	<select id="getHeaderScoreList" resultType="com.enrising.ctsc.assess.api.vo.AssessScoreVo">
		SELECT
		    a.id as template_id,
			b.secondary_target_id as targetId,
			c.target_name,
			c.score,
			c.assess_period,
			d.target_category,
		    b.assess_method,
		    c.formula
		FROM
			assess_template a
		left JOIN assess_template_target b on a.id = b.template_id
		LEFT JOIN assess_target_secondary c on c.id = b.secondary_target_id
		LEFT JOIN assess_target d on c.primary_target_id = d.id
		WHERE template_id = #{bo.templateId}
		<if test="bo.targetIds !=null and bo.targetIds.size()>0">
			and b.secondary_target_id in
			<foreach collection="bo.targetIds" open="(" separator="," item="targetId" close=")">
				#{targetId}
			</foreach>
		</if>
	</select>
	<select id="getAssessScoreByTemplateObjectIds"
			resultType="com.enrising.ctsc.assess.api.vo.AssessTemplateTargetObjectVo">
		 SELECT
		   COALESCE(SUM(rule_score), 0) as assess_score,
			template_object_id
		 FROM assess_task_report
		 WHERE template_object_id in (
		   SELECT
			 M.ID
				FROM
				assess_template_target_object m
				LEFT JOIN assess_template_target p on m.template_target_id = p.id
				WHERE
				p.template_id = #{bo.templateId}
		 )
		 and report_status = '1'
		 GROUP BY template_object_id
	</select>
	<select id="getComRankByTarAndTemId" resultType="com.enrising.ctsc.assess.api.vo.AssessScoreVo">
		SELECT
			M.dept_id as companyId,
			M.assess_score,
			m.ID as targetId,
			n.org_name AS companyName
		FROM
			(
			SELECT
				A.dept_id,
				b.ID,
				COALESCE(sum(e.rule_score), 0 ) AS assess_score
			FROM
				assess_template_target_object
				A LEFT JOIN assess_template_target f ON A.template_target_id = f.id
				LEFT JOIN assess_target_secondary b ON f.secondary_target_id = b.id
				LEFT JOIN assess_task_report e ON A.ID = e.template_object_id
			WHERE
				f.template_id = #{bo.templateId}
				AND b.id in
				  <foreach collection="bo.targetIds" open="(" separator="," item="targetId" close=")">
					  #{targetId}
				  </foreach>
				GROUP BY
		        b.ID ,A.dept_id
			)
			M LEFT JOIN rmp.sys_organizations n ON M.dept_id = n.id
			ORDER BY M.assess_score desc
	</select>

	<select id="getTargetScoreByCompanyIds" resultType="com.enrising.ctsc.assess.api.vo.AssessScoreVo">
			SELECT
				f.secondary_target_id as targetId,
				a.company_id,
				COALESCE(SUM(e.rule_score), 0 ) AS assess_score
			FROM
				assess_template_target_object A
				LEFT JOIN assess_template_target f ON A.template_target_id = f.id
				LEFT JOIN assess_task_report e ON A.ID = e.template_object_id
			WHERE
				f.template_id = #{bo.templateId}
				AND a.company_id in
				<foreach collection="bo.companyIds" open="(" separator="," item="companyId" close=")">
					#{companyId}
				</foreach>
			GROUP BY
		        a.company_id,f.secondary_target_id
	</select>
	<select id="getUserByIds" resultType="com.enrising.ctsc.carbon.common.entity.User">
		SELECT
		    su.id,
		    su.user_name,
		    sruo.department_no,
		so.org_name
		FROM rmp.sys_user su
		    INNER JOIN rmp.sys_r_user_organizations sruo on su.id = sruo.user_id
			LEFT JOIN rmp.sys_organizations so on sruo.department_no = so.id
		WHERE
		    so.del_flag='0'
		  AND sruo.del_flag = '0'
		  AND su.id in
			<foreach collection="ids" open="(" separator="," item="item" close=")">
				#{item}
			</foreach>
		ORDER BY su.id,so.id asc
	</select>
</mapper>
