package com.enrising.ctsc.assess.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.assess.api.bo.AssessTemplateBo;
import com.enrising.ctsc.assess.api.bo.AssessTemplateTargetBo;
import com.enrising.ctsc.assess.api.entity.*;
import com.enrising.ctsc.assess.api.enums.*;
import com.enrising.ctsc.assess.api.vo.*;
import com.enrising.ctsc.assess.mapper.AssessReportMapper;
import com.enrising.ctsc.assess.mapper.AssessTemplateMapper;
import com.enrising.ctsc.assess.mapper.AssessTemplateTargetMapper;
import com.enrising.ctsc.assess.service.*;
import com.enrising.ctsc.carbon.common.constant.CommonConstants;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.JwtUtils;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.carbon.common.utils.TreeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 考核模板管理服务实现
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-07
 */
@Slf4j
@Service
@AllArgsConstructor
public class AssessTemplateServiceImpl extends ServiceImpl<AssessTemplateMapper, AssessTemplate> implements AssessTemplateService {

	private final AssessTemplateTargetObjectService assessTemplateTargetObjectService;

	private final AssessTemplateTargetService assessTemplateTargetService;

	private final AssessTemplateTargetMapper assessTemplateTargetMapper;
	private final AssessTemplateMapper assessTemplateMapper;

	private final AssessTaskReportService assessTaskReportService;

	private final AssessReportMapper assessReportMapper;

	private final AssessTargetService assessTargetService;

//	todo 1
//	private final RemoteAdminService remoteAdminService;

	/**
	 * 查询考核模板详情数据
	 * @param id  考核模板id
	 * @return 考核模板详情数据
	 */
	@Override
	public AssessTemplateVo getDetailById(Long id) {
		if (id == null) {
			throw new BusinessException("查询参数不能为空！");
		}
		AssessTemplate assessTemplate = baseMapper.selectById(id);
		if (ObjectUtil.isEmpty(assessTemplate)) {
			return null;
		}
		AssessTemplateVo assessTemplateVo = new AssessTemplateVo();
		BeanUtils.copyProperties(assessTemplate ,assessTemplateVo);
		//获取考核指标列表
		assessTemplateVo.setAssessTemplateTargetVoList(this.getAssessTemplateTargetList(id));
		return  assessTemplateVo;
	}

	/**
	 * 新增考核模板
	 * @param bo 考核模板数据
	 * @return 是否成功
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean add(AssessTemplateBo bo) {
		AssessTemplate assessTemplate = new AssessTemplate();
		BeanUtils.copyProperties(bo, assessTemplate);

		//设置模板结束时间
		assessTemplate.setTemplateEndTime(this.getTemplateEndTime(assessTemplate.getTemplateStartTime(),
				assessTemplate.getPeriod()));

		//指标使用状态
		String targetStatus = null;
		if (StrUtil.isNotBlank(assessTemplate.getSendStatus()) &&
				assessTemplate.getSendStatus().equals(AssessTemplateSendStatus.SENT.getValue())) {
			Date dateNow = new Date();
			//设置模板下发日期
			assessTemplate.setSendTime(dateNow);
			//设置模板状态
			if (assessTemplate.getTemplateEndTime().before(dateNow)) {
				assessTemplate.setStatus(AssessTemplateStatus.COMPLETED.getValue());
			} else if (assessTemplate.getTemplateStartTime().after(dateNow)) {
				assessTemplate.setStatus(AssessTemplateStatus.NOT_START.getValue());
			} else {
				assessTemplate.setStatus(AssessTemplateStatus.IN_PROGRESS.getValue());
			}
			targetStatus = AssessStatus.HAVE_BEEN_USED.getValue();
		}

		//保存考核模板
		save(assessTemplate);

		//保存考核模板指标
		this.saveAssessTemplateTarget(assessTemplate.getId(), bo.getAssessTemplateTargetBoList());

		//设置指标使用状态
		if (StrUtil.isNotBlank(targetStatus) && targetStatus.equals(AssessStatus.HAVE_BEEN_USED.getValue())) {
			this.setTargetStatus(assessTemplate.getId(), targetStatus);
		}
		//保存空白报告
		AssessReport assessReport = new AssessReport();
		assessReport.setTemplateId(assessTemplate.getId());
		assessReportMapper.insert(assessReport);
		return true;
	}

	/**
	 * 编辑考核模板
	 * @param bo 考核模板数据
	 * @return 是否成功
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean edit(AssessTemplateBo bo) {
		AssessTemplate assessTemplate = new AssessTemplate();
		BeanUtils.copyProperties(bo, assessTemplate);
		//设置模板结束时间
		if (ObjectUtil.isNotEmpty(assessTemplate.getTemplateStartTime()) &&
				ObjectUtil.isNotEmpty(assessTemplate.getPeriod())) {
			assessTemplate.setTemplateEndTime(this.getTemplateEndTime(assessTemplate.getTemplateStartTime(),
					assessTemplate.getPeriod()));
		}
		//指标使用状态
		if (StrUtil.isNotBlank(assessTemplate.getSendStatus()) &&
				assessTemplate.getSendStatus().equals(AssessTemplateSendStatus.SENT.getValue())) {
			Date dateNow = new Date();
			//设置模板下发日期
			assessTemplate.setSendTime(dateNow);
			//设置模板状态
			if (assessTemplate.getTemplateEndTime().before(dateNow)) {
				assessTemplate.setStatus(AssessTemplateStatus.COMPLETED.getValue());
			} else if (assessTemplate.getTemplateStartTime().after(dateNow)) {
				assessTemplate.setStatus(AssessTemplateStatus.NOT_START.getValue());
			} else {
				assessTemplate.setStatus(AssessTemplateStatus.IN_PROGRESS.getValue());
			}
			this.setTargetStatus(assessTemplate.getId(), AssessStatus.HAVE_BEEN_USED.getValue());
		} else {
			assessTemplate.setSendTime(null);
		}
		//删除原有考核模板指标
		this.delAssessTemplateTarget(bo.getId());
		//保存考核模板指标
		this.saveAssessTemplateTarget(bo.getId(), bo.getAssessTemplateTargetBoList());
		//保存考核模板
		return baseMapper.updateById(assessTemplate) == 1;
	}

	/**
	 * 删除考核模板
	 * @param id  核模板id
	 * @return 是否成功
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean del(Long id) {
		//删除考核模板指标
		this.delAssessTemplateTarget(id);
		//删除考核模板
		return baseMapper.deleteById(id) == 1;
	}

	/**
	 * 考核模板分页查询
	 * @param queryPage  分页查询参数
	 * @return 分页查询结果
	 */
	@Override
	public Page<AssessTemplateVo> getAssessTemplatePage(QueryPage<AssessTemplateBo> queryPage) {
		LambdaQueryWrapper<AssessTemplate> qw = Wrappers.lambdaQuery();
		AssessTemplateBo assessTemplateBo = queryPage.getModel();
		if (ObjectUtil.isNotEmpty(assessTemplateBo)) {
			if (StrUtil.isNotBlank(assessTemplateBo.getTemplateName())) {
				//关键字查询：模糊查询，模板名称或者模板内容
				qw.and(t->{
					t.like(AssessTemplate::getTemplateName, assessTemplateBo.getTemplateName())
							.or().like(AssessTemplate::getContent, assessTemplateBo.getTemplateName());
				});
			}
			//查询条件，下发状态
			qw.eq(StrUtil.isNotBlank(assessTemplateBo.getSendStatus()),AssessTemplate::getSendStatus,
					assessTemplateBo.getSendStatus());
			//查询条件，任务状态
			qw.eq(StrUtil.isNotBlank(assessTemplateBo.getStatus()),AssessTemplate::getStatus,
					assessTemplateBo.getStatus());

			// 处理新的查询方式：使用queryStartTime和queryEndTime
			if (ObjectUtil.isNotEmpty(assessTemplateBo.getQueryStartTime()) && ObjectUtil.isNotEmpty(assessTemplateBo.getQueryEndTime())) {
				// 场景1：开始时间≤结束时间
				qw.and(wrapper -> wrapper
						.or().or(
								wrapper_1 -> wrapper_1.ge(AssessTemplate::getTemplateStartTime,
										assessTemplateBo.getQueryStartTime()).le(AssessTemplate::getTemplateStartTime,
										assessTemplateBo.getQueryEndTime()))
						.or().or(
								wrapper_2 -> wrapper_2.ge(AssessTemplate::getTemplateEndTime,
										assessTemplateBo.getQueryStartTime()).le(AssessTemplate::getTemplateEndTime,
										assessTemplateBo.getQueryEndTime()))
						.or().or(
								wrapper_3 -> wrapper_3.le(AssessTemplate::getTemplateStartTime,
										assessTemplateBo.getQueryStartTime()).ge(AssessTemplate::getTemplateEndTime,
										assessTemplateBo.getQueryEndTime()))
				);
			} else if (ObjectUtil.isNotEmpty(assessTemplateBo.getQueryStartTime())) {
				// 场景2：只选择开始时间：查询从所选时间到当前时间的所有数据
				qw.and(wrapper -> wrapper
						.or().ge(AssessTemplate::getTemplateStartTime, assessTemplateBo.getQueryStartTime())
						.or().ge(AssessTemplate::getTemplateEndTime, assessTemplateBo.getQueryStartTime())
				);
			} else if (ObjectUtil.isNotEmpty(assessTemplateBo.getQueryEndTime())) {
				// 场景3：只选择结束时间：查询到所选时间为止的所有数据
				qw.and(wrapper -> wrapper
						.or().le(AssessTemplate::getTemplateStartTime, assessTemplateBo.getQueryEndTime())
						.or().le(AssessTemplate::getTemplateEndTime, assessTemplateBo.getQueryEndTime())
				);
			}

			// 保留原有的templateTimeArea处理逻辑，以保持向后兼容
			if (ArrayUtil.isNotEmpty(assessTemplateBo.getTemplateTimeArea())) {
				qw.and(wrapper -> wrapper
						.or().or(
								wrapper_1 -> wrapper_1.ge(AssessTemplate::getTemplateStartTime,
												assessTemplateBo.getTemplateTimeArea()[0]).le(AssessTemplate::getTemplateStartTime,
												assessTemplateBo.getTemplateTimeArea()[1]))
						.or().or(
								wrapper_2 -> wrapper_2.ge(AssessTemplate::getTemplateEndTime,
										assessTemplateBo.getTemplateTimeArea()[0]).le(AssessTemplate::getTemplateEndTime,
										assessTemplateBo.getTemplateTimeArea()[1]))
				);
			}

			//查询条件，任务时间区域
			if (ArrayUtil.isNotEmpty(assessTemplateBo.getCreateDateTimeArea())) {
				qw.ge(AssessTemplate::getCreateTime, assessTemplateBo.getCreateDateTimeArea()[0]).
						le(AssessTemplate::getCreateTime, assessTemplateBo.getCreateDateTimeArea()[1]);
			}
		}
		qw.orderByDesc(AssessTemplate::getCreateTime);
		Page<AssessTemplate> assessTemplatePage = new Page<>(queryPage.getCurrent(), queryPage.getSize(), true);
		this.page(assessTemplatePage, qw);
		return this.pageEntityToVo(assessTemplatePage);
	}

	@Override
	public Integer countTotalTask() {
		List<AssessTemplateVo> allAssessTemplate = new ArrayList<>();
		AssessTemplateBo bo = new AssessTemplateBo();
		bo.setYear(String.valueOf(DateUtil.year(new Date())));
		//todo 权限区分
//		AiUser user = SecurityUtils.getUser();
//		if(SecurityUtils.isAdmin()||SecurityUtils.getRoleCodes().contains(SysRoleEnums.ROLE_PROVINCE_ADMIN.getValue())){
			// 管理员、省级管理员看 省级 下的所有任务
			allAssessTemplate = assessTemplateMapper.getProvinceAssessTemplate(bo);
//		} else {
//			// 获取部门id  只看自己的模板
//			Long deptId = user.getDeptId();
//			if(SecurityUtils.getRoleCodes().contains(SysRoleEnums.ROLE_CITY_ADMIN.getValue())){
//				// 地市管理员获取
//				bo.setCompanyId(deptId);
//			} else  {
//				bo.setDeptId(deptId);
//			}
//			allAssessTemplate = assessTemplateMapper.getAllAssessTemplate(bo);
//		}
		return allAssessTemplate.size();
	}

	@Override
	public Page<AssessTemplateVo> getDeliveredTaskByPage(QueryPage<AssessTemplateBo> queryPage) {
		AssessTemplateBo bo = queryPage.getModel();
		// 处理旧的查询方式
		if(ObjectUtil.isNotEmpty(bo.getTemplateTimeArea())){
			bo.setTemplateStartTime(bo.getTemplateTimeArea()[0]);
			bo.setTemplateEndTime(bo.getTemplateTimeArea()[1]);
		}

		// 处理新的查询方式
		if (ObjectUtil.isNotEmpty(bo.getQueryStartTime()) && ObjectUtil.isNotEmpty(bo.getQueryEndTime())) {
			// 如果同时提供了开始和结束时间，则使用这两个时间
			bo.setTemplateStartTime(bo.getQueryStartTime());
			bo.setTemplateEndTime(bo.getQueryEndTime());
		} else if (ObjectUtil.isNotEmpty(bo.getQueryStartTime())) {
			// 如果只提供了开始时间，则使用开始时间作为查询条件
			bo.setTemplateStartTime(bo.getQueryStartTime());
			bo.setTemplateEndTime(null); // 清空结束时间，让SQL查询逻辑处理
		} else if (ObjectUtil.isNotEmpty(bo.getQueryEndTime())) {
			// 如果只提供了结束时间，则使用结束时间作为查询条件
			bo.setTemplateStartTime(null); // 清空开始时间，让SQL查询逻辑处理
			bo.setTemplateEndTime(bo.getQueryEndTime());
		}

		Page<AssessTemplateVo> assessTemplatePage = new Page<>(queryPage.getCurrent(), queryPage.getSize(), true);
		Page<AssessTemplateVo> deliveredTaskByPage = assessTemplateMapper.getDeliveredTaskByPage(assessTemplatePage, bo);
		if(CollectionUtil.isNotEmpty(deliveredTaskByPage.getRecords())){
			List<AssessTemplateVo> records = deliveredTaskByPage.getRecords();
			records.forEach(node->{
				node.setTemplateTaskTime(getTemplateTime(node.getTemplateStartTime(),
						node.getPeriod()));

			});
		}
		return deliveredTaskByPage;
	}

	@Override
	public List<AssessTemplate> getDeliveredTaskList() {
		return assessTemplateMapper.selectList(Wrappers.<AssessTemplate>lambdaQuery().eq(AssessTemplate::getSendStatus, "1")
				.eq(AssessTemplate::getDelFlag, CommonConstants.ZERO_STRING));
	}

	@Override
	public List<AssessTemplateVo> getAllAssessTemplateByYear() {
		AssessTemplateBo bo = new AssessTemplateBo();
/*	todo 权限区分
		if(SecurityUtils.getRoleCodes().contains(SysRoleEnums.ROLE_CITY_ADMIN.getValue())){
			AiUser user = SecurityUtils.getUser();
			Long deptId = user.getDeptId();
			// 地市管理员 只看自己的模板
			bo.setCompanyId(deptId);
		}*/
		return assessTemplateMapper.getAllAssessTemplateByYear(bo);
	}

	@Override
	public List<AssessTemplateVo> getAllAssessTemplate(AssessTemplateBo bo) {
        if(StrUtil.isBlank(bo.getYear())){
        	bo.setYear(String.valueOf(DateUtil.year(new Date())));
		}
        // 根据当前登录角色获取对应的模板
		List<AssessTemplateVo> allAssessTemplate = getAllAssessTemplateByRole(bo);

		// 查询未上报的记录
		if(CollectionUtil.isEmpty(allAssessTemplate)) {
		      return  allAssessTemplate;
		}
		List<Long> templateIds = allAssessTemplate.stream()
				.map(AssessTemplateVo::getId).collect(Collectors.toList());
		// 管理员看所有模板ids对应的指标数据
		List<AssessTaskReportVo> allAssessTemplateByYear = assessTaskReportService.getAllAssessTemplateByYear(templateIds, bo);

		// 将所有的指标数据查询除对应的报告
		List<Long> templateObjectIds = allAssessTemplateByYear.stream().map(AssessTaskReportVo::getTemplateObjectId).collect(Collectors.toList());

		if(CollectionUtil.isEmpty(templateObjectIds)){
			return  allAssessTemplate;
		}
		// 查询该模板下所有指标的所有报告
		List<AssessTaskReport> reportList = getReportByTemplateObjectIds(templateObjectIds);
		int targetNum = 0;
		for (AssessTemplateVo node : allAssessTemplate) {
//			allAssessTemplate.forEach(node->{
			//拿到该模板的考核周期
			String templatePeriod = node.getPeriod();
			node.setTemplateTaskTime(getTemplateTime(node.getTemplateStartTime(),
					node.getPeriod()));
			// 拿到该模板下的所有指标
			List<AssessTaskReportVo> targetList = getAllAssessTargetByTemplateId(allAssessTemplateByYear, node.getId());
			AtomicInteger undoTask = new AtomicInteger(CommonConstants.ZERO_NUMBER);

			// 通过这些指标ids筛选出对应的报告
			List<Long> collect = targetList.stream().map(AssessTaskReportVo::getTemplateObjectId).collect(Collectors.toList());
			List<AssessTaskReport> taskReportList = reportList.stream().filter(report -> collect.contains(report.getTemplateObjectId())).collect(Collectors.toList());

			// 在循环每个考核指标
			targetList.forEach(target -> {
				// 根据模板周期和指标周期 计算需要上报的数量
				int reportNum = getReportNum(target.getTargetPeriod(), templatePeriod);
				Long templateObjectId = target.getTemplateObjectId();
				// 查询该部门该指标下有多少份已上报的报告
				List<AssessTaskReport> list = taskReportList.stream().filter(report -> report.getTemplateObjectId().equals(templateObjectId)).collect(Collectors.toList());
				if (list.size() < reportNum) {
					// 若是没有达到报告要求则 +1
					undoTask.getAndIncrement();
				}
			});
			node.setUndoTaskNum(undoTask.intValue());
			// 统计考核指标数量
			targetNum += targetList.size();
		}
		log.info("考核指标数量:{}", targetNum);

		if(QueryType.HOME_PAGE.getValue().equals(bo.getQueryTpe())){
			return allAssessTemplate.stream().filter(node -> node.getUndoTaskNum() > 0).collect(Collectors.toList());
		}
		return allAssessTemplate;
	}

	@Override
	public long countTargetByAssessTemplate(AssessTemplateBo bo) {
		if(StrUtil.isBlank(bo.getYear())){
			bo.setYear(String.valueOf(DateUtil.year(new Date())));
		}
		// 根据当前登录角色获取对应的模板
		List<AssessTemplateVo> allAssessTemplate = getAllAssessTemplateByRole(bo);
		if(CollectionUtil.isNotEmpty(allAssessTemplate)) {
			// 模板列表
			List<Long> templateIdList = allAssessTemplate.stream().map(AssessTemplateVo::getId).collect(Collectors.toList());
			Long count = assessTemplateTargetMapper.selectCount(Wrappers.<AssessTemplateTarget>lambdaQuery()
					.in(AssessTemplateTarget::getTemplateId, templateIdList)
			);
			log.info("考核指标数量:{}", count);
			return count;
		}

		return 0;
	}

	private List<AssessTaskReport> getReportByTemplateObjectIds(List<Long> templateObjectIds) {
		return assessTaskReportService.list(Wrappers.<AssessTaskReport>lambdaQuery()
				.in(AssessTaskReport::getTemplateObjectId, templateObjectIds)
				.eq(AssessTaskReport::getReportStatus, ReportStatus.HAVE_REPORTED.getValue()));
	}

	private int getReportNum(String targetPeriod, String templatePeriod) {
		int reportNum = 0;
		// 处理模板周期和指标周期 模板周期 >  指标周期
		if(Integer.parseInt(templatePeriod) < Integer.parseInt(targetPeriod)){
			// 模板为季度，指标为月，三份报告
			if(AssessTemplatePeriod.QUARTER.getValue().equals(templatePeriod)
					&& AssessTemplatePeriod.MONTH.getValue().equals(targetPeriod)
			){
				reportNum = 3;
			}
			// 模板为年，指标为季度，四份报告。
			if(AssessTemplatePeriod.YEAR.getValue().equals(templatePeriod)
					&& AssessTemplatePeriod.QUARTER.getValue().equals(targetPeriod)
			){
				reportNum = 4;
			}
			// 模板为年，指标为月，12份报告。
			if(AssessTemplatePeriod.YEAR.getValue().equals(templatePeriod)
					&& AssessTemplatePeriod.MONTH.getValue().equals(targetPeriod)
			){
				reportNum = 12;
			}
		} else {
			// 模板周期 <= 指标周期 一份报告。
			reportNum = 1;
		}
		return reportNum;
	}

	private List<AssessTaskReportVo> getAllAssessTargetByTemplateId(List<AssessTaskReportVo> allAssessTemplateByYear, Long id) {
		// 拿到该模板下的考核指标
		return  allAssessTemplateByYear.stream().filter(item -> {
			return item.getTemplateId().equals(id);
		}).collect(Collectors.toList());
	}

	private Map<Long, String> buildUserIdToDeptNameMap(List<AssessTemplateVo> allAssessTemplate) {
		Map<Long, String> map = new HashMap<>();
		List<Long> collect = allAssessTemplate.stream().map(AssessTemplateVo::getCreateBy).distinct().collect(Collectors.toList());
/*	todo 1
		collect.forEach(node->{
			UserVO userVO = remoteAdminService.getUserInfoById(node).getData();
			map.put(node,userVO.getDeptName());
		});*/
		return map;
	}

	private List<AssessTemplateVo> getAllAssessTemplateByRole(AssessTemplateBo bo) {
		Long companyId = JwtUtils.getCurrentUserCompanyId();
		if(ObjectUtil.isEmpty(companyId) || companyId.equals(2600000000L)){
			// 管理员看所有的模板
			log.info("管理员看所有的模板：{}", bo);
			return assessTemplateMapper.getAllAssessTemplate(bo);
		} else {
//			if(SecurityUtils.getRoleCodes().contains(SysRoleEnums.ROLE_CITY_ADMIN.getValue())){
//				// 地市管理员 只看自己的模板
//				bo.setCompanyId(deptId);
//			}else {
//				// 部门员工 只看自己的模板
//				bo.setDeptId(deptId);
//			}
			bo.setCompanyId(companyId);
			return assessTemplateMapper.getAllAssessTemplate(bo);
		}
	}

	/**
	 * 模板是否有上报数据
	 *
	 * @param id 参数
	 * @return 模板是否有上报数据
	 */
	@Override
	public boolean isHasReportData(Long id) {
		//获取模板指标列表
		List<AssessTemplateTarget> assessTemplateTargetList = assessTemplateTargetService.list(
				new LambdaQueryWrapper<AssessTemplateTarget>().eq(AssessTemplateTarget::getTemplateId, id).
						select(AssessTemplateTarget::getId));
		if (CollectionUtil.isNotEmpty(assessTemplateTargetList)) {
			//获取模板指标id列表
			List<Long> assessTemplateTargetIdList = assessTemplateTargetList.stream().
					map(AssessTemplateTarget::getId).collect(Collectors.toList());
			//获取模板指标对象列表
			List<AssessTemplateTargetObject> assessTemplateTargetObjectList =
					assessTemplateTargetObjectService.list(new LambdaQueryWrapper<AssessTemplateTargetObject>()
					.in(AssessTemplateTargetObject::getTemplateTargetId, assessTemplateTargetIdList)
							.select(AssessTemplateTargetObject::getId));
			if (CollectionUtil.isNotEmpty(assessTemplateTargetObjectList)) {
				//获取模板指标对象id列表
				List<Long> assessTemplateTargetObjectIdList = assessTemplateTargetObjectList.stream().
						map(AssessTemplateTargetObject::getId).collect(Collectors.toList());
				//获取对象上报数据列表
				List<AssessTaskReport> assessTaskReportList = assessTaskReportService.list(
						new LambdaQueryWrapper<AssessTaskReport>().in(AssessTaskReport::getTemplateObjectId,
								assessTemplateTargetObjectIdList).select(AssessTaskReport::getId));
				return CollectionUtil.isNotEmpty(assessTaskReportList);
			}
		}
		return false;
	}

	/**
	 * 设置考核模板下发状态
	 * @param id 参数
	 * @param sendStatus 考核模板下发状态，1-已下发 2-未下发 3-已撤回
	 * @return 操作是否成功
	 */
	@Override
	public boolean setTemplateSendStatus(Long id, String sendStatus) {
		AssessTemplate assessTemplate = assessTemplateMapper.selectById(id);
		if (ObjectUtil.isEmpty(assessTemplate)){
			return false;
		}
		//设置模板下发状态
		String targetStatus;
		assessTemplate.setSendStatus(sendStatus);
		if (sendStatus.equals(AssessTemplateSendStatus.SENT.getValue())) {
			Date dateNow = new Date();
			//设置模板下发日期
			assessTemplate.setSendTime(dateNow);
			//设置模板状态
			if (assessTemplate.getTemplateEndTime().before(dateNow)) {
				assessTemplate.setStatus(AssessTemplateStatus.COMPLETED.getValue());
			} else if (assessTemplate.getTemplateStartTime().after(dateNow)) {
				assessTemplate.setStatus(AssessTemplateStatus.NOT_START.getValue());
			} else {
				assessTemplate.setStatus(AssessTemplateStatus.IN_PROGRESS.getValue());
			}
			targetStatus = AssessStatus.HAVE_BEEN_USED.getValue();
		} else {
			assessTemplate.setStatus(AssessTemplateStatus.NOT_START.getValue());
			targetStatus = AssessStatus.OUT_OF_USE.getValue();
		}
		assessTemplateMapper.updateById(assessTemplate);
		//设置指标使用状态
		this.setTargetStatus(id, targetStatus);
		return true;
	}

	@Override
	public Boolean checkReportTime(Long id) {
		AssessTemplate assessTemplate = this.getById(id);
		int compare = DateUtil.compare(new Date(), assessTemplate.getTemplateStartTime());
		return compare >= 0;
	}

	@Override
	public List<DeptTreeVo> listAssessDeptTrees(DeptTreeVo deptTree) {
		List<HashMap<String, Object>> sysDepts = assessTemplateMapper.getSysDeptList(deptTree);
		List<HashMap<String, Object>> sysParentList1 = getDeptParent(sysDepts);
		List<HashMap<String, Object>> sysChildrenList1 = null;
		List<HashMap<String, Object>> sysChildrenList2 = null;
		List<HashMap<String, Object>> sysParentList2 = null;
		if (cn.hutool.core.util.ObjectUtil.isNotEmpty(deptTree) &&
				"2".equals(deptTree.getObjectType())) {
			sysParentList2 = getDeptParent(sysParentList1);
			sysChildrenList1 = getDeptChildren(sysDepts);
			sysChildrenList2 = getDeptParent(sysChildrenList1);
		}
		sysDepts.addAll(sysParentList1);
		if (cn.hutool.core.util.ObjectUtil.isNotEmpty(sysParentList2)) {
			sysDepts.addAll(sysParentList2);
		}
		if (cn.hutool.core.util.ObjectUtil.isNotEmpty(sysChildrenList1)) {
			sysDepts.addAll(sysChildrenList1);
		}
		if (cn.hutool.core.util.ObjectUtil.isNotEmpty(sysChildrenList2)) {
			sysDepts.addAll(sysChildrenList2);
		}
		sysDepts = removeDuplicate(sysDepts);
		List<DeptTreeVo> treeList = sysDepts.stream().filter(dept -> !dept.get("id").equals(dept.get("parent_company_no"))).map(dept -> {
			DeptTreeVo node = new DeptTreeVo();
				node.setId(Long.parseLong(dept.get("id").toString()));
				node.setParentId(Long.parseLong(dept.get("parent_company_no").toString()));
				node.setName(dept.get("org_name").toString());
				return node;
			}).collect(Collectors.toList());
		return TreeUtils.build(treeList, 0L);
	}

	private List<HashMap<String, Object>> getDeptParent(List<HashMap<String, Object>> sysDeptList) {
		List<HashMap<String, Object>> resultList = new ArrayList<>();
		sysDeptList.forEach(sysDept -> {
			if (!"0000000000".equals(sysDept.get("parent_company_no").toString())) {
				HashMap<String, Object> parentDept = assessTemplateMapper.getSysDeptById(Long.parseLong(sysDept.get("parent_company_no").toString()));
				if (cn.hutool.core.util.ObjectUtil.isNotEmpty(parentDept)) {
					resultList.add(parentDept);
				}
			}
		});
		return resultList;
	}

	private List<HashMap<String, Object>> getDeptChildren(List<HashMap<String, Object>> sysDeptList) {
		List<HashMap<String, Object>> resultList = new ArrayList<>();
		sysDeptList.forEach(sysDept -> {
			List<HashMap<String, Object>> sysDeptChildren = assessTemplateMapper.getSysDeptChildrenList(
					sysDept.get("id").toString().trim());
			if (CollectionUtil.isNotEmpty(sysDeptChildren)) {
				resultList.addAll(sysDeptChildren);
			}
		});
		return resultList;
	}

	private List<HashMap<String, Object>> removeDuplicate(List<HashMap<String, Object>> list) {
		List<String> deptIds = new ArrayList<>();//用来临时存储person的id
		List<HashMap<String, Object>> newList = list.stream().filter(// 过滤去重
				item -> {
					boolean flag = !deptIds.contains(item.get("id").toString());
					deptIds.add(item.get("id").toString());
					return flag;
				}
		).collect(Collectors.toList());
		return newList;
	}

	/**
	 * 更新考核模板状态
	 */
	private void assessTemplateJobHandler() {
		Date dateNow = new Date();
		this.update(new LambdaUpdateWrapper<AssessTemplate>().set(AssessTemplate::getStatus,
						AssessTemplateStatus.COMPLETED.getValue())
				.eq(AssessTemplate::getSendStatus, AssessTemplateSendStatus.SENT.getValue())
				.ne(AssessTemplate::getStatus, AssessTemplateStatus.COMPLETED.getValue())
				.le(AssessTemplate::getTemplateEndTime, dateNow));
		this.update(new LambdaUpdateWrapper<AssessTemplate>().set(AssessTemplate::getStatus,
						AssessTemplateStatus.IN_PROGRESS.getValue())
				.eq(AssessTemplate::getSendStatus, AssessTemplateSendStatus.SENT.getValue())
				.ne(AssessTemplate::getStatus, AssessTemplateStatus.IN_PROGRESS.getValue())
				.le(AssessTemplate::getTemplateStartTime, dateNow)
				.gt(AssessTemplate::getTemplateEndTime, dateNow));
	}

	/**
	 * 设置考核模板指标状态
	 * @param templateId 模板id
	 * @param status 考核模板指标状态，1-已使用 2-未使用
	 */
	private void setTargetStatus(Long templateId, String status) {
		if (status.equals(AssessStatus.HAVE_BEEN_USED.getValue())) {
			this.setTargetStatusUse(templateId);
		} else {
			this.setTargetStatusUnUse(templateId);
		}
	}

	/**
	 * 设置考核模板指标状态已使用
	 * @param templateId 模板id
	 */
	private void setTargetStatusUse(Long templateId) {
		List<AssessTemplateTarget> assessTemplateTargetList = assessTemplateTargetService.list(
				new LambdaQueryWrapper<AssessTemplateTarget>().eq(AssessTemplateTarget::getTemplateId, templateId));
		if (CollectionUtil.isNotEmpty(assessTemplateTargetList)) {
			//获取模板指标id列表
			List<Long> assessTargetIdList = assessTemplateTargetList.stream().
					map(AssessTemplateTarget::getTargetId).collect(Collectors.toList());
			//获取考核指标列表
			List<AssessTarget> assessTargetList = assessTargetService.list(
					new LambdaQueryWrapper<AssessTarget>().in(AssessTarget::getId, assessTargetIdList));
			//设置考核指标状态已使用
			assessTargetList.forEach(assessTarget -> {
				assessTarget.setStatus(AssessStatus.HAVE_BEEN_USED.getValue());
			});
			assessTargetService.updateBatchById(assessTargetList);
		}
	}

	/**
	 * 设置考核模板指标状态未使用
	 * @param templateId 模板id
	 */
	private void setTargetStatusUnUse(Long templateId) {
		List<AssessTemplateTarget> assessTemplateTargetList = assessTemplateTargetService.list(
				new LambdaQueryWrapper<AssessTemplateTarget>().eq(AssessTemplateTarget::getTemplateId, templateId));
		if (CollectionUtil.isNotEmpty(assessTemplateTargetList)) {
			//获取当前模板指标id列表
			List<Long> assessTargetIdList = assessTemplateTargetList.stream().
					map(AssessTemplateTarget::getTargetId).collect(Collectors.toList());
			//获取已下发模板列表
			List<AssessTemplate> assessTemplateList = this.list(new LambdaQueryWrapper<AssessTemplate>().
					eq(AssessTemplate::getSendStatus, AssessTemplateSendStatus.SENT.getValue()));
			//获取已下发模板列表id
			List<Long> assessTemplateIdList = assessTemplateList.stream().map(AssessTemplate::getId).
					collect(Collectors.toList());
			//获取已下发模板指标列表
			List<AssessTemplateTarget> sentAssessTemplateTargetList = assessTemplateTargetService.list(
					new LambdaQueryWrapper<AssessTemplateTarget>().in(AssessTemplateTarget::getTemplateId, assessTemplateIdList));
			//获取已下发模板指标id列表
			List<Long> sentAssessTargetIdList = sentAssessTemplateTargetList.stream().
					map(AssessTemplateTarget::getTargetId).collect(Collectors.toList());
			//当前模板指标id列表，删除在已下发模板指标id列表中的id
			assessTargetIdList.removeAll(sentAssessTargetIdList);
			if (CollectionUtil.isNotEmpty(assessTargetIdList)) {
				List<AssessTarget> assessTargetList = assessTargetService.list(
						new LambdaQueryWrapper<AssessTarget>().in(AssessTarget::getId, assessTargetIdList));
				//设置考核指标状态未使用
				assessTargetList.forEach(assessTarget -> {
					assessTarget.setStatus(AssessStatus.OUT_OF_USE.getValue());
				});
				//保持指标
				assessTargetService.updateBatchById(assessTargetList);
			}
		}
	}

	/***
	 * 模板实体页转换为VO页
	 * @param assessTemplatePage 模板实体页
	 * @return 返回文章VO页
	 */
	private Page<AssessTemplateVo> pageEntityToVo(Page<AssessTemplate> assessTemplatePage){
		Page<AssessTemplateVo> assessTemplateVoPage = new Page<>();
		BeanUtils.copyProperties(assessTemplatePage, assessTemplateVoPage);
		List<AssessTemplateVo> assessTemplateVoList = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(assessTemplatePage.getRecords())) {
			assessTemplatePage.getRecords().forEach(assessTemplate -> {
				AssessTemplateVo assessTemplateVo = new AssessTemplateVo();
				BeanUtils.copyProperties(assessTemplate, assessTemplateVo);
				assessTemplateVo.setTemplateTaskTime(getTemplateTime(assessTemplateVo.getTemplateStartTime(),
						assessTemplateVo.getPeriod()));
				//模板为下发状态时，需设置模板状态
//				if (assessTemplateVo.getSendStatus().equals(AssessTemplateSendStatus.SENT.getValue())) {
//					Date dateNow = new Date();
//					//设置模板状态
//					if (assessTemplateVo.getTemplateEndTime().before(dateNow)) {
//						assessTemplateVo.setStatus(AssessTemplateStatus.COMPLETED.getValue());
//					} else if (assessTemplateVo.getTemplateStartTime().after(dateNow)) {
//						assessTemplateVo.setStatus(AssessTemplateStatus.NOT_START.getValue());
//					} else {
//						assessTemplateVo.setStatus(AssessTemplateStatus.IN_PROGRESS.getValue());
//					}
//				}
				assessTemplateVoList.add(assessTemplateVo);
			});
			assessTemplateVoPage.setRecords(assessTemplateVoList);
		}
		return assessTemplateVoPage;
	}

	/***
	 * 转换考核时间格式
	 * @param templateStartTime 考核开始时间
	 * @param period 考核周期
	 * @return 返回考核时间
	 */
	private String getTemplateTime(Date templateStartTime, String period) {
		return assessTemplateTargetObjectService.getTemplateTime(templateStartTime, period);
	}

	/***
	 * 计算考核模板结束时间
	 * @param templateStartTime 考核开始时间
	 * @param period 考核周期
	 * @return 返回考核结束时间
	 */
	private Date getTemplateEndTime(Date templateStartTime, String period) {
		if (period.equals(AssessTemplatePeriod.YEAR.getValue())) {
			return DateUtil.endOfYear(templateStartTime);
		} else if (period.equals(AssessTemplatePeriod.QUARTER.getValue())) {
			return DateUtil.endOfQuarter(templateStartTime);
		} else if (period.equals(AssessTemplatePeriod.MONTH.getValue())) {
			return DateUtil.endOfMonth(templateStartTime);
		}
		return null;
	}

	/***
	 * 获取考核模板指标列表
	 * @param templateId 考核模板ID
	 * @return 返回考核模板指标列表
	 */
	private List<AssessTemplateTargetVo> getAssessTemplateTargetList(Long templateId){
		List<AssessTemplateTargetVo> assessTemplateTargetVoList = new ArrayList<>();
		List<HashMap<String,String>> templateTargetList = assessTemplateTargetMapper.getTemplateTargetList(templateId);
		templateTargetList.forEach(templateTarget->{
			AssessTemplateTargetVo assessTemplateTargetVo = new AssessTemplateTargetVo();
			Object value = templateTarget.get("id");
			if (ObjectUtil.isNotEmpty(value)) {
				assessTemplateTargetVo.setId(Long.parseLong(String.valueOf(value)));
			}
			assessTemplateTargetVo.setTemplateId(templateId);
			value = templateTarget.get("target_id");
			if (ObjectUtil.isNotEmpty(value)) {
				assessTemplateTargetVo.setTargetId(Long.parseLong(String.valueOf(value)));
			}
			assessTemplateTargetVo.setAssessMethod(templateTarget.get("assess_method"));
			value = templateTarget.get("secondary_target_id");
			if (ObjectUtil.isNotEmpty(value)) {
				assessTemplateTargetVo.setSecondaryId(Long.parseLong(String.valueOf(value)));
			}
			value = templateTarget.get("score");
			if (ObjectUtil.isNotEmpty(value)) {
				assessTemplateTargetVo.setSecondaryTargetScore(Double.parseDouble(String.valueOf(value)));
			}
			assessTemplateTargetVo.setSecondaryTargetFormula(templateTarget.get("formula"));
			assessTemplateTargetVo.setTargetCategory(templateTarget.get("target_category"));
			assessTemplateTargetVo.setTargetType(templateTarget.get("target_type"));
			assessTemplateTargetVo.setAssessPeriod(templateTarget.get("assess_period"));
			assessTemplateTargetVo.setDataSource(templateTarget.get("data_source"));
			assessTemplateTargetVo.setObjectType(templateTarget.get("object_type"));
			assessTemplateTargetVo.setSecondaryTargetName(templateTarget.get("secondary_target_name"));
			List<AssessTemplateTargetObjectVo> assessTemplateTargetObjectVoList = new ArrayList<>();
			List<HashMap<String,String>> objectList =
					assessTemplateTargetMapper.getTemplateTargetObjectList(assessTemplateTargetVo.getId());
			objectList.forEach(item->{
				AssessTemplateTargetObjectVo assessTemplateTargetObjectVo = new AssessTemplateTargetObjectVo();
				Object valueId = item.get("id");
				if (ObjectUtil.isNotEmpty(valueId)) {
					assessTemplateTargetObjectVo.setId(Long.parseLong(String.valueOf(valueId)));
				}
				valueId = item.get("template_target_id");
				if (ObjectUtil.isNotEmpty(valueId)) {
					assessTemplateTargetObjectVo.setTemplateTargetId(Long.parseLong(String.valueOf(valueId)));
				}
				valueId = item.get("company_id");
				if (ObjectUtil.isNotEmpty(valueId)) {
					assessTemplateTargetObjectVo.setCompanyId(Long.parseLong(String.valueOf(valueId)));
				}
				valueId = item.get("dept_id");
				if (ObjectUtil.isNotEmpty(valueId)) {
					assessTemplateTargetObjectVo.setDeptId(Long.parseLong(String.valueOf(valueId)));
				}
				assessTemplateTargetObjectVo.setObjectType(item.get("object_type"));
				assessTemplateTargetObjectVo.setName(item.get("name"));
				assessTemplateTargetObjectVo.setDept(item.get("dept"));
				assessTemplateTargetObjectVoList.add(assessTemplateTargetObjectVo);
			});
			assessTemplateTargetVo.setAssessTemplateTargetObjectVoList(assessTemplateTargetObjectVoList);
			assessTemplateTargetVoList.add(assessTemplateTargetVo);
		});
		return assessTemplateTargetVoList;
	}

	/***
	 * 删除考核模板指标
	 * @param templateId 考核模板ID
	 */
	private void delAssessTemplateTarget(Long templateId){
		List<AssessTemplateTarget> assessTemplateTargetList = assessTemplateTargetService.list(
				new LambdaQueryWrapper<AssessTemplateTarget>().
						eq(AssessTemplateTarget::getTemplateId, templateId));

		if (CollectionUtil.isNotEmpty(assessTemplateTargetList)) {
			assessTemplateTargetList.forEach(assessTemplateTarget -> {
				//删除原有考核模板指标对象
				assessTemplateTargetObjectService.remove(
						new LambdaQueryWrapper<AssessTemplateTargetObject>().
								eq(AssessTemplateTargetObject::getTemplateTargetId, assessTemplateTarget.getId()));
			});
		}
		//删除原有考核模板指标
		assessTemplateTargetService.remove(
				new LambdaQueryWrapper<AssessTemplateTarget>().eq(AssessTemplateTarget::getTemplateId, templateId));

	}

	/***
	 * 获取考核模板指标对象列表
	 * @param templateTargetId 考核模板指标ID
	 * @return 返回考核模板指标对象列表
	 */
	private List<AssessTemplateTargetObject> getAssessTemplateTargetObjectList(Long templateTargetId){
		return assessTemplateTargetObjectService.list(new LambdaQueryWrapper<AssessTemplateTargetObject>()
						.eq(AssessTemplateTargetObject::getTemplateTargetId, templateTargetId));
	}

	/***
	 * 保存考核模板指标
	 * @param templateId 考核模板ID
	 * @param assessTemplateTargetBoList 考核模板指标列表
	 */
	private void saveAssessTemplateTarget(Long templateId, List<AssessTemplateTargetBo> assessTemplateTargetBoList) {
		if (CollectionUtil.isNotEmpty(assessTemplateTargetBoList)) {
			assessTemplateTargetBoList.forEach(assessTemplateTargetBo -> {
				AssessTemplateTarget assessTemplateTarget = new AssessTemplateTarget();
				BeanUtils.copyProperties(assessTemplateTargetBo, assessTemplateTarget);
				if (ObjectUtil.isNull(assessTemplateTarget.getSecondaryTargetId())) {
					assessTemplateTarget.setSecondaryTargetId(assessTemplateTargetBo.getSecondaryId());
				}
				assessTemplateTarget.setId(null);
				assessTemplateTarget.setTemplateId(templateId);
				//保存模板指标
				assessTemplateTargetService.save(assessTemplateTarget);
				List<AssessTemplateTargetObject> assessTemplateTargetObjectList =
						assessTemplateTargetBo.getAssessTemplateTargetObjectList();
				if (CollectionUtil.isNotEmpty(assessTemplateTargetObjectList)) {
					//保存模板指标对象
					assessTemplateTargetObjectList.forEach(assessTemplateTargetObject -> {
						assessTemplateTargetObject.setTemplateTargetId(assessTemplateTarget.getId());
						if (ObjectUtil.isNull(assessTemplateTargetObject.getCompanyId())) {
							assessTemplateTargetObject.setCompanyId(assessTemplateTargetObject.getId());
						}
						assessTemplateTargetObject.setId(null);
						assessTemplateTargetObject.setObjectType(assessTemplateTarget.getObjectType());
						assessTemplateTargetObjectService.save(assessTemplateTargetObject);
					});
				}
			});
		}
	}

}
