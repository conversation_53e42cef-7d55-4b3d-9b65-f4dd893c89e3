package com.enrising.ctsc.discharge.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.query.DischargeJituanEnergySyncLogQuery;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.vo.DischargeJituanEnergySyncLogVo;
import com.enrising.ctsc.discharge.service.DischargeJituanEnergySyncLogService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* 集团能耗同步日志
*
* <AUTHOR> <EMAIL>
* @since 1.0.0 2024-01-03
*/
@RestController
@RequestMapping("/discharge/jituanEnergySyncLog")
@AllArgsConstructor
public class DischargeJituanEnergySyncLogController {
    private final DischargeJituanEnergySyncLogService dischargeJituanEnergySyncLogService;

    @GetMapping("/list")
    public TableDataInfo<DischargeJituanEnergySyncLogVo> page(Page<DischargeJituanEnergySyncLogVo> page, DischargeJituanEnergySyncLogQuery query){
        return dischargeJituanEnergySyncLogService.findList(page, query);
    }

//    @GetMapping("/detail")
//    public R<DischargeJituanEnergySyncLogVo> get(DischargeJituanEnergySyncLogQuery query){
//        DischargeJituanEnergySyncLogVo detail = dischargeJituanEnergySyncLogService.detail(query);
//        return R.success(detail, "查询成功");
//    }
//
//    @PostMapping(value = "/save")
//    public R<String> save(@RequestBody @Valid DischargeJituanEnergySyncLogBo bo){
//        dischargeJituanEnergySyncLogService.add(bo);
//        return R.success("保存成功");
//    }
//
//    @PostMapping(value = "/update")
//    public R<String> update(@RequestBody @Valid DischargeJituanEnergySyncLogBo bo){
//        dischargeJituanEnergySyncLogService.edit(bo);
//        return R.success("修改成功");
//    }
//
//    @PostMapping(value = "/delete/{id}")
//    public R<String> delete(@PathVariable Long id){
//        dischargeJituanEnergySyncLogService.del(id);
//        return R.success("删除成功");
//    }
}
