<template>
    <Modal
      v-model="selectAmmeterModel"
      :width="screenWidth"
      title="选择局站"
      @on-ok="onModalOK"
      @on-cancel="onModalCancel"
    >
      <div>
        <Form ref="ammeterForm" :model="ammeterObj" :label-width="120" inline>
          <Row>
          
            <Col span="8">
              <FormItem label="局站编码:" prop="stationcode">
                <cl-input
                  v-model="ammeterObj.stationcode"
                  placeholder="请输入电表/协议编号"
                  :style="formItemWidth"
                />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem
                label="局站名称:"
                prop="stationname"
                class="form-line-height"
              >
                <cl-input
                  v-model="ammeterObj.stationname"
                  placeholder="请输入项目名称"
                  :style="formItemWidth"
                />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="8">
              <FormItem
                label="所属分公司："
                prop="company"
                class="form-line-height"
              >
                <Select
                  v-model="ammeterObj.company"
                  @on-change="selectChange2(ammeterObj.company)"
                  :style="formItemWidth"
                >
                  <Option value="" v-if="companies.length != 1">全部</Option>
                  <Option
                    v-for="item in companies"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}</Option
                  >
                </Select>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem
                label="所属部门："
                prop="countryName"
                v-if="isAdmin == true"
                class="form-line-height"
              >
                <Input
                  :clearable="true"
                  icon="ios-archive"
                  v-model="ammeterObj.countryName"
                  placeholder="点击图标选择"
                  @on-click="chooseResponseCenter()"
                  readonly
                  :style="formItemWidth"
                />
              </FormItem>
              <FormItem
                label="所属部门："
                prop="country"
                v-if="isAdmin == false"
                class="form-line-height"
              >
                <Select v-model="ammeterObj.country" :style="formItemWidth">
                  <Option value="">全部</Option>
                  <Option
                    v-for="item in departments"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}</Option
                  >
                </Select>
              </FormItem>
            </Col>
          </Row>
          <country-modal
            ref="countryModal"
            v-on:getDataFromModal="getDataFromModal"
          ></country-modal>
          <Row>
            <!-- v-if="'sc' == version" -->
            
            <!-- <Col span="8" v-else-if="'ln' == version">
              <FormItem label="供电局电表编号:" prop="supplybureauammetercode">
                <cl-input
                  v-model="ammeterObj.supplybureauammetercode"
                  placeholder="请输入供电局电表编号"
                  :style="formItemWidth"
                />
              </FormItem>
            </Col> -->
            <Col span="16">
              <div align="right">
                <Button
                  type="success"
                  icon="ios-search"
                  @click="getAccountMessages()"
                  >搜索</Button
                >
                <Button type="info" icon="ios-redo" @click="onResetHandle()"
                  >重置</Button
                >
              </div>
            </Col>
          </Row>
        </Form>
      </div>
      <!-- <div style="padding-left: 3%; padding-right: 3%"> -->
        <Table
             class="mytable"
          :columns="ammeterTb.columns"
          ref="ammeterTable"
          height="300"
          border
          :loading="ammeterTb.loading"
          :data="insideData"
        ></Table>
        <Page
          :total="pageTotal"
          :current="pageNum"
          :page-size="pageSize"
          show-elevator
          show-sizer
          show-total
          placement="top"
          @on-change="handlePage"
          @on-page-size-change="handlePageSize"
        ></Page>
      <!-- </div> -->
      
      <div slot="footer" style="text-align: center!important">
          <Button type="text" @click="cancelEM">取消</Button>
          <Button type="primary" @click="commitEM">确定</Button>
        </div>
    </Modal>
  </template>
  
  <script>
  import
   { whiteOneStopIsMoreThanOneWatch } 
  from '@/api/account';
  import axios from "@/libs/api.request";
  import indexData from "@/config/index";
  import {
    getClassification,
    getUserdata,
    getUserByUserRole,
    getCountrysdata,
    getCountryByUserId,
  } from "@/api/basedata/ammeter.js";
  import CountryModal from "@/view/basedata/ammeter/countryModal";
  import { widthstyle } from "@/view/business/mssAccountbill/mssAccountbilldata";
  import { listStation }from"@/api/alertcontrol/alertcontrol";
  
  export default {
    components: { CountryModal },
    prop: {
      getQuery: {
                type: Object,
            },
    },
    data() {
      return {


        columns: [
          { type: "selection", width: 60, align: "center" }, 
          { title: "项目名称", key: "projectname", align: "center" },
            { title: "所属分公司", key: "companyName", align: "center" },
            { title: "所属部门", key: "countryName", align: "center" },
            { title: "用电类型", key: "electrotypename", align: 'center'},
            { title: '对外结算类型', align: 'center', key: 'directsupplyflag', width: 80,},
            { title: "局站", key: "stationName", align: "center" },
            { title: "单价（元）", key: "price", align: "center" },],

        selectAmmeterModel: false,
        formItemWidth: widthstyle,
        version: "",
        screenWidth: 1200,
        companies: [],
        departments: [],
        isAdmin: false,
        ammeterObj: {
          stationname: "", 
          companyname: "", 
          countryname: "", 
          stationcode: "",
          ammeterName: "", //电表/协议编号
          supplybureauammetercode: "",
          company: "", //所属分公司
          country: "", //所属部门
          accountType: "", //台账类型
          accountno: "", //期号
          accountestype: null,
          projectName: null,
          countryName: null,
          isentityammeter: null,
        },
        company: null, //用户默认公司
        country: null, //用户默认所属部门
        countryName: null, //用户默认所属部门
        ammeterTb: {
          loading: false,
          columns: [ 
                        { type: "selection", width: 30, align: "center", },
                        
                        {
                        title: "局站编码", key: "stationcode",  minWidth: 65, align: 'center'
                        },
                        // {
                        //     title: "局站名称", key: "projectname", minWidth: 90, align: 'center'
                        // }, 
                        {
                            title: "局站名称", key: "stationname", minWidth: 100, align: 'center'
                        }, 
                        {
                            title: "所属分公司", key: "companyName", minWidth: 100, align: 'center'
                        }, {
                            title: "所属部门", key: "countryName", minWidth: 110, align: 'center'
                        },{
                            title: "局站类型", key: "stationtypeName", minWidth: 110, align: 'center'
                        }, {
                            title: "关联电表数", key: "meterTotel", minWidth: 110, align: 'center'
                        },
                        ],
          headColumn: [
            // { type: "selection", width: 60, align: "center" }
        ],
          tailColumn: [
            // { title: "项目名称", key: "projectname", align: "center" },
            // { title: "所属分公司", key: "companyName", align: "center" },
            // { title: "所属部门", key: "countryName", align: "center" },
            // { title: "用电类型", key: "electrotypename", align: 'center'},
            // { title: '对外结算类型', align: 'center', key: 'directsupplyflag', width: 80,},
            // { title: "局站", key: "stationName", align: "center" },
            // { title: "单价", key: "price", align: "center" },
          ],
          lnColumn: [
            // { title: "电表户号/协议编码", key: "ammetername", align: "center" },
            // {
            //   title: "供电局电表编号",
            //   key: "supplybureauammetercode",
            //   align: "center",
            // },
          ],
          scColumn: [
            // { title: "电表户号/协议编码", key: "ammetername", align: "center" },
          ],
        },
        insideData: [], //数据
        pageTotal: 0,
        pageNum: 1,
        pageSize: 10, //当前页
      };
    },
    watch: {
      // 'ammeterObj'() {

      //   this.getElecmeter();
      // },
      // ammeterObj: {
      //   handler(newV, oldV) {
      //     if(newV&&oldV) {
      //     console.log(newV, oldV, "newV, oldV");
      //       this.ammeterObj = newV;
      //       this.getElecmeter();
      //     }
          
      //   },
      //   deep: true,
      // }
    },
    methods: {
      getElecmeterList(obj) {
        this.ammeterObj = {
        ammeterName: "", //电表/协议编号
        projectName: "", //项目名称
      //   ammeterName: "", //电表/协议编号
      //   company: "", //所属分公司
      //   country: "", //所属部门
      //   accountType: "", //台账类型
      //   accountno: "", //期号
      //   accountestype: null,
      //   isentityammeter: null,
      };
      // this.ammeterObj.company = this.company;
      // this.ammeterObj.country = Number(this.country);
      // this.ammeterObj.countryName = this.countryName;
      if (obj != null) {
        this.ammeterObj.accountType = obj.accountType;
        this.ammeterObj.accountno = obj.accountno;
        this.ammeterObj.accountestype = obj.accountestype;
        this.ammeterObj.isentityammeter = obj.isentityammeter;
        this.pageNum = 1;
        this.getInfoJZ1();
        this.selectAmmeterModel = true;
      }
        },
        
    errorTips(str) {
      this.$Notice.error({
        title: "提示",
        desc: str,
        duration: 10,
      });
    },
    
    // getInfoJZ1() {
    //       console.log(this.ammeterObj, "getAccountMessages=============this.ammeterObj");
    //     getUserByUserRole().then((res) => {
    //     //根据权限获取分公司
    //     this.companies = res.data.companies;
    //     if (
    //       res.data.isCityAdmin == true ||
    //       res.data.isProAdmin == true ||
    //       res.data.isSubAdmin == true
    //     ) {
    //       this.isAdmin = true;
    //     }
    //     getCountrysdata({ orgCode: res.data.companies[0].id }).then((res) => {
    //       //根据权限获取所属部门
    //       this.departments = res.data;
    //       this.getUserData();
    //     setTimeout(() => {
    //     let params = {};
    //     params.company = this.ammeterObj.company;
    //     params.country = this.ammeterObj.country;
    //     params.pageNum = this.pageNum;
    //     params.pageSize = this.pageSize;
    //     params.projectName = this.ammeterObj.projectName;
    //     params.ammeterName = this.ammeterObj.ammeterName;
    //       whiteOneTableMultiStationList(params).then(res => {
    //       console.log(res, "whiteOneTableMultiStationList(params).then(res => {");
    //       if(res.data.code == 0) {
    //         this.insideData = res.data.rows;
    //         this.pageTotal = res.data.total;
    //       }
          
    //       console.log(res, "res55555555555555");
    //       })
    //     },100)
    //     });
        
    //   });
        
    //   },
    getInfoJZ() {
        setTimeout(() => {
        let params = {};
        if(this.ammeterObj.countryName == ""){
                    this.ammeterObj.country = "";
                }
        params.company = this.ammeterObj.company;
        params.country = this.ammeterObj.country;
        params.stationcode  = this.ammeterObj.stationcode;
        params.stationname = this.ammeterObj.stationname;
        params.current = this.pageNum;
        params.size = this.pageSize;
        whiteOneStopIsMoreThanOneWatch(params).then(res => {
          if(res.data.code == 0) {
            for(let item of res.data.rows){
                    if(item.isWhitelist == true){
                        item._disabled = true;//禁止选择
                    }
                }
            this.insideData = res.data.rows;
            this.pageTotal = res.data.total;
          }
          
          console.log(res, "res55555555555555");
          })
        },100)
      },
    getInfoJZ1() {
          console.log(this.ammeterObj, "getAccountMessages=============this.ammeterObj");
        getUserByUserRole().then((res) => {
        //根据权限获取分公司
        this.companies = res.data.companies;
        if (
          res.data.isCityAdmin == true ||
          res.data.isProAdmin == true ||
          res.data.isSubAdmin == true
        ) {
          this.isAdmin = true;
        }
        getCountrysdata({ orgCode: res.data.companies[0].id }).then((res) => {
          //根据权限获取所属部门
          this.departments = res.data;
          this.getUserData();
        setTimeout(() => {
        let params = {};
        if(this.ammeterObj.countryName == ""){
                    this.ammeterObj.country = "";
                }
        params.company = this.ammeterObj.company;
        params.country = this.ammeterObj.country;
        params.stationcode  = this.ammeterObj.stationcode;
        params.stationname = this.ammeterObj.stationname;
        params.current = this.pageNum;
        params.size = this.pageSize;
        whiteOneStopIsMoreThanOneWatch(params).then(res => {
          if(res.data.code == 0) {
            this.insideData = res.data.rows;
            this.pageTotal = res.data.total;
          }
          
          console.log(res, "res55555555555555");
          })
        },100)
        });
        
      });
      },
        //向后台请求数据 
        getAccountMessages() {
          console.log(this.pageNum, "this.pageNum");
          console.log(this.pageSize, "this.pageSize");
          // params.pageNum = this.pageNum;
          // params.pageSize = this.pageSize;
          this.getInfoJZ();
        },
    // getAccountMessages1() {
    //   if (this.ammeterObj.countryName == "") {
    //     this.ammeterObj.country = "-1";
    //   }
    //   console.log(this.ammeterObj, "getAccountMessages=============this.ammeterObj");
    //   let params = this.ammeterObj;
    //   params.pageNum = this.pageNum;
    //   params.pageSize = this.pageSize;
    //   let req = {
    //     url: "/business/accountEs/selectAmmeterByCompany",
    //     method: "get",
    //     params: params,
    //   };
    //   this.ammeterTb.loading = true;
    //   axios
    //     .request(req)
    //     .then((res) => {
    //       this.ammeterTb.loading = false;
    //       if (res.data) {
    //         this.insideData = res.data.rows;
    //         console.log(this.insideData, "this.insideData7777777777777");
    //         this.pageTotal = res.data.total || 0;
    //       }
    //     })
    //     .catch((err) => {
    //       console.log(err);
    //     });
    // },
    cancelEM(){
        console.log(**********);
        this.selectAmmeterModel = false;
    }, 
    commitEM() {
      let dataL = this.$refs.ammeterTable.getSelection();
      console.log(dataL, "dataL6666666666666666");
      if(dataL.length != 1) {
        // this.$Message.error("只能选择一个电表");
        this.errorTips("只能选择一个电表");
      }else {
        this.$emit("getInfoJZ", dataL);
        this.selectAmmeterModel = false;
      }
      
    },
    initAmmeter(obj) {
      this.ammeterObj = {
        ammeterName: "", //电表/协议编号
        company: "", //所属分公司
        country: "", //所属部门
        accountType: "", //台账类型
        accountno: "", //期号
        accountestype: null,
        isentityammeter: null,
      };
      this.ammeterObj.company = this.company;
      this.ammeterObj.country = Number(this.country);
      this.ammeterObj.countryName = this.countryName;
      if (obj != null) {
        this.ammeterObj.accountType = obj.accountType;
        this.ammeterObj.accountno = obj.accountno;
        this.ammeterObj.accountestype = obj.accountestype;
        this.ammeterObj.isentityammeter = obj.isentityammeter;
        this.pageNum = 1;
        this.getAccountMessages();
        this.selectAmmeterModel = true;
      }
    },
    onModalOK() {
      let rows = this.$refs.ammeterTable.getSelection();
      this.$emit("listenToSelectAmmeter", rows);
    },
    onModalCancel() {
      this.selectAmmeterModel = false;
    },
    handlePage(value) {
      this.pageNum = value;
      this.getAccountMessages();
    },
    handlePageSize(value) {
      this.pageSize = value;
      this.getAccountMessages();
    },
    //向后台请求数据
    // getAccountMessages() {
    //   if (this.ammeterObj.countryName == "") {
    //     this.ammeterObj.country = "-1";
    //   }
    //   console.log(this.ammeterObj, "getAccountMessages=============this.ammeterObj");
    //   let params = this.ammeterObj;
    //   params.pageNum = this.pageNum;
    //   params.pageSize = this.pageSize;
    //   let req = {
    //     url: "/business/accountEs/selectAmmeterByCompany",
    //     method: "get",
    //     params: params,
    //   };
    //   this.ammeterTb.loading = true;
    //   axios
    //     .request(req)
    //     .then((res) => {
    //       this.ammeterTb.loading = false;
    //       if (res.data) {
    //         this.insideData = res.data.rows;
    //         console.log(this.insideData, "this.insideData7777777777777");
    //         this.pageTotal = res.data.total || 0;
    //       }
    //     })
    //     .catch((err) => {
    //       console.log(err);
    //     });
    // },
    //复选框全选
    handleSelectAll(status) {
      this.$refs.ammeterTable.selectAll(status);
    },
    //重置
    onResetHandle() {
      this.$refs["ammeterForm"].resetFields();
      this.ammeterObj.company = this.company;
      this.ammeterObj.country = Number(this.country);
      this.ammeterObj.countryName = this.countryName;
        this.ammeterObj = {
        ammeterName: "", //电表/协议编号
        projectName: "", //项目名称
      };
      this.getAccountMessages();
    },
    selectChange2() {
      let that = this;
      if (that.ammeterObj.company != undefined) {
        if (that.ammeterObj.company == "") {
          that.ammeterObj.country = "";
          that.ammeterObj.countryName = "";
        } else {
          that.ammeterObj.countryName = "";
          getCountryByUserId(that.ammeterObj.company).then((res) => {
            if (res.data.departments.length != 0) {
              that.ammeterObj.country = res.data.departments[0].id;
              that.ammeterObj.countryName = res.data.departments[0].name;
            }
          });
        }
      }
    },
    //选择所属部门开始
    chooseResponseCenter() {
      if (this.ammeterObj.company == null || this.ammeterObj.company == "") {
        this.$Message.info("请先选择分公司");
        return;
      }
      this.$refs.countryModal.choose(this.ammeterObj.company); //所属部门
    },
    getUserData() {
      let that = this;
      getUserdata().then((res) => {
        //当前登录用户所在公司和所属部门
        if (res.data.companies.length != 0) {
          let companies = res.data.companies;
          if (res.data.companies[0].id == "2600000000") {
            companies = that.companies;
          }
          that.company = companies[0].id;
          that.ammeterObj.company = companies[0].id;
        }
        if (res.data.departments.length != 0) {
          let departments = res.data.departments;
          if (
            res.data.companies[0].id == "2600000000" &&
            that.departments.length != 0
          ) {
            departments = that.departments;
          }
          that.country = departments[0].id;
          that.countryName = departments[0].name;
          that.ammeterObj.country = Number(departments[0].id);
          that.ammeterObj.countryName = departments[0].name;
        }
      });
    },
    getDataFromModal(data) {
      this.ammeterObj.country = Number(data.id);
      this.ammeterObj.countryName = data.name;
      this.pageNum = 1;
      this.getAccountMessages();
    },
    },
    mounted() {
      let that = this;
      that.version = indexData.version;
      that.getInfoJZ();
    //   switch (indexData.version) {
    //     case "sc":
    //       this.ammeterTb.columns = this.ammeterTb.headColumn
    //         .concat(this.ammeterTb.scColumn)
    //         .concat(this.ammeterTb.tailColumn);
    //       break;
    //     case "ln":
    //       this.ammeterTb.columns = this.ammeterTb.headColumn
    //         .concat(this.ammeterTb.lnColumn)
    //         .concat(this.ammeterTb.tailColumn);
    //       break;
    //   }
  
      getUserByUserRole().then((res) => {
        //根据权限获取分公司
        that.companies = res.data.companies;
        if (
          res.data.isCityAdmin == true ||
          res.data.isProAdmin == true ||
          res.data.isSubAdmin == true
        ) {
          that.isAdmin = true;
        }
        getCountrysdata({ orgCode: res.data.companies[0].id }).then((res) => {
          //根据权限获取所属部门
          that.departments = res.data;
          that.getUserData();
        });
      });
  
      window.onresize = () => {
        return (() => {
          window.screenWidth = document.body.clientWidth;
          that.screenWidth = window.screenWidth * 0.7;
        })();
      };
      window.onresize();
    },
  };
  </script>
  
  <style scoped>
  </style>