package com.enrising.ctsc.carbon.common.utils;


import org.apache.poi.ss.formula.functions.T;

/**
 * 缓存查询操作后回调，主要处理缓存中无法查询到结果，改成从数据库检索
 *
 * <AUTHOR>
 */
public abstract class CacheHandler {
    protected Class<T> entityClass;
    private String sqlMapNamespace;

    /**
     * 业务KEY
     */
    private String busiKey;

    /**
     * 二级业务key
     */
    private String subBisiKey;


    private CacheHandler() {

    }
    /**
     * <p>Title: </p>
     * <p>Description: </p>
     *
     * @param busiKey busiKey
     */
    public CacheHandler(String busiKey) {
        this.busiKey = busiKey;
    }



    /**
     * <p>Title: 增加缓存查询的构造方法，支持二级key</p>
     * <p>Description: </p>
     *
     * @param busiKey busiKey
     */
    public CacheHandler(String busiKey, String subBisiKey) {
        this.busiKey = busiKey;
        this.subBisiKey = subBisiKey;
    }



    /**
     * 根据业务key查询业务数据信息
     *
     * @return 数据库查询结果
     */
    public abstract Object getDataFromDB();





    /**
    * 获取 mapper名称
    * @return java.lang.String
    */
    public String getSqlMapNamespace() {
        this.sqlMapNamespace = entityClass.getSimpleName() + "Mapper";
        return sqlMapNamespace;
    }

    public String getBusiKey() {
        return busiKey;
    }

    public void setBusiKey(String busiKey) {
        this.busiKey = busiKey;
    }

    public String getSubBisiKey() {
        return subBisiKey;
    }

    public void setSubBisiKey(String subBisiKey) {
        this.subBisiKey = subBisiKey;
    }

    public Class<T> getEntityClass() {
        return entityClass;
    }
}
