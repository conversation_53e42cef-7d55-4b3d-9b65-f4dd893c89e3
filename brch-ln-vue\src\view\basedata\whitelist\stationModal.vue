<template>
  <!-- *****选择局站信息 - 添加电表 <AUTHOR> -->
    <div>
    <Modal v-model="stationModel" :width="screenWidth" :styles="{top: '20px'}" title="选择局站信息" @on-ok="onModalOK" @on-cancel="onModalCancel">
       <p slot="header" style="top: 20px">
<!--        <Icon type="ios-information-circle"></Icon>-->
        <span>选择局站信息</span>
         <Icon type="ios-information-circle"></Icon>
         <span style="color:#f60">注意：如找不到基站站址，请上报市无线站址有效性清单，导入后<Button type="primary" size="small" @click="chooseLteStation">查找</Button></span>

      </p>

      <cl-table ref="station" v-if="stationModel"
                  :height="350"
                  :url="url"
                  :query-params="queryparams"
                  :searchLayout="station.filter"
                  :columns="station.columns"
                  :data="station.data"
                  :loading="station.loading"
                  selectEnabled
                  :exportable="false"
                  :total="station.total"
                  :pageSize="station.pageSize"
                  @on-row-dblclick="chooseOk"
        >
        </cl-table>
    </Modal>
    <Modal
            v-model="modal1"
            title="温馨提示"
            okText="是"
            cancelText="否"
            @on-ok="okModel"
            @on-cancel="cancelModel">
        <p style="margin: 25px 0 25px 40px">是否新型室分?</p>
    </Modal>
<!--      <stationselectfromlte ref="ltestationSelect" v-on:getDataFromlteStationModal="getDataFromlteStationModal"></stationselectfromlte>-->
    </div>
</template>

<script>
    import {listStation,checkAmmeterByStation} from '@/api/basedata/ammeter.js'
    import {blist} from "@/libs/tools";
    import stationselectfromlte from "@/view/basedata/ammeter/all/stationselectfromlte";
    export default {
        name: "stationList",
      components:{stationselectfromlte},
        data() {
            //局站状态
            let renderStationStatus = (h, params) => {
                let value = "";
                for (let item of this.stationStatus) {
                    if (item.typeCode == params.row.status) {
                        value = item.typeName;
                        break;
                    }
                }
                return h("div", value);
            };
            let renderStationIfTimeOut = (h, params) => {
                let value = "";
              let color = "";
                    if ( params.row.map.iftimeout == '1' ) {
                      if(params.row.map.endtime!=null&&params.row.map.endtime!=''){
                        value = params.row.map.endtime;
                        color = "green"
                      }else{
                        value = "不过期"
                        color = "green"
                      }
                    }else if( params.row.map.iftimeout == '3' ){
                      value = params.row.map.endtime+'(已到期)'
                      color = "red"
                    }else if( params.row.map.iftimeout == '2' ){
                      value = params.row.map.endtime+'(即将到期)';
                      color = "red"
                    }
                return h("div",{
                  style:{
                    color:color
                  }
                }, value);
            };
            let renderCode = (h, params) => {
                let value = "";
                //只有当局站类型为‘生产用房-移动基站’且产权为‘租用’时，存放站址编码
                if (params.row.stationtype == 10002 && params.row.propertyright == 3) {
                    value = params.row.resstationcode;
                }
                return h("div", value);
            };
            return {
                modal1:false,
                modal4:false,
                rows:null,
                errorMessage:'',
                queryparams: {type:0,stationtype:null,company:null},
                stationModel: false,
                screenWidth:200,
                url:null,
                type:0,
                ammeterid:null,
                electrotype:null,
                ammeteruse:null,
                stationStatus:[],
                station: {
                    loading: false,
                    filter: [
                        {
                            formItemType: 'input',
                            prop: 'resstationcode',
                            label: "资源局站/房屋/站址编码",
                            labelWidth: 180,
                            width:150
                        },
                        {
                            formItemType: 'input',
                            prop: 'stationname',
                            label: "局(站)名称",
                            labelWidth: 120,
                            width:150
                        }

                    ],
                    columns: [
                        {
                            title: '局(站)名称',
                            align: 'center',
                            key: 'stationname',
                        },
                        {
                            title: '是否关联资源',
                            key: 'statusname',
                            align: 'center',
                            width:'100px',
                            render: (h, params) => {
                                const row = params.row;
                                const text = row.resstationname == null ? '否' : '是';
                                return  h('span', {},text);
                            },
                        },
                        {
                            title: '局(站)类',
                            align: 'center',
                            key:'stationtypename',
                        },
                        {
                            title: '局(站)状态',
                            align: 'center',
                            render: renderStationStatus,
                            width:'90px'
                        },
                        {
                            title: '局(站)地址',
                            key: 'address',
                            align: 'center',
                        },
                        {
                            title: '资源局站/房屋/站址编码',
                            align: 'center',
                            key: 'resstationcode',
                        },
                        /*<!-- wait*****局站是否到期  <AUTHOR> -->*/
                        {
                            title: '局站是否到期',
                            align: 'center',
                            render: renderStationIfTimeOut,
                        },
                        ],
                    data: [],
                    total: 0,
                    pageSize: 10
                }

            }
        },
        methods: {
            okModel(){
                //不验证个数
                this.$emit("getDataFromStationModal", this.rows,false,true);
                this.onModalCancel();
            },
            cancelModel(){
                this.$Modal.warning({title: "温馨提示", content: this.errorMessage});
            },
            /**
             * 根据条件进行查询有效电表/协议
             */
            initDataList(electrotype,type,ammeteruse,company,params) {
                this.queryparams.stationname = null;
                this.queryparams.resstationcode = null;

                this.type = type;
                this.ammeteruse = ammeteruse;
                this.queryparams.company = company;
                this.queryparams.stationtype = electrotype;
                this.electrotype = electrotype;
                if(params != undefined && params.row.id != null){
                    this.queryparams.stationtype = params.row.id;
                    this.queryparams.type = 1;
                }
                this.stationStatus = blist("stationStatus");//局站类型
                this.url = "business/stationInfo/getCheckListByAmmeter";
                this.stationModel = true;
            },

            chooseOk(currentRow) {
                this.checkData(currentRow);
            },
            onModalOK() {
                let rows = this.$refs.station.getSelection();
                this.checkData(rows);
            },
          chooseLteStation()
          {

            this.$refs.ltestationSelect.initDataList();
            this.stationModel=false;
          },
            checkData(rows){
                this.rows = rows;
                let that = this;
                if(rows.id == undefined || rows.id == null){
                    this.$Message.info("请至少选择一行");
                }else if(!rows.address || rows.address.length == 0 ){
                    this.$Message.info("局站地址不能为空，请维护局站信息！");
                }else{
                    checkAmmeterByStation({id:this.ammeterid,type:this.type,electrotype:this.electrotype,stationcode:rows.id,ammeteruse:this.ammeteruse}).then(res => {
                        let code = res.data.code;
                        if (code == "error") {
                            this.errorMessage = res.data.msg;
                            //一个局站关联实际报账电表/协议最多5个的限制优化：局站关联的实际报账电表/协议当大于了5个，
                            // 局站类型又是“生产用房-移动基站”类时，弹出提示框：是否新型室分。如果选“是”，不校验局站关联实际报账电表/协议个数。
                            if(null != rows.stationtype && rows.stationtype== 10002 && res.data.flag5){
                                this.modal1 = true;
                            }else{
                                this.$Modal.warning({title: "温馨提示", content: res.data.msg});
                            }
                        }else if(code == "success"){
                            this.$Modal.warning({title: "温馨提示", content: res.data.msg});
                            this.$emit("getDataFromStationModal", rows,true);
                            this.onModalCancel();
                        }else{
                            this.$emit("getDataFromStationModal", rows,true);
                            this.onModalCancel();
                        }
                    });
                }
            },
            onModalCancel() {
                this.stationModel = false;
            },
        },
        mounted() {
            const that = this;
            window.onresize = () => {
                return (() => {
                    window.screenWidth = document.body.clientWidth;
                    that.screenWidth = window.screenWidth * 0.7;
                })();
            };
            window.onresize();
        }
    }
</script>
