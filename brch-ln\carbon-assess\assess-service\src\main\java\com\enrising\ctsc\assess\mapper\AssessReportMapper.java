package com.enrising.ctsc.assess.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.assess.api.entity.AssessReport;
import com.enrising.ctsc.assess.api.query.AssessReportQuery;
import com.enrising.ctsc.assess.api.vo.AssessReportVo;
import com.enrising.ctsc.carbon.common.entity.CarbonAttachment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 考核报告
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Mapper
public interface AssessReportMapper extends BaseMapper<AssessReport> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param query 条件
	 * @return 列表
	 */
	IPage<AssessReportVo> findList(@Param("page") Page<AssessReportVo> page, @Param("query") AssessReportQuery query);

	/**
	 * 详情查询
	 *
	 * @param wrapper 条件
	 * @return 结果
	 */
	AssessReportVo detail(@Param(Constants.WRAPPER) Wrapper<AssessReportQuery> wrapper);

	int saveCarbonAttachment(CarbonAttachment carbonAttachment);

    CarbonAttachment getAttachmentById(Long id);
}