package com.enrising.ctsc.assess.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 考核计算规则
 */
@Getter
@AllArgsConstructor
public enum RuleJudge {
	/***/
	GREATER_THAN("1", ">"),
	BE_EQUAL_OR_GREATER_THAN("2", ">="),
	LESS_THAN("3", "<"),
	LESS_THAN_OR_EQUAL_TO("4", "<="),
	EQUAL_TO("5", "="),
	;
	private final String value;
	private final String name;

	public static String getNameByValue(String value) {
		for (RuleJudge rule : RuleJudge.values()) {
			if (Objects.equals(rule.getValue(), value)) {
				return rule.getName();
			}
		}
		return null;
	}
}
