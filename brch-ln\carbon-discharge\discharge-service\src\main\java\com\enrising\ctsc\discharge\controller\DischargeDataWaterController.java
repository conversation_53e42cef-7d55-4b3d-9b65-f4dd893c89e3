package com.enrising.ctsc.discharge.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.discharge.api.bo.DischargeDataWaterBo;
import com.enrising.ctsc.discharge.api.vo.DischargeDataWaterVo;
import com.enrising.ctsc.discharge.service.DischargeDataWaterService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 碳排放数据填报表（水）
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-01-03
 */
@RestController
@RequestMapping("/discharge/water")
@AllArgsConstructor
public class DischargeDataWaterController {
	private final DischargeDataWaterService dischargeDataWaterService;

	@PostMapping("/getWaterListPage")
		public R<Page<DischargeDataWaterVo>> getWaterListPage(@RequestBody QueryPage<DischargeDataWaterBo> queryPage) {
		return R.success(dischargeDataWaterService.getWaterListPage(queryPage));
	}

	@PostMapping("/getWaterListToExcel")
		public R<List<DischargeDataWaterVo>> getWaterListToExcel(@RequestBody DischargeDataWaterBo dischargeDataWaterBo) {
		return R.success(dischargeDataWaterService.getWaterListToExcel(dischargeDataWaterBo));
	}

	@GetMapping("/getDataList")
		public R<List<DischargeDataWaterVo>> getDataList(Integer dataYear, Long companyId) {
		return R.success(dischargeDataWaterService.getDataList(dataYear, companyId));
	}

	@GetMapping("/detail")
		public R<DischargeDataWaterVo> get(Long id) {
		DischargeDataWaterVo detail = dischargeDataWaterService.detail(id);
		return R.success(detail, "查询成功");
	}

		@PostMapping(value = "/save")
		public R<String> save(@RequestBody DischargeDataWaterBo bo) {
		String sRet = dischargeDataWaterService.add(bo);
		if (StrUtil.isBlank(sRet)){
			return R.success("保存成功");
		}
		return R.failed(sRet);
	}

		@PostMapping(value = "/update")
		public R<String> update(@RequestBody DischargeDataWaterBo bo) {
		dischargeDataWaterService.edit(bo);
		return R.success("修改成功");
	}

		@PostMapping(value = "/delete/{id}")
		public R<String> delete(@PathVariable Long id) {
		dischargeDataWaterService.del(id);
		return R.success("删除成功");
	}
}
