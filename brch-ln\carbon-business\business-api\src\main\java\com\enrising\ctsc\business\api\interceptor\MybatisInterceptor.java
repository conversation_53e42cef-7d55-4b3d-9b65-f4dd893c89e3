package com.enrising.ctsc.business.api.interceptor;

import com.enrising.ctsc.carbon.common.entity.User;
import com.enrising.ctsc.carbon.common.utils.JwtUtils;
import com.sccl.common.constant.CommonConstants;
import com.sccl.common.constant.enums.DelFlagEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Date;

@Slf4j
@Component
@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})})
public class MybatisInterceptor implements Interceptor {

	@Override
	public Object intercept(Invocation invocation) throws Throwable {
		User user = JwtUtils.getUser();
		MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
		SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
		Object parameter = invocation.getArgs()[1];
		if (parameter == null) {
			return invocation.proceed();
		}
		String entitySimpleName = parameter.getClass().getSimpleName();
		if (SqlCommandType.INSERT == sqlCommandType) {
			if (log.isInfoEnabled()) {
				log.info("当前实体类:{},进行插入操作!", entitySimpleName);
			}
			Field[] fields = parameter.getClass().getDeclaredFields();
			for (Field field : fields) {
				try {
					field.setAccessible(true);
//					if (CommonConstants.ID.equals(field.getName())) {
//						Object id = field.get(parameter);
//						// 强制设置id
//						if(ObjectUtil.isNull(id)){
//							id = IdWorker.getId();
//							log.info("当前实体类:{},设置的id:{}!", entitySimpleName, id);
//						}else {
//							log.info("当前实体类:{},自定义id:{}!", entitySimpleName, id);
//						}
//						if (field.getType().isAssignableFrom(String.class)) {
//							field.set(parameter, id + "");
//						} else if (field.getType().isAssignableFrom(Long.class)) {
//							field.set(parameter, id);
//						}
//					}
					if (CommonConstants.CREATE_BY.equals(field.getName()) || CommonConstants.CREATOR_ID.equals(field.getName())) {
						Object createBy = field.get(parameter);
						if (createBy == null || "".equals(createBy)) {
							if (user != null) {
								if (field.getType().isAssignableFrom(String.class)) {
									field.set(parameter, user.getId().toString());
								} else if (field.getType().isAssignableFrom(Long.class)) {
									field.set(parameter, user.getId());
								}
							}
						}
					}
					if (CommonConstants.CREATOR_NAME.equals(field.getName())) {
						Object creatorName = field.get(parameter);
						if (creatorName == null || "".equals(creatorName)) {
							if (user != null) {
								field.set(parameter, user.getUserName());
							}
						}
					}
					if (CommonConstants.CREATE_TIME.equals(field.getName())) {
						Object createTime = field.get(parameter);
						if (createTime == null || "".equals(createTime)) {
							setDateTime(field, parameter);
						}
					}
					if (CommonConstants.DEL_FLAG.equals(field.getName()) || CommonConstants.DELETE_FLAG.equals(field.getName())) {
						field.set(parameter, DelFlagEnum.NOT_DELETED.getValue());
					}
					field.setAccessible(false);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		if (SqlCommandType.UPDATE == sqlCommandType) {
			Field[] fields;
			if (parameter instanceof MapperMethod.ParamMap) {
				MapperMethod.ParamMap<?> p = (MapperMethod.ParamMap<?>) parameter;
				//update-begin-author:scott date:20190729 for:批量更新报错issues/IZA3Q--
				if (p.containsKey("et")) {
					parameter = p.get("et");
				} else {
					parameter = p.get("param1");
				}
				//update-end-author:scott date:20190729 for:批量更新报错issues/IZA3Q-
				//update-begin-author:scott date:20190729 for:更新指定字段时报错 issues/#516-
				if (parameter == null) {
					return invocation.proceed();
				}
				//update-end-author:scott date:20190729 for:更新指定字段时报错 issues/#516-
				fields = parameter.getClass().getDeclaredFields();
			} else {
				fields = parameter.getClass().getDeclaredFields();
			}
			for (Field field : fields) {
				try {
					field.setAccessible(true);
					if (CommonConstants.UPDATE_BY.equals(field.getName())) {
						if (field.getType().isAssignableFrom(String.class)) {
							field.set(parameter, user.getId().toString());
						} else if (field.getType().isAssignableFrom(Long.class)) {
							field.set(parameter, user.getId());
						}
					}
					if (CommonConstants.UPDATER_NAME.equals(field.getName())) {
						field.set(parameter, user.getUserName());
					}
					if (CommonConstants.UPDATE_TIME.equals(field.getName())) {
						setDateTime(field, parameter);
					}
					field.setAccessible(false);
				} catch (Exception e) {
					log.error("设置通用属性失败", e);
				}
			}
		}
		return invocation.proceed();
	}

	private void setDateTime(Field field, Object parameter) throws IllegalAccessException {
		String fieldType = field.getType().getSimpleName();
		if (fieldType.startsWith("Local")) {
			field.set(parameter, LocalDateTime.now());
		} else {
			field.set(parameter, new Date());
		}
	}

	@Override
	public Object plugin(Object target) {
		return Plugin.wrap(target, this);
	}


}
