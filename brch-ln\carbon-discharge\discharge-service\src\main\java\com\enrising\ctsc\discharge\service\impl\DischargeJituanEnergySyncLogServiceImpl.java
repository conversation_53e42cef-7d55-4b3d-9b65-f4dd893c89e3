package com.enrising.ctsc.discharge.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.discharge.api.entity.DischargeJituanEnergySyncLog;
import com.enrising.ctsc.discharge.api.query.DischargeJituanEnergySyncLogQuery;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.vo.DischargeJituanEnergySyncLogVo;
import com.enrising.ctsc.discharge.mapper.DischargeJituanEnergySyncLogMapper;
import com.enrising.ctsc.discharge.service.DischargeJituanEnergySyncLogService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 集团能耗同步日志
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2024-01-03
 */
@Service
@AllArgsConstructor
public class DischargeJituanEnergySyncLogServiceImpl extends ServiceImpl<DischargeJituanEnergySyncLogMapper, DischargeJituanEnergySyncLog> implements DischargeJituanEnergySyncLogService {

    @Override
    public TableDataInfo<DischargeJituanEnergySyncLogVo> findList(Page<DischargeJituanEnergySyncLogVo> page, DischargeJituanEnergySyncLogQuery query) {
        IPage<DischargeJituanEnergySyncLogVo> resultPage = baseMapper.findList(page, query);
        return TableDataInfo.build(resultPage);
    }
//    @Override
//    public DischargeJituanEnergySyncLogVo detail(DischargeJituanEnergySyncLogQuery query) {
//        if (ObjectUtil.allFieldIsNull(query)) {
//            throw new BusinessException("查询参数不能为空");
//        }
//        return baseMapper.selectJoinOne(DischargeJituanEnergySyncLogVo.class, wrapper);
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void add(DischargeJituanEnergySyncLogBo bo) {
//        DischargeJituanEnergySyncLog entity = new DischargeJituanEnergySyncLog();
//        BeanUtils.copyProperties(bo, entity);
//        baseMapper.insert(entity);
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void edit(DischargeJituanEnergySyncLogBo bo) {
//        if (bo.getId() == null) {
//            throw new BusinessException("id不能为空");
//        }
//        DischargeJituanEnergySyncLog entity = new DischargeJituanEnergySyncLog();
//        BeanUtils.copyProperties(bo, entity);
//        baseMapper.updateById(entity);
//    }
//
//    @Override
//    public void del(Long id) {
//        baseMapper.deleteById(id);
//    }

}
