FROM yd-artifact.srdcloud.cn/ctsichuan-lshare-docker-mc/k8s_18883991817_15411-openjdk-alpine-pp2:1.11
ARG JAR_FILE=target/business-service.jar
WORKDIR /app
COPY ${JAR_FILE} app.jar
ENV TZ=Asia/Shanghai JAVA_OPTS="-Xms128m -Xmx256m" SPRING_BOOT_PROFILE="prod-ln"
EXPOSE 10060
ENTRYPOINT java -jar app.jar ${JAVA_OPTS} --spring.profiles.active=${SPRING_BOOT_PROFILE}


#FROM yd-artifact.srdcloud.cn/ctsichuan-lshare-docker-mc/k8s_18883991817_15411-openjdk-alpine-pp2:1.11
#ADD ./docker/hlog/client.keystore.jks /app/hlog
#ADD ./docker/hlog/client.truststore.jks /app/hlog
#ADD ./docker/startup.sh /app/
#ADD target/business-service.jar /app/
#
#RUN chmod a+x /app/startup.sh
#WORKDIR /app/
## 修改docker时区为东八区，规避应用程序和北京时间相差8小时问题
#ENV TZ=Asia/Shanghai
#EXPOSE 10060
#CMD ["/bin/sh", "-c", "/app/startup.sh"]
