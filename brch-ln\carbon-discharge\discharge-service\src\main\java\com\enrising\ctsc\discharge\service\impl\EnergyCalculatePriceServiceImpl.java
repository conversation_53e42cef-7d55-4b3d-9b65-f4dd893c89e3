package com.enrising.ctsc.discharge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.discharge.api.entity.EnergyCalculatePrice;
import com.enrising.ctsc.discharge.mapper.EnergyCalculatePriceMapper;
import com.enrising.ctsc.discharge.service.EnergyCalculatePriceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 能源单价表 服务实现
 *
 * <AUTHOR>
 * @since 2024-09-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EnergyCalculatePriceServiceImpl extends ServiceImpl<EnergyCalculatePriceMapper, EnergyCalculatePrice> implements EnergyCalculatePriceService {

    @Override
    public EnergyCalculatePrice getLastOne(EnergyCalculatePrice query) {
        return baseMapper.getLastOne(query);
    }
}
