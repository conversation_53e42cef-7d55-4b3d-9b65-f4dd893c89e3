<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.EnergyCountDataMapper">

	<!-- 表字段 -->
	<sql id="baseColumns">
		t.id,
		t.company_id,
		t.report_time,
		t.energy_indicator_id,
		t.count_type,
		t.group_data,
		t.stock_data,
		t.amount,
		t.del_flag
	</sql>
	<select id="getIndicatorIdByCode"  resultType="java.lang.Long">
		SELECT
			id
		FROM discharge_energy_indicator_new
		<where>
			del_flag='0'
			AND status='1'
			<if test="indicatorCode != null and indicatorCode != ''">
				AND indicator_code = #{indicatorCode}
			</if>
		</where>
		limit 1
	</select>
	<select id="selectListByBudgetSetName"  resultType="java.util.HashMap">
		SELECT
		    DISTINCT
			ma.ID,
			ma.COMPANY_CODE,
			ma.BILLTYPE,
			pap.billtype preBillType,
			ma.BUDGETSETNAME,
			ma.COMPANY_NAME_TXT,
			ma.`STATUS`
		FROM mss_accountbill ma
		LEFT JOIN power_accountbillpre pap on ma.ID = pap.pabid
		LEFT JOIN mss_accountbillitem msi on ma.ID = msi.WRITEOFF_INSTANCE_ID
		<where>
			ma.`STATUS`='7'
			AND msi.BUDGET_TYPE = 1
			<if test="query.companyCode != null and query.companyCode != ''">
				AND ma.COMPANY_CODE = #{query.companyCode}
			</if>
			<if test="query.YEAR != null and query.YEAR != ''">
				AND ma.`YEAR` = #{query.YEAR}
			</if>
			<if test="query.BIZ_ENTRY_CODE != null and query.BIZ_ENTRY_CODE != ''">
				AND ma.BIZ_ENTRY_CODE = #{query.BIZ_ENTRY_CODE}
			</if>
		</where>
	</select>
	<select id="getAccountBaseByMss"  resultType="com.enrising.ctsc.discharge.api.entity.AccountBaseResult">
		SELECT
			SUM( mrba.money ) AS accountmoney,
			SUM(
					ROUND(
								pa.totalusedreadings * ((
															IFNULL( mrba.money, 0 )) / ( IFNULL( pa.accountmoney, 0 ) - IFNULL( pa.taxamount, 0 ) )),
								2
						)) AS curusedreadings,
			pam.electrotype
		FROM
			mss_r_billitem_account mrba
				LEFT JOIN power_account pa ON pa.pcid = mrba.account_id
				LEFT JOIN power_ammeterorprotocol pam ON pa.ammeterid = pam.id
		WHERE
				mrba.bill_id IN (
				SELECT DISTINCT
					ROUND( ma.ID )
				FROM
					mss_accountbill ma
						LEFT JOIN power_accountbillpre pap ON ma.ID = pap.pabid
						LEFT JOIN mss_accountbillitem msi ON ma.ID = msi.WRITEOFF_INSTANCE_ID
				WHERE
					ma.`STATUS` = '7'
				  AND msi.BUDGET_TYPE = 1
				<if test="query.YEAR != null and query.YEAR != ''">
				  AND ma.`YEAR` = #{query.YEAR}
				</if>
				<if test="query.BIZ_ENTRY_CODE != null and query.BIZ_ENTRY_CODE != ''">
				  AND ma.BIZ_ENTRY_CODE = #{query.BIZ_ENTRY_CODE}
				</if>
				<if test="query.COMPANY_CODE != null and query.COMPANY_CODE != ''">
				  AND ma.COMPANY_CODE = #{query.COMPANY_CODE}
				</if>
				<if test="query.COMPANY_NAME_TXT != null and query.COMPANY_NAME_TXT != ''">
				  AND ma.COMPANY_NAME_TXT = #{query.COMPANY_NAME_TXT}
				</if>
				)
		GROUP BY
			electrotype
	</select>
	<select id="getAccountBaseByBillId"  resultType="com.enrising.ctsc.discharge.api.entity.AccountBaseResult">
		SELECT
			mrba.money AS accountmoney,
			pa.pcid,
			ROUND( pa.totalusedreadings * (( IFNULL(mrba.money, 0)) / ( IFNULL(pa.accountmoney, 0) - IFNULL(pa.taxamount, 0) )), 2 ) AS curusedreadings,
			pam.category,
			pam.electrotype,
			pam.ammetertype,
			pa.ammeterid,
			pam.paytype,
			pa.accountno,
			pa.company,
			pam.property
		FROM
			mss_r_billitem_account mrba
				LEFT JOIN power_account pa ON pa.pcid = mrba.account_id
				LEFT JOIN power_ammeterorprotocol pam ON pa.ammeterid = pam.id
		WHERE
			mrba.bill_id = #{billId}
	</select>
	<select id="getAccountEsBaseByBillId"  resultType="com.enrising.ctsc.discharge.api.entity.AccountBaseResult">
		SELECT
			mrba.money AS accountmoney,
			pa.pcid,
			ROUND( pa.totalusedreadings * (( IFNULL(mrba.money, 0)) / ( IFNULL(pa.accountmoney, 0) - IFNULL(pa.taxamount, 0) )), 2 ) AS curusedreadings,
			pam.category,
			pam.electrotype,
			pam.ammetertype,
			pa.ammeterid,
			pam.paytype,
			pa.accountno,
			pa.company,
			pam.property
		FROM
			mss_r_billitem_account mrba
				LEFT JOIN power_account_es pa ON pa.pcid = mrba.account_id
				LEFT JOIN power_ammeterorprotocol pam ON pa.ammeterid = pam.id
		WHERE
			mrba.bill_id = #{billId}
	</select>
	<select id="getAccountThermalBaseByBillId"  resultType="com.enrising.ctsc.discharge.api.entity.AccountBaseResult">
		SELECT
			ha.id as pcid,
			ha.heat_amount as curusedreadings,
			ha.paid_money as accountmoney,
			1900 as electrotype,
			ha.account_no,
			ha.company
		from heat_account ha
		<where>
			ha.id in (
				SELECT
				DISTINCT account_id
				FROM mss_r_billitem_account
				WHERE bill_id = #{billId}
			)
		</where>
	</select>
	<select id="getAccountCoalBaseByBillId"  resultType="com.enrising.ctsc.discharge.api.entity.AccountBaseResult">
		SELECT
			ca.id as pcid,
			ca.coal_amount as curusedreadings,
			ca.paid_money as accountmoney,
			ca.coal_type as category,
			ca.coal_use as ammetertype,
			2000 as electrotype,
			ca.accountno,
			ca.company
		from coal_account ca
		<where>
			ca.id in (
				SELECT
				DISTINCT account_id
				FROM mss_r_billitem_account
				WHERE bill_id = #{billId}
			)
		</where>
	</select>
	<select id="getAccountOilBaseByBillId"  resultType="com.enrising.ctsc.discharge.api.entity.AccountBaseResult">
		SELECT
			oa.id as pcid,
			oa.oil_amount as curusedreadings,
			oa.total_money as accountmoney,
			oa.oil_category as category,
			oa.oil_type as ammetertype,
			2100 as electrotype,
			oa.accountno,
			oa.company
		from oil_account oa
		<where>
			oa.id in (
				SELECT
				DISTINCT account_id
				FROM mss_r_billitem_account
				WHERE bill_id = #{billId}
			)
		</where>
	</select>
</mapper>