package com.enrising.ctsc.assess.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.assess.api.bo.AssessTemplateTargetObjectBo;
import com.enrising.ctsc.assess.api.bo.CheckAssessRuleBo;
import com.enrising.ctsc.assess.api.bo.DischargeDataTotalBo;
import com.enrising.ctsc.assess.api.entity.*;
import com.enrising.ctsc.assess.api.enums.*;
import com.enrising.ctsc.assess.api.feign.RemoteBusinessService;
import com.enrising.ctsc.assess.api.feign.RemoteDischargeService;
import com.enrising.ctsc.assess.api.vo.*;
import com.enrising.ctsc.assess.mapper.AssessTargetSecondaryMapper;
import com.enrising.ctsc.assess.mapper.AssessTemplateMapper;
import com.enrising.ctsc.assess.mapper.AssessTemplateTargetObjectMapper;
import com.enrising.ctsc.assess.service.AssessTaskReportAttachmentService;
import com.enrising.ctsc.assess.service.AssessTaskReportService;
import com.enrising.ctsc.assess.service.AssessTemplateTargetObjectService;
import com.enrising.ctsc.assess.service.AssessTemplateTargetService;
import com.enrising.ctsc.carbon.common.constant.CommonConstants;
import com.enrising.ctsc.carbon.common.entity.User;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.service.CommonService;
import com.enrising.ctsc.carbon.common.utils.*;
import com.enrising.ctsc.carbon.common.vo.IdNameVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 考核模板对象
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-08
 */
@Slf4j
@Service
@AllArgsConstructor
public class AssessTemplateTargetObjectServiceImpl extends ServiceImpl<AssessTemplateTargetObjectMapper, AssessTemplateTargetObject> implements AssessTemplateTargetObjectService {

	private final AssessTaskReportService assessTaskReportService;
	private final AssessTaskReportAttachmentService assessTaskReportAttachmentService;
	private final AssessTemplateTargetObjectMapper assessTemplateTargetObjectMapper;
	private final AssessTargetSecondaryMapper assessTargetSecondaryMapper;
	private final AssessTemplateTargetService assessTemplateTargetService;
	private final AssessTemplateMapper assessTemplateMapper;
	private final RemoteDischargeService remoteDischargeService;
	private final RemoteBusinessService remoteBusinessService;
	private final CommonService commonService;

	@Override
	public List<AssessTemplateTargetObjectVo> getTaskInfoByOrgId(AssessTemplateTargetObjectBo bo) {
		if (null == bo.getCompanyId()) {
            User user = JwtUtils.getUser();
            List<IdNameVo> companies = user.getCompanies();
            if (CollUtil.isNotEmpty(companies)) {
                bo.setCompanyId(Long.valueOf(companies.get(0).getId()));
            }
		}
		if (bo.getCompanyId() == 2600000000L) {
			// 最高部门查看所有
			bo.setCompanyId(null);
		}
		log.info("部门companyId:{}", bo.getCompanyId());
		AssessTemplate assessTemplate = assessTemplateMapper.selectById(bo.getTemplateId());
		List<AssessTemplateTargetObjectVo> voList = getTemplateAssessInfo(bo);
		log.info("模板对象数量:{}", voList.size());
		HashMap<Long, Double> map = new HashMap<>();
		// 计算是否上报
		calculateIsReport(voList, assessTemplate, map, 2);
		return voList;
	}

	@Override
	public List<AssessProvinceComVo> getCompanyInfoList(AssessTemplateTargetObjectBo bo) {
		// 获取这个模板拿到预警值
		AssessTemplate assessTemplate = assessTemplateMapper.selectById(bo.getTemplateId());
		// 获取某个模板下考核部门对应的考核分值
		List<AssessProvinceComVo> companyAssScoreList = assessTemplateTargetObjectMapper.getCompanyAssScoreList(bo);
		if (CollectionUtil.isEmpty(companyAssScoreList)) {
			return CollectionUtil.newArrayList();
		}
		// 设置预警值 1--绿色标识 2--橙色标识
		companyAssScoreList.forEach(node -> {
			if (node.getAssessScore() >= assessTemplate.getWarningValue()) {
				node.setType(1);
			} else {
				node.setType(2);
			}
		});
		return companyAssScoreList;
	}

	@Override
	public List<AssessTemplateTargetObjectVo> getTaskInfoByTemplateId(AssessTemplateTargetObjectBo bo) {
		User user = JwtUtils.getUser();
		List<AssessTemplateTargetObjectVo> list = new ArrayList<>();
		AssessTemplate assessTemplate = assessTemplateMapper.selectById(bo.getTemplateId());
//		todo 区分权限
//		if (SecurityUtils.isAdmin() || SecurityUtils.getRoleCodes().contains(SysRoleEnums.ROLE_PROVINCE_ADMIN.getValue())) {
			// 省级、超级管理员
			// 设置任务总分、考核得分
			list = assessTemplateTargetObjectMapper.getAllTaskByTemplateId(bo);
			System.out.println("===管理员任务===");
//		} else {
//			Long deptId = user.getDeptId();
//			if (SecurityUtils.getRoleCodes().contains(SysRoleEnums.ROLE_CITY_ADMIN.getValue())) {
//				// 地市管理员获取
//				bo.setCompanyId(deptId);
//			} else {
//				bo.setDeptId(deptId);
//			}
//			list = assessTemplateTargetObjectMapper.getTaskInfoByTemplateId(bo);
//			System.err.println("===地市任务===");
//		}
		list.forEach(System.err::println);
		System.err.println("===END===");
		if (CollectionUtil.isEmpty(list)) {
			return CollectionUtil.newArrayList();
		}
		// 计算考核得分
		List<AssessTemplateTargetObjectVo> scoreList = assessTemplateTargetObjectMapper.getAssessScoreByTemplateObjectIds(bo);
		HashMap<Long, Double> map = new HashMap<>();
		if (CollectionUtil.isNotEmpty(scoreList)) {
			scoreList.forEach(node -> map.put(node.getTemplateObjectId(), node.getAssessScore()));
		}
		// 若是主动上报 则需要系统自己计算
		calculate(list, bo.getCompanyId());
		// 计算是否上报
		calculateIsReport(list, assessTemplate, map, 1);
		return list;
	}

	private void calculateIsReport(List<AssessTemplateTargetObjectVo> list, AssessTemplate assessTemplate, HashMap<Long, Double> map, Integer type) {
		if(CollectionUtil.isEmpty(list)){
			return;
		}
		// 先拿到所有考核报告
		List<Long> collect = list.stream().map(AssessTemplateTargetObjectVo::getTemplateObjectId).collect(Collectors.toList());
		List<AssessTaskReport> assessTaskReportList = assessTaskReportService.list(new LambdaQueryWrapper<AssessTaskReport>().in(AssessTaskReport::getTemplateObjectId, collect));

		list.forEach(node -> {
			if (type == 1) {
				if (ReportType.MANUAL_REPORTING.getValue().equals(node.getAssessMethod())) {
					node.setAssessScore(ObjectUtil.isNull(map.get(node.getTemplateObjectId())) ? 0 : map.get(node.getTemplateObjectId()));
				}
			}
			// 当前指标有好多份报告
			List<AssessTaskReport> reportList = assessTaskReportList.stream().filter(item ->
							item.getTemplateObjectId().equals(node.getTemplateObjectId())
									&& ReportStatus.HAVE_REPORTED.getValue().equals(item.getReportStatus()))
					.collect(Collectors.toList());

			int reportType = 0;
			// 处理模板周期和指标周期 模板周期 >  指标周期
			if (Integer.parseInt(assessTemplate.getPeriod()) < Integer.parseInt(node.getAssessPeriod())) {
				String templatePeriod = assessTemplate.getPeriod();
				String targetPeriod = node.getAssessPeriod();
				// 模板为季度，指标为月，三份报告
				if (AssessTemplatePeriod.QUARTER.getValue().equals(templatePeriod)
						&& AssessTemplatePeriod.MONTH.getValue().equals(targetPeriod)
				) {
					// 则指标分值乘以4
					node.setScoreTotal(new BigDecimal(node.getScore()).multiply(new BigDecimal(4)));
					reportType = 3;
				}
				// 模板为年，指标为季度，四份报告。
				if (AssessTemplatePeriod.YEAR.getValue().equals(templatePeriod)
						&& AssessTemplatePeriod.QUARTER.getValue().equals(targetPeriod)
				) {
					// 则指标分值乘以4
					node.setScoreTotal(new BigDecimal(node.getScore()).multiply(new BigDecimal(4)));
					reportType = 4;
				}
				// 模板为年，指标为月，12份报告。。
				if (AssessTemplatePeriod.YEAR.getValue().equals(templatePeriod)
						&& AssessTemplatePeriod.MONTH.getValue().equals(targetPeriod)
				) {
					// 则指标分值乘以4
					node.setScoreTotal(new BigDecimal(node.getScore()).multiply(new BigDecimal(12)));
					reportType = 12;
				}
			} else {
				// 模板周期 <= 指标周期 一份报告。
				// 则指标分值 == 指标总分
				node.setScoreTotal(new BigDecimal(node.getScore()));
				reportType = 1;
			}
			if (!IsReportStatus.NO_NEED_REPORT.getValue().equals(node.getIsReport())) {
				node.setIsReport(reportType > reportList.size() ?
						IsReportStatus.NOT_REPORTED.getValue() :
						IsReportStatus.HAVE_REPORTED.getValue());
			}
		});
	}

	/**
	 * @param list      考核任务列表
	 * @param companyId 公司/部门id
	 *                  <p>
	 *                  Java实现AssessTemplateTargetObjectServiceImpl类计算目标对象的评估分数
	 *                  基于一定的规则和公式。它为需要自动报告的目标计算分数
	 *                  还计算与综合能源消耗(碳排放)相关的目标得分。
	 *                  最终分数在输入列表中相应的AssessTemplateTargetObjectVo对象中设置。
	 */
	private void calculate(List<AssessTemplateTargetObjectVo> list, Long companyId) {
		// 若是主动上报 则需要系统自己计算
		List<AssessTemplateTargetObjectVo> calculateList = list.stream().filter(item ->
						ReportType.AUTO_REPORTING.getValue().equals(item.getAssessMethod()))
				.collect(Collectors.toList());
		// 如果不是系统自评，结束
		if (CollectionUtil.isEmpty(calculateList)) {
			return;
		}
		// 计算公式 2、综合能耗（碳排放总量）增幅（16分）
		List<AssessTemplateTargetObjectVo> upList = list.stream().filter(node ->
						StrUtil.isNotBlank(node.getFormula()) &&
								AssessType.ENERGY_UP.getValue().equals(node.getFormula()))
				.collect(Collectors.toList());
		// 如果任务中不存在公式，结束
		if (CollectionUtil.isEmpty(upList)) {
			return;
		}
		List<Long> companyIds = new ArrayList<>();
		companyIds.add(companyId);
		List<DischargeMonitorSettingVo> remoteList = remoteDischargeService.getCarbonDownByTemplateId(companyIds);
		System.out.println("===考核计算列表1===");
		remoteList.forEach(System.out::println);
		System.out.println("===考核计算列表1=== END");
		// 拿到计算规则
		upList.forEach(vo -> {
			List<AssessTargetSecondaryRule> ruleList = new AssessTargetSecondaryRule().selectList(Wrappers.<AssessTargetSecondaryRule>lambdaQuery().eq(AssessTargetSecondaryRule::getSecondaryTargetId, vo.getSecondaryTargetId()));
			double finalScore = 0;
			// 公式：综合能耗（碳排放总量）增幅
			if (AssessType.ENERGY_UP.getValue().equals(vo.getFormula())) {
				log.info("计算公式：{}", AssessType.ENERGY_UP.getName());
				finalScore = this.getAutoCalScoreByEnergyUp(remoteList.get(0), ruleList);
			}
			// 公式：单位电信业务总量碳排放同比下降率
			else if (AssessType.CARBON_DOWN.getValue().equals(vo.getFormula())) {
				log.info("计算公式：{}", AssessType.CARBON_DOWN.getName());
				finalScore = this.getAutoCarbonDown(remoteList.get(0), ruleList);
			}

			AssessTemplateTargetObjectVo assessTemplateTargetObjectVo = list.stream().filter(item ->
					item.equals(vo)).findFirst().orElse(new AssessTemplateTargetObjectVo());
			assessTemplateTargetObjectVo.setAssessScore(finalScore);
		});
	}

	/**
	 * 1、单位电信业务总量碳排放同比下降率
	 */
	public double getAutoCarbonDown(DischargeMonitorSettingVo vo, List<AssessTargetSecondaryRule> ruleList) {
		// 计算 (今年碳排/今年业务总量 - 去年碳排/去年业务总量) / (去年碳排/去年业务总量)
		double score = 0.0;
		double rate = 0.0;
		// (今年碳排/今年业务总量 - 去年碳排/去年业务总量)

		// 今年碳排/今年业务总量
		BigDecimal denominator = BigDecimal.ZERO;
		// 去年碳排/去年业务总量
		BigDecimal subtract = BigDecimal.ZERO;
		if (vo.getCarbonTotal().compareTo(BigDecimal.ZERO) != 0) {
			denominator = vo.getCarbonTotal().divide(vo.getThisYearTelecomBusinessTotal(), 4, BigDecimal.ROUND_HALF_UP);
		}
		if (vo.getPreCarbonTotal().compareTo(BigDecimal.ZERO) != 0) {
			subtract = vo.getPreCarbonTotal().divide(vo.getLastYearTelecomBusinessTotal(), 4, BigDecimal.ROUND_HALF_UP);
		}
		log.info("今年碳排量/今年业务总量:{}/{}={} 去年碳排量/去年业务总量:{}/{}={}", vo.getCarbonTotal(), vo.getThisYearTelecomBusinessTotal(), denominator, vo.getPreCarbonTotal(), vo.getLastYearTelecomBusinessTotal(), subtract);
		// (今年碳排/今年业务总量 - 去年碳排/去年业务总量)
		BigDecimal subtract1 = denominator.subtract(subtract);
		log.info("今年-去年:{}", subtract1);
		if (subtract1.compareTo(BigDecimal.ZERO) != 0 && subtract.compareTo(BigDecimal.ZERO) != 0) {
			// (今年碳排/今年业务总量 - 去年碳排/去年业务总量) / (去年碳排/去年业务总量)
			rate = subtract1.divide(subtract, 2, BigDecimal.ROUND_HALF_UP).doubleValue();
			log.info("(今年-去年的结果)/去年={}", rate);
		}
		score = calculateScore(ruleList, rate);
		log.info("得分:{}", score);
		return score;
	}

	/**
	 * 2、综合能耗（碳排放总量）增幅（16分） 自动计算分数
	 */
	@Override
	public double getAutoCalScoreByEnergyUp(DischargeMonitorSettingVo vo, List<AssessTargetSecondaryRule> ruleList) {
		// 计算
		double score = 0.0;
		// 当年综合能耗（碳排放总量） − 上年综合能耗（碳排放总量） / 上年综合能耗（碳排放总量）
		BigDecimal subtract = vo.getCarbonTotal().subtract(vo.getPreCarbonTotal());
		log.info("{}-{}={}", vo.getCarbonTotal(), vo.getPreCarbonTotal(), subtract);
		double rate = 0;
		if (subtract.compareTo(new BigDecimal(0)) > 0) {
			// 保留两位小数 计算出百分率
			rate = subtract.divide(vo.getPreCarbonTotal().compareTo(new BigDecimal(0)) > 0 ? vo.getPreCarbonTotal() : new BigDecimal(1), 2, RoundingMode.HALF_UP).doubleValue();
		}
		score = calculateScore(ruleList, rate);
		return score;
	}

	@Override
	public HashMap<String, Object> getAssessRanking(AssessTemplateTargetObjectBo bo) {
		HashMap<String, Object> resultMap = new HashMap<>();
		if (ObjectUtil.isEmpty(bo) || ObjectUtil.isEmpty(bo.getTemplateId())) {
			throw new BusinessException("请求参数错误");
		}
		double averageScore = 0D;
		double maxScore = 0D;
		double minScore = 0D;
		double standScore = 0;
		double totalNum = 0;
		BigDecimal standardReachingRate = new BigDecimal(0);
		List<AssessRankVo> dataList = new ArrayList<>();
		List<String> labelList = new ArrayList<>();
		// 拿到模板
		AssessTemplate assessTemplate = assessTemplateMapper.selectById(bo.getTemplateId());
		//拿到公司列表
		List<CarbonRankingVo> companyList = remoteBusinessService.getCompanyList().getData();
		if (CollectionUtil.isNotEmpty(companyList)) {
			List<CompletableFuture<AssessRankVo>> futureList = new ArrayList<>();
			RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
			for (int i = 0; i < companyList.size(); i++) {
				CarbonRankingVo companyVo = companyList.get(i);
				//获取每个公司指标得分详情
				AssessTemplateTargetObjectBo queryBo = new AssessTemplateTargetObjectBo();
				queryBo.setCompanyId(companyVo.getDeptId());
				queryBo.setTemplateId(bo.getTemplateId());

				int finalI = i;
				CompletableFuture<AssessRankVo> future = CompletableFuture.supplyAsync(() -> {
					RequestContextHolder.setRequestAttributes(requestAttributes);
					return getCompanyAssessRankVo(finalI, queryBo, assessTemplate, companyVo.getDeptName());
				});
				futureList.add(future);
			}
			try {
				if (CollectionUtil.isNotEmpty(futureList)) {
					for (CompletableFuture<AssessRankVo> future : futureList) {
						AssessRankVo assessRankVo = future.get();
						if (CollectionUtil.isNotEmpty(assessRankVo.getTargetNameList())) {
							labelList = assessRankVo.getTargetNameList();
							totalNum = assessRankVo.getTotalScore();
							standScore = assessRankVo.getStandScore();
						} else {
							assessRankVo.setTotalScore(totalNum);
							assessRankVo.setStandScore(standScore);
						}
						dataList.add(assessRankVo);
					}
					// 排序
					dataList = dataList.stream().sorted(Comparator.comparing(AssessRankVo::getAssessScore).reversed())
							.collect(Collectors.toList());
					for (int i = 0; i < dataList.size(); i++) {
						dataList.get(i).setAssessRank("第" + NumberFormatUtils.arabicNumToChineseNum(i + 1) + "名");
					}
					averageScore = dataList.stream().mapToDouble(AssessRankVo::getAssessScore).average().orElse(0D);
					maxScore = dataList.stream().max(Comparator.comparing(AssessRankVo::getAssessScore)).get().getAssessScore();
					minScore = dataList.stream().min(Comparator.comparing(AssessRankVo::getAssessScore)).get().getAssessScore();
					double finalStandScore = standScore;
					Long aboveAvgNumber = dataList.stream().filter(data -> data.getAssessScore() >= finalStandScore).count();
					standardReachingRate = MathUtils.d2d(new BigDecimal(aboveAvgNumber).multiply(BigDecimal.valueOf(100)),
							new BigDecimal(dataList.size()));
				}
			} catch (InterruptedException e) {
				e.printStackTrace();
			} catch (ExecutionException e) {
				e.printStackTrace();
			}
		}
		resultMap.put("maxScore", maxScore);
		resultMap.put("minScore", minScore);
		resultMap.put("averageScore", new BigDecimal(averageScore).setScale(2, RoundingMode.HALF_UP));
		resultMap.put("standScore", standScore);
		resultMap.put("standardReachingRate", standardReachingRate);
		resultMap.put("dataList", dataList);
		resultMap.put("labelList", labelList);
		return resultMap;
	}

	/**
	 * 线性计分
	 * 有一个list对象名为AssessTargetSecondaryRule，里面有计算规则ruleJudge，值分别为>,>=,<,<=,=。有规则得分字段ruleScore，有规则值字段ruleValue。比如现在规则有3条，如果考核得分rateScore等于30，需要在规则列表中进行线性计分，现在的规则列表为：rateScore>= 10得1分，rateScore>=50得3分，rateScore>=100得6分。现在需要把30代入，算出两个数中间得分，如果超过100，那么最终得分为6.请考虑所有规则(>,>=,<,<=,=)计算，用java代码实现
	 *
	 * @param ruleList  规则列表
	 * @param rateScore 考核得分
	 * @return 最终得分
	 */
	public double calculateScore(List<AssessTargetSecondaryRule> ruleList, double rateScore) {
		double score = 0;

		for (int i = 0; i < ruleList.size(); i++) {
			AssessTargetSecondaryRule rule = ruleList.get(i);
			log.info("{} {} {} = {}?", rateScore, RuleJudge.getNameByValue(rule.getRuleJudge()), rule.getRuleValue(), rule.getRuleScore());
			CheckAssessRuleBo checkAssessRuleBo = checkRule(rule.getRuleJudge(), rateScore, rule.getRuleValue());
			if (checkAssessRuleBo.getRulePass()) {
				score = rule.getRuleScore();
				// 第一个满足就结束
				if (checkAssessRuleBo.getFirstBreak()) {
					return score;
				}

				if (i < ruleList.size() - 1) {
					AssessTargetSecondaryRule nextRule = ruleList.get(i + 1);
					CheckAssessRuleBo assessRuleBo = checkRule(nextRule.getRuleJudge(), rateScore, nextRule.getRuleValue());
					if (assessRuleBo.getRulePass()) {
						score = nextRule.getRuleScore();
					} else {
						double scoreDifference = nextRule.getRuleScore() - rule.getRuleScore();
						double valueDifference = nextRule.getRuleValue() - rule.getRuleValue();
						double increment = scoreDifference / valueDifference;
						score += increment * (rateScore - rule.getRuleValue());
					}
				}
			}
		}
		DecimalFormat df = new DecimalFormat("#.00");
		return Double.parseDouble(df.format(score));
	}

	public static CheckAssessRuleBo checkRule(String ruleJudge, double rateScore, double ruleValue) {
		CheckAssessRuleBo checkAssessRuleBo = new CheckAssessRuleBo();
		switch (ruleJudge) {
			case "1":
				checkAssessRuleBo.setRulePass(rateScore > ruleValue);
				checkAssessRuleBo.setFirstBreak(false);
				break;
			case "2":
				checkAssessRuleBo.setRulePass(rateScore >= ruleValue);
				checkAssessRuleBo.setFirstBreak(false);
				break;
			case "3":
				checkAssessRuleBo.setRulePass(rateScore < ruleValue);
				checkAssessRuleBo.setFirstBreak(true);
				break;
			case "4":
				checkAssessRuleBo.setRulePass(rateScore <= ruleValue);
				checkAssessRuleBo.setFirstBreak(true);
				break;
			case "5":
				checkAssessRuleBo.setRulePass(rateScore == ruleValue);
				checkAssessRuleBo.setFirstBreak(true);
			default:
				checkAssessRuleBo.setRulePass(false);
				checkAssessRuleBo.setFirstBreak(true);
		}
		return checkAssessRuleBo;
	}

	@Override
	public AssessProvinceComVo getScoreByTemplateId(AssessTemplateTargetObjectBo bo) {
		AssessProvinceComVo assessProvinceComVo = new AssessProvinceComVo();
		//todo 区分权限
//		AiUser user = SecurityUtils.getUser();
//		if (SecurityUtils.isAdmin()) {
			assessProvinceComVo = assessTemplateTargetObjectMapper.getScoreByTemplateId(bo);
//		} else {
//			// 获取部门id
//			Long deptId = user.getDeptId();
//			bo.setCompanyId(deptId);
//			assessProvinceComVo = assessTemplateTargetObjectMapper.getScoreByTemplateId(bo);
//		}
		return assessProvinceComVo;
	}

	@Override
	public AssessTaskInfoVo getTaskInfoById(AssessTemplateTargetObjectBo bo) {
		log.info("scoreParams:{}", bo);
		AssessTaskInfoVo vo = new AssessTaskInfoVo();
		//  指标数据
		vo = assessTargetSecondaryMapper.getInfoByTargetId(bo);

		vo.setTemplateTaskTime(getTemplateTime(vo.getTemplateStartTime(), vo.getTemplatePeriod()));
		if (ObjectUtil.isNull(vo)) {
			return vo;
		}
		//指标规则
		List<AssessTargetSecondaryRule> ruleList = new AssessTargetSecondaryRule().selectList(Wrappers.<AssessTargetSecondaryRule>lambdaQuery().eq(AssessTargetSecondaryRule::getSecondaryTargetId, bo.getSecondaryTargetId()));
		vo.setRuleList(ruleList);
		AssessTemplateTargetObject assessTemplateTargetObject;
		assessTemplateTargetObject = this.getOne(Wrappers.<AssessTemplateTargetObject>lambdaQuery()
				.eq(AssessTemplateTargetObject::getCompanyId, bo.getCompanyId())
				.eq(AssessTemplateTargetObject::getDeptId, bo.getDeptId())
				.eq(AssessTemplateTargetObject::getTemplateTargetId, vo.getTemplateTargetId()));
		if (ObjectUtil.isNull(assessTemplateTargetObject))  {
			return vo;
		}
		List<AssessTaskReport> list = assessTaskReportService.list(Wrappers.<AssessTaskReport>lambdaQuery()
				.eq(AssessTaskReport::getTemplateObjectId, assessTemplateTargetObject.getId())
		);

		// 处理上报人 和评分人
		if (CollectionUtil.isNotEmpty(list)) {
			List<Long> idList = new ArrayList<>();
			list.forEach(node -> {
				idList.add(node.getCreateBy());
				idList.add(node.getAssesser());
			});
			ArrayList<Long> userIds = CollectionUtil.distinct(idList);
			List<User> userList = assessTemplateTargetObjectMapper.getUserByIds(userIds);
			list.forEach(node -> {
				User userVO = userList.stream().filter(user -> user.getId().equals(node.getCreateBy())).findFirst()
						.orElse(new User());
				node.setReportBy(userVO.getOrgName());
				if (ObjectUtil.isNotNull(node.getAssesser())) {
					User assessUserVO = userList.stream().filter(user -> user.getId().equals(node.getAssesser())).findFirst()
							.orElse(new User());
					node.setAssessBy(assessUserVO.getUserName());
				}
			});
		}
		// 若是自主上报
		if (vo.getAssessMethod().equals(ReportType.MANUAL_REPORTING.getValue())) {
			// 处理模板周期和指标周期 模板周期 >  指标周期
			if (Integer.parseInt(vo.getTemplatePeriod()) < Integer.parseInt(vo.getTargetPeriod())) {
				String templatePeriod = vo.getTemplatePeriod();
				String targetPeriod = vo.getTargetPeriod();
				int type = 1;
				List<AssessTaskReportVo> assessTaskReportVoList = new ArrayList<>();
				List<Integer> months = new ArrayList<>();
				// 模板为季度，指标为月，三份报告
				if (AssessTemplatePeriod.QUARTER.getValue().equals(templatePeriod)
						&& AssessTemplatePeriod.MONTH.getValue().equals(targetPeriod)
				) {
					vo.setReportType("2");
					/// 拿到季度开始时间
					Date templateStartTime = vo.getTemplateStartTime();

					int month = DateUtil.month(templateStartTime) + 1;
					months = Arrays.asList(month, month + 1, month + 2);
				}
				// 模板为年，指标为季度，四份报告。
				if (AssessTemplatePeriod.YEAR.getValue().equals(templatePeriod)
						&& AssessTemplatePeriod.QUARTER.getValue().equals(targetPeriod)
				) {
					vo.setReportType("3");
					type = 2;
					months = Arrays.asList(1, 2, 3, 4);
				}
				// 模板为年，指标为月，12份报告。。
				if (AssessTemplatePeriod.YEAR.getValue().equals(templatePeriod)
						&& AssessTemplatePeriod.MONTH.getValue().equals(targetPeriod)
				) {
					vo.setReportType("4");
					months = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12);
				}
				assessTaskReportVoList = getDataByMonth(list, months, type);
				vo.setAssessTaskReportVoList(assessTaskReportVoList);
			} else {
				// 模板周期 <= 指标周期 一份报告。
				vo.setReportType("1");
				if (CollectionUtil.isNotEmpty(list)) {
					AssessTaskReport assessTaskReport = list.get(0);
					AssessTaskReportVo assessTaskReportVo = new AssessTaskReportVo();
					BeanUtils.copyProperties(assessTaskReport, assessTaskReportVo);
					getAttachmentList(assessTaskReport, assessTaskReportVo);
					vo.setAssessTaskReportVo(assessTaskReportVo);
				}
			}
		}
		// 若是自动评分
		else if (vo.getAssessMethod().equals(ReportType.AUTO_REPORTING.getValue())) {
			// 获取公式
			AssessTargetSecondary secondary = new AssessTargetSecondary().selectById(bo.getSecondaryTargetId());
			log.info("指标:{}", secondary);

			// 当前年份
			int year = Calendar.getInstance().get(Calendar.YEAR);
			List<DischargeDataTotalVo> carbonEmissionStatisticsForTheYear = null;
			List<DischargeDataTotalVo> carbonEmissionStatisticsForLastYear = null;
			// 公式：单位电信业务总量碳排放同比下降率
			if (AssessType.CARBON_DOWN.getValue().equals(secondary.getFormula())) {
				DischargeDataTotalBo dischargeDataTotalBoThisYear = new DischargeDataTotalBo();
				dischargeDataTotalBoThisYear.setDataYear(year);
				dischargeDataTotalBoThisYear.setCompanyId(bo.getCompanyId());
				carbonEmissionStatisticsForTheYear = remoteDischargeService.getCarbonAssessDataList(dischargeDataTotalBoThisYear).getData();
				DischargeDataTotalBo dischargeDataTotalBoLastYear = new DischargeDataTotalBo();
				dischargeDataTotalBoLastYear.setDataYear(year - 1);
				dischargeDataTotalBoLastYear.setCompanyId(bo.getCompanyId());
				carbonEmissionStatisticsForLastYear = remoteDischargeService.getCarbonAssessDataList(dischargeDataTotalBoLastYear).getData();

				// 查询碳排强度排名列表
				log.info("去年:{}", carbonEmissionStatisticsForLastYear);
				for (DischargeDataTotalVo datum : carbonEmissionStatisticsForLastYear) {
					log.info("碳排放下降率:{}", datum);
				}
			}
			// 公式：综合能耗（碳排放总量）增幅
			else if (AssessType.ENERGY_UP.getValue().equals(secondary.getFormula())) {
				// 获取当年的碳排放总量
				carbonEmissionStatisticsForTheYear = this.getTotalCarbonEmission(year, bo.getCompanyId());
				// 获取去年的碳排放总量
				carbonEmissionStatisticsForLastYear = this.getTotalCarbonEmission(year - 1, bo.getCompanyId());
			}


			// 处理模板周期和指标周期 模板周期 >  指标周期
			List<CarbonAssessCountVo> months = new ArrayList<>();
			if (Integer.parseInt(vo.getTemplatePeriod()) < Integer.parseInt(vo.getTargetPeriod())) {
				// 模板周期
				String templatePeriod = vo.getTemplatePeriod();
				// 指标周期
				String targetPeriod = vo.getTargetPeriod();
				int type = 1;
				List<AssessTaskReportVo> assessTaskReportVoList;
				// 模板为季度，指标为月，三份报告
				if (AssessTemplatePeriod.QUARTER.getValue().equals(templatePeriod) && AssessTemplatePeriod.MONTH.getValue().equals(targetPeriod)) {
					vo.setReportType("2");
					/// 拿到季度开始时间
					Date templateStartTime = vo.getTemplateStartTime();
					// 季度内的月份
					int month = DateUtil.month(templateStartTime) + 1;
					for (int i = 0; i < 3; i++) {
						CarbonAssessCountVo count = new CarbonAssessCountVo();
						count.setNode(month);
						// 降幅
						if (AssessType.CARBON_DOWN.getValue().equals(secondary.getFormula())) {
							count.setThisYearCarbonCoal(this.carbonDownIsMeasuredByMonth(carbonEmissionStatisticsForTheYear, i + 1));
							count.setLastYearCarbonCoal(this.carbonDownIsMeasuredByMonth(carbonEmissionStatisticsForLastYear, i + 1));
							count.setThisYearTelecomBusinessTotal(this.carbonDownBusinessTotalIsMeasuredByMonth(carbonEmissionStatisticsForTheYear, i + 1));
							count.setLastYearTelecomBusinessTotal(this.carbonDownBusinessTotalIsMeasuredByMonth(carbonEmissionStatisticsForLastYear, i + 1));
						}
						// 增幅
						else if (AssessType.ENERGY_UP.getValue().equals(secondary.getFormula())) {
							count.setThisYearCarbonCoal(this.carbonIntensityIsMeasuredByMonth(carbonEmissionStatisticsForTheYear, i + 1));
							count.setLastYearCarbonCoal(this.carbonIntensityIsMeasuredByMonth(carbonEmissionStatisticsForLastYear, i + 1));
						}
						months.add(count);
						month++;
					}
					log.info("模板为季度，指标为月，三份报告");
				}
				// 模板为年，指标为季度，四份报告。
				if (AssessTemplatePeriod.YEAR.getValue().equals(templatePeriod)
						&& AssessTemplatePeriod.QUARTER.getValue().equals(targetPeriod)
				) {
					vo.setReportType("3");
					type = 2;
					for (int i = 0; i < 4; i++) {
						CarbonAssessCountVo quarter = new CarbonAssessCountVo();
						quarter.setNode(i + 1);
						// 降幅
						if (AssessType.CARBON_DOWN.getValue().equals(secondary.getFormula())) {
							quarter.setThisYearCarbonCoal(this.carbonDownIsMeasuredByMonth(carbonEmissionStatisticsForTheYear, i * 3 + 1, i * 3 + 2, i * 3 + 3));
							quarter.setLastYearCarbonCoal(this.carbonDownIsMeasuredByMonth(carbonEmissionStatisticsForLastYear, i * 3 + 1, i * 3 + 2, i * 3 + 3));
							quarter.setThisYearTelecomBusinessTotal(this.carbonDownBusinessTotalIsMeasuredByMonth(carbonEmissionStatisticsForTheYear, i * 3 + 1, i * 3 + 2, i * 3 + 3));
							quarter.setLastYearTelecomBusinessTotal(this.carbonDownBusinessTotalIsMeasuredByMonth(carbonEmissionStatisticsForLastYear, i * 3 + 1, i * 3 + 2, i * 3 + 3));

						}
						// 增幅
						else if (AssessType.ENERGY_UP.getValue().equals(secondary.getFormula())) {
							quarter.setThisYearCarbonCoal(this.carbonIntensityIsMeasuredByMonth(carbonEmissionStatisticsForTheYear, i * 3 + 1, i * 3 + 2, i * 3 + 3));
							quarter.setLastYearCarbonCoal(this.carbonIntensityIsMeasuredByMonth(carbonEmissionStatisticsForLastYear, i * 3 + 1, i * 3 + 2, i * 3 + 3));

						}
						months.add(quarter);
					}
					log.info("模板为年，指标为季度，四份报告");
				}
				// 模板为年，指标为月，12份报告。。
				if (AssessTemplatePeriod.YEAR.getValue().equals(templatePeriod)
						&& AssessTemplatePeriod.MONTH.getValue().equals(targetPeriod)
				) {
					vo.setReportType("4");
					for (int i = 0; i < 12; i++) {
						CarbonAssessCountVo count = new CarbonAssessCountVo();
						count.setNode(i + 1);
						count.setThisYearCarbonCoal(this.carbonIntensityIsMeasuredByMonth(carbonEmissionStatisticsForTheYear, i + 1));
						count.setLastYearCarbonCoal(this.carbonIntensityIsMeasuredByMonth(carbonEmissionStatisticsForLastYear, i + 1));
						count.setThisYearTelecomBusinessTotal(this.carbonIntensityIsMeasuredByMonth(carbonEmissionStatisticsForTheYear, i + 1));
						count.setLastYearTelecomBusinessTotal(this.carbonIntensityIsMeasuredByMonth(carbonEmissionStatisticsForLastYear, i + 1));
						months.add(count);
					}
					log.info("模板为年，指标为月，12份报告");
				}
				// 判断自动评分类型
				log.info("上报类型:{}", vo.getReportType());
				assessTaskReportVoList = this.automaticScoringAssignment(months, type, secondary.getFormula(), ruleList);
				vo.setAssessTaskReportVoList(assessTaskReportVoList);
			} else {
				// 模板周期 <= 指标周期 一份报告。
				vo.setReportType("1");

				DischargeMonitorSettingVo settingVo = new DischargeMonitorSettingVo();
				settingVo.setCarbonTotal(this.carbonIntensityIsMeasuredByMonth(carbonEmissionStatisticsForTheYear, 1));
				settingVo.setPreCarbonTotal(this.carbonIntensityIsMeasuredByMonth(carbonEmissionStatisticsForLastYear, 1));
				settingVo.setThisYearTelecomBusinessTotal(this.carbonIntensityIsMeasuredByMonth(carbonEmissionStatisticsForTheYear, 1));
				settingVo.setLastYearTelecomBusinessTotal(this.carbonIntensityIsMeasuredByMonth(carbonEmissionStatisticsForLastYear, 1));
				// todo 根据不同类型走不同公式
				double autoCalScore = this.getAutoCalScoreByEnergyUp(settingVo, ruleList);
				AssessTaskReportVo assessTaskReport = new AssessTaskReportVo();
				assessTaskReport.setAssessScore(autoCalScore);
				assessTaskReport.setRuleScore(autoCalScore);
				vo.setAssessTaskReportVo(assessTaskReport);

			}
		}
		return vo;
	}


	/**
	 * 计算按月度测量的总碳强度。
	 *
	 * @param carbonEmissionStatisticsForTheYear 年度碳排放统计列表。
	 * @param monthList                          要计算碳强度的月份列表。
	 * @return 指定月份的总碳强度。
	 */
	private BigDecimal carbonDownIsMeasuredByMonth(List<DischargeDataTotalVo> carbonEmissionStatisticsForTheYear, int... monthList) {
		BigDecimal count = BigDecimal.ZERO;
		for (Integer month : monthList) {
			for (DischargeDataTotalVo totalVo : carbonEmissionStatisticsForTheYear) {
				if (month.equals((totalVo.getMonth() + 1))) {
					count = count.add(totalVo.getCarbonEmissions());
					log.info("碳排放量:{}", totalVo.getCarbonEmissions());
				}
			}
		}
		return count;
	}

	/**
	 * 计算按月度测量的业务数据总量
	 *
	 * @param carbonEmissionStatisticsForTheYear 年度碳排放统计列表。
	 * @param monthList                          要计算碳强度的月份列表。
	 * @return 指定月份的总碳强度。
	 */
	private BigDecimal carbonDownBusinessTotalIsMeasuredByMonth(List<DischargeDataTotalVo> carbonEmissionStatisticsForTheYear, int... monthList) {
		BigDecimal count = BigDecimal.ZERO;
		for (Integer month : monthList) {
			for (DischargeDataTotalVo totalVo : carbonEmissionStatisticsForTheYear) {
				if (month.equals((totalVo.getMonth() + 1))) {
					count = count.add(totalVo.getTelecomBusinessTotal());
				}
			}
		}
		return count;
	}

	/**
	 * 计算按月度测量的总碳强度。
	 *
	 * @param carbonEmissionStatisticsForTheYear 年度碳排放统计列表。
	 * @param monthList                          要计算碳强度的月份列表。
	 * @return 指定月份的总碳强度。
	 */
	private BigDecimal carbonIntensityIsMeasuredByMonth(List<DischargeDataTotalVo> carbonEmissionStatisticsForTheYear, int... monthList) {
		BigDecimal count = BigDecimal.ZERO;
		for (Integer month : monthList) {
			for (DischargeDataTotalVo totalVo : carbonEmissionStatisticsForTheYear) {
				if (month.equals((totalVo.getMonth() + 1))) {
					count = count.add(totalVo.getCarbonEmissions());
				}
			}
		}
		return count;
	}


	/**
	 * 根据年份拿到对应的数据
	 *
	 * @param year 年份
	 * @return 返回数据
	 */
	private List<DischargeDataTotalVo> getTotalCarbonEmission(int year, long companyId) {
		DischargeDataTotalBo dischargeDataTotalBo = new DischargeDataTotalBo();
		dischargeDataTotalBo.setDataYear(year);
		dischargeDataTotalBo.setCompanyId(companyId);
		R<List<DischargeDataTotalVo>> res = remoteDischargeService.getDataList(dischargeDataTotalBo);
		List<DischargeDataTotalVo> totalCarbonEmission = res != null ? res.getData() : Collections.emptyList();
		log.info("碳排放列表:{}", totalCarbonEmission);
		for (DischargeDataTotalVo totalVo : totalCarbonEmission) {
			BigDecimal carbonCoal = totalVo.getCarbonCoal();
			Integer month = totalVo.getMonth() + 1;
			log.info("公司Id:{},月份:{},碳排放量:{}", companyId, month, carbonCoal);
		}
		return totalCarbonEmission;
	}

	/**
	 * 自动评分赋值
	 *
	 * @param months   数据集合
	 * @param type     报告类型
	 * @param formula  公式
	 * @param ruleList 规则列表
	 * @return 返回值
	 */
	private List<AssessTaskReportVo> automaticScoringAssignment(List<CarbonAssessCountVo> months, int type, String formula, List<AssessTargetSecondaryRule> ruleList) {
		// 获取碳排放量进行计算
		log.info("碳排放量:,报告列表:{},类型:{},公式:{}", months, type, formula);
		List<AssessTaskReportVo> assessTaskReportVoList = new ArrayList<>();
		// 拿到当前月份
		int nowMonth = DateUtil.month(new Date()) + 1;
		for (CarbonAssessCountVo month : months) {

			AssessTaskReportVo assessTaskReportVo = new AssessTaskReportVo();

			// 统计 综合能耗（碳排放总量）增幅
			if (AssessType.ENERGY_UP.getValue().equals(formula)) {
				DischargeMonitorSettingVo settingVo = new DischargeMonitorSettingVo();
				settingVo.setCarbonTotal(month.getThisYearCarbonCoal());
				settingVo.setPreCarbonTotal(month.getLastYearCarbonCoal());
				// 得分
				double autoCalScore = this.getAutoCalScoreByEnergyUp(settingVo, ruleList);

				assessTaskReportVo.setAssessScore(autoCalScore);
				assessTaskReportVo.setRuleScore(autoCalScore);
				log.info("月份：{}；计算得分:{}", month.getNode(), autoCalScore);
			}
			// 统计 单位电信业务总量碳排放同比下降率
			else if (AssessType.CARBON_DOWN.getValue().equals(formula)) {
				log.info("统计 单位电信业务总量碳排放同比下降率");

				DischargeMonitorSettingVo settingVo = new DischargeMonitorSettingVo();
				settingVo.setCarbonTotal(month.getThisYearCarbonCoal());
				settingVo.setPreCarbonTotal(month.getLastYearCarbonCoal());
				settingVo.setThisYearTelecomBusinessTotal(month.getThisYearTelecomBusinessTotal());
				settingVo.setLastYearTelecomBusinessTotal(month.getLastYearTelecomBusinessTotal());
				// 得分
				double autoCalScore = this.getAutoCarbonDown(settingVo, ruleList);

				assessTaskReportVo.setAssessScore(autoCalScore);
				assessTaskReportVo.setRuleScore(autoCalScore);
				log.info("月份：{}；计算得分:{}", month.getNode(), autoCalScore);
			}


			// 装配月份数据
			makeUpMonthData(type, nowMonth, month.getNode(), assessTaskReportVo);
			assessTaskReportVo.setNum(month.getNode());
			assessTaskReportVoList.add(assessTaskReportVo);
		}

		return assessTaskReportVoList;
	}

	/**
	 * 通过文件id获取附件列表
	 */
	private void getAttachmentList(AssessTaskReport vo, AssessTaskReportVo assessTaskReportVo) {
		List<AssessTaskReportAttachment> attachmentList = assessTaskReportAttachmentService.list(Wrappers.<AssessTaskReportAttachment>lambdaQuery()
				.eq(AssessTaskReportAttachment::getTaskReportId, vo.getId()));
		if (CollectionUtil.isEmpty(attachmentList)) {
			return;
		}
		List<Long> attachmentIds = attachmentList.stream().map(AssessTaskReportAttachment::getAttachmentId).collect(Collectors.toList());
		List<Attachment> attachments = assessTemplateTargetObjectMapper.getFileByIds(attachmentIds);
		assessTaskReportVo.setAttachmentList(attachments);
	}

	/***
	 * 转换考核时间格式
	 * @param templateStartTime 考核开始时间
	 * @param period 考核周期
	 * @return 返回考核时间
	 */
	@Override
	public String getTemplateTime(Date templateStartTime, String period) {
		return getConstance(templateStartTime, period);
	}

	private static String getConstance(Date templateStartTime, String period) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(templateStartTime);
		int year = cal.get(Calendar.YEAR);
		if (period.equals(AssessTemplatePeriod.YEAR.getValue())) {
			return year + "年";
		} else if (period.equals(AssessTemplatePeriod.QUARTER.getValue())) {
			int quarter = cal.get(Calendar.MONTH) / 3 + 1;
			return year + "年第" + quarter + "季度";
		} else if (period.equals(AssessTemplatePeriod.MONTH.getValue())) {
			int mon = cal.get(Calendar.MONTH) + 1;
			return year + "年" + mon + "月";
		}
		return "";
	}

	@Override
	public List<AssessScoreVo> getComRankByTarAndTemId(AssessTemplateTargetObjectBo bo) {
		return assessTemplateTargetObjectMapper.getComRankByTarAndTemId(bo);
	}

	@Override
	public List<AssessScoreVo> getTargetScoreByCompanyIds(AssessTemplateTargetObjectBo queryBo) {
		return assessTemplateTargetObjectMapper.getTargetScoreByCompanyIds(queryBo);
	}

	private List<AssessTaskReportVo> getDataByMonth(List<AssessTaskReport> list, List<Integer> months, Integer type) {
		List<AssessTaskReportVo> assessTaskReportVoList = new ArrayList<>();
		// 拿到当前月份
		int nowMonth = DateUtil.month(new Date()) + 1;
		if (CollectionUtil.isNotEmpty(list)) {
			months.forEach(node -> {
				AssessTaskReportVo assessTaskReportVo = new AssessTaskReportVo();
				List<AssessTaskReport> collect = list.stream().filter(item -> {
					return item.getReportTime().equals(node);
				}).collect(Collectors.toList());
				// 查看当前月份是否上报了
				if (CollectionUtil.isNotEmpty(collect)) {
					AssessTaskReport assessTaskReport1 = collect.get(0);
					BeanUtils.copyProperties(assessTaskReport1, assessTaskReportVo);
					assessTaskReportVo.setReportStatus(ReportStatus.HAVE_REPORTED.getValue()
							.equals(assessTaskReport1.getReportStatus()) ?
							ReportStatus.HAVE_REPORTED.getName() :
							ReportStatus.FAIL_TO_REPORT.getName());
					getAttachmentList(assessTaskReport1, assessTaskReportVo);
				} else {
					assessTaskReportVo.setReportStatus(ReportStatus.FAIL_TO_REPORT.getName());
				}
				makeUpMonthData(type, nowMonth, node, assessTaskReportVo);
				assessTaskReportVo.setNum(node);
				assessTaskReportVoList.add(assessTaskReportVo);
			});
		} else {
			months.forEach(node -> {
				AssessTaskReportVo assessTaskReportVo = new AssessTaskReportVo();
				assessTaskReportVo.setReportStatus(ReportStatus.FAIL_TO_REPORT.getName());
				assessTaskReportVo.setNum(node);
				makeUpMonthData(type, nowMonth, node, assessTaskReportVo);
				assessTaskReportVoList.add(assessTaskReportVo);
			});
		}
		return assessTaskReportVoList;
	}

	/**
	 * 装配月份数据
	 */
	private void makeUpMonthData(Integer type, int nowMonth, Integer node, AssessTaskReportVo assessTaskReportVo) {
		if (type == 1) {
			assessTaskReportVo.setIndex(NumberFormatUtils.arabicNumToChineseNum(node) + "月");
			assessTaskReportVo.setReport(node <= nowMonth);
		} else {
			assessTaskReportVo.setIndex("第" + NumberFormatUtils.arabicNumToChineseNum(node) + "季度");
			// 计算当前是第几季度
			Calendar cal = Calendar.getInstance();
			int quarter = cal.get(Calendar.MONTH) / 3 + 1;
			assessTaskReportVo.setReport(node <= quarter);
		}
	}

	@Override
	public Page<AssessRankVo> getTargetListByTemplateId(QueryPage<AssessTemplateTargetObjectBo> queryPage) {
		long pageNo = queryPage.getCurrent();
		long pageSize = queryPage.getSize();
		AssessTemplateTargetObjectBo bo = queryPage.getModel();
		Page<AssessRankVo> dataPage = new Page<>();
		List<AssessRankVo> dataList = new ArrayList<>();
		// 根据模板id查询出所有的指标的考核方式 和 指标公式
		List<AssessTemplateTargetVo> targetList = assessTemplateTargetService.getTargetAndTemInfo(bo);
		// 拿到模板
		AssessTemplate assessTemplate = assessTemplateMapper.selectById(bo.getTemplateId());
		// 根据模板id查询分公司的分值
		List<AssessTemplateTargetObjectVo> list = assessTemplateTargetObjectMapper.getAssessScoreByTemplateId(bo);
		// 查询模板总分
		if (CollectionUtil.isEmpty(list)) {
			return dataPage;
		}
		//  指标ids
		List<Long> targetIds = new ArrayList<>();
		List<Long> companyIds = list.stream().map(AssessTemplateTargetObjectVo::getCompanyId).collect(Collectors.toList());
		// 查询指标是否存在自动上报
		targetList.forEach(node -> {
			targetIds.add(node.getSecondaryTargetId());
			// 若是主动上报 则需要系统自己计算
			if (ReportType.AUTO_REPORTING.getValue().equals(node.getAssessMethod())) {
				if (StrUtil.isNotBlank(node.getFormula()) && AssessType.ENERGY_UP.getValue().equals(node.getFormula())) {
					// 找到指标对应的几个公司
					List<DischargeMonitorSettingVo> remoteList = remoteDischargeService.getCarbonDownByTemplateId(companyIds);
					// 拿到考核规则
					List<AssessTargetSecondaryRule> ruleList = new AssessTargetSecondaryRule().selectList(Wrappers.<AssessTargetSecondaryRule>lambdaQuery().eq(AssessTargetSecondaryRule::getSecondaryTargetId, node.getSecondaryTargetId()));
					// 增加考核得分
					remoteList.forEach(item -> {
						double autoCalScore = getAutoCalScoreByEnergyUp(item, ruleList);
						list.stream().filter(score ->
										item.getCompanyId().equals(score.getCompanyId()))
								.findFirst()
								.ifPresent(u -> u.setAssessScore(new BigDecimal(u.getAssessScore()).add(new BigDecimal(autoCalScore)).doubleValue()));
					});
				}
			}
		});
		// 查询模板总分
		double totalNum = assessTargetSecondaryMapper.getTotalScoreByTargetIds(targetIds);
		list.forEach(node -> {
			AssessRankVo assessRankVo = new AssessRankVo();
			assessRankVo.setTemplateId(bo.getTemplateId());
			assessRankVo.setCompanyId(node.getCompanyId());
			assessRankVo.setTemplateName(assessTemplate.getTemplateName());
			assessRankVo.setAssessScore(node.getAssessScore());
			assessRankVo.setCompanyName(node.getCompanyName());
			if (ObjectUtil.isNotNull(node.getAssessScore())) {
				String v = MathUtils.d2dRatio(new BigDecimal(node.getAssessScore()), new BigDecimal(totalNum));
				assessRankVo.setAssessRate(v);
			} else {
				assessRankVo.setAssessRate(CommonConstants.ZERO_STRING);
			}
			assessRankVo.setTotalScore(totalNum);
			dataList.add(assessRankVo);
		});
		// 排序
		List<AssessRankVo> sortList = dataList.stream().sorted(Comparator.comparing(AssessRankVo::getAssessScore).reversed()).collect(Collectors.toList());
		for (int i = 0; i < sortList.size(); i++) {
			sortList.get(i).setAssessRank(NumberFormatUtils.arabicNumToChineseNum(i + 1));
		}
		// 进行分页
		List<AssessRankVo> subList = sortList.stream().skip((pageNo - 1) * pageSize).limit(pageSize).
				collect(Collectors.toList());
		dataPage.setTotal(list.size());
		dataPage.setRecords(subList);
		return dataPage;
	}

	@Override
	public Page<AssessRankVo> getScoreListByTemplateId(QueryPage<AssessTemplateTargetObjectBo> queryPage) {
		return getTargetListByTemplateId(queryPage);
	}

	@Override
	public List<AssessScoreVo> getHeaderScoreList(AssessTemplateTargetObjectBo bo) {
		List<AssessScoreVo> headerScoreList = assessTemplateTargetObjectMapper.getHeaderScoreList(bo);
		if (CollectionUtil.isNotEmpty(headerScoreList)) {
			double sum = headerScoreList.stream().mapToDouble(AssessScoreVo::getScore).sum();
			headerScoreList.forEach(node -> {
				node.setTotalScore(sum);
			});
		}
		return headerScoreList;
	}

	@Override
	public AssessRankRateVo getRankRateInfoById(AssessTemplateTargetObjectBo bo) {
		// 根据模板id获取模板详情
		AssessRankRateVo vo = assessTemplateTargetObjectMapper.getTemplateById(bo);
		vo.setTemplateTaskTime(getTemplateTime(vo.getTemplateStartTime(), vo.getPeriod()));
		vo.setRankNum(NumberFormatUtils.chineseNumToArabicNum(bo.getAssessRank()));

		// 根据模板id和公司id查询详情
		List<RankRateInfoVo> detailList = assessTemplateTargetObjectMapper.getRankRateInfoById(bo);
		if (CollectionUtil.isNotEmpty(detailList)) {
			detailList = detailList.stream().distinct().collect(Collectors.toList());
			// 计算指标总分、得分率、考核总分
			double targetTotal = detailList.stream().mapToDouble(RankRateInfoVo::getScore).sum();
			vo.setTargetTotal(targetTotal);
			double assessTotal = detailList.stream().mapToDouble(RankRateInfoVo::getAssessScore).sum();
			vo.setTargetTotal(assessTotal);
			String scoreRate = CommonConstants.ZERO_STRING;
			if (assessTotal == CommonConstants.ZERO_NUMBER) {
				scoreRate = MathUtils.d2dRatio(new BigDecimal(assessTotal), new BigDecimal(targetTotal));
			}
			RankRateInfoVo queryBo = new RankRateInfoVo();
			queryBo.setTemplateId(bo.getTemplateId());
			List<Long> targetIds = detailList.stream().map(RankRateInfoVo::getSecondaryTargetId).collect(Collectors.toList());
			queryBo.setSecondaryTargetIds(targetIds);
			List<AssessTaskReportVo> result = assessTemplateTargetObjectMapper.getOrderByCompany(queryBo);
			// 设置当前公司的当前指标排在第几
			detailList.forEach(node -> {
				node.setTemplateId(bo.getTemplateId());
				node.setCompanyName(bo.getCompanyName());
				// 找到当前指标
				List<AssessTaskReportVo> collect = result.stream().filter(item -> {
					return node.getSecondaryTargetId().equals(item.getSecondaryTargetId());
				}).collect(Collectors.toList());
				// 若是主动上报 则需要系统自己计算
				if (ReportType.AUTO_REPORTING.getValue().equals(node.getAssessMethod())) {
					//
					if (StrUtil.isNotBlank(node.getFormula()) && AssessType.ENERGY_UP.getValue().equals(node.getFormula())) {
						// 找到指标对应的几个公司
						List<Long> companyIds = collect.stream().map(AssessTaskReportVo::getCompanyId).collect(Collectors.toList());
						List<DischargeMonitorSettingVo> remoteList = remoteDischargeService.getCarbonDownByTemplateId(companyIds);
						// 拿到考核规则
						List<AssessTargetSecondaryRule> ruleList = new AssessTargetSecondaryRule().selectList(Wrappers.<AssessTargetSecondaryRule>lambdaQuery().eq(AssessTargetSecondaryRule::getSecondaryTargetId, node.getSecondaryTargetId()));
						// 排序
						List<AssessTaskReportVo> finalCollect = collect;
						remoteList.forEach(node1 -> {
							AssessTaskReportVo assessTaskReportVo1 = finalCollect.stream().filter(item -> {
								return item.getCompanyId().equals(node1.getCompanyId());
							}).collect(Collectors.toList()).get(CommonConstants.ZERO_NUMBER);
							assessTaskReportVo1.setAssessScore(getAutoCalScoreByEnergyUp(node1, ruleList));
						});
						// 计算出当前公司的考核分数
						List<DischargeMonitorSettingVo> collect1 = remoteList.stream().filter(item -> {
							return item.getCompanyId().equals(node.getDeptId());
						}).collect(Collectors.toList());
						if (CollectionUtil.isNotEmpty(collect1)) {
							DischargeMonitorSettingVo dischargeMonitorSettingVo = collect1.get(CommonConstants.ZERO_NUMBER);
							node.setAssessScore(getAutoCalScoreByEnergyUp(dischargeMonitorSettingVo, ruleList));
						} else {
							node.setAssessScore(0L);
						}

					}
				}
				// 排序
				collect = collect.stream().sorted(Comparator.comparing(AssessTaskReportVo::getAssessScore).reversed()).collect(Collectors.toList());
				// 设置当前公司排在第几名
				for (int i = 0; i < collect.size(); i++) {
					if (bo.getCompanyId().equals(collect.get(i).getCompanyId())) {
						node.setRankNum(NumberFormatUtils.arabicNumToChineseNum(i + 1));
					}
				}
			});
			vo.setRankDetail(detailList);
		}
		return vo;
	}

	/**
	 * 计算考核模板总分
	 */
	private double getTemplateTotalScore(List<AssessTemplateTargetVo> targetList, String templatePeriod) {
		double totalScore = 0;
		for (AssessTemplateTargetVo targetVo : targetList) {
			double targetScore = targetVo.getScore();
			if (targetVo.getAssessPeriod().compareTo(templatePeriod) > 0) {
				if (AssessTemplatePeriod.QUARTER.getValue().equals(targetVo.getAssessPeriod())) {
					targetScore = targetScore * 4;
				} else {
					if (AssessTemplatePeriod.YEAR.getValue().equals(templatePeriod)) {
						targetScore = targetScore * 12;
					} else {
						targetScore = targetScore * 3;
					}
				}
			}
			totalScore += targetScore;
		}
		return totalScore;
	}

	private List<AssessTemplateTargetObjectVo> getTemplateAssessInfo(AssessTemplateTargetObjectBo bo) {
		List<AssessTemplateTargetObjectVo> voList = assessTemplateTargetObjectMapper.getTaskInfoByOrgId(bo);
		if (CollectionUtil.isEmpty(voList)) {
			return CollectionUtil.newArrayList();
		}
		System.out.println("===省级--查询某个分公司的考核任务===");
		voList.forEach(System.out::println);
		System.out.println("===省级--查询某个分公司的考核任务===END");
		// 调用详情页面，把得分累加
		for (AssessTemplateTargetObjectVo targetObjectVo : voList) {
			AssessTemplateTargetObjectBo scoreParams = new AssessTemplateTargetObjectBo();
			scoreParams.setCompanyId(targetObjectVo.getCompanyId());
			scoreParams.setDeptId(targetObjectVo.getCompanyId());
			scoreParams.setSecondaryTargetId(targetObjectVo.getSecondaryTargetId());
			scoreParams.setTemplateId(bo.getTemplateId());
			AssessTaskInfoVo data = getTaskInfoById(scoreParams);
			if (data != null) {
				if (data.getAssessTaskReportVoList() != null) {
					for (AssessTaskReportVo reportVo : data.getAssessTaskReportVoList()) {
						log.info("规则得分:{}", reportVo.getRuleScore());
					}
					double score = data.getAssessTaskReportVoList().stream()
							.filter(it -> it.getRuleScore() != null)
							.mapToDouble(AssessTaskReportVo::getRuleScore)
							.sum();
					targetObjectVo.setAssessScore(score);
				}
			}
		}
		return voList;
	}

	private AssessRankVo getCompanyAssessRankVo(int sequence, AssessTemplateTargetObjectBo queryBo, AssessTemplate assessTemplate,
												String companyName) {
		AssessRankVo assessRankVo = new AssessRankVo();
		List<AssessTemplateTargetObjectVo> targetScoreList = getTemplateAssessInfo(queryBo);
		if (sequence == 0) {
			double totalScore = 0;
			List<String> labelList = targetScoreList.stream().map(AssessTemplateTargetObjectVo::getTargetName).collect(Collectors.toList());
			for (AssessTemplateTargetObjectVo node : targetScoreList) {
				double score = Double.parseDouble(node.getScore());
				if (node.getAssessPeriod().compareTo(assessTemplate.getPeriod()) > 0) {
					if (AssessTemplatePeriod.QUARTER.getValue().equals(node.getAssessPeriod())) {
						score = score * 4;
					} else {
						if (AssessTemplatePeriod.YEAR.getValue().equals(assessTemplate.getPeriod())) {
							score = score * 12;
						} else {
							score = score * 3;
						}
					}
				}
				totalScore += score;
			}
			//达标分数线
			double standScore = MathUtils.d2d(new BigDecimal(totalScore).multiply(new BigDecimal(assessTemplate.getWarningValue())),
					BigDecimal.valueOf(100)).doubleValue();
			assessRankVo.setTargetNameList(labelList);
			assessRankVo.setTotalScore(totalScore);
			assessRankVo.setStandScore(standScore);
		}
		assessRankVo.setTemplateId(queryBo.getTemplateId());
		assessRankVo.setCompanyId(queryBo.getCompanyId());
		assessRankVo.setTemplateName(assessTemplate.getTemplateName());
		assessRankVo.setAssessScore(targetScoreList.stream().mapToDouble(AssessTemplateTargetObjectVo::getAssessScore).sum());
		assessRankVo.setCompanyName(companyName);
		assessRankVo.setTargetScoreList(targetScoreList.stream().map(AssessTemplateTargetObjectVo::getAssessScore).collect(Collectors.toList()));
		return assessRankVo;
	}
}
