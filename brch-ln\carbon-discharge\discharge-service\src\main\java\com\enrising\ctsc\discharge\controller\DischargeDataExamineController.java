package com.enrising.ctsc.discharge.controller;

import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.discharge.api.bo.DischargeDataExamineBo;
import com.enrising.ctsc.discharge.api.vo.DischargeDataExamineVo;
import com.enrising.ctsc.discharge.service.DischargeDataExamineService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 碳盘查数据
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@RestController
@RequestMapping("/discharge/examine")
@AllArgsConstructor
public class DischargeDataExamineController {

	private final DischargeDataExamineService dischargeDataExamineService;

	@GetMapping("/list")
		public R<List<DischargeDataExamineVo>> getList(DischargeDataExamineBo bo) {
		return R.success(dischargeDataExamineService.getList(bo));
	}

	@PostMapping("/saveList")
		public R saveList(@RequestBody List<DischargeDataExamineBo> boList) {
		dischargeDataExamineService.saveList(boList);
		return R.success();
	}

	@GetMapping("/download")
		public void download(HttpServletRequest request, HttpServletResponse response, DischargeDataExamineBo bo) {
		dischargeDataExamineService.download(request,response,bo);
	}

}
