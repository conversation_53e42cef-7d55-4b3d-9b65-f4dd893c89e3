package com.enrising.ctsc.discharge.api.vo;


import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DischargePortraitVo {

	/**
	 * 数据对比
	 *
	 * */
	private List<DischargeEmissionTrendVo> dataCompareList;


	/**
	 * 碳排放结构分析 -- 省级
	 *
	 * */
	private DischargeStructuralAnalysisVo structuralAnalysis;


	/**
	 * 中部展示内容  化石能源
	 * */
	private DischargeFossilVo fossilVo;

	/**
	 * 各类型能源碳排放趋势 -- 地市公司
	 *
	 * */
	private List<DischargeStructuralAnalysisVo> energyCarbonTrendList;

	/**
	 * 截至目前碳排量达监测值百分比 -- 地市公司
	 */
	private String useRate;

}
