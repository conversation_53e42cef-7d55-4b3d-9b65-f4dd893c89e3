package com.enrising.ctsc.discharge.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.bo.DischargeMonitorSettingBo;
import com.enrising.ctsc.discharge.api.query.AssessTargetSecondaryQuery;
import com.enrising.ctsc.discharge.api.query.DischargeMonitorSettingQuery;
import com.enrising.ctsc.discharge.api.vo.CompanyCarbonVo;
import com.enrising.ctsc.discharge.api.vo.DischargeMonitorSettingVo;
import com.enrising.ctsc.discharge.api.vo.DischargeSettingCompanyVo;
import com.enrising.ctsc.discharge.service.DischargeMonitorSettingService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 碳排放监测设置表
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-01-03
 */
@RestController
@RequestMapping("/discharge/setting")
@AllArgsConstructor
public class DischargeMonitorSettingController {
	private final DischargeMonitorSettingService dischargeMonitorSettingService;

	@GetMapping("/list")
	public TableDataInfo<DischargeMonitorSettingVo> page(Page<DischargeMonitorSettingVo> page, DischargeMonitorSettingQuery query) {
		return dischargeMonitorSettingService.findList(page, query);
	}

	@GetMapping("/getSettingList")
		public TableDataInfo<DischargeMonitorSettingVo> getSettingList(Page<DischargeMonitorSettingVo> page, DischargeMonitorSettingBo bo) {
		return dischargeMonitorSettingService.getSettingList(page, bo);
	}

	@GetMapping("/getRecordsList")
		public TableDataInfo<DischargeMonitorSettingVo> getRecordsList(Page<DischargeMonitorSettingVo> page, DischargeMonitorSettingBo bo) {
		return dischargeMonitorSettingService.getRecordsList(page, bo);
	}

	@PostMapping("/getCarbonDownByTemplateId")
	public List<DischargeMonitorSettingVo> getCarbonDownByTemplateId(@RequestBody List<Long> companyIds) {
		return dischargeMonitorSettingService.getCarbonDownByTemplateId(companyIds);
	}

	@PostMapping("/getCompanyCarbonByTarget")
	public List<CompanyCarbonVo> getCompanyCarbonByTarget(@RequestBody AssessTargetSecondaryQuery query) {
		return dischargeMonitorSettingService.getCompanyCarbonByTarget(query);
	}

	@PostMapping("/getMonthCarbonByTarget")
	public List<CompanyCarbonVo> getMonthCarbonByTarget(@RequestBody AssessTargetSecondaryQuery query) {
		return dischargeMonitorSettingService.getMonthCarbonByTarget(query);
	}

	@GetMapping("/getCompanyCarbonList")
		public R<List<DischargeSettingCompanyVo>> getCompanyCarbonList(DischargeMonitorSettingBo bo) {
		return R.success(dischargeMonitorSettingService.getCompanyCarbonList(bo));
	}

	@GetMapping("/detail")
		public R<DischargeMonitorSettingVo> get(DischargeMonitorSettingQuery query) {
		DischargeMonitorSettingVo detail = dischargeMonitorSettingService.detail(query);
		return R.success(detail, "查询成功");
	}

	@GetMapping("/getRecordInfo")
		public R<DischargeMonitorSettingVo> getRecordInfo(DischargeMonitorSettingQuery query) {
		DischargeMonitorSettingVo detail = dischargeMonitorSettingService.getRecordInfo(query);
		return R.success(detail, "查询成功");
	}

		@PostMapping(value = "/save")
		public R<String> save(@RequestBody DischargeMonitorSettingBo bo) {
		dischargeMonitorSettingService.add(bo);
		return R.success("保存成功");
	}

		@PostMapping(value = "/update")
	public R<String> update(@RequestBody DischargeMonitorSettingBo bo) {
		dischargeMonitorSettingService.edit(bo);
		return R.success("修改成功");
	}

		@PostMapping(value = "/delete/{id}")
	public R<String> delete(@PathVariable Long id) {
		dischargeMonitorSettingService.del(id);
		return R.success("删除成功");
	}

		@PostMapping(value = "/batchSetting")
		public R<String> batchSetting(@RequestBody List<DischargeMonitorSettingBo> list) {
		dischargeMonitorSettingService.batchSetting(list);
		return R.success("操作成功");
	}

	@GetMapping("/getMonitorSituationList")
		public R getMonitorSituationList() {
		return R.success(dischargeMonitorSettingService.getMonitorSituationList());
	}
}
