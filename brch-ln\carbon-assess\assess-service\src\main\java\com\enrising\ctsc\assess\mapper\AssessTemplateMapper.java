package com.enrising.ctsc.assess.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.assess.api.bo.AssessTemplateBo;
import com.enrising.ctsc.assess.api.entity.AssessTemplate;
import com.enrising.ctsc.assess.api.vo.AssessTemplateVo;
import com.enrising.ctsc.assess.api.vo.DeptTreeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * 考核模板
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-07
 */
@Mapper
public interface AssessTemplateMapper extends BaseMapper<AssessTemplate> {

	/**
	 * 考核任务管理--省级列表
	 */
	Page<AssessTemplateVo> getDeliveredTaskByPage(@Param("page") Page<AssessTemplateVo> assessTemplatePage, @Param("bo") AssessTemplateBo bo);

	/**
	 * 考核任务管理--市州/部门列表
	 */
	List<AssessTemplateVo> getAllAssessTemplateByYear(@Param("bo") AssessTemplateBo bo);

	/**
	 * 市州公司/部门列表--所有考核模板列表
	 */
	List<AssessTemplateVo> getAllAssessTemplate(@Param("bo") AssessTemplateBo bo);

	/**
	 * 所有省级模板
	 */
	List<AssessTemplateVo> getProvinceAssessTemplate(@Param("bo") AssessTemplateBo bo);

	/**
	 * 查询部门列表
	 */
	List<HashMap<String, Object>> getSysDeptList(DeptTreeVo deptTree);
	/**
	 * 查询部门列表
	 */
	HashMap<String, Object> getSysDeptById(Long id);
	/**
	 * 查询部门列表
	 */
	List<HashMap<String, Object>> getSysDeptChildrenList(String parentId);
}