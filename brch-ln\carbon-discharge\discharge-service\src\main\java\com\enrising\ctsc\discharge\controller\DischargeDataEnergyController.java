package com.enrising.ctsc.discharge.controller;

import com.enrising.ctsc.discharge.api.bo.DischargeDataEnergyBo;
import com.enrising.ctsc.discharge.api.bo.EnergyConsumptionReportToTheGroupBo;
import com.enrising.ctsc.discharge.api.query.DischargeDataEnergyQuery;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorVo;
import com.enrising.ctsc.discharge.service.DischargeDataEnergyService;
import lombok.AllArgsConstructor;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.WebRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 碳排放数据填报表（能源）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@RestController
@RequestMapping("/discharge/energy")
@AllArgsConstructor
public class DischargeDataEnergyController {

	private final DischargeDataEnergyService dischargeDataEnergyService;

	@PostMapping("/getDataList")
    public R<List<DischargeDataEnergyVo>> getDataList(@RequestBody DischargeDataEnergyBo dischargeDataEnergyBo) {
		List<DischargeDataEnergyVo> dischargeDataEnergyVoList = dischargeDataEnergyService.getDataList(dischargeDataEnergyBo);
		return R.success(dischargeDataEnergyVoList);
	}

	@PostMapping("/getDataUpdateList")
    public R<List<DischargeDataEnergyVo>> getDataUpdateList(@RequestBody DischargeDataEnergyBo dischargeDataEnergyBo) {
		List<DischargeDataEnergyVo> dischargeDataEnergyVoList = dischargeDataEnergyService.getDataUpdateList(dischargeDataEnergyBo);
		return R.success(dischargeDataEnergyVoList);
	}

	@PostMapping("/getAllDataList")
    public R<List<DischargeDataEnergyVo>> getAllDataList(@RequestBody DischargeDataEnergyQuery query) {
		return R.success(dischargeDataEnergyService.getAllDataList(query));
	}

	@PostMapping("/getReportList")
	public R<List<DischargeDataEnergyVo>> getReportList(@RequestBody DischargeDataEnergyQuery query) {
		List<DischargeDataEnergyVo> dischargeDataEnergyVoList = dischargeDataEnergyService.getReportList(query);
		Set<DischargeDataEnergyVo> dataSet = new HashSet<>(dischargeDataEnergyVoList);
		List<DischargeDataEnergyVo> list = new ArrayList<>(dataSet);
		return R.success(list);
	}

	/**
	 * 能耗上报到集团
	 * @param bo 参数
	 */
	@PostMapping(value = "/energyConsumptionReportToTheGroup")
	public R energyConsumptionReportToTheGroup(@RequestBody EnergyConsumptionReportToTheGroupBo bo) {
		dischargeDataEnergyService.energyConsumptionReportToTheGroup(bo);
		return R.success("查询成功");
	}

	@GetMapping("/detail")
	public R<DischargeDataEnergyVo> get(DischargeDataEnergyQuery query) {
		DischargeDataEnergyVo detail = dischargeDataEnergyService.detail(query);
		return R.success(detail, "查询成功");
	}

	@PostMapping(value = "/saveList")
	public R<String> saveList(@RequestBody List<DischargeEnergyIndicatorVo> dischargeEnergyIndicatorVos) {
		boolean iSuc = dischargeDataEnergyService.saveList(dischargeEnergyIndicatorVos);
		if (iSuc) {
			return R.success("保存成功");
		} else {
			return R.success("保存失败");
		}
	}

	@PostMapping(value = "/updateList")
	public R<String> updateList(@RequestBody DischargeDataEnergyBo dischargeDataEnergyBo) {
		boolean iSuc = dischargeDataEnergyService.updateList(dischargeDataEnergyBo);
		if (iSuc) {
			return R.success("保存成功");
		} else {
			return R.success("保存失败");
		}
	}

	@PostMapping(value = "/rejectData")
	public R<String> rejectData(@RequestBody DischargeDataEnergyBo dischargeDataEnergyBo) {
		boolean iSuc = dischargeDataEnergyService.rejectData(dischargeDataEnergyBo);
		if (iSuc) {
			return R.success("退回数据成功");
		} else {
			return R.success("退回数据失败");
		}
	}

	@PostMapping(value = "/applyReturn")
	public R<String> applyReturn(@RequestBody DischargeDataEnergyBo dischargeDataEnergyBo) {
		boolean iSuc = dischargeDataEnergyService.applyReturn(dischargeDataEnergyBo);
		if (iSuc) {
			return R.success("申请退回成功");
		} else {
			return R.success("申请退回失败");
		}
	}

	@InitBinder
	public void initBinder(WebDataBinder binder, WebRequest request) {
		//转换日期
		DateFormat dateFormat=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));// CustomDateEditor为自定义日期编辑器
	}

	@PostMapping("/download")
	public void download(HttpServletRequest request, HttpServletResponse response, @RequestBody  DischargeDataEnergyQuery query) {
		dischargeDataEnergyService.download(request,response,query);
	}

	@GetMapping("/downloadTemplate")
	public void downloadTemplate(HttpServletRequest request, HttpServletResponse response) {
		dischargeDataEnergyService.downloadTemplate(request,response);
	}

	@GetMapping("/getPowerStruct")
	public R getPowerStruct() {
		return R.success(dischargeDataEnergyService.getPowerStruct());
	}

	@PostMapping("/getIndicatorDataList")
	public R<List<DischargeDataEnergyVo>> getIndicatorDataList(@RequestBody DischargeDataEnergyBo dischargeDataEnergyBo) {
		List<DischargeDataEnergyVo> dischargeDataEnergyVoList = dischargeDataEnergyService.getIndicatorDataList(dischargeDataEnergyBo);
		return R.success(dischargeDataEnergyVoList);
	}

	@PostMapping("/reCountDataList")
	public R<List<DischargeDataEnergyVo>> reCountDataList(@RequestBody DischargeDataEnergyBo dischargeDataEnergyBo) {
		List<DischargeDataEnergyVo> dischargeDataEnergyVoList = dischargeDataEnergyService.reCountDataList(dischargeDataEnergyBo);
		return R.success(dischargeDataEnergyVoList);
	}
}
