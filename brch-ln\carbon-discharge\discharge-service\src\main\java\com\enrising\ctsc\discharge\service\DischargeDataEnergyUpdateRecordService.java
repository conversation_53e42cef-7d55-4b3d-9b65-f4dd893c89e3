package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.discharge.api.bo.DischargeDataEnergyBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergyUpdateRecord;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyUpdateLogVo;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyUpdateRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 能源数据汇总数据修改记录
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
public interface DischargeDataEnergyUpdateRecordService extends IService<DischargeDataEnergyUpdateRecord> {


	List<DischargeDataEnergyUpdateRecordVo> getUpdateRecordList(DischargeDataEnergyBo bo);

	List<DischargeDataEnergyUpdateLogVo> getDataUpdateList(DischargeDataEnergyBo dischargeDataEnergyBo);
}