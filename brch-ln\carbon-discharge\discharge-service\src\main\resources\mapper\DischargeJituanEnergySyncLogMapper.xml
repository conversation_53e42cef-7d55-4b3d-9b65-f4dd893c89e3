<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeJituanEnergySyncLogMapper">
	<resultMap id="BaseResultMap" type="com.enrising.ctsc.discharge.api.entity.DischargeJituanEnergySyncLog">
		<result column="id" property="id" />
		<result column="report_user" property="reportUser" />
		<result column="report_dept" property="reportDept" />
		<result column="create_time" property="createTime" />
		<result column="report_date" property="reportDate" />
	</resultMap>
    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.report_user,
            t.report_dept,
            t.create_time,
            t.report_date,
            so.org_name as report_dept_name,
            su.user_name as report_user_name
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeJituanEnergySyncLogVo">
        SELECT
        	<include refid="baseColumns" />
        FROM discharge_jituan_energy_sync_log t
		LEFT JOIN rmp.sys_organizations so on t.report_dept = so.id
		LEFT JOIN rmp.sys_user su on t.report_user = su.id
		<where>
		    1 = 1
            <if test="query.id != null">
                AND t.id = #{query.id}
            </if>
            <if test="query.queryReportDate != null">
                AND t.report_date = #{query.queryReportDate}
            </if>
            <if test="query.keys != null and query.keys != ''">
                AND (so.org_name LIKE concat('%', #{query.keys}, '%') OR su.user_name LIKE concat('%', #{query.keys}, '%'))
            </if>
        </where>
        ORDER BY t.create_time ASC
    </select>
</mapper>