package com.enrising.ctsc.assess.api.vo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 考核模板
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
public class AssessTemplateVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建者id
     */
    private Long createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    /**
     * 更新者id
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date updateTime;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 任务周期
     */
    private String period;

    /**
     * 任务开始时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date templateStartTime;

    /**
     * 任务结束时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date templateEndTime;

    /**
     * 考核预警值
     */
    private Integer warningValue;

    /**
     * 模板内容
     */
    private String content;

    /**
     * 下发状态
     */
    private String sendStatus;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 模板任务时间
     */
    private String templateTaskTime;

    /**
     * 考核指标列表
     */
    private List<AssessTemplateTargetVo> assessTemplateTargetVoList;

    /**
     * 考核对象，1-公司，2-指定部门
     */
    private String objectType;

    /**
     * 市州公司\部门左侧列表，显示好多未处理个数
     */
    private Integer undoTaskNum;

    /*
     *
     * */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date sendTime;

    /*
     *下发任务的公司
     * */
    private String companyName;
}
