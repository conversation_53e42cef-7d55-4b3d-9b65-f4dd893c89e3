package com.enrising.ctsc.discharge.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.discharge.api.bo.DischargeDataElectricBo;
import com.enrising.ctsc.discharge.api.vo.DischargeDataElectricVo;
import com.enrising.ctsc.discharge.service.DischargeDataElectricService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 碳排放数据填报表（电）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@RestController
@RequestMapping("/discharge/electric")
@AllArgsConstructor
public class DischargeDataElectricController {
	private final DischargeDataElectricService dischargeDataElectricService;

	@PostMapping("/getElectricListPage")
		public R<Page<DischargeDataElectricVo>> getElectricListPage(@RequestBody QueryPage<DischargeDataElectricBo> queryPage) {
		return R.success(dischargeDataElectricService.getElectricListPage(queryPage));
	}

	@PostMapping("/getElectricListToExcel")
		public R<List<DischargeDataElectricVo>> getElectricListToExcel(@RequestBody DischargeDataElectricBo dischargeDataElectricBo) {
		return R.success(dischargeDataElectricService.getElectricListToExcel(dischargeDataElectricBo));
	}

	@GetMapping("/getDataList")
		public R<List<DischargeDataElectricVo>> getDataList(Integer dataYear, Long companyId) {
		return R.success(dischargeDataElectricService.getDataList(dataYear, companyId));
	}

	@GetMapping("/detail")
	public R<DischargeDataElectricVo> get(Long id) {
		DischargeDataElectricVo detail = dischargeDataElectricService.detail(id);
		return R.success(detail, "查询成功");
	}

		@PostMapping(value = "/save")
		public R<String> save(@RequestBody DischargeDataElectricBo bo) {
		String sRet = dischargeDataElectricService.add(bo);
		if (StrUtil.isBlank(sRet)) {
			return R.success("保存成功");
		}
		return R.failed(sRet);
	}

		@PostMapping(value = "/update")
		public R<String> update(@RequestBody DischargeDataElectricBo bo) {
		dischargeDataElectricService.edit(bo);
		return R.success("修改成功");
	}

		@PostMapping(value = "/delete/{id}")
		public R<String> delete(@PathVariable Long id) {
		dischargeDataElectricService.del(id);
		return R.success("删除成功");
	}
}
