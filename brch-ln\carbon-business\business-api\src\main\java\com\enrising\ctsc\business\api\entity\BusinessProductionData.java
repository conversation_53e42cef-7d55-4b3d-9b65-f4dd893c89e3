package com.enrising.ctsc.business.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 生产业务数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-03-23
 */

@Data
@TableName("business_production_data")
public class BusinessProductionData extends Model<BusinessProductionData> {

	/**
	 * 主键id,采用雪花id
	 */
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 创建者id
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 更新者id
	 */
	private Long updateBy;

	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 填报单位id
	 */
	private Long companyId;

	/**
	 * 填报时间
	 */
	private Date reportTime;

	/**
	 * 电信业务总量（万元）
	 */
	private BigDecimal telecomBusinessTotal;

	/**
	 * 业务流量总量（TB）
	 */
	private BigDecimal businessFlowTotal;

	/**
	 * 固定电话业务总量（万元）
	 */
	private BigDecimal fixedPhoneBusiness;

	/**
	 * 宽带接入业务总量（万元）
	 */
	private BigDecimal broadbandBusiness;

	/**
	 * 专线接入业务总量（万元）
	 */
	private BigDecimal specialBroadbandBusiness;

	/**
	 * IPTV业务总量（万元）
	 */
	private BigDecimal iptvBusiness;

	/**
	 * 移动电话业务总量（万元）
	 */
	private BigDecimal mobilePhonBusiness;

	/**
	 * 移动互联网业务总量（TB）
	 */
	private BigDecimal mobileInternetBusiness;

	/**
	 * 移动短信业务总量（万元）
	 */
	private BigDecimal mobileSmsBusiness;

	/**
	 * 物联网业务总量（万元）
	 */
	private BigDecimal iotBusiness;

	/**
	 * 互联网数据中心业务总量（万元）
	 */
	private BigDecimal idcBusiness;

	/**
	 * 其他业务总量（万元）
	 */
	private BigDecimal otherBusiness;

	/**
	 * 删除标志：0-正常；1-删除
	 */
	@TableLogic
	private String delFlag;

}