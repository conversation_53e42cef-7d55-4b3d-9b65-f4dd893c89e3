package com.enrising.ctsc.discharge.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 集团能耗同步日志
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2024-01-03
 */
@Data
public class DischargeJituanEnergySyncLogVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	private Long id;

	/**
	 * 上报人员
	 */
	private Long reportUser;

	/**
	 * 上报部门
	 */
	private Long reportDept;

	/**
	 * 上报数据
	 */
	@JsonFormat(pattern = "YYYY年MM月能源数据")
	private Date reportDate;

	/**
	 * 上报时间
	 */
	@JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss")
	private Date createTime;

	/**
	 * 上报用户
	 */
	private String reportUserName;

	/**
	 * 上报部门
	 */
	private String reportDeptName;
}
