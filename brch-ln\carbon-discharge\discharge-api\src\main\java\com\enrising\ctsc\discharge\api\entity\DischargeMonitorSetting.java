package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 碳排放监测设置表
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-01-03
 */

@Data
@TableName("discharge_monitor_setting")
public class DischargeMonitorSetting extends Model<DischargeMonitorSetting> {

	/**
	 * 主键id,采用雪花id
	 */
		@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 创建者id
	 */
		private Long createBy;

	/**
	 * 创建时间
	 */
		private Date createTime;

	/**
	 * 更新者id
	 */
		private Long updateBy;

	/**
	 * 更新时间
	 */
		private Date updateTime;

	/**
	 * 单位id
	 */
		private Long companyId;

	/**
	 * 年份
	 */
		private String year;

	/**
	 * 定额值
	 */
		private String quota = "0";

	/**
	 * 监测选项：1-去年全省碳排放量均值；2-去年碳排放总量%；3-自定义
	 */
		private String quotaSelection = "3";

	/**
	 * 监测选择值
	 */
		private String selectionValue;

	/**
	 * 删除标志：0-正常；1-删除
	 */
		private String delFlag;

}
