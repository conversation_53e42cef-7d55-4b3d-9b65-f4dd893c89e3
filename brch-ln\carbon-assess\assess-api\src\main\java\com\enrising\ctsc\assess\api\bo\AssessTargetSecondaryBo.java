package com.enrising.ctsc.assess.api.bo;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 考核二级指标
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
public class AssessTargetSecondaryBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 所属一级指标id
     */
    private Long primaryTargetId;

    /**
     * 指标名称
     */
    @NotBlank(message = "指标名称不能为空")
    @Length(max = 100, message = "指标名称不能超过100字符")
    private String targetName;

    /**
     * 指标分值
     */
    @NotNull(message = "指标分值不能为空")
    @Max(value = 100, message = "指标分值不能超过100")
    @Min(value = 0, message = "指标分值不能小于0")
    private Double score;

    /**
     * 指标说明
     */
    @Length(max = 1000, message = "指标说明不能超过1000个字符")
    private String targetDescription;

    /**
     * 指标算法
     */
    @Length(max = 1000, message = "指标算法不能超过1000字符")
    @NotBlank(message = "指标算法不能为空")
    private String algorithm;

    /**
     * 考核规则说明
     */
    @Length(max = 1000, message = "考核规则说明不能超过1000字符")
    private String rulesDescription;

    /**
     * 指标公式
     */
    private String formula;

    /**
     * 考核周期
     */
    @NotBlank(message = "考核周期不能为空")
    private String assessPeriod;


    /**
     * 考核二级指标规则列表
     */
    @Valid
    @NotNull(message = "考核规则不能为空")
    private List<AssessTargetSecondaryRuleBo> ruleList;

}
