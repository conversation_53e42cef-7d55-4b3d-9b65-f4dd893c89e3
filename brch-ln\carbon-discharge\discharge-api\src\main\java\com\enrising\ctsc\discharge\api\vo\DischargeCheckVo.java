package com.enrising.ctsc.discharge.api.vo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 碳排放核查
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeCheckVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 创建者id
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date createTime;

	/**
	 * 更新者id
	 */
	private Long updateBy;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date updateTime;

	/**
	 * 填报单位id
	 */
	private Long companyId;

	/**
	 * 核查单位名称
	 */
	private String checkCompany;

	/**
	 * 核查时间
	 */
	private String checkTime;

	/**
	 * 核查负责人
	 */
	private String checkDirector;

	/**
	 * 核查联系电话
	 */
	private String checkPhone;

	/**
	 * 说明
	 */
	private String remarks;

	/**
	 * 报告附件id
	 */
	private Long reportAttachmentId;

	/**
	 * 文件列表
	 */
//	private List<Attachment> attachmentList = new ArrayList<>();

	/**
	 * 文件名称
	 */
	private String fileName;

	/**
	 * 文件的url
	 */
	private String url;


}
