package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放数据填报表（油）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */

@Data
@TableName("discharge_data_oil")
public class DischargeDataOil extends Model<DischargeDataOil> {

	/**
	 * 主键id,采用雪花id
	 */
		@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 创建者id
	 */
		private Long createBy;

	/**
	 * 创建时间
	 */
		private Date createTime;

	/**
	 * 更新者id
	 */
		private Long updateBy;

	/**
	 * 更新时间
	 */
		private Date updateTime;

	/**
	 * 填报单位id
	 */
		private Long companyId;

	/**
	 * 填报时间
	 */
		private Date reportTime;

	/**
	 * 汽油
	 */
		private BigDecimal gasoline;

	/**
	 * 柴油
	 */
		private BigDecimal diesel;

	/**
	 * 原油
	 */
		private BigDecimal crude;

	/**
	 * 燃料油
	 */
		private BigDecimal fuel;

	/**
	 * 煤油
	 */
		private BigDecimal kerosene;

	/**
	 * 删除标志：0-正常；1-删除
	 */
	@TableLogic
		private String delFlag;

}
