package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.bo.DischargeMonitorSettingBo;
import com.enrising.ctsc.discharge.api.entity.DischargeMonitorSetting;
import com.enrising.ctsc.discharge.api.query.AssessTargetSecondaryQuery;
import com.enrising.ctsc.discharge.api.query.DischargeMonitorSettingQuery;
import com.enrising.ctsc.discharge.api.vo.CompanyCarbonVo;
import com.enrising.ctsc.discharge.api.vo.DischargeEmissionTrendVo;
import com.enrising.ctsc.discharge.api.vo.DischargeMonitorSettingVo;
import com.enrising.ctsc.discharge.api.vo.DischargeSettingCompanyVo;

import java.util.HashMap;
import java.util.List;

/**
 * 碳排放监测设置表
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-01-03
 */
public interface DischargeMonitorSettingService extends IService<DischargeMonitorSetting> {

	/**
	 * 分页查询
	 *
	 * @param page  分页
	 * @param query 参数
	 * @return 列表
	 */
	TableDataInfo<DischargeMonitorSettingVo> findList(Page<DischargeMonitorSettingVo> page, DischargeMonitorSettingQuery query);


	/**
	 * 获取监测设置分页列表
	 */
	TableDataInfo<DischargeMonitorSettingVo> getSettingList(Page<DischargeMonitorSettingVo> page, DischargeMonitorSettingBo bo);

	/**
	 * 获取监测记录分页列表
	 */
	TableDataInfo<DischargeMonitorSettingVo> getRecordsList(Page<DischargeMonitorSettingVo> page, DischargeMonitorSettingBo bo);

	/**
	 * 综合能耗（碳排放总量）增幅（16分）
	 */
	List<DischargeMonitorSettingVo> getCarbonDownByTemplateId(List<Long> companyIds);

	/**
	* 指标数据管理--获取某个指标对应的公司下的碳排量
	* */
	List<CompanyCarbonVo> getCompanyCarbonByTarget(AssessTargetSecondaryQuery query);

	/**
	 * 详情
	 *
	 * @param query 参数
	 * @return 详情
	 */
	DischargeMonitorSettingVo detail(DischargeMonitorSettingQuery query);

	/**
	 * 监测记录单个详情
	 *
	 * @param query 参数
	 * @return 详情
	 */
	DischargeMonitorSettingVo getRecordInfo(DischargeMonitorSettingQuery query);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	void add(DischargeMonitorSettingBo bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	void edit(DischargeMonitorSettingBo bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	void del(Long id);

	/**
	 * 批量设置定额值
	 */
	void batchSetting(List<DischargeMonitorSettingBo> list);

	/**
	 * 获取单位总碳排详情
	 *
	 */
	List<DischargeSettingCompanyVo> getCompanyCarbonList(DischargeMonitorSettingBo bo);

	/**
	 * 获取单位每月碳排详情
	 */
	List<CompanyCarbonVo> getMonthCarbonByTarget(AssessTargetSecondaryQuery query);

	/**
	 * 查询所有地市公司的 业务总量 / 业务流量总量
	 */
	List<DischargeEmissionTrendVo> getTelecomBusinessTotal(String nowYear);

	/**
	 * 查询各个公司的能源消耗总量
	 */
	TableDataInfo<DischargeMonitorSettingVo> getEnergyCompanyList(Page<DischargeMonitorSettingVo> page, DischargeMonitorSettingBo bo);

	HashMap<String, Object> getMonitorSituationList();
}
