package com.enrising.ctsc.discharge.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.carbon.common.utils.JwtUtils;
import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergyCalculate;
import com.enrising.ctsc.discharge.api.enums.DownType;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.discharge.api.query.DischargeDataEnergyQuery;
import com.enrising.ctsc.carbon.common.utils.ExcelExportStyler;
import com.enrising.ctsc.carbon.common.utils.ExcelUtil;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo;
import com.enrising.ctsc.discharge.mapper.DischargeDataEnergyCalculateMapper;
import com.enrising.ctsc.discharge.service.DischargeDataEnergyCalculateService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * 按规则计算能源数据表 服务实现
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DischargeDataEnergyCalculateServiceImpl extends ServiceImpl<DischargeDataEnergyCalculateMapper, DischargeDataEnergyCalculate> implements DischargeDataEnergyCalculateService {
    private final List<String> CalculateIndicatorCodeList = Collections.unmodifiableList(Arrays.asList("1.3", "1.3.1", "1.3.1.1", "1.3.1.2",
            "1.3.1.2.1", "1.3.1.2.2", "1.3.1.2.3", "1.3.1.3", "1.3.1.3.2", "1.3.1.3.3", "1.3.1.4", "1.3.2",  "1.3.2.1", "1.3.2.2", "1.1", "1.1.1", "1.2"));

    @Override
    public int saveData(DischargeDataEnergyCalculate dischargeDataEnergyCalculate) {
        int ret = 0;
        DischargeDataEnergyCalculate old = baseMapper.selectOne(new LambdaQueryWrapper<DischargeDataEnergyCalculate>()
                .eq(DischargeDataEnergyCalculate::getCompanyId, dischargeDataEnergyCalculate.getCompanyId())
                .eq(DischargeDataEnergyCalculate::getReportTime, dischargeDataEnergyCalculate.getReportTime())
                .eq(DischargeDataEnergyCalculate::getEnergyIndicatorId, dischargeDataEnergyCalculate.getEnergyIndicatorId()));
        if (ObjectUtil.isNotEmpty(old)) {
            ret = baseMapper.update(new LambdaUpdateWrapper<DischargeDataEnergyCalculate>()
                    .set(ObjectUtil.isNotEmpty(dischargeDataEnergyCalculate.getGroupData()), DischargeDataEnergyCalculate::getGroupData, dischargeDataEnergyCalculate.getGroupData())
                    .set(ObjectUtil.isNotEmpty(dischargeDataEnergyCalculate.getStockData()), DischargeDataEnergyCalculate::getStockData, dischargeDataEnergyCalculate.getStockData())
                    .set(ObjectUtil.isNotEmpty(dischargeDataEnergyCalculate.getLargeData()), DischargeDataEnergyCalculate::getLargeData, dischargeDataEnergyCalculate.getLargeData())
                    .set(ObjectUtil.isNotEmpty(dischargeDataEnergyCalculate.getMediumData()), DischargeDataEnergyCalculate::getMediumData, dischargeDataEnergyCalculate.getMediumData())
                    .set(ObjectUtil.isNotEmpty(dischargeDataEnergyCalculate.getMobileData()), DischargeDataEnergyCalculate::getMobileData, dischargeDataEnergyCalculate.getMobileData())
                    .set(StrUtil.isNotBlank(dischargeDataEnergyCalculate.getReportFlag()), DischargeDataEnergyCalculate::getReportFlag, dischargeDataEnergyCalculate.getReportFlag())
                    .set(DischargeDataEnergyCalculate::getUpdateTime, new Date())
                    .set(DischargeDataEnergyCalculate::getUpdateBy, JwtUtils.getUser().getId())
                    .eq(DischargeDataEnergyCalculate::getCompanyId, dischargeDataEnergyCalculate.getCompanyId())
                    .eq(DischargeDataEnergyCalculate::getReportTime, dischargeDataEnergyCalculate.getReportTime())
                    .eq(DischargeDataEnergyCalculate::getEnergyIndicatorId, dischargeDataEnergyCalculate.getEnergyIndicatorId()));
        } else {
            ret = baseMapper.insert(dischargeDataEnergyCalculate);
        }
        return ret;
    }

    @Override
    public List<DischargeDataEnergyVo> getAllDataList(DischargeDataEnergyCalculate query) {
        if (ObjectUtil.isEmpty(query.getReportTime())) {
            throw new BusinessException("数据时间不能为空！");
        }
        List<DischargeDataEnergyVo> dataList = baseMapper.getAllDataList(query.getCompanyId(), query.getReportTime());
        handleGroupChange(dataList);
        handleStockChange(dataList);
        BigDecimal mediumData11 = BigDecimal.ZERO
                .add(getValue(dataList.get(11).getGroupData()))
                .add(getValue(dataList.get(11).getStockData()));
        dataList.get(11).setMediumData(mediumData11.setScale(6, BigDecimal.ROUND_HALF_UP).toString());
        dataList.get(4).setMediumData(mediumData11.setScale(6, BigDecimal.ROUND_HALF_UP).toString());
        for (int i = 12; i < 17; i++) {
            BigDecimal mediumData = BigDecimal.ZERO
                    .add(getValue(dataList.get(i).getGroupData()))
                    .add(getValue(dataList.get(i).getStockData()));
            dataList.get(i).setMediumData(mediumData.setScale(6, BigDecimal.ROUND_HALF_UP).toString());
        }
        /*
        dataList.get(13).setMediumData(dataList.get(13).getStockData());
        dataList.get(14).setMediumData(dataList.get(14).getStockData());
        dataList.get(15).setMediumData(dataList.get(15).getStockData());
        dataList.get(16).setMediumData(dataList.get(16).getStockData());
        BigDecimal data11 = BigDecimal.ZERO;
        if (StrUtil.isNotBlank(dataList.get(13).getMediumData()) && !"/".equals(dataList.get(13).getMediumData())) {
            data11 = data11.add(new BigDecimal(dataList.get(13).getMediumData()));
        }
        if (StrUtil.isNotBlank(dataList.get(15).getMediumData()) && !"/".equals(dataList.get(15).getMediumData())) {
            data11 = data11.add(new BigDecimal(dataList.get(15).getMediumData()));
        }
        dataList.get(11).setMediumData(data11.setScale(4, BigDecimal.ROUND_HALF_UP).toString());
        BigDecimal data12 = BigDecimal.ZERO;
        if (StrUtil.isNotBlank(dataList.get(14).getMediumData()) && !"/".equals(dataList.get(14).getMediumData())) {
            data12 = data12.add(new BigDecimal(dataList.get(14).getMediumData()));
        }
        if (StrUtil.isNotBlank(dataList.get(16).getMediumData()) && !"/".equals(dataList.get(16).getMediumData())) {
            data12 = data12.add(new BigDecimal(dataList.get(16).getMediumData()));
        }
        dataList.get(12).setMediumData(data12.setScale(4, BigDecimal.ROUND_HALF_UP).toString());
        dataList.get(7).setMobileData(dataList.get(7).getStockData());
        dataList.get(8).setMobileData(dataList.get(8).getStockData());
        dataList.get(9).setMobileData(dataList.get(9).getStockData());
        dataList.get(10).setMobileData(dataList.get(10).getStockData());
        dataList.get(4).setMobileData(dataList.get(7).getMobileData());*/
        //集团数据
        BigDecimal consumptionGroup = countData(
                dataList.get(1).getGroupData(), dataList.get(1).getCoefficient(),
                dataList.get(3).getGroupData(), dataList.get(3).getCoefficient(),
                dataList.get(4).getGroupData(), dataList.get(4).getCoefficient(),
                dataList.get(21).getGroupData(), dataList.get(21).getCoefficient(),
                dataList.get(22).getGroupData(), dataList.get(22).getCoefficient(),
                dataList.get(25).getGroupData(), dataList.get(25).getCoefficient(),
                dataList.get(26).getGroupData(), dataList.get(26).getCoefficient(),
                dataList.get(29).getGroupData(), dataList.get(29).getCoefficient(),
                dataList.get(30).getGroupData(), dataList.get(30).getCoefficient(),
                dataList.get(31).getGroupData(), dataList.get(31).getCoefficient(),
                dataList.get(32).getGroupData(), dataList.get(32).getCoefficient(),
                dataList.get(33).getGroupData(),
                dataList.get(34).getGroupData(), dataList.get(34).getCoefficient(),
                dataList.get(35).getGroupData());
        /*if (StrUtil.isNotBlank(dataList.get(1).getGroupData()) && !"/".equals(dataList.get(1).getGroupData())) {
            consumptionGroup = consumptionGroup.add((new BigDecimal(dataList.get(1).getGroupData())).multiply(dataList.get(1).getCoefficient()));
        }
        if (StrUtil.isNotBlank(dataList.get(3).getGroupData()) && !"/".equals(dataList.get(3).getGroupData())) {
            consumptionGroup = consumptionGroup.add((new BigDecimal(dataList.get(3).getGroupData())).multiply(dataList.get(3).getCoefficient()));
        }
        if (StrUtil.isNotBlank(dataList.get(4).getGroupData()) && !"/".equals(dataList.get(4).getGroupData())) {
            consumptionGroup = consumptionGroup.add((new BigDecimal(dataList.get(4).getGroupData())).multiply(dataList.get(4).getCoefficient()));
        }*/
        dataList.get(0).setGroupData(consumptionGroup.setScale(6, BigDecimal.ROUND_HALF_UP).toString());

        //股份数据
        BigDecimal consumptionStock = countData(
                dataList.get(1).getStockData(), dataList.get(1).getCoefficient(),
                dataList.get(3).getStockData(), dataList.get(3).getCoefficient(),
                dataList.get(4).getStockData(), dataList.get(4).getCoefficient(),
                dataList.get(21).getStockData(), dataList.get(21).getCoefficient(),
                dataList.get(22).getStockData(), dataList.get(22).getCoefficient(),
                dataList.get(25).getStockData(), dataList.get(25).getCoefficient(),
                dataList.get(26).getStockData(), dataList.get(26).getCoefficient(),
                dataList.get(29).getStockData(), dataList.get(29).getCoefficient(),
                dataList.get(30).getStockData(), dataList.get(30).getCoefficient(),
                dataList.get(31).getStockData(), dataList.get(31).getCoefficient(),
                dataList.get(32).getStockData(), dataList.get(32).getCoefficient(),
                dataList.get(33).getStockData(),
                dataList.get(34).getStockData(), dataList.get(34).getCoefficient(),
                dataList.get(35).getStockData());

        /*if (StrUtil.isNotBlank(dataList.get(1).getStockData()) && !"/".equals(dataList.get(1).getStockData())) {
            consumptionStock = consumptionStock.add((new BigDecimal(dataList.get(1).getStockData())).multiply(dataList.get(1).getCoefficient()));
        }
        if (StrUtil.isNotBlank(dataList.get(3).getStockData()) && !"/".equals(dataList.get(3).getStockData())) {
            consumptionStock = consumptionStock.add((new BigDecimal(dataList.get(3).getGroupData())).multiply(dataList.get(3).getCoefficient()));
        }
        if (StrUtil.isNotBlank(dataList.get(4).getStockData()) && !"/".equals(dataList.get(4).getStockData())) {
            consumptionStock = consumptionStock.add((new BigDecimal(dataList.get(4).getGroupData())).multiply(dataList.get(4).getCoefficient()));
        }*/
        dataList.get(0).setStockData(consumptionStock.setScale(6, BigDecimal.ROUND_HALF_UP).toString());

        //大型数据中心数据
        BigDecimal consumptionLarge = countData(
                dataList.get(1).getLargeData(), dataList.get(1).getCoefficient(),
                dataList.get(3).getLargeData(), dataList.get(3).getCoefficient(),
                dataList.get(4).getLargeData(), dataList.get(4).getCoefficient(),
                dataList.get(21).getLargeData(), dataList.get(21).getCoefficient(),
                dataList.get(22).getLargeData(), dataList.get(22).getCoefficient(),
                dataList.get(25).getLargeData(), dataList.get(25).getCoefficient(),
                dataList.get(26).getLargeData(), dataList.get(26).getCoefficient(),
                dataList.get(29).getLargeData(), dataList.get(29).getCoefficient(),
                dataList.get(30).getLargeData(), dataList.get(30).getCoefficient(),
                dataList.get(31).getLargeData(), dataList.get(31).getCoefficient(),
                dataList.get(32).getLargeData(), dataList.get(32).getCoefficient(),
                dataList.get(33).getLargeData(),
                dataList.get(34).getLargeData(), dataList.get(34).getCoefficient(),
                dataList.get(35).getLargeData());
        /*if (StrUtil.isNotBlank(dataList.get(1).getLargeData()) && !"/".equals(dataList.get(1).getLargeData())) {
            consumptionLarge = consumptionLarge.add((new BigDecimal(dataList.get(1).getLargeData())).multiply(dataList.get(1).getCoefficient()));
        }
        if (StrUtil.isNotBlank(dataList.get(3).getLargeData()) && !"/".equals(dataList.get(3).getLargeData())) {
            consumptionLarge = consumptionLarge.add((new BigDecimal(dataList.get(3).getLargeData())).multiply(dataList.get(3).getCoefficient()));
        }
        if (StrUtil.isNotBlank(dataList.get(4).getLargeData()) && !"/".equals(dataList.get(4).getLargeData())) {
            consumptionLarge = consumptionLarge.add((new BigDecimal(dataList.get(4).getLargeData())).multiply(dataList.get(4).getCoefficient()));
        }*/
        dataList.get(0).setLargeData(consumptionLarge.setScale(6, BigDecimal.ROUND_HALF_UP).toString());

        //中小型数据中心数据
        BigDecimal consumptionMedium = countData(
                dataList.get(1).getMediumData(), dataList.get(1).getCoefficient(),
                dataList.get(3).getMediumData(), dataList.get(3).getCoefficient(),
                dataList.get(4).getMediumData(), dataList.get(4).getCoefficient(),
                dataList.get(21).getMediumData(), dataList.get(21).getCoefficient(),
                dataList.get(22).getMediumData(), dataList.get(22).getCoefficient(),
                dataList.get(25).getMediumData(), dataList.get(25).getCoefficient(),
                dataList.get(26).getMediumData(), dataList.get(26).getCoefficient(),
                dataList.get(29).getMediumData(), dataList.get(29).getCoefficient(),
                dataList.get(30).getMediumData(), dataList.get(30).getCoefficient(),
                dataList.get(31).getMediumData(), dataList.get(31).getCoefficient(),
                dataList.get(32).getMediumData(), dataList.get(32).getCoefficient(),
                dataList.get(33).getMediumData(),
                dataList.get(34).getMediumData(), dataList.get(34).getCoefficient(),
                dataList.get(35).getMediumData());
        /*if (StrUtil.isNotBlank(dataList.get(1).getMediumData()) && !"/".equals(dataList.get(1).getMediumData())) {
            consumptionMedium = consumptionMedium.add((new BigDecimal(dataList.get(1).getMediumData())).multiply(dataList.get(1).getCoefficient()));
        }
        if (StrUtil.isNotBlank(dataList.get(3).getMediumData()) && !"/".equals(dataList.get(3).getMediumData())) {
            consumptionMedium = consumptionMedium.add((new BigDecimal(dataList.get(3).getMediumData())).multiply(dataList.get(3).getCoefficient()));
        }
        if (StrUtil.isNotBlank(dataList.get(4).getMediumData()) && !"/".equals(dataList.get(4).getMediumData())) {
            consumptionMedium = consumptionMedium.add((new BigDecimal(dataList.get(4).getMediumData())).multiply(dataList.get(4).getCoefficient()));
        }*/
        dataList.get(0).setMediumData(consumptionMedium.setScale(6, BigDecimal.ROUND_HALF_UP).toString());

        //移动业务数据
        BigDecimal consumptionMobile = countData(
                dataList.get(1).getMobileData(), dataList.get(1).getCoefficient(),
                dataList.get(3).getMobileData(), dataList.get(3).getCoefficient(),
                dataList.get(4).getMobileData(), dataList.get(4).getCoefficient(),
                dataList.get(21).getMobileData(), dataList.get(21).getCoefficient(),
                dataList.get(22).getMobileData(), dataList.get(22).getCoefficient(),
                dataList.get(25).getMobileData(), dataList.get(25).getCoefficient(),
                dataList.get(26).getMobileData(), dataList.get(26).getCoefficient(),
                dataList.get(29).getMobileData(), dataList.get(29).getCoefficient(),
                dataList.get(30).getMobileData(), dataList.get(30).getCoefficient(),
                dataList.get(31).getMobileData(), dataList.get(31).getCoefficient(),
                dataList.get(32).getMobileData(), dataList.get(32).getCoefficient(),
                dataList.get(33).getMobileData(),
                dataList.get(34).getMobileData(), dataList.get(34).getCoefficient(),
                dataList.get(35).getMobileData());
        /*if (StrUtil.isNotBlank(dataList.get(1).getMobileData()) && !"/".equals(dataList.get(1).getMobileData())) {
            consumptionMobile = consumptionMobile.add((new BigDecimal(dataList.get(1).getMobileData())).multiply(dataList.get(1).getCoefficient()));
        }
        if (StrUtil.isNotBlank(dataList.get(3).getMobileData()) && !"/".equals(dataList.get(3).getMobileData())) {
            consumptionMobile = consumptionMobile.add((new BigDecimal(dataList.get(3).getMobileData())).multiply(dataList.get(3).getCoefficient()));
        }
        if (StrUtil.isNotBlank(dataList.get(4).getMobileData()) && !"/".equals(dataList.get(4).getMobileData())) {
            consumptionMobile = consumptionMobile.add((new BigDecimal(dataList.get(4).getMobileData())).multiply(dataList.get(4).getCoefficient()));
        }*/
        dataList.get(0).setMobileData(consumptionMobile.setScale(6, BigDecimal.ROUND_HALF_UP).toString());
        return dataList;
    }

    //计算 能源消费总量(吨标煤)-集团数据/股份数据
    private BigDecimal countData(String data1, BigDecimal coefficient1,
                                 String data3, BigDecimal coefficient3,
                                 String data4, BigDecimal coefficient4,
                                 String data21, BigDecimal coefficient21,
                                 String data22, BigDecimal coefficient22,
                                 String data25, BigDecimal coefficient25,
                                 String data26, BigDecimal coefficient26,
                                 String data29, BigDecimal coefficient29,
                                 String data30, BigDecimal coefficient30,
                                 String data31, BigDecimal coefficient31,
                                 String data32, BigDecimal coefficient32,
                                 String data33,
                                 String data34, BigDecimal coefficient34,
                                 String data35
                                 ) {
        BigDecimal res = BigDecimal.ZERO;
        res = res.add(calData(data1, coefficient1, false));
        res = res.add(calData(data3, coefficient3, false));
        res = res.add(calData(data4, coefficient4, true));
        res = res.add(calData(data21, coefficient21, false));
        res = res.add(calData(data22, coefficient22, true));
        res = res.add(calData(data25, coefficient25, true));
        res = res.add(calData(data26, coefficient26, true));
        res = res.add(calData(data29, coefficient29, true));
        res = res.add(calData(data30, coefficient30, false));
        res = res.add(calData(data31, coefficient31, true));
        res = res.add(calData(data32, coefficient32, false));
        res = res.add(calData(data33, null, false));
        res = res.add(calData(data34, coefficient34, true));
        res = res.subtract(calData(data35, null, false));
        return res;
    }

    /**
     * 数据转换
     * @param data         数据
     * @param coefficient  系数
     * @param isDivide1000 是否除以1000
     * @return
     */
    private BigDecimal calData(String data, BigDecimal coefficient, boolean isDivide1000) {
        BigDecimal res = BigDecimal.ZERO;
        if (StrUtil.isNotBlank(data) && !"/".equals(data)) {
            res = new BigDecimal(data);
            if (coefficient != null) {
                res = res.multiply(coefficient);
            }
            if (isDivide1000) {
                res = res.divide(new BigDecimal("1000"));
            }
        }
        return res;
    }

    private void handleGroupChange(List<DischargeDataEnergyVo> dataList) {
        BigDecimal groupData7 = BigDecimal.ZERO
                .add(getValue(dataList.get(8).getGroupData()))
                .add(getValue(dataList.get(9).getGroupData()))
                .add(getValue(dataList.get(10).getGroupData()));
        dataList.get(7).setGroupData(groupData7.setScale(6, BigDecimal.ROUND_HALF_UP).toString());

        BigDecimal groupData5 = BigDecimal.ZERO
                .add(getValue(dataList.get(6).getGroupData()))
                .add(getValue(dataList.get(7).getGroupData()))
                .add(getValue(dataList.get(11).getGroupData()))
                .add(getValue(dataList.get(17).getGroupData()));
        dataList.get(5).setGroupData(groupData5.setScale(6, BigDecimal.ROUND_HALF_UP).toString());

        BigDecimal groupData18 = BigDecimal.ZERO
                .add(getValue(dataList.get(19).getGroupData()))
                .add(getValue(dataList.get(20).getGroupData()));
        dataList.get(18).setGroupData(groupData18.setScale(6, BigDecimal.ROUND_HALF_UP).toString());

        BigDecimal groupData4 = BigDecimal.ZERO
                .add(getValue(dataList.get(5).getGroupData()))
                .add(getValue(dataList.get(18).getGroupData()));
        dataList.get(4).setGroupData(groupData4.setScale(6, BigDecimal.ROUND_HALF_UP).toString());

        BigDecimal groupData22 = BigDecimal.ZERO
                .add(getValue(dataList.get(23).getGroupData()))
                .add(getValue(dataList.get(24).getGroupData()));
        dataList.get(22).setGroupData(groupData22.setScale(6, BigDecimal.ROUND_HALF_UP).toString());

        BigDecimal groupData26 = BigDecimal.ZERO
                .add(getValue(dataList.get(27).getGroupData()))
                .add(getValue(dataList.get(28).getGroupData()));
        dataList.get(26).setGroupData(groupData26.setScale(6, BigDecimal.ROUND_HALF_UP).toString());
    }

    private void handleStockChange(List<DischargeDataEnergyVo> dataList) {
        for (int i = 8; i <= 10; i++) {
            BigDecimal mobileData = BigDecimal.ZERO
                    .add(getValue(dataList.get(i).getGroupData()))
                    .add(getValue(dataList.get(i).getStockData()));
            dataList.get(i).setMobileData(mobileData.setScale(6, BigDecimal.ROUND_HALF_UP).toString());
        }

        BigDecimal stockData7 = BigDecimal.ZERO
                .add(getValue(dataList.get(8).getStockData()))
                .add(getValue(dataList.get(9).getStockData()))
                .add(getValue(dataList.get(10).getStockData()));
        dataList.get(7).setStockData(stockData7.setScale(6, BigDecimal.ROUND_HALF_UP).toString());
        dataList.get(7).setMobileData(stockData7.setScale(6, BigDecimal.ROUND_HALF_UP).toString());
        dataList.get(4).setMobileData(stockData7.setScale(6, BigDecimal.ROUND_HALF_UP).toString());

        BigDecimal stockData5 = BigDecimal.ZERO
                .add(getValue(dataList.get(6).getStockData()))
                .add(getValue(dataList.get(7).getStockData()))
                .add(getValue(dataList.get(11).getStockData()))
                .add(getValue(dataList.get(17).getStockData()));
        dataList.get(5).setStockData(stockData5.setScale(6, BigDecimal.ROUND_HALF_UP).toString());

        BigDecimal stockData18 = BigDecimal.ZERO
                .add(getValue(dataList.get(19).getStockData()))
                .add(getValue(dataList.get(20).getStockData()));
        dataList.get(18).setStockData(stockData18.setScale(6, BigDecimal.ROUND_HALF_UP).toString());

        BigDecimal stockData4 = BigDecimal.ZERO
                .add(getValue(dataList.get(5).getStockData()))
                .add(getValue(dataList.get(18).getStockData()));
        dataList.get(4).setStockData(stockData4.setScale(6, BigDecimal.ROUND_HALF_UP).toString());

        BigDecimal stockData22 = BigDecimal.ZERO
                .add(getValue(dataList.get(23).getStockData()))
                .add(getValue(dataList.get(24).getStockData()));
        dataList.get(22).setStockData(stockData22.setScale(6, BigDecimal.ROUND_HALF_UP).toString());

        BigDecimal stockData26 = BigDecimal.ZERO
                .add(getValue(dataList.get(27).getStockData()))
                .add(getValue(dataList.get(28).getStockData()));
        dataList.get(26).setStockData(stockData26.setScale(6, BigDecimal.ROUND_HALF_UP).toString());
    }

    private BigDecimal getValue(String data){
        BigDecimal res = BigDecimal.ZERO;
        if (StrUtil.isNotBlank(data) && !"/".equals(data)) {
            res = new BigDecimal(data);
        }
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveList(List<DischargeDataEnergyCalculate> dischargeDataEnergyCalculateList) {
        if (CollectionUtil.isEmpty(dischargeDataEnergyCalculateList)) {
            return false;
        }
        Long companyId;
        if (ObjectUtil.isNotEmpty(dischargeDataEnergyCalculateList.get(0).getCompanyId())) {
            companyId = dischargeDataEnergyCalculateList.get(0).getCompanyId();
        } else {
            companyId = JwtUtils.getCurrentUserCompanyId();
        }
        for (DischargeDataEnergyCalculate dischargeDataEnergyCalculate : dischargeDataEnergyCalculateList) {
            dischargeDataEnergyCalculate.setCompanyId(companyId);
//            dischargeDataEnergyCalculate.setReportFlag(DischargeDataTotalEnum.ReportGroupFlagType.未上报集团.getValue());
            if (ObjectUtil.isNotEmpty(dischargeDataEnergyCalculate.getGroupData()) ||
                    ObjectUtil.isNotEmpty(dischargeDataEnergyCalculate.getStockData()) ||
                    ObjectUtil.isNotEmpty(dischargeDataEnergyCalculate.getLargeData()) ||
                    ObjectUtil.isNotEmpty(dischargeDataEnergyCalculate.getMediumData()) ||
                    ObjectUtil.isNotEmpty(dischargeDataEnergyCalculate.getMobileData()))
            saveData(dischargeDataEnergyCalculate);
        }
        return true;
    }

    @Override
    public int removeCalculateData(Long companyId, Date reportTime) {
        return baseMapper.removeCalculateData(companyId, reportTime, CalculateIndicatorCodeList);
    }

    @Override
    @SneakyThrows
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, DischargeDataEnergyQuery query) {
        Workbook workbook = null;
        if (DownType.ALL.getValue().equals(query.getDownType())) {
            if (ObjectUtil.isEmpty(query.getReportTime())) {
                throw new BusinessException("数据时间不能为空！");
            }
            if (CollectionUtil.isNotEmpty(query.getCompanyIds())) {
                //多个map，对应了多个sheet
                List<Map<String, Object>> listMap = new ArrayList<>();
                for (Long companyId : query.getCompanyIds()) {
                    DischargeDataEnergyCalculate dischargeDataEnergyCalculate = new DischargeDataEnergyCalculate();
                    dischargeDataEnergyCalculate.setCompanyId(companyId);
                    dischargeDataEnergyCalculate.setReportTime(query.getReportTime());
                    List<DischargeDataEnergyVo> dataList = getAllDataList(dischargeDataEnergyCalculate);
                    if (ObjectUtil.isEmpty(companyId)) {
                        dataList.get(0).setCompanyName("全省汇总");
                    }
                    if (CollectionUtil.isNotEmpty(dataList) && StrUtil.isNotBlank(dataList.get(0).getCompanyName())) {
                        Map<String, Object> map = new HashMap<>();
                        String sheetName = dataList.get(0).getCompanyName();
                        ExportParams exportParams = new ExportParams(null, sheetName, ExcelType.HSSF);
                        exportParams.setStyle(ExcelExportStyler.class);
                        exportParams.setSheetName(sheetName);
                        map.put("title", exportParams);
                        //表格对应实体
                        map.put("entity", DischargeDataEnergyVo.class);
                        map.put("data", dataList);
                        listMap.add(map);
                    }
                }
                workbook = ExcelExportUtil.exportExcel(listMap, ExcelType.HSSF);
            }
        } else {
            DischargeDataEnergyCalculate dischargeDataEnergyCalculate = new DischargeDataEnergyCalculate();
            dischargeDataEnergyCalculate.setCompanyId(query.getCompanyId());
            dischargeDataEnergyCalculate.setReportTime(query.getReportTime());
            List<DischargeDataEnergyVo> dataList = getAllDataList(dischargeDataEnergyCalculate);
            if (CollectionUtil.isNotEmpty(dataList)) {
                ExportParams exportParams = new ExportParams();
                //exportParams.setTitle(query.getReportCompany()+" "+ "填报月份： "+query.getReportMonth());
                exportParams.setStyle(ExcelExportStyler.class);
                exportParams.setSheetName("能源数据汇总");
                workbook = ExcelExportUtil.exportExcel(exportParams,
                        DischargeDataEnergyVo.class, dataList);
            }
        }
        //实现页面下载
        ExcelUtil.setResponseHeader(request, response, "能源数据汇总.xls");
        //创建页面输出流对象
        ServletOutputStream outputStream = response.getOutputStream();
        //把文件写入输出流的对象中
        assert workbook != null;
        workbook.write(outputStream);
        outputStream.close();
    }

    @Override
    public List<DischargeDataEnergyVo> getCalcReportList(DischargeDataEnergyQuery query) {
        if (ObjectUtil.isEmpty(query.getReportTime())) {
            throw new BusinessException("数据时间不能为空！");
        }
        return baseMapper.getCalcReportList(query);
    }
}
