package com.enrising.ctsc.carbon.common.entity;

import cn.zhxu.bs.bean.SearchBean;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 附件表 attachments
 *
 * <AUTHOR>
 * @date 2019-01-24
 */
@Data
@SearchBean(tables = "attachments")
@TableName("attachments")
public class Attachments implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** mongodb的文件ID */
    @TableField("mongodb_file_id")
    private String mongodbFileId;

    /** 业务模块主表id */
    @TableField("busi_id")
    private Long busiId;

    /** 附件分类id */
    @TableField("category_id")
    private Long categoryId;

    /** 分类名称(模块) */
    @TableField("category_name")
    private String categoryName;

    /** 分类编码(模块) */
    @TableField("category_code")
    private String categoryCode;

    /** 附件分类名称 */
    @TableField("busi_alias_name")
    private String busiAliasName;

    /** 附件分类编码 */
    @TableField("busi_alias")
    private String busiAlias;

    /** 附件名称 */
    @TableField("file_name")
    private String fileName;

    /** 数据库名 */
    @TableField("database_name")
    private String databaseName;

    /** 集合 */
    @TableField("collection")
    private String collection;

    /** 文件大小 */
    @TableField("file_size")
    private Long fileSize;

    /** 文件类型 */
    @TableField("file_type")
    private String fileType;

    /** 区域 */
    @TableField("area_code")
    private String areaCode;

    /** 分片字段 */
    @TableField("shard_key")
    private String shardKey;

    /** 文件存储方式，默认空是mongodb存储 gj方式下载附件，如果是割接的附件那直接从url里取地址下载 */
    @TableField("save_type")
    private String saveType;

    /** 缩略图url(mongdbid#dabasename#collection 数据分割存储) */
    @TableField("litimg_url")
    private String litimgUrl;

    /** 文件物理路径 */
    @TableField("url")
    private String url;

    /** 描述 */
    @TableField("description")
    private String description;

    /** 创建人id */
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    private Long creatorId;

    /** 创建人姓名 */
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    private String creatorName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建年份
     */
    @TableField(value = "year", fill = FieldFill.INSERT)
    private Integer year;

    /**
     * 删除标识
     */
    @TableLogic
    @TableField("del_flag")
    private String delFlag;

    /**
     * 桶名称
     */
    @TableField("bucket_name")
    private String bucketName;

    /**
     * minio对象名
     */
    @TableField("object_name")
    private String objectName;

    /**
     * 发票标识 1 是 0 否（报账管理处用）
     */
    @TableField("invoice_flag")
    private String invoiceFlag;

    /**
     * 发票供货商名称（报账管理用）
     */
    @TableField("invoice_supplier")
    private String invoiceSupplier;
}
