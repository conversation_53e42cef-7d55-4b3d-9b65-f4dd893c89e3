package com.enrising.ctsc.discharge.api.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CompanyCarbonVo {

	/*
	* 公司id
	* */
	private Long companyId;

	/*
	 * 公司名称
	 * */
	private String companyName;

	/*
	 * 去年业务碳排放量（吨）
	 * */
	private BigDecimal preCarbonTotal = new BigDecimal(0);

	/*
	 * 今年业务碳排放量（吨）
	 * */
	private BigDecimal nowCarbonTotal = new BigDecimal(0);

	/*
	 * 去年业务碳排放量（吨）
	 * */
	private BigDecimal preAllCompanyTotal = new BigDecimal(0);

	/*
	 * 今年业务碳排放量（吨）(全公司)
	 * */
	private BigDecimal nowAllCompanyTotal = new BigDecimal(0);

	/*
	 * 下降率（全公司）
	 * */
	private String allCompanyDownRate;
	/*
	 * 下降率
	 * */
	private String downRate;

	/*
	 * 月份
	 * */
	private String month;

}
