package com.enrising.ctsc.discharge.api.vo;

import cn.zhxu.bs.bean.DbIgnore;
import cn.zhxu.bs.bean.SearchBean;
import com.enrising.ctsc.carbon.common.entity.Attachments;
import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 绿电管理
*
* <AUTHOR> <EMAIL>
* @since 1.0.0 2024-09-15
*/
@Data
@SearchBean(tables = "green_energy_management")
public class GreenEnergyManagementVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	* 主键ID
	*/
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private Long id;

	/**
	* 交易年月
	*/
	@JsonFormat(pattern = DateUtils.YYYY_MM)
	private Date transactionMonth;

	/**
	* 所属分公司
	*/
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private Long companies;

	/**
	 * 所属分公司名称
	 */
	@DbIgnore
	private String companiesName;

	/**
	 * 所属部门
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private Long companyBranch;

	/**
	 * 所属部门名称
	 */
	@DbIgnore
	private String companyBranchName;

	/**
	* 分月合同绿电量(兆瓦时)
	*/
	private BigDecimal monthlyContractPower;

	/**
	* 所属主体
	*/
	private String subjectEntity;

	/**
	* 电源所属地
	*/
	private String powerSourceLocation;

	/**
	* 能源类型
	*/
	private String energyType;

	/**
	* 价格
	*/
	private BigDecimal price;

	/**
	* 证明材料文件路径
	*/
	private Long supportingDocument;


	/**
	 * 抵扣碳排放（tCO2)
	 */
	private BigDecimal deduction;

	/**
	* 备注
	*/
	private String remarks;

	/**
	* 创建人
	*/
	private Long createBy;

	/**
	* 创建时间
	*/
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date createTime;

	/**
	* 更新时间
	*/
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date updateTime;

	/**
	 * 审核结果
	 */
	private String auditResult;

	/**
	 * 附件
	 */
	@DbIgnore
	private Attachments attachments;

}
