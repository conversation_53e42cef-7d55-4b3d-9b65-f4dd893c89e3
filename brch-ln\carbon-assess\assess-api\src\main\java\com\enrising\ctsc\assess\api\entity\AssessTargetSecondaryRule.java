package com.enrising.ctsc.assess.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 考核二级指标规则
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */

@Data
@TableName("assess_target_secondary_rule")
public class AssessTargetSecondaryRule extends Model<AssessTargetSecondaryRule> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 所属二级指标id
     */
    private Long secondaryTargetId;

    /**
     * 考核规则判断
     */
    private String ruleJudge;

    /**
     * 考核规则值
     */
    private Double ruleValue;

    /**
     * 考核规则得分
     */
    private Double ruleScore;

    /**
     * 所属一级指标id
     */
    private Long primaryTargetId;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;
}
