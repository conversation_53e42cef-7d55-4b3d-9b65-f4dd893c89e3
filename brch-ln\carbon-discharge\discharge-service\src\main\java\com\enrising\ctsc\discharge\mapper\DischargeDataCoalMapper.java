package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.bo.DischargeDataCoalBo;
import com.enrising.ctsc.discharge.api.bo.DischargeDataElectricBo;
import com.enrising.ctsc.discharge.api.bo.DischargeMonitorSettingBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataCoal;
import com.enrising.ctsc.discharge.api.query.DischargeDataCoalQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeDataCoalVo;
import com.enrising.ctsc.discharge.api.vo.DischargeDataTotalVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 碳排放数据填报表（煤碳）
 *
 * <AUTHOR>
 * @since 1.0.0 2023-03-06
 */
@Mapper
public interface DischargeDataCoalMapper extends BaseMapper<DischargeDataCoal> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
	IPage<DischargeDataCoalVo> findList(Page<DischargeDataCoalVo> page, @Param(Constants.WRAPPER) Wrapper<DischargeDataCoalQuery> wrapper);

	/**
	 * 列表查询
	 *
	 * @param wrapper 条件
	 * @return 列表
	 */
	List<DischargeDataCoalVo> findList(@Param(Constants.WRAPPER) Wrapper<DischargeDataCoalQuery> wrapper);

	/**
	 * 详情查询
	 *
	 * @param wrapper 条件
	 * @return 结果
	 */
	DischargeDataCoalVo detail(@Param(Constants.WRAPPER) Wrapper<DischargeDataCoalQuery> wrapper);


	List<DischargeDataCoalVo> getCompanyCarbonList(@Param("bo") DischargeMonitorSettingBo bo);

	/**
	 * 查询统计数据列表
	 *
	 * @param queryBo 查询条件
	 * @return 结果
	 */
	List<DischargeDataCoalVo> countCompanyData(@Param("queryBo") DischargeDataCoalBo queryBo);

	/**
	 * 查询公司数据列表
	 *
	 * @param queryBo 查询条件
	 * @return 结果
	 */
	List<DischargeDataCoalVo> getCompanyDataList(@Param("queryBo") DischargeDataCoalBo queryBo);


	/**
	 * 查询统计月数据列表
	 *
	 * @param queryBo 查询条件
	 * @return 统计月数据列表
	 */
	List<DischargeDataTotalVo> countCarbonCompare(@Param("queryBo") DischargeDataElectricBo queryBo);

}