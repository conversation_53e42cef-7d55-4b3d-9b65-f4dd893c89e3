package com.enrising.ctsc.discharge.api.bo;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 碳排放能源转换系数表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeEnergyCoefficientBo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
		private Long id;


	/**
	 * 能源类型id
	 */
		private Long energyTypeId;

	/**
	 * 时间区间
	 */
	@NotNull(message = "有效期不能为空")
	private List<String> daterange;

	/**
	 * 转换系数
	 */
	@Max(value = 9999, message = "转换系数不能大于9999")
		private BigDecimal coefficient;

	/**
	 * 数据标准来源
	 */
	@NotBlank(message = "数据标准来源不能为空")
	@Length(max = 255,message = "数据标准来源字符不能超过255")
		private String source;


	/**
	 * 能源类型单位名称
	 */
	@Length(max = 32,message = "能源类型单位名称字符不能超过32")
	@NotBlank(message = "能源类型单位名称")
	private String energyTypeUnitName;
}
