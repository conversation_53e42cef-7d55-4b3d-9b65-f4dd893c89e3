package com.enrising.ctsc.discharge.api.bo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergyUpdateLog;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 碳排放数据填报表（能源）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeDataEnergyBo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
	private Long id;


	/**
	 * 填报单位id
	 */
	private Long companyId;

	/**
	 * 数据修改记录表id
	 */
	private Long updateRecordId;

	/**
	 * 填报时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date reportTime;

	/**
	 * 能源指标id
	 */
	private Long energyIndicatorId;

	/**
	 * 上报标志：2-已上报；3-未上报
	 */
	private String reportFlag;

	/**
	 * 集团数据
	 */
	private BigDecimal groupData;

	/**
	 * 股份数据
	 */
	private BigDecimal stockData;

	/**
	 * 大型数据中心数据
	 */
	private BigDecimal largeData;

	/**
	 * 中小型数据中心数据
	 */
	private BigDecimal mediumData;

	/**
	 * 移动业务数据
	 */
	private BigDecimal mobileData;

	/**
	 * 修改的数据
	 */
	private List<DischargeEnergyIndicatorVo> dischargeEnergyIndicatorVos;

	/**
	 * 修改的坐标数据
	 */
	private List<DischargeDataEnergyUpdateLog> dischargeDataEnergyUpdateLogList;
	/**
	 * 申请退回理由
	 */
	private String applyReason;
	/**
	 * 退回理由
	 */
	private String returnReason;

	/**
	 * 统计类型：0-原始数据，1-按规则计算
	 */
	private String countType;
}
