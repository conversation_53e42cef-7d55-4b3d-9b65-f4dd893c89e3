package com.enrising.ctsc.assess.api.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.List;

/**
 * 考核模板指标
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-08
 */

@Data
public class AssessTemplateTargetVo extends Model<AssessTemplateTargetVo> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 考核模板id
     */
    private Long templateId;

    /**
     * 考核指标id
     */
    private Long targetId;

    /**
     * 考核指标类型
     */
    private String targetType;

    /**
     * 考核指标周期
     */
    private String assessPeriod;

    /**
     * 指标类别
     */
    private String targetCategory;

    /**
     * 二级考核指标id
     */
    private Long secondaryId;

    /**
     * 二级考核指标id
     */
    private Long secondaryTargetId;

    /**
     * 二级考指标名称
     */
    private String secondaryTargetName;

    /**
     * 二级考指标分数
     */
    private Double secondaryTargetScore;

    /**
     * 二级考指标分数
     */
    private Double score;

    /**
     * 二级考指标公式
     */
    private String secondaryTargetFormula;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 考核方式
     */
    private String assessMethod;

    /**
     * 考核对象类型
     */
    private String objectType;

    /**
     * 指标算法
     */
    private String formula;

    /**
     * 考核对象列表
     */
    private List<AssessTemplateTargetObjectVo> assessTemplateTargetObjectVoList;
}
