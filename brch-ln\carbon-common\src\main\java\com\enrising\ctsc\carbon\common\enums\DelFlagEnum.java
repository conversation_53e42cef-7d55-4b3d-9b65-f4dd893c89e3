package com.enrising.ctsc.carbon.common.enums;

/**
 * 删除标识枚举
 */
public enum DelFlagEnum {

    /**
     *逻辑删除
     */
    DELETED("1", "逻辑删除"),
    /**
     * 正常
     */
    NOT_DELETED("0", "正常");

    private String value;
    private String name;

    DelFlagEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static String getNameByKey(char key) {
        for (DelFlagEnum vo : DelFlagEnum.values()) {
            if (vo.getValue().equals(key)) {
                return vo.getName();
            }
        }
        return null;
    }
}
