package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.bo.DischargeBasedataRankingBo;
import com.enrising.ctsc.discharge.api.entity.DischargeBasedataRanking;
import com.enrising.ctsc.discharge.api.enums.DisplayState;
import com.enrising.ctsc.discharge.api.query.DischargeBasedataRankingQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeBasedataRankingVo;
import com.enrising.ctsc.discharge.mapper.DischargeBasedataRankingMapper;
import com.enrising.ctsc.discharge.service.DischargeBasedataRankingService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 集团排名情况
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-10-13
 */
@Service
@AllArgsConstructor
public class DischargeBasedataRankingServiceImpl extends ServiceImpl<DischargeBasedataRankingMapper, DischargeBasedataRanking> implements DischargeBasedataRankingService {

	@Override
	public TableDataInfo<DischargeBasedataRankingVo> findList(Page<DischargeBasedataRankingVo> page, DischargeBasedataRankingQuery query) {
		MPJLambdaWrapper<DischargeBasedataRanking> wrapper = this.getWrapper(query);
		IPage<DischargeBasedataRankingVo> resultPage = baseMapper.selectJoinPage(page, DischargeBasedataRankingVo.class, wrapper);
		return TableDataInfo.build(resultPage);
	}

	@Override
	public DischargeBasedataRankingVo detail(DischargeBasedataRankingQuery query) {
		if (ObjectUtil.allFieldIsNull(query)) {
			throw new BusinessException("查询参数不能为空");
		}
		MPJLambdaWrapper<DischargeBasedataRanking> wrapper = this.getWrapper(query);
		return baseMapper.selectJoinOne(DischargeBasedataRankingVo.class, wrapper);
	}

	private MPJLambdaWrapper<DischargeBasedataRanking> getWrapper(DischargeBasedataRankingQuery query) {
		MPJLambdaWrapper<DischargeBasedataRanking> wrapper = new MPJLambdaWrapper<>();
		wrapper.selectAll(DischargeBasedataRanking.class);
		if (StrUtil.isNotBlank(query.getKeys())) {
			wrapper.eq(DischargeBasedataRanking::getTimePeriodYear, query.getKeys());
		}
		wrapper.eq(StrUtil.isNotBlank(query.getDisplayState()), DischargeBasedataRanking::getDisplayState, query.getDisplayState());
		wrapper.eq(query.getId() != null, "t.id", query.getId());
		wrapper.orderByDesc("t.create_time");
		return wrapper;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void add(DischargeBasedataRankingBo bo) {
		DischargeBasedataRanking entity = new DischargeBasedataRanking();
		BeanUtils.copyProperties(bo, entity);
		entity.setDisplayState(DisplayState.DO_NOT_SHOW.getValue());
		baseMapper.insert(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void edit(DischargeBasedataRankingBo bo) {
		if (bo.getId() == null) {
			throw new BusinessException("id不能为空");
		}
		DischargeBasedataRanking entity = new DischargeBasedataRanking();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.updateById(entity);
	}

	@Override
	public void setDisplayState(DischargeBasedataRankingBo bo) {
		if (null == bo.getId()) {
			throw new BusinessException("id不能为空");
		}
		if (StrUtil.isBlank(bo.getDisplayState())) {
			throw new BusinessException("展示状态不能为空");
		}
		// 展示
		if (bo.getDisplayState().equals(DisplayState.SHOW.getValue())) {
			// 把所有的设置为否
			DischargeBasedataRanking cleanDisplay = new DischargeBasedataRanking();
			cleanDisplay.setDisplayState(DisplayState.DO_NOT_SHOW.getValue());
			baseMapper.update(cleanDisplay, Wrappers.<DischargeBasedataRanking>lambdaUpdate().eq(DischargeBasedataRanking::getDisplayState, DisplayState.SHOW.getValue()));
			// 当前的设置为是
			DischargeBasedataRanking entity = new DischargeBasedataRanking();
			entity.setId(bo.getId());
			entity.setDisplayState(bo.getDisplayState());
			baseMapper.updateById(entity);
		}

	}

	@Override
	public void del(Long id) {
		DischargeBasedataRanking ranking = getById(id);
		if (ranking != null && ranking.getDisplayState().equals(DisplayState.SHOW.getValue())) {
			throw new BusinessException("展示中，不能删除！");
		}
		baseMapper.deleteById(id);
	}

	@Override
	public DischargeBasedataRanking getShowObj() {
		DischargeBasedataRanking one = this.getOne(Wrappers.<DischargeBasedataRanking>lambdaQuery()
				.eq(DischargeBasedataRanking::getDisplayState, DisplayState.SHOW.getValue())
				.last("LIMIT 1")
		);
		if (one != null) {
			return one;
		}
		return new DischargeBasedataRanking();
	}

}
