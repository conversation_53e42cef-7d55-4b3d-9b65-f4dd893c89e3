package com.enrising.ctsc.discharge.api.bo;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放数据填报表（能源）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */

@Data
public class DischargeDataEnergyUpdateLogBo extends Model<DischargeDataEnergyUpdateLogBo> {

	/**
	 * 主键id,采用雪花id
	 */
		private Long id;

	/**
	 * 记录id
	 */
		private Long recordId;

	/**
	 * 填报单位id
	 */
		private Long energyId;

	/**
	 * 修改前的数据
	 */
		private BigDecimal previousData;

	/**
	 * 修改后的数据
	 */
		private BigDecimal nowData;

	/**
	 * 修改的类型 1-集团 2-股份 3-大型 4-小型 5移动业务
	 */
		private String num;

	/**
	 * 创建者id
	 */
		private Long createBy;

	/**
	 * 创建时间
	 */
		private Date createTime;

	/**
	 * 更新者id
	 */
		private Long updateBy;

	/**
	 * 更新时间
	 */
		private Date updateTime;

	/**
	 * 删除标志：0-正常；1-删除
	 */
	@TableLogic
		private String delFlag;


}
