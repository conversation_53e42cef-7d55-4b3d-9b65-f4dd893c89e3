package com.enrising.ctsc.carbon.common.utils;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.zhxu.bs.BeanSearcher;
import cn.zhxu.bs.util.MapBuilder;
import cn.zhxu.bs.util.MapUtils;
import com.enrising.ctsc.carbon.common.utils.request.ServletUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * BeanSearcher 工具类
 */
@Slf4j
public class Bs {
    private static boolean isJsonContentType(String contentType) {
        return contentType != null && (contentType.startsWith("application/json"));
    }

    @SneakyThrows
    private static Map<String, Object> getAllParameters() {
        HttpServletRequest request = ServletUtil.getRequest();
        Map<String, Object> parameters = new HashMap<>();

        // 获取GET和POST参数
        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            String paramValue = request.getParameter(paramName);
            parameters.put(paramName, paramValue);
        }

        // 如果是POST请求，尝试读取JSON请求体
        if ("POST".equalsIgnoreCase(request.getMethod()) && isJsonContentType(request.getContentType())) {
            StringBuilder jsonBody = new StringBuilder();
            BufferedReader reader = request.getReader();
            String line;
            while ((line = reader.readLine()) != null) {
                jsonBody.append(line);
            }
            // 将JSON字符串转换为Map并合并
            JSONObject jsonObject = JSONUtil.parseObj(jsonBody.toString());
            for (String key : jsonObject.keySet()) {
                parameters.put(key, jsonObject.get(key));
            }
        }

        return parameters;
    }

    /**
     * 构建参数
     *
     * @return MapBuilder
     */
    public static MapBuilder params() {
        return MapUtils.builder().putAll(getAllParameters());
    }

    /**
     * 获取BeanSearcher
     *
     * @return BeanSearcher
     */
    public static BeanSearcher getBean() {
        return SpringUtils.getBean(BeanSearcher.class);
    }

}
