package com.enrising.ctsc.assess.api.vo;

import com.enrising.ctsc.assess.api.entity.AssessTargetSecondaryRule;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 考核二级指标
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
public class AssessTargetSecondaryVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 所属一级指标id
     */
    private Long primaryTargetId;

    /**
     * 所属一级指标类型
     */
    private String targetType;

    /**
     * 所属一级指标年份
     */
    private String targetYear;

    /**
     * 所属一级指标类别
     */
    private String targetCategory;

    /**
     * 考核周期
     */
    private String assessPeriod;

    /**
     * 主键id
     */
    private Long secondaryTargetId;

    /**
     * 指标名称
     */
    private String secondaryTargetName;

    /**
     * 指标名称
     */
    private String targetName;

    /**
     * 指标分值
     */
    private Double score;

    /**
     * 指标说明
     */
    private String targetDescription;

    /**
     * 指标算法
     */
    private String algorithm;

    /**
     * 考核规则说明
     */
    private String rulesDescription;

    /**
     * 指标公式
     */
    private String formula;

    /**
     * 规则列表
     */
    private List<AssessTargetSecondaryRule> ruleList;

    /**
     * 获取规则列表
     *
     * @return 规则列表
     */
    public List<AssessTargetSecondaryRule> getRuleList() {
        if (ruleList == null) {
            return Lists.newArrayList();
        }
        return ruleList;
    }
}
