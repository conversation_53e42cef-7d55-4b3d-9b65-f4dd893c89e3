package com.enrising.ctsc.assess.api.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class AssessTemplateProvinceVo {

    /**
     * 考核任务管理--省级，第一行
     */
    private List<AssessTemplateVo> firstCol = new ArrayList<>();

    /**
     * 考核任务管理--省级，第二行
     */
    private List<AssessTemplateVo> secondCol = new ArrayList<>();

    /**
     * 考核任务管理--省级，第三行
     */
    private List<AssessTemplateVo> thirdlyCol = new ArrayList<>();

    /**
     * 考核任务管理--省级，第四行
     */
    private List<AssessTemplateVo> fourthlyCol = new ArrayList<>();
}
