package com.enrising.ctsc.assess.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.assess.api.bo.AssessTargetBo;
import com.enrising.ctsc.assess.api.bo.AssessTargetSecondaryBo;
import com.enrising.ctsc.assess.api.bo.AssessTargetSecondaryRuleBo;
import com.enrising.ctsc.assess.api.entity.AssessTarget;
import com.enrising.ctsc.assess.api.entity.AssessTargetSecondary;
import com.enrising.ctsc.assess.api.entity.AssessTargetSecondaryRule;
import com.enrising.ctsc.assess.api.enums.AssessStatus;
import com.enrising.ctsc.carbon.common.enums.DelFlagEnum;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.assess.api.query.AssessTargetQuery;
import com.enrising.ctsc.assess.api.query.AssessTargetSecondaryQuery;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.assess.api.vo.AssessTargetSecondaryVo;
import com.enrising.ctsc.assess.api.vo.AssessTargetVo;
import com.enrising.ctsc.assess.mapper.AssessTargetMapper;
import com.enrising.ctsc.assess.mapper.AssessTargetSecondaryMapper;
import com.enrising.ctsc.assess.mapper.AssessTargetSecondaryRuleMapper;
import com.enrising.ctsc.assess.service.AssessTargetService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;

/**
 * 考核指标
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Service
@AllArgsConstructor
public class AssessTargetServiceImpl extends ServiceImpl<AssessTargetMapper, AssessTarget> implements AssessTargetService {

	private final AssessTargetSecondaryMapper secondaryMapper;
	private final AssessTargetSecondaryRuleMapper secondaryRuleMapper;

	@Override
	public TableDataInfo<AssessTargetVo> findList(Page<AssessTargetVo> page, AssessTargetQuery query) {
		QueryWrapper<AssessTargetQuery> wrapper = this.getWrapper(query);
		IPage<AssessTargetVo> resultPage = baseMapper.findList(page, wrapper);
		return TableDataInfo.build(resultPage);
	}

	@Override
	public AssessTargetVo detail(AssessTargetQuery query) {
		if (ObjectUtil.allFieldIsNull(query)) {
			throw new BusinessException("查询参数不能为空");
		}
		QueryWrapper<AssessTargetQuery> wrapper = this.getWrapper(query);
		AssessTargetVo detail = baseMapper.detail(wrapper);
		// 获取二级指标
		AssessTargetSecondaryQuery secondaryQuery = new AssessTargetSecondaryQuery();
		secondaryQuery.setPrimaryTargetId(detail.getId());
		List<AssessTargetSecondaryVo> secondaryVoList = secondaryMapper.findList(Wrappers.<AssessTargetSecondaryQuery>lambdaQuery().eq(AssessTargetSecondaryQuery::getPrimaryTargetId, detail.getId()));
		// 获取二级指标规则
		secondaryVoList.forEach(vo -> {
			List<AssessTargetSecondaryRule> ruleList = new AssessTargetSecondaryRule().selectList(Wrappers.<AssessTargetSecondaryRule>lambdaQuery().eq(AssessTargetSecondaryRule::getSecondaryTargetId, vo.getId()));
			vo.setRuleList(ruleList);
		});
		detail.setTargetSecondaryList(secondaryVoList);
		return detail;
	}

	private QueryWrapper<AssessTargetQuery> getWrapper(AssessTargetQuery query) {
		QueryWrapper<AssessTargetQuery> wrapper = new QueryWrapper<>();
		wrapper.eq(query.getId() != null, "t.id", query.getId());
		wrapper.eq(StrUtil.isNotBlank(query.getTargetCategory()), "t.target_category", query.getTargetCategory());
		wrapper.eq(StrUtil.isNotBlank(query.getTargetYear()), "t.target_year", query.getTargetYear());
		wrapper.eq(StrUtil.isNotBlank(query.getStatus()), "t.status", query.getStatus());
		// 周期搜索
		wrapper.eq(StrUtil.isNotBlank(query.getAssessPeriod()), "target_secondary.assess_period", query.getAssessPeriod());
		// 关键字搜索
		if (StrUtil.isNotBlank(query.getKeys())) {
			wrapper.and(w -> w.or().like("t.target_type", query.getKeys()).or().like("target_secondary.target_name", query.getKeys()));
		}
		wrapper.orderByDesc("t.create_time");
		wrapper.eq("t.del_flag", DelFlagEnum.NOT_DELETED.getValue());
		return wrapper;
	}

	@Override
	public void add(AssessTargetBo bo) {
		// 清空二级指标id,兼容复制新增
		bo.getTargetSecondaryList().forEach(it -> {
			it.setId(null);
			it.getRuleList().forEach(i -> i.setId(null));
		});

		// 添加一级指标
		AssessTarget entity = new AssessTarget();
		BeanUtils.copyProperties(bo, entity);
		// 设置为未使用状态
		entity.setStatus(AssessStatus.OUT_OF_USE.getValue());
		baseMapper.insert(entity);

		this.addTargetSecondary(bo, entity);
	}

	/**
	 * 添加二级指标
	 *
	 * @param bo     入参
	 * @param entity 一级指标
	 */
	private void addTargetSecondary(AssessTargetBo bo, AssessTarget entity) {
		// 添加二级指标
		for (int j = 0; j < bo.getTargetSecondaryList().size(); j++) {
			AssessTargetSecondaryBo item = bo.getTargetSecondaryList().get(j);
			AssessTargetSecondary secondary = new AssessTargetSecondary();
			BeanUtils.copyProperties(item, secondary);
			secondary.setPrimaryTargetId(entity.getId());
			secondary.insert();
			// 校验二级规则
			for (int i = 0; i < item.getRuleList().size(); i++) {
				AssessTargetSecondaryRuleBo rule = item.getRuleList().get(i);
				// 得分不能大于指标分数
				if (rule.getRuleScore() > item.getScore()) {
					throw new BusinessException(StrUtil.format("第{}个二级指标第{}行考核规则得分不能大于指标分值", (j + 1), (i + 1)));
				}
			};
			// 添加二级指标规则
			// 得分从小到大排序
			ListUtil.sort(item.getRuleList(), Comparator.comparingDouble(AssessTargetSecondaryRuleBo::getRuleValue));
			for (AssessTargetSecondaryRuleBo rule : item.getRuleList()) {
				AssessTargetSecondaryRule secondaryRule = new AssessTargetSecondaryRule();
				BeanUtils.copyProperties(rule, secondaryRule);
				secondaryRule.setSecondaryTargetId(secondary.getId());
				secondaryRule.setPrimaryTargetId(entity.getId());
				secondaryRule.insert();
			}
		};
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void edit(AssessTargetBo bo) {
		AssessTarget entity = new AssessTarget();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.updateById(entity);
		// 删除二级指标
		secondaryMapper.delByPrimaryTargetId(bo.getId());
		// 删除二级指标规则
		secondaryRuleMapper.delByPrimaryTargetId(bo.getId());
		// 添加新的指标
		this.addTargetSecondary(bo, entity);
	}

	@Override
	public void del(Long id) {
		baseMapper.deleteById(id);
	}

}
