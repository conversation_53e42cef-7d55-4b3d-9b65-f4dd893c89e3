package com.enrising.ctsc.assess.api.bo;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 考核打分
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2022-12-26
 */
@Data
public class AssessReportScoreBo {

	/**
	 * 上报id
	 */
	@NotNull(message = "上报id不能为空")
	private Long reportId;

	/**
	 * 分数
	 */
	@NotNull(message = "分数不能为空")
	@Min(value = 0, message = "分数不能小于0")
	@Max(value = 100, message = "分数不能大于100")
	private Double assessScore;

	/**
	 * 规则得分
	 */
	@NotNull(message = "规则得分不能为空")
	private Double ruleScore;

	/**
	 * 考核评价
	 */
	@NotBlank(message = "考核评价不能为空")
	@Length(max = 1000, message = "考核评价字符长度为1-1000")
	private String assessEvaluate;
}
