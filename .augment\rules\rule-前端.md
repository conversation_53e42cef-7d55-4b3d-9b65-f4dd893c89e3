---
type: "manual"
---

# brch-ln-vue 项目开发规则

## 项目架构概述

brch-ln-vue 是一个基于 Vue 2.x + iView + Element UI 的能耗费管理系统前端项目，采用模块化架构设计。

### 技术栈
- **框架**: Vue 2.5.10 + Vue Router + Vuex
- **UI组件库**: iView 3.4.1 + Element UI 2.15.14 + @smallwei/avue 2.12.8
- **构建工具**: Vue CLI 4.x + Webpack
- **样式预处理**: Less + Sass
- **HTTP客户端**: Axios
- **其他**: ECharts、OpenLayers、xlsx等

## 目录结构规范

```
src/
├── api/                    # API接口定义
│   ├── basedata/          # 基础数据相关API
│   ├── account/           # 账务相关API
│   └── ...
├── assets/                # 静态资源
├── components/            # 公共组件
│   ├── cl/               # 自定义组件库(核心)
│   ├── charts/           # 图表组件
│   └── ...
├── config/               # 配置文件
├── libs/                 # 工具库
├── router/               # 路由配置
├── store/                # Vuex状态管理
├── styles/               # 全局样式
├── utils/                # 工具函数
└── view/                 # 页面组件
    ├── basedata/         # 基础数据模块
    ├── account/          # 账务模块
    └── ...
```

## 开发规范

### 1. 组件使用规范

#### 1.1 优先使用 cl-table 组件
- **强制使用**: 所有数据表格必须使用 `cl-table` 组件，不使用原生 Element UI 表格
- **参考模板**: `src/view/basedata/station/stationList.vue`

```vue
<template>
  <cl-table
    ref="table"
    :data="tableData"
    :columns="columns"
    :searchable="true"
    :loading="loading"
    @on-search="handleSearch"
    @on-reset="handleReset"
  >
    <template slot="buttons">
      <Button type="primary" @click="handleAdd">新增</Button>
    </template>
  </cl-table>
</template>
```

#### 1.2 表单组件规范
- 使用 `cl-form` 进行表单构建
- 输入框使用 `cl-input`，支持清除功能
- 下拉选择使用 `cl-select`

#### 1.3 UI组件优先级
1. **cl组件** (最高优先级) - 项目自定义组件
2. **iView组件** - 主要UI框架
3. **Element UI组件** - 辅助UI组件
4. **Avue组件** - 特定场景使用

### 2. API接口规范

#### 2.1 接口文件组织
```javascript
// src/api/basedata/moduleName.js
import axios from '@/libs/api.request'

// 查询列表
export const listModuleName = (params) => {
  return axios.request({
    url: 'business/moduleName/list',
    params: params,
    method: 'post'
  })
}

// 新增
export const addModuleName = (data) => {
  return axios.request({
    url: 'business/moduleName/add',
    data: data,
    method: 'post'
  })
}

// 编辑
export const updateModuleName = (data) => {
  return axios.request({
    url: 'business/moduleName/edit',
    data: data,
    method: 'post'
  })
}

// 删除
export const deleteModuleName = (id) => {
  return axios.request({
    url: 'business/moduleName/delete',
    params: { id },
    method: 'post'
  })
}
```

#### 2.2 接口调用规范
- 统一使用 `@/libs/api.request` 进行HTTP请求
- 接口路径遵循 RESTful 风格
- 统一错误处理和响应拦截

### 3. 路由配置规范

#### 3.1 路由结构
```javascript
// src/router/routers.js
{
  path: '/basedata',
  name: 'basedata',
  component: Main,
  meta: {
    icon: 'md-analytics',
    title: '基础数据',
    access: ['/basedata']
  },
  children: [
    {
      path: 'moduleName',
      name: 'moduleName',
      meta: {
        icon: 'md-build',
        title: '模块名称',
        access: ['/basedata/moduleName']
      },
      component: () => import('@/view/basedata/moduleName/index.vue')
    }
  ]
}
```

#### 3.2 权限控制
- 使用 `meta.access` 数组定义页面访问权限
- 权限路径与后端保持一致

### 4. 页面开发规范

#### 4.1 页面结构模板
```vue
<template>
  <card class="menu-card" dis-hover style="padding: 0;margin: 0">
    <Spin size="large" fix v-if="loading"></Spin>
    
    <!-- 弹窗组件 -->
    <modal-component ref="modalComponent"></modal-component>
    
    <!-- 主要内容 -->
    <cl-table
      ref="table"
      :data="tableData"
      :columns="columns"
      :searchable="true"
      :loading="loading"
      @on-search="handleSearch"
    >
      <!-- 搜索表单 -->
      <template slot="filter">
        <!-- 自定义搜索表单 -->
      </template>
      
      <!-- 操作按钮 -->
      <template slot="buttons">
        <Button type="primary" @click="handleAdd">新增</Button>
        <Button type="warning" @click="handleEdit">编辑</Button>
        <Button type="error" @click="handleDelete">删除</Button>
      </template>
    </cl-table>
  </card>
</template>

<script>
import { listModuleName, deleteModuleName } from '@/api/basedata/moduleName'

export default {
  name: 'ModuleNameList',
  data() {
    return {
      loading: false,
      tableData: [],
      columns: [],
      queryParams: {}
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      try {
        const res = await listModuleName(this.queryParams)
        this.tableData = res.data.rows
      } catch (error) {
        this.$Message.error('获取数据失败')
      } finally {
        this.loading = false
      }
    },
    
    handleSearch(params) {
      this.queryParams = { ...params }
      this.getList()
    },
    
    handleReset() {
      this.queryParams = {}
      this.getList()
    }
  }
}
</script>
```

#### 4.2 命名规范
- **文件名**: 使用 camelCase，如 `stationList.vue`
- **组件名**: 使用 PascalCase，如 `StationList`
- **方法名**: 使用 camelCase，以动词开头，如 `handleAdd`、`getList`
- **变量名**: 使用 camelCase，如 `tableData`、`queryParams`

### 5. 样式规范

#### 5.1 样式文件组织
- 全局样式: `src/styles/`
- 组件样式: 写在组件内部 `<style>` 标签中
- 模块样式: 按模块组织，如 `moduleTable.css`

#### 5.2 样式命名
- 使用 BEM 命名规范
- 类名使用 kebab-case
- 避免使用 ID 选择器

### 6. 状态管理规范

#### 6.1 Vuex模块化
```javascript
// src/store/module/moduleName.js
export default {
  namespaced: true,
  state: {
    list: [],
    loading: false
  },
  mutations: {
    SET_LIST(state, list) {
      state.list = list
    },
    SET_LOADING(state, loading) {
      state.loading = loading
    }
  },
  actions: {
    async getList({ commit }, params) {
      commit('SET_LOADING', true)
      try {
        const res = await listAPI(params)
        commit('SET_LIST', res.data)
      } finally {
        commit('SET_LOADING', false)
      }
    }
  }
}
```

### 7. 工具函数规范

#### 7.1 工具函数位置
- 通用工具: `src/libs/util.js`
- 业务工具: `src/utils/`
- 组件工具: 组件内部定义

#### 7.2 常用工具函数
```javascript
// 深拷贝
import { deepClone } from '@/libs/util'

// 日期处理
import { formatDate } from '@/utils/date'

// 权限判断
import { hasPermission } from '@/utils/permission'
```

### 8. 环境配置规范

#### 8.1 环境变量
- 开发环境: `dev-ln` (辽宁) / `dev-sc` (四川)
- 生产环境: `build-ln` / `build-sc`

#### 8.2 代理配置
```javascript
// vue.config.js
devServer: {
  proxy: {
    '/energy-cost/': {
      target: 'http://127.0.0.1:18098',
      changeOrigin: true
    }
  }
}
```

## 最佳实践

### 1. 性能优化
- 使用路由懒加载
- 合理使用 v-if 和 v-show
- 避免在模板中使用复杂表达式
- 使用 Object.freeze() 冻结大型数据

### 2. 代码质量
- 使用 ESLint 进行代码检查
- 遵循 Vue 官方风格指南
- 编写清晰的注释
- 使用 TypeScript (可选)

### 3. 调试技巧
- 使用 Vue DevTools
- 合理使用 console.log (生产环境会被移除)
- 使用 debugger 断点调试

### 4. 部署注意事项
- 生产环境关闭 source map
- 压缩静态资源
- 配置正确的 publicPath
- 处理跨域问题

## 常见问题解决

### 1. 组件引入问题
```javascript
// 错误
import { Table } from 'element-ui'

// 正确 - 优先使用 cl-table
import ClTable from '@/components/cl/table/table.vue'
```

### 2. API调用问题
```javascript
// 确保使用正确的请求方法
const res = await listAPI(params) // POST请求传params
const res = await addAPI(data)    // POST请求传data
```

### 3. 路由权限问题
```javascript
// 确保权限路径与后端一致
meta: {
  access: ['/basedata/moduleName'] // 与后端权限配置保持一致
}
```