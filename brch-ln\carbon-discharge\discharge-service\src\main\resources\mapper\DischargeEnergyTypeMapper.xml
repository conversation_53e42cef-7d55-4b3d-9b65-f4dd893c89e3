<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeEnergyTypeMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.energy_type,
            t.unit,
            t.second_name,
            t.del_flag,
			t.status,
			t.size_type
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeEnergyTypeVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_energy_type t
        ${ew.customSqlSegment}
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.discharge.api.vo.DischargeEnergyTypeVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_energy_type t
        ${ew.customSqlSegment}
        limit 1
    </select>
</mapper>