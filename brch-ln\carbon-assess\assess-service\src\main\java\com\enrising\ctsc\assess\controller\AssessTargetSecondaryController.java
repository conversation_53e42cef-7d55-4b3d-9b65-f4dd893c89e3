package com.enrising.ctsc.assess.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.assess.api.bo.AssessTargetSecondaryBo;
import com.enrising.ctsc.assess.api.bo.AssessTemplateBo;
import com.enrising.ctsc.assess.api.query.AssessTargetSecondaryQuery;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.assess.api.vo.AssessTargetSecondaryVo;
import com.enrising.ctsc.assess.api.vo.CompanyCarbonVo;
import com.enrising.ctsc.assess.service.AssessTargetSecondaryService;
import com.enrising.ctsc.assess.service.AssessTemplateService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 考核二级指标
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@RestController
@RequestMapping("/assess/targetSecondary")
@AllArgsConstructor
public class AssessTargetSecondaryController {
	private final AssessTargetSecondaryService assessTargetSecondaryService;
	private final AssessTemplateService assessTemplateService;

	@GetMapping("/list")
	public TableDataInfo<AssessTargetSecondaryVo> page(Page<AssessTargetSecondaryVo> page, AssessTargetSecondaryQuery query) {
		return assessTargetSecondaryService.findList(page, query);
	}

	/**
	 * 考核指标数量统计
	 *
	 * @return 结果
	 */
	@GetMapping(value = "/count")
	public R<Long> countTotal() {
		long count = 0;
		//系统管理员或省管理员
//		boolean isAdmin = SecurityUtils.isRolesContain(SysRoleEnums.ROLE_ADMIN, SysRoleEnums.ROLE_PROVINCE_ADMIN);
//		if (isAdmin) {
//			count = assessTargetSecondaryService.count();
//		} else {
			count = assessTemplateService.countTargetByAssessTemplate(new AssessTemplateBo());
//		}
		return R.success(count, "考核指标统计");
	}

	@PostMapping("/getCompanyCarbonByPage")
	public R<Page<CompanyCarbonVo>> getCompanyCarbonByPage(@RequestBody QueryPage<AssessTargetSecondaryQuery> queryPage) {
		return R.success(assessTargetSecondaryService.getCompanyCarbonByPage(queryPage));
	}

	@PostMapping("/getMonthCarbonList")
	public R<List<CompanyCarbonVo>> getMonthCarbonList(@RequestBody AssessTargetSecondaryQuery query) {
		return R.success(assessTargetSecondaryService.getMonthCarbonByPage(query));
	}

	@GetMapping("/targetSecondaryList")
	public R<List<AssessTargetSecondaryVo>> targetSecondaryList(AssessTargetSecondaryQuery query) {
		return R.success(assessTargetSecondaryService.targetSecondaryList(query));
	}

	@GetMapping("/detail")
	public R<AssessTargetSecondaryVo> get(AssessTargetSecondaryQuery query) {
		AssessTargetSecondaryVo detail = assessTargetSecondaryService.detail(query);
		return R.success(detail, "查询成功");
	}

	@PostMapping(value = "/save")
	public R<String> save(@RequestBody AssessTargetSecondaryBo bo) {
		assessTargetSecondaryService.add(bo);
		return R.success("保存成功");
	}

	@PostMapping(value = "/update")
	public R<String> update(@RequestBody AssessTargetSecondaryBo bo) {
		assessTargetSecondaryService.edit(bo);
		return R.success("修改成功");
	}

	@PostMapping(value = "/delete/{id}")
	public R<String> delete(@PathVariable Long id) {
		assessTargetSecondaryService.del(id);
		return R.success("删除成功");
	}

	@GetMapping("/getFullDetail")
	public R<AssessTargetSecondaryVo> getFullDetail(AssessTargetSecondaryQuery query) {
		AssessTargetSecondaryVo detail = assessTargetSecondaryService.getFullDetail(query);
		return R.success(detail, "查询成功");
	}


	@PostMapping("/downloadExcel")
	public void downloadExcel(HttpServletRequest request, HttpServletResponse response, @RequestBody QueryPage<AssessTargetSecondaryQuery> queryPage) {
		assessTargetSecondaryService.downloadExcel(request, response, queryPage);
	}
}
