package com.enrising.ctsc.discharge.api.vo;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 生产业务数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-03-29
 */

@Data
public class BusinessProductionDataCompareVo extends Model<BusinessProductionDataCompareVo> {

	/**
	 * 主键id,采用雪花id
	 */
	private Long id;

	/**
	 * 填报单位id
	 */
	private Long companyId;

	/**
	 * 填报单位名称
	 */
	private String companyName;

	/**
	 * 填报时间月份
	 */
	private String DateMonth;

	/**
	 * 填报时间
	 */
	private Date reportTime;

	/**
	 * 电信业务总量（万元）
	 */
	private BigDecimal telecomBusinessTotal = new BigDecimal(0);

	/**
	 * 业务流量总量（TB）
	 */
	private BigDecimal businessFlowTotal = new BigDecimal(0);

	/**
	 * 固定电话业务总量（万元）
	 */
	private BigDecimal fixedPhoneBusiness;

	/**
	 * 宽带接入业务总量（万元）
	 */
	private BigDecimal broadbandBusiness;

	/**
	 * 专线接入业务总量（万元）
	 */
	private BigDecimal specialBroadbandBusiness;

	/**
	 * IPTV业务总量（万元）
	 */
	private BigDecimal iptvBusiness;

	/**
	 * 移动电话业务总量（万元）
	 */
	private BigDecimal mobilePhonBusiness;

	/**
	 * 移动互联网业务总量（TB）
	 */
	private BigDecimal mobileInternetBusiness;

	/**
	 * 移动短信业务总量（万元）
	 */
	private BigDecimal mobileSmsBusiness;

	/**
	 * 物联网业务总量（万元）
	 */
	private BigDecimal iotBusiness;

	/**
	 * 互联网数据中心业务总量（万元）
	 */
	private BigDecimal idcBusiness;

	/**
	 * 其他业务总量（万元）
	 */
	private BigDecimal otherBusiness;

	/**
	 * 电信业务总量（万元）
	 */
	private BigDecimal telecomBusinessTotalLastYear;

	/**
	 * 业务流量总量（TB）
	 */
	private BigDecimal businessFlowTotalLastYear;

	/**
	 * 固定电话业务总量（万元）
	 */
	private BigDecimal fixedPhoneBusinessLastYear;

	/**
	 * 宽带接入业务总量（万元）
	 */
	private BigDecimal broadbandBusinessLastYear;

	/**
	 * 专线接入业务总量（万元）
	 */
	private BigDecimal specialBroadbandBusinessLastYear;

	/**
	 * IPTV业务总量（万元）
	 */
	private BigDecimal iptvBusinessLastYear;

	/**
	 * 移动电话业务总量（万元）
	 */
	private BigDecimal mobilePhonBusinessLastYear;

	/**
	 * 移动互联网业务总量（TB）
	 */
	private BigDecimal mobileInternetBusinessLastYear;

	/**
	 * 移动短信业务总量（万元）
	 */
	private BigDecimal mobileSmsBusinessLastYear;

	/**
	 * 物联网业务总量（万元）
	 */
	private BigDecimal iotBusinessLastYear;

	/**
	 * 互联网数据中心业务总量（万元）
	 */
	private BigDecimal idcBusinessLastYear;

	/**
	 * 其他业务总量（万元）
	 */
	private BigDecimal otherBusinessLastYear;

	/**
	 * 电信业务总量（万元）
	 */
	private BigDecimal telecomBusinessTotalRiseRate;

	/**
	 * 业务流量总量（TB）
	 */
	private BigDecimal businessFlowTotalRiseRate;

	/**
	 * 固定电话业务总量（万元）
	 */
	private BigDecimal fixedPhoneBusinessRiseRate;

	/**
	 * 宽带接入业务总量（万元）
	 */
	private BigDecimal broadbandBusinessRiseRate;

	/**
	 * 专线接入业务总量（万元）
	 */
	private BigDecimal specialBroadbandBusinessRiseRate;

	/**
	 * IPTV业务总量（万元）
	 */
	private BigDecimal iptvBusinessRiseRate;

	/**
	 * 移动电话业务总量（万元）
	 */
	private BigDecimal mobilePhonBusinessRiseRate;

	/**
	 * 移动互联网业务总量（TB）
	 */
	private BigDecimal mobileInternetBusinessRiseRate;

	/**
	 * 移动短信业务总量（万元）
	 */
	private BigDecimal mobileSmsBusinessRiseRate;

	/**
	 * 物联网业务总量（万元）
	 */
	private BigDecimal iotBusinessRiseRate;

	/**
	 * 互联网数据中心业务总量（万元）
	 */
	private BigDecimal idcBusinessRiseRate;

	/**
	 * 其他业务总量（万元）
	 */
	private BigDecimal otherBusinessRiseRate;
}