<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.assess.mapper.AssessReportMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.template_id,
            t.report_name,
            t.content,
            t.suggestion,
            t.is_notification,
            t.reporter,
            t.report_time,
            t.del_flag
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.assess.api.vo.AssessReportVo">
        SELECT
            b.id,
            A.id as templateId,
			A.template_name,
			b.generate_status,
			b.report_name,
			b.is_notification
		FROM
			assess_template
			A LEFT JOIN assess_report b ON A.id = b.template_id
		WHERE
			A.del_flag = '0'
		and A.send_status = '1'
		and A.status = '3'
		<if test="query.generateStatus!=null and query.generateStatus!=''">
			and b.generate_status = #{query.generateStatus}
		</if>
		<if test="query.isNotification!=null and query.isNotification!=''">
			and b.is_notification = #{query.isNotification}
		</if>
		<if test="query.keyword!=null and query.keyword!=''">
			and A.template_name like concat('%',#{query.keyword},'%')
			or b.content like concat('%',#{query.keyword},'%')
		</if>
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.assess.api.vo.AssessReportVo">
        SELECT
        <include refid="baseColumns" />
        FROM assess_report t
        ${ew.customSqlSegment}
        limit 1
    </select>
    <insert id="saveCarbonAttachment" parameterType="com.enrising.ctsc.carbon.common.entity.CarbonAttachment">
        INSERT INTO `carbon_attachment` (
            `id`,
            `busi_type`,
            `province_code`,
            `file_name`,
            `file_namefile_ext`,
            `file_size`,
            `saved_url`,
            `saved_name`,
            `description`,
            `resource_id`,
            `business_id`,
            `create_by`,
            `create_time`,
            `year`,
            `status`,
            `del_flag`
        )
        VALUES
            (
                #{id},
                #{busiType},
                #{provinceCode},
                #{fileName},
                #{fileExt},
                #{fileSize},
                #{savedUrl},
                #{savedName},
                #{description},
                #{resourceId},
                #{businessId},
                #{createBy},
                #{createTime},
                #{year},
                #{status},
                '0'
            );
    </insert>
    <!-- 查询附件详情 -->
    <select id="getAttachmentById" resultType="com.enrising.ctsc.carbon.common.entity.CarbonAttachment">
        SELECT
            *
        FROM
            carbon_attachment
        WHERE
            id = #{id}
    </select>
</mapper>