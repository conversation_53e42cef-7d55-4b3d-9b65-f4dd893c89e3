package com.enrising.ctsc.carbon.common.config;

import io.minio.MinioClient;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Getter
@Configuration
@ConfigurationPropertiesScan
public class MinioConfig {
    @Value("${minio.access-key}")
    private String accessKey;
    @Value("${minio.secret-key}")
    private String secretKey;
    @Value("${minio.end-point}")
    private String endPoint;
    @Value("${minio.bucket-name}")
    private String bucketName;
    /**
     * 图片的最大大小
     */
    @Value("${minio.image-size:10485760}")
    private long imageSize;

    /**
     * 其他文件的最大大小
     */
    @Value("${minio.file-size:1073741824}")
    private long fileSize;

    /**
     * 支持的文件类型
     */
    @Value("${minio.file-type:jpg、png、doc、docx、pdf、xls、xlsx、mp4}")
    private String fileType;

    /**
     * 正文的下载模板
     */
    @Value("${minio.article-template:}")
    private String articleTemplate;

    @Bean
    public MinioClient minioClient() {
        return MinioClient.builder()
                .endpoint(endPoint)
                .credentials(accessKey, secretKey)
                .build();
    }
}

