package com.enrising.ctsc.assess.api.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 考核二级指标规则
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
public class AssessTargetSecondaryRuleVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 所属二级指标id
     */
    private Long secondaryTargetId;

    /**
     * 考核规则判断
     */
    private String ruleJudge;

    /**
     * 考核规则值
     */
    private Double ruleValue;

    /**
     * 考核规则得分
     */
    private Double ruleScore;

    /**
     * 所属一级指标id
     */
    private Long primaryTargetId;


}
