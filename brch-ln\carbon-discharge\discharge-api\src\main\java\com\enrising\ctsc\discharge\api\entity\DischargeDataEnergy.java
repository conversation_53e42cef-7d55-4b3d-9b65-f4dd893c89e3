package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放数据填报表（能源）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */

@Data
@TableName("discharge_data_energy")
public class DischargeDataEnergy extends Model<DischargeDataEnergy> {

	/**
	 * 主键id,采用雪花id
	 */
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 创建者id
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date createTime;

	/**
	 * 更新者id
	 */
		private Long updateBy;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
		private Date updateTime;

	/**
	 * 填报单位id
	 */
		private Long companyId;

	/**
	 * 填报时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
		private Date reportTime;

	/**
	 * 能源指标id
	 */
		private Long energyIndicatorId;

	/**
	 * 集团数据
	 */
		private BigDecimal groupData;

	/**
	 * 股份数据
	 */
		private BigDecimal stockData;

	/**
	 * 大型数据中心数据
	 */
		private BigDecimal largeData;

	/**
	 * 中小型数据中心数据
	 */
		private BigDecimal mediumData;

	/**
	 * 移动业务数据
	 */
		private BigDecimal mobileData;

	/**
	 * 上报标志：2-已上报；3-未上报
	 */
		private String reportFlag;

	/**
	 * 申请退回理由
	 */
		private String applyReason;
	/**
	 * 退回理由
	 */
		private String returnReason;

	/**
	 * 删除标志：0-正常；1-删除
	 */
	@TableLogic
	private String delFlag;

}
