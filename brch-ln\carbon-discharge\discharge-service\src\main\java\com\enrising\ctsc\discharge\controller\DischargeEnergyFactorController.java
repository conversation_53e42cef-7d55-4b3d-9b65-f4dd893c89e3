package com.enrising.ctsc.discharge.controller;

import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.discharge.api.bo.DischargeEnergyFactorBo;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyFactor;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyFactorOpenBo;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyFactorQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyFactorOpenVo;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyFactorVo;
import com.enrising.ctsc.discharge.service.DischargeEnergyFactorService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 碳排放能源转换因子表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Slf4j
@RestController
@RequestMapping("/discharge/factor")
@AllArgsConstructor
public class DischargeEnergyFactorController {
	private final DischargeEnergyFactorService dischargeEnergyFactorService;

	@PostMapping("/list")
	public TableDataInfo<DischargeEnergyFactorVo> page(@RequestBody QueryPage<DischargeEnergyFactorQuery> page) {
		log.info("page:{}" , page);
		return dischargeEnergyFactorService.findList(page);
	}

	@PostMapping("/getGainFactor")
	public R<DischargeEnergyFactorOpenVo> getGainFactor(@Valid @RequestBody DischargeEnergyFactorOpenBo bo) {
		return R.success(dischargeEnergyFactorService.getGainFactor(bo));
	}

	/**
	 * 导出excel
	 *
	 * @param query 查询条件
	 * @param response 响应
	 */
		@PostMapping(value = "/exportExcel")
	public void exportExcel(@RequestBody DischargeEnergyFactorQuery query, HttpServletResponse response) {
		dischargeEnergyFactorService.exportExcel(query, response);
	}

	@PostMapping(value = "/selectTypeByDate")
	public R<List<DischargeEnergyFactor>> selectTypeByDate(@RequestBody DischargeEnergyFactorQuery query) {
		List<DischargeEnergyFactor> factors = dischargeEnergyFactorService.selectTypeByDate(query);
		return R.success(factors, factors.size() > 0 ? "有效期重合，添加后已有数据有效期将自动结束" : "有效期无重合");
	}

	@GetMapping("/detail")
	public R<DischargeEnergyFactorVo> get(DischargeEnergyFactorQuery query) {
		DischargeEnergyFactorVo detail = dischargeEnergyFactorService.detail(query);
		return R.success(detail, "查询成功");
	}

		@PostMapping(value = "/save")
	public R<String> save(@RequestBody DischargeEnergyFactorBo bo) {
		dischargeEnergyFactorService.add(bo);
		return R.success("保存成功");
	}

		@PostMapping(value = "/update")
	public R<String> update(@RequestBody DischargeEnergyFactorBo bo) {
		dischargeEnergyFactorService.edit(bo);
		return R.success("修改成功");
	}

		@PostMapping(value = "/delete/{id}")
	public R<String> delete(@PathVariable Long id) {
		dischargeEnergyFactorService.del(id);
		return R.success("删除成功");
	}
}
