package com.enrising.ctsc.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.business.api.bo.BusinessProductionDataBo;
import com.enrising.ctsc.business.api.entity.BusinessProductionData;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.business.api.vo.BusinessProductionDataCompareVo;
import com.enrising.ctsc.business.api.vo.BusinessProductionDataVo;
import com.enrising.ctsc.business.api.vo.CarbonRankingVo;
import com.sccl.common.utils.QueryPage;

import java.util.HashMap;
import java.util.List;

/**
 * 生产业务数据表服务接口
 *
 * <AUTHOR>
 * @since 1.0.0 2023-03-23
 */
public interface BusinessProductionDataService extends IService<BusinessProductionData> {
	/**
	 * 列表查询
	 *
	 * @param queryPage 查询参数
	 * @return 分页列表
	 */
	TableDataInfo<BusinessProductionDataVo> getByPage(QueryPage<BusinessProductionDataBo> queryPage);

	boolean saveBusinessProductionData(BusinessProductionDataBo businessProductionDataBo);

	boolean addList(List<BusinessProductionDataBo> businessProductionDataBos);

	List<BusinessProductionDataCompareVo> getDataCompareList(BusinessProductionDataBo businessProductionDataBo);

	List<BusinessProductionDataCompareVo> getCompanyBusList(BusinessProductionDataBo businessProductionDataBo);

	List<CarbonRankingVo> getCarbonRanking(String year);

	List<HashMap<String, Object>> getCompanyBusinessTotalList(Long companyId, String dataYear);

	HashMap<String, Object> getBusinessRanking();

	HashMap<String, Object> getBusinessOverview(Long companyId);

	List<CarbonRankingVo> getCompanyList();
}