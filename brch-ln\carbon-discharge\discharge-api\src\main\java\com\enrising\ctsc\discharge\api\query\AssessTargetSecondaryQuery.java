package com.enrising.ctsc.discharge.api.query;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AssessTargetSecondaryQuery {

	/*
	* 部门ids
	* */
	private List<Long> companyIds;

	/*
	 * 部门id
	 * */
	private Long companyId;

	/*
	* 查询时间
	* */
	private List<String> searchTime;

	/*
	 * 查询开始时间
	 * */
	private Date beginTime;

	/*
	 * 查询结束时间
	 * */
	private Date endTime;

	/*
	* 查询类型
	* */
	private String type;

	/*
	* 查询年份
	* */
	private String year;
}
