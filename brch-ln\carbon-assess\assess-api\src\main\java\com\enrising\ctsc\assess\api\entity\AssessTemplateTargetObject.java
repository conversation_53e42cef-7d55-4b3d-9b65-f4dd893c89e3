package com.enrising.ctsc.assess.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 考核模板对象
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-08
 */

@Data
@TableName("assess_template_target_object")
public class AssessTemplateTargetObject extends Model<AssessTemplateTargetObject> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 考核模板指标id
     */
    private Long templateTargetId;

    /**
     * 考核对象公司id
     */
    private Long companyId;

    /**
     * 考核对象部门id
     */
    private Long deptId;

    /**
     * 考核对象类型
     */
    private String objectType;

}
