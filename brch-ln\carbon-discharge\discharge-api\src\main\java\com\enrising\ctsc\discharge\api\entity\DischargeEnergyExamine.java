package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 *  碳盘查数据表
 *
 * <AUTHOR>
 * @since 3/13
 */

@Data
@TableName("discharge_energy_examine")
public class DischargeEnergyExamine extends Model<DischargeEnergyExamine> {

	/**
	 * 主键id,采用雪花id
	 */
		@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 创建者id
	 */
		private Long createBy;

	/**
	 * 创建时间
	 */
		private Date createTime;

	/**
	 * 更新者id
	 */
		private Long updateBy;

	/**
	 * 更新时间
	 */
		private Date updateTime;

	/**
	 * 能源指标类型名称
	 */
		private String examineName;

	/**
	 * 单位字典value
	 */
		private String unit;

	/**
	 * 排序字段
	 */
		private String sort;

	/**
	 * 碳排放因子id
	 */
		private Long factorId;

	/**
	 * 删除标志：0-正常；1-删除
	 */
		private String delFlag;

}
