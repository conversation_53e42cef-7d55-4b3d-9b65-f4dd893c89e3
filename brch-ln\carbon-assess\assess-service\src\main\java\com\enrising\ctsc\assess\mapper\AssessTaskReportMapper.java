package com.enrising.ctsc.assess.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.assess.api.bo.AssessTemplateBo;
import com.enrising.ctsc.assess.api.entity.AssessTaskReport;
import com.enrising.ctsc.assess.api.entity.Attachment;
import com.enrising.ctsc.carbon.common.entity.Organization;
import com.enrising.ctsc.assess.api.query.AssessTaskReportQuery;
import com.enrising.ctsc.assess.api.vo.AssessTaskReportScoreDetailVo;
import com.enrising.ctsc.assess.api.vo.AssessTaskReportScoreVo;
import com.enrising.ctsc.assess.api.vo.AssessTaskReportVo;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 考核任务上报表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Mapper
public interface AssessTaskReportMapper extends MPJBaseMapper<AssessTaskReport> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
/*	todo 1
	@DataPermission({
			@DataColumn(key = "deptName", value = "sys_dept.id"),
			@DataColumn(key = "userName", value = "sys_user.id")
	})*/
	IPage<AssessTaskReportScoreVo> scoreList(Page<AssessTaskReportVo> page, @Param(Constants.WRAPPER) Wrapper<AssessTaskReportQuery> wrapper);

	/**
	 * 详情查询
	 *
	 * @param wrapper 条件
	 * @return 结果
	 */
	AssessTaskReportVo detail(@Param(Constants.WRAPPER) Wrapper<AssessTaskReportQuery> wrapper);

	/**
	 * 任务管理--市州/部门获取任务列表
	 *
	 * @param templateIds 条件
	 */
	List<AssessTaskReportVo> getAllAssessTemplateByYear(@Param("templateIds") List<Long> templateIds, @Param("bo") AssessTemplateBo bo);

	/**
	 * 通过id查询分公司
	 *
	 * @param id 条件
	 */
	Organization getOrganizationById(Long id);

	int updateAttachment(@Param("reportId") Long reportId, @Param("attachmentIds") List<Long> attachmentIds);

	AssessTaskReportVo getScoreDetail(Long reportId);

	List<Attachment> getAttachmentListByIds(@Param("attachmentIds") List<Long> attachmentIds);
}
