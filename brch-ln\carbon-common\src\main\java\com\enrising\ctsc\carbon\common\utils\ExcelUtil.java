package com.enrising.ctsc.carbon.common.utils;

import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;

public class ExcelUtil {

	public static void setResponseHeader(HttpServletRequest request, HttpServletResponse response, String fileName)  {
		//fileName 文件名称
		try {
			String agent = request.getHeader("USER-AGENT").toLowerCase();
			if(StringUtils.contains(agent, "Mozilla")){
				fileName = new String(fileName.getBytes(), "ISO8859-1");
			}else {
				fileName = URLEncoder.encode(fileName, "utf8");
			}
			response.setCharacterEncoding("UTF-8");
			response.setContentType("application/vnd.ms-excel;charset=utf-8");// 设置contentType为excel格式
			response.setHeader("Content-Disposition", "Attachment;Filename="+ fileName);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
