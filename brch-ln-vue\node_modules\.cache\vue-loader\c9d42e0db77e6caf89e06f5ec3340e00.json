{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basic\\login\\login.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basic\\login\\login.vue", "mtime": 1753853977878}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/view/basic/login", "sourcesContent": ["<style lang=\"less\">\r\n@import \"login.less\";\r\n@import \"login.css\";\r\n</style>\r\n<template>\r\n  <div class=\"login satic-area\">\r\n    <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n    <!--<div class=\"dynamic-area1\"></div>-->\r\n    <!--<div class=\"dynamic-area2\"></div>-->\r\n    <img src=\"../../../assets/images/login_logo_new.png\" class=\"login_logo\" />\r\n    <img src=\"../../../assets/images/login_bottom_bg.png\" class=\"login_bottom_bg\" />\r\n    <img src=\"../../../assets/images/login_bottom_img1.png\" class=\"login_bottom_img1\" />\r\n    <img src=\"../../../assets/images/login_bottom_img2.png\" class=\"login_bottom_img2\" />\r\n    <p class=\"login_copyright\">中通服创立信息科技有限责任公司</p>\r\n    <div v-if=\"showLogin\" class=\"login-con\" id=\"login-con\" :style=\"loginStyle\">\r\n      <!--<img src=\"../../../assets/images/login_fly.png\" class=\"login_fly\"/>\r\n            <img src=\"../../../assets/images/login_fly_light.png\" class=\"login_fly_light\"/>-->\r\n      <!--<Card dis-hover class=\"login-card\" icon=\"log-in\" title=\"欢迎登录\" :bordered=\"false\">-->\r\n      <div class=\"form-con\">\r\n        <login-form ref=\"loginForm\" @on-success-valid=\"handleSubmit\"></login-form>\r\n        <!--<p class=\"login-tip\">中通服创立信息科技有限责任公司</p>-->\r\n      </div>\r\n      <!-- </Card> -->\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport config from \"@/config/index\";\r\nimport LoginForm from \"_c/login-form\";\r\nimport { mapActions, mapMutations } from \"vuex\";\r\n\r\nexport default {\r\n  components: {\r\n    LoginForm,\r\n  },\r\n  mounted() {\r\n\r\n    this.login_sso();\r\n    if (\"development\" == process.env.NODE_ENV || \"dev\" === config.deploy_env)\r\n      this.showLogin = true;\r\n\r\n    this.reStyle();\r\n    window.addEventListener(\"resize\", () => {\r\n      this.reStyle();\r\n    });\r\n  },\r\n  data() {\r\n    return {\r\n      loginStyle: \"\",\r\n      loading: false,\r\n      showLogin: false,\r\n    };\r\n  },\r\n  methods: {\r\n    ...mapMutations([\"setTagNavList\", \"addTag\"]),\r\n    ...mapActions([\"handleLogin\", \"getUserInfo\", \"getPublicKey\", \"dictInit\"]),\r\n    handleSubmit({ userName, password }, button) {\r\n      this.getPublicKey(userName).then((res) => {\r\n        localStorage.setItem(\"exponent\", res.data.exponent);\r\n        localStorage.setItem(\"modulus\", res.data.modulus);\r\n        const pwdKey = new RSAUtils.getKeyPair(res.data.exponent, \"\", res.data.modulus);\r\n        const reversedPwd = password.split(\"\").reverse().join(\"\");\r\n        const encrypedPwd = RSAUtils.encryptedString(pwdKey, reversedPwd);\r\n\r\n        let page = this.$route.query.page;\r\n\r\n        this.handleLogin({\r\n          userName: userName,\r\n          encrypedPwd: encrypedPwd,\r\n          loginType: \"0\",\r\n        }).then((res) => {\r\n          if (res.code == 0) {\r\n            this.dictInit();\r\n            this.getUserInfo().then((res) => {\r\n              if (page) {\r\n                this.$router.push({\r\n                  name: this.$config.homeName,\r\n                  params: {\r\n                    page: \"wfProcInst\",\r\n                    fromLogin: true,\r\n                  },\r\n                });\r\n              } else {\r\n                this.$router.push({\r\n                  name: this.$config.homeName,\r\n                  params: {\r\n                    fromLogin: true,\r\n                  },\r\n                });\r\n              }\r\n\r\n              this.$nextTick(() => {\r\n                this.setTagNavList([]);\r\n                this.addTag({\r\n                  route: this.$store.state.app.homeRoute,\r\n                });\r\n              });\r\n            });\r\n          } else {\r\n            this.$refs.loginForm.buttonLoad(false);\r\n          }\r\n        });\r\n      });\r\n    },\r\n    reStyle() {\r\n      this.$nextTick(() => {\r\n        var window_width = document.documentElement.clientWidth;\r\n        var window_height = document.documentElement.clientHeight;\r\n        var login_content_left = (window_width - $(\".login-con\").width()) / 2;\r\n        var login_content_top = (window_height - $(\".login-con\").height()) / 2;\r\n\r\n        this.loginStyle = \"left:\" + login_content_left + \"px\";\r\n      });\r\n    },\r\n    login_sso() {\r\n      // 辽宁 单点登录\r\n      let code = this.$route.query.code;\r\n      let userName = this.$route.query.UserName;\r\n      let appKey = this.$route.query.AppKey;\r\n      let page = this.$route.query.page;\r\n      if (code != null && code != \"\") {\r\n        console.log(\"省认证中心统一认证登录\")\r\n        //统一认证单点登录\r\n        this.showLogin = false;\r\n        this.loading = true;\r\n        this.handleLogin({ userName: code, encrypedPwd: code, loginType: \"ln\" }).then(\r\n          async (res) => {\r\n            this.loading = false;\r\n            if (res.code == 0) {\r\n              this.login_success(null);\r\n            } else {\r\n              this.$refs.loginForm.buttonLoad(false);\r\n            }\r\n          }\r\n        );\r\n      } else if (userName != null && userName != \"\" && appKey != null && appKey != \"\") {\r\n        console.log(\"OA单点登录\")\r\n        //原有OA单点登录\r\n        this.showLogin = false;\r\n        this.loading = true;\r\n        this.getPublicKey(userName).then((res) => {\r\n          const pwdKey = new RSAUtils.getKeyPair(res.data.exponent, \"\", res.data.modulus);\r\n          const reversedPwd = appKey.split(\"\").reverse().join(\"\");\r\n          const encrypedPwd = RSAUtils.encryptedString(pwdKey, reversedPwd);\r\n\r\n          this.handleLogin({\r\n            userName: userName,\r\n            encrypedPwd: encrypedPwd,\r\n            loginType: \"1\",\r\n          }).then(async (res) => {\r\n            this.loading = false;\r\n            if (res.code == 0) {\r\n              this.login_success(page);\r\n            } else {\r\n              this.$refs.loginForm.buttonLoad(false);\r\n            }\r\n          });\r\n        });\r\n      } else {\r\n        this.showLogin = true;\r\n        // if (\"production\" == process.env.NODE_ENV && \"dev\" !== config.deploy_env)\r\n        //   window.location.href =\r\n        //     \"http://eam.sc.ctc.com:8002/eam-apps/oauth/authorize?client_id=CTSCNHXT20210819&response_type=code&redirect_uri=http://172.16.47.127:80/login\";\r\n        //alert(\"pc href:\"+window.location.href);\r\n        /*if(\"production\"==process.env.NODE_ENV&&\"dev\"!==config.deploy_env)\r\n                        window.location.href=\"http://uamportal.paas.sc.ctc.com:22002？redirect_uri=http://172.16.47.127:80/login\"\r\n*/\r\n      }\r\n    },\r\n    login_success(page) {\r\n      this.dictInit();\r\n      this.getUserInfo().then((res) => {\r\n        if (page) {\r\n          this.$router.push({\r\n            name: this.$config.homeName,\r\n            params: {\r\n              page: page,\r\n              fromLogin: true,\r\n            },\r\n          });\r\n        } else {\r\n          this.$router.push({\r\n            name: this.$config.homeName,\r\n            params: {\r\n              fromLogin: true,\r\n            },\r\n          });\r\n        }\r\n        this.$nextTick(() => {\r\n          this.setTagNavList([]);\r\n          this.addTag({\r\n            route: this.$store.state.app.homeRoute,\r\n          });\r\n        });\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style></style>\r\n"]}]}