package com.enrising.ctsc.assess.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 考核报告
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */

@Data
@TableName("assess_report")
public class AssessReport extends Model<AssessReport> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 考核任务id
     */
    private Long templateId;

    /**
     * 报告名称
     */
    private String reportName;

    /**
     * 报告内容
     */
    private String content;

    /**
     * 意见建议
     */
    private String suggestion;

    /**
     * 是否通知公告
     */
    private String isNotification;

    /**
     * 报告落款人
     */
    private String reporter;

    /**
     * 报告时间
     */
    private String reportTime;

    /**
     * 生成状态
     */
    private String generateStatus;

    /**
     * 删除标志
     */
    private String delFlag;

}
