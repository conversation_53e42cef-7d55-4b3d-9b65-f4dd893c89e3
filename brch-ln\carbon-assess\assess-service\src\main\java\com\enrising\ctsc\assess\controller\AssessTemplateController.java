package com.enrising.ctsc.assess.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.assess.api.bo.AssessTemplateBo;
import com.enrising.ctsc.assess.api.entity.AssessTemplate;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.assess.api.vo.AssessTemplateVo;
import com.enrising.ctsc.assess.api.vo.DeptTreeVo;
import com.enrising.ctsc.assess.service.AssessTemplateService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 考核模板管理前端接口
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-07
 */
@RestController
@RequestMapping("/assess/assessTemplate")
@AllArgsConstructor
public class AssessTemplateController {
	private final AssessTemplateService assessTemplateService;

	@PostMapping("/getAssessTemplatePage")
	public R<Page<AssessTemplateVo>> getAssessTemplatePage(@RequestBody QueryPage<AssessTemplateBo> queryPage) {
		return R.success(assessTemplateService.getAssessTemplatePage(queryPage));
	}

	@PostMapping("/getDeliveredTaskByPage")
	public R<Page<AssessTemplateVo>> getDeliveredTaskByPage(@RequestBody QueryPage<AssessTemplateBo> queryPage) {
		return R.success(assessTemplateService.getDeliveredTaskByPage(queryPage));
	}

	@GetMapping("/getDeliveredTaskList")
	public R<List<AssessTemplate>> getDeliveredTaskList() {
		return R.success(assessTemplateService.getDeliveredTaskList());
	}

	@GetMapping("/countTotalTask")
	public R<Integer> countTotalTask() {
		return R.success(assessTemplateService.countTotalTask());
	}

	@PostMapping("/getAllAssessTemplate")
	public R<List<AssessTemplateVo>> getAllAssessTemplate(@RequestBody AssessTemplateBo bo) {
		return R.success(assessTemplateService.getAllAssessTemplate(bo));
	}

	@PostMapping("/getAllAssessTemplateByYear")
	public R<List<AssessTemplateVo>> getAllAssessTemplateByYear() {
		return R.success(assessTemplateService.getAllAssessTemplateByYear());
	}

	@GetMapping("/getById/{id}")
	public R<AssessTemplateVo> getById(@PathVariable(name = "id") Long id) {
		AssessTemplateVo detail = assessTemplateService.getDetailById(id);
		return R.success(detail, "查询成功");
	}

	@PostMapping(value = "/save")
	public R<String> save(@RequestBody AssessTemplateBo bo) {
		boolean isSuc = assessTemplateService.add(bo);
		if (isSuc){
			return R.success("操作成功");
		}
		return R.failed("操作失败");
	}

	@PostMapping(value = "/update")
	public R<String> update(@RequestBody AssessTemplateBo bo) {
		boolean isSuc = assessTemplateService.edit(bo);
		if (isSuc){
			return R.success("操作成功");
		}
		return R.failed("操作失败");
	}

	@PostMapping(value = "/delete/{id}")
	public R<String> delete(@PathVariable Long id) {
		boolean isSuc = assessTemplateService.del(id);
		if (isSuc){
			return R.success("操作成功");
		}
		return R.failed("操作失败");
	}

	@GetMapping("/isHasReportData/{id}")
	public R<Boolean> isHasReportData(@PathVariable(name = "id") Long id) {
		boolean isHasReportData = assessTemplateService.isHasReportData(id);
		return R.success(isHasReportData, "查询成功");
	}

	/***
	 * 设置考核模板下发状态
	 * @param id 考核模板ID
	 * @param sendStatus 考核模板下发状态，1-已下发 2-未下发 3-已撤回
	 * @return 返回操作是否成功
	 */
	@GetMapping(value = "/setTemplateSendStatus")
	public R<Boolean> setTemplateSendStatus(@RequestParam(required = true) Long id,
										@RequestParam(required = true) String sendStatus) {
		boolean isUpdate = assessTemplateService.setTemplateSendStatus(id, sendStatus);
		if (isUpdate) {
			return R.success(true,"操作成功");
		}
		return R.failed(false, "操作失败");
	}


	@GetMapping("/checkReportTime/{id}")
	public R<Boolean> checkReportTime(@PathVariable(name = "id")  Long id) {
		return R.success(assessTemplateService.checkReportTime(id));
	}
	/**
	 * 返回部门树形集合
	 *
	 * @return 树形部门
	 */
	@GetMapping(value = "/assessTree")
	public R<List<DeptTreeVo>> listAssessDeptTrees(DeptTreeVo deptTree) {
		return R.success(assessTemplateService.listAssessDeptTrees(deptTree));
	}
}
