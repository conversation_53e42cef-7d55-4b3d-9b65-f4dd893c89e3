package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.discharge.api.utils.IndicatorTree;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.bo.DischargeEnergyIndicatorBo;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyIndicator;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyIndicatorQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorVo;

import java.util.List;

/**
 * 碳排放能源指标表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
public interface DischargeEnergyIndicatorService extends IService<DischargeEnergyIndicator> {

	/**
	 * 分页查询
	 *
	 * @param query 参数
	 * @return 列表
	 */
	TableDataInfo<DischargeEnergyIndicatorVo> findList(DischargeEnergyIndicatorQuery query);

	/**
	 * 列表查询
	 *
	 * @return 列表
	 */
	List<DischargeEnergyIndicatorVo> getIndicatorList();

	/**
	 * 构建树
	 * @param lazy 是否是懒加载
	 * @param parentId 父节点ID
	 * @return
	 */
	List<IndicatorTree> getTree(boolean lazy, Long parentId);

	/**
	 * 详情
	 *
	 * @param query 参数
	 * @return 详情
	 */
	DischargeEnergyIndicatorVo detail(DischargeEnergyIndicatorQuery query);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	void add(DischargeEnergyIndicatorBo bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	void edit(DischargeEnergyIndicatorBo bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	void del(Long id);
}
