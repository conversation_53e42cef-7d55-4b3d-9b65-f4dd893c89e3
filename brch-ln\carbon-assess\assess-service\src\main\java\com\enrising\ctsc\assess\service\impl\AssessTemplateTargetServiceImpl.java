package com.enrising.ctsc.assess.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.assess.api.bo.AssessTemplateTargetObjectBo;
import com.enrising.ctsc.assess.api.entity.AssessTemplateTarget;
import com.enrising.ctsc.assess.api.vo.AssessTemplateTargetVo;
import com.enrising.ctsc.assess.mapper.AssessTemplateTargetMapper;
import com.enrising.ctsc.assess.service.AssessTemplateTargetService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 考核模板指标
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-08
 */
@Service
@AllArgsConstructor
public class AssessTemplateTargetServiceImpl extends ServiceImpl<AssessTemplateTargetMapper, AssessTemplateTarget> implements AssessTemplateTargetService {

	private final AssessTemplateTargetMapper assessTemplateTargetMapper;
	@Override
	public List<AssessTemplateTargetVo> getTargetAndTemInfo(AssessTemplateTargetObjectBo bo) {
		return assessTemplateTargetMapper.getTargetAndTemInfo(bo);
	}
}