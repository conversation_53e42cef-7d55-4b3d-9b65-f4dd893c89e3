package com.enrising.ctsc.assess.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 考核模板指标
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-08
 */

@Data
@TableName("assess_template_target")
public class AssessTemplateTarget extends Model<AssessTemplateTarget> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 考核模板id
     */
    private Long templateId;

    /**
     * 考核指标id
     */
    private Long targetId;

    /**
     * 二级考指标id
     */
    private Long secondaryTargetId;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 考核方式
     */
    private String assessMethod;

    /**
     * 考核对象类型
     */
    private String objectType;

}
