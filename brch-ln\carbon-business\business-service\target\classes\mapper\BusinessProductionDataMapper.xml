<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.business.mapper.BusinessProductionDataMapper">
	<resultMap id="BaseResultMap" type="com.enrising.ctsc.business.api.entity.BusinessProductionData">
		<result column="id" property="id" />
		<result column="create_by" property="createBy" />
		<result column="create_time" property="createTime" />
		<result column="update_by" property="updateBy" />
		<result column="update_time" property="updateTime" />
		<result column="company_id" property="companyId" />
		<result column="report_time" property="reportTime" />
		<result column="telecom_business_total" property="telecomBusinessTotal" />
		<result column="business_flow_total" property="businessFlowTotal" />
		<result column="fixed_phone_business" property="fixedPhoneBusiness" />
		<result column="broadband_business" property="broadbandBusiness" />
		<result column="special_broadband_business" property="specialBroadbandBusiness" />
		<result column="iptv_business" property="iptvBusiness" />
		<result column="mobile_phon_business" property="mobilePhonBusiness" />
		<result column="mobile_internet_business" property="mobileInternetBusiness" />
		<result column="mobile_sms_business" property="mobileSmsBusiness" />
		<result column="iot_business" property="iotBusiness" />
		<result column="idc_business" property="idcBusiness" />
		<result column="other_business" property="otherBusiness" />
		<result column="del_flag" property="delFlag" />
	</resultMap>

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.company_id,
            t.report_time,
            t.telecom_business_total,
            t.business_flow_total,
            t.fixed_phone_business,
            t.broadband_business,
            t.special_broadband_business,
            t.iptv_business,
            t.mobile_phon_business,
            t.mobile_internet_business,
            t.mobile_sms_business,
            t.iot_business,
            t.idc_business,
            t.other_business,
            t.del_flag
    </sql>
	<!-- 查询列表 -->
	<select id="findList" resultType="com.enrising.ctsc.business.api.vo.BusinessProductionDataVo">
		SELECT
			bpd.*,
			so.org_name as company_name
		FROM business_production_data bpd
		LEFT JOIN rmp.sys_organizations so ON bpd.company_id = so.id
		<where>
			bpd.del_flag = '0'
			<if test="bo.companyId != null">
				AND bpd.company_id = #{bo.companyId}
			</if>
			<if test="bo.reportTime != null">
				AND bpd.report_time = #{bo.reportTime}
			</if>
		</where>
		ORDER BY bpd.company_id,bpd.report_time ASC
	</select>
	<!-- 查询全省数据对比列表 -->
	<select id="getDataCompareList" resultType="com.enrising.ctsc.business.api.entity.BusinessProductionData">
		SELECT
			report_time,
			sum(telecom_business_total) as telecom_business_total,
			sum(business_flow_total) as business_flow_total,
			sum(fixed_phone_business) as fixed_phone_business,
			sum(broadband_business) as broadband_business,
			sum(special_broadband_business) as special_broadband_business,
			sum(iptv_business) as iptv_business,
			sum(mobile_phon_business) as mobile_phon_business,
			sum(mobile_internet_business) as mobile_internet_business,
			sum(mobile_sms_business) as mobile_sms_business,
			sum(iot_business) as iot_business,
			sum(idc_business) as idc_business,
			sum(other_business) as other_business
		FROM
			business_production_data
		${ew.customSqlSegment}
		GROUP BY report_time
		ORDER BY report_time ASC
	</select>
	<select id="getCompanyBusList"
			resultType="com.enrising.ctsc.business.api.vo.BusinessProductionDataCompareVo">
		SELECT
			company_id,
			COALESCE ( SUM ( telecom_business_total ), 0 ) AS telecom_business_total,
			COALESCE ( SUM ( business_flow_total ), 0 )  as business_flow_total
		FROM
			"business_production_data"
		WHERE
			to_char( report_time, 'yyyy' ) = #{bo.reportYear}
		GROUP BY
			company_id
	</select>
	<select id="getDeptList"
			resultType="com.enrising.ctsc.business.api.vo.CarbonRankingVo">
		SELECT
		    id as dept_id,
		    province_code as city_code,
		    org_name as dept_name,
		    0 as telecom_business_total,
			0 as telecom_business_total_last_year,
		    0 as business_flow_total,
			0 as carbon_emissions,
			0 as carbon_emissions_last_year,
			0 as energy_consumption
		FROM rmp.sys_organizations
		WHERE del_flag='0'
		  AND parent_company_no = '2600000000'
	</select>
	<select id="getBusinessDataList"
			resultType="com.enrising.ctsc.business.api.vo.CarbonRankingVo">
		SELECT
			a.id as dept_id,
			a.province_code as city_code,
			a.org_name as dept_name,
			b.telecom_business_total,
			c.telecom_business_total_last_year,
			b.business_flow_total,
			0 as carbon_emissions,
			0 as carbon_emissions_last_year,
			0 as energy_consumption
		FROM rmp.sys_organizations a
		LEFT JOIN (
			SELECT
				t.company_id,
				COALESCE ( SUM ( t.telecom_business_total ), 0 ) AS telecom_business_total,
				COALESCE ( SUM ( t.business_flow_total ), 0 ) AS business_flow_total
			FROM
				business_production_data t
			WHERE
				t.del_flag = '0'
				AND to_char( t.report_time, 'yyyy' ) = #{year}
				<if test="currentMonth != null and currentMonth != ''">
					AND to_char( report_time, 'mm' ) <![CDATA[<]]> #{currentMonth}
				</if>
				AND t.company_id NOT IN (2600000000, 0)
			GROUP BY company_id) b on a.id=b.company_id
		LEFT JOIN (
			SELECT
				company_id,
				COALESCE(SUM(telecom_business_total ), 0 ) as telecom_business_total_last_year
			FROM business_production_data
			WHERE del_flag = '0'
				AND to_char( report_time, 'yyyy' ) = #{lastYear}
				<if test="currentMonth != null and currentMonth != ''">
					AND to_char( report_time, 'mm' ) <![CDATA[<]]> #{currentMonth}
				</if>
			GROUP BY company_id
		) c on a.id = c.company_id
		WHERE a.del_flag='0'
			AND a.parent_company_no = '2600000000'
-- 		AND name NOT like '%省本部%'
-- 		AND name NOT like '%省公司%'
	</select>
	<select id="getElectricDataList"
			resultType="com.enrising.ctsc.business.api.vo.CarbonRankingVo">
		SELECT
			a.id as dept_id,
			a.province_code as city_code,
			a.org_name as dept_name,
			0 as telecom_business_total,
			0 as telecom_business_total_last_year,
			0 as business_flow_total,
			b.carbon_emissions,
			c.carbon_emissions_last_year,
			b.energy_consumption
		FROM rmp.sys_organizations a
		LEFT JOIN (
			SELECT
				t.company_id,
				CAST(COALESCE
				( (sum(t.outsourcing_thermal_power) - sum(t.own_green_power)) * (
					SELECT
					factor
					FROM
					discharge_energy_factor
					WHERE
					energy_type_id = '3'
					AND validity_start <![CDATA[<=]]> CAST(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
					AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ) / 1000 as DECIMAL(18,2)) AS carbon_emissions,
				CAST(COALESCE
						 ( (SUM ( t.outsourcing_thermal_power ) - SUM ( t.own_green_power )) * (
							SELECT
								coefficient
							FROM
								discharge_energy_coefficient
							WHERE
								energy_type_id = '3'
							  AND validity_start <![CDATA[<=]]> CAST(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
							  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
						 ) , 0 ) / 1000 as DECIMAL(18,2)) AS energy_consumption
			FROM
				discharge_data_electric t
			WHERE
				t.del_flag = '0'
				AND to_char( t.report_time, 'yyyy' )=#{year}
				<if test="currentMonth != null and currentMonth != ''">
					AND to_char( report_time, 'mm' ) <![CDATA[<]]> #{currentMonth}
				</if>
				AND t.company_id NOT IN (2600000000, 0)
			GROUP BY company_id) b on a.id = b.company_id
		LEFT JOIN (
			SELECT
			    company_id,
				CAST(COALESCE
				( (sum(outsourcing_thermal_power) - sum(own_green_power)) * (
				SELECT
				factor
				FROM
				discharge_energy_factor
				WHERE
				energy_type_id = '3'
				AND validity_start <![CDATA[<=]]> CAST(CONCAT(#{lastYear}, '-01-01 0:00:00') as TIMESTAMP)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ) / 1000 as DECIMAL(18,2)) AS carbon_emissions_last_year
			FROM discharge_data_electric
			WHERE
				del_flag = '0'
				AND to_char( report_time, 'yyyy' )=#{lastYear}
				<if test="currentMonth != null and currentMonth != ''">
					AND to_char( report_time, 'mm' ) <![CDATA[<]]> #{currentMonth}
				</if>
			GROUP BY company_id
		) c on a.id = c.company_id
		WHERE a.del_flag='0'
			AND a.parent_company_no = '2600000000'
-- 		AND name NOT like '%省本部%'
-- 		AND name NOT like '%省公司%'
	</select>
	<select id="getGasDataList"
			resultType="com.enrising.ctsc.business.api.vo.CarbonRankingVo">
		SELECT
			a.id as dept_id,
			a.province_code as city_code,
			a.org_name as dept_name,
			0 as telecom_business_total,
			0 as telecom_business_total_last_year,
			0 as business_flow_total,
			b.carbon_emissions,
			c.carbon_emissions_last_year,
			b.energy_consumption
		FROM rmp.sys_organizations a
		LEFT JOIN (
			SELECT
				t.company_id,
				CAST(( COALESCE
						   ( SUM ( t.ng )  * (
							SELECT
								factor
							FROM
								"discharge_energy_factor"
							WHERE
								energy_type_id = '5'
							  AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
							  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
						   ) , 0 ) +
				 COALESCE
						  ( SUM ( t.lpg )  * (
							 SELECT
								 factor
							 FROM
								 "discharge_energy_factor"
							 WHERE
								 energy_type_id = '6'
							   AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
							   AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
						  ) , 0 ))/ 1000 AS DECIMAL(18,2)) AS carbon_emissions,
				CAST((COALESCE
						  ( SUM ( t.ng ) * (
							SELECT
								coefficient
							FROM
								"discharge_energy_coefficient"
							WHERE
								energy_type_id = '5'
							  AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
							  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
						  ) , 0 ) / 1000 +
					  COALESCE
						   ( SUM ( t.lpg ) * (
							SELECT
								coefficient
							FROM
								"discharge_energy_coefficient"
							WHERE
								energy_type_id = '6'
							  AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
							  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
											   ) , 0 ) / 1000) as DECIMAL(18,2)) AS energy_consumption
			FROM
				discharge_data_gas t
			WHERE
				t.del_flag = '0'
			  AND to_char( t.report_time, 'yyyy' )=#{year}
			<if test="currentMonth != null and currentMonth != ''">
				AND to_char( report_time, 'mm' ) <![CDATA[<]]> #{currentMonth}
			</if>
			  AND t.company_id NOT IN (2600000000, 0)
			GROUP BY company_id) b on a.id = b.company_id
		LEFT JOIN (
			SELECT
				company_id,
				CAST(
				( COALESCE
				( SUM ( ng )  * (
				SELECT
				factor
				FROM
				"discharge_energy_factor"
				WHERE
				energy_type_id = '5'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as TIMESTAMP)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ) +
				COALESCE
				( SUM ( lpg )  * (
				SELECT
				factor
				FROM
				"discharge_energy_factor"
				WHERE
				energy_type_id = '6'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as TIMESTAMP)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ))/ 1000 AS DECIMAL(18,2)) AS carbon_emissions_last_year
			FROM discharge_data_gas
			WHERE
				del_flag = '0'
				AND to_char( report_time, 'yyyy' )=#{lastYear}
				<if test="currentMonth != null and currentMonth != ''">
					AND to_char( report_time, 'mm' ) <![CDATA[<]]> #{currentMonth}
				</if>
			GROUP BY company_id) c on a.id = c.company_id
		WHERE a.del_flag='0'
			AND a.parent_company_no = '2600000000'
-- 		AND name NOT like '%省本部%'
-- 		AND name NOT like '%省公司%'
	</select>
	<select id="getOilDataList"
			resultType="com.enrising.ctsc.business.api.vo.CarbonRankingVo">
		SELECT
			a.id as dept_id,
			province_code as city_code,
			a.org_name as dept_name,
			0 as telecom_business_total,
			0 as telecom_business_total_last_year,
			0 as business_flow_total,
			b.carbon_emissions,
			c.carbon_emissions_last_year,
			b.energy_consumption
		FROM rmp.sys_organizations a
		LEFT JOIN (
			SELECT
				t.company_id,
				sum(0) as telecom_business_total,
				sum(0) as telecom_business_total_last_year,
				sum(0) as business_flow_total,
				CAST(( COALESCE
						   ( SUM ( t.gasoline )  * (
							SELECT
								factor
							FROM
								"discharge_energy_factor"
							WHERE
								energy_type_id = '7'
							  AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
							  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
						   ) , 0 ) +
					   COALESCE
						   ( SUM ( t.diesel )  * (
						   SELECT
							   factor
						   FROM
							   "discharge_energy_factor"
						   WHERE
							   energy_type_id = '8'
							 AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
							 AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
						   ) , 0 ) +
					   COALESCE
						   ( SUM ( t.crude )  * (
						   SELECT
							   factor
						   FROM
							   "discharge_energy_factor"
						   WHERE
							   energy_type_id = '10'
							 AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
							 AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
						   ) , 0 ) +
					   COALESCE
						   ( SUM ( t.fuel )  * (
						   SELECT
							   factor
						   FROM
							   "discharge_energy_factor"
						   WHERE
							   energy_type_id = '11'
							 AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
							 AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
						   ) , 0 ) +
					   COALESCE
						   ( SUM ( t.kerosene )  * (
						   SELECT
							   factor
						   FROM
							   "discharge_energy_factor"
						   WHERE
							   energy_type_id = '9'
							 AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
							 AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
						   ) , 0 )
						   )/ 1000 AS DECIMAL(18,2)) AS carbon_emissions,
				CAST((COALESCE
				  ( SUM ( t.gasoline ) * (
					SELECT
						coefficient
					FROM
						"discharge_energy_coefficient"
					WHERE
						energy_type_id = '7'
					  AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
					  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				  ) , 0 ) / 1000 +
					COALESCE
				   ( SUM ( t.diesel ) * (
					SELECT
						coefficient
					FROM
						"discharge_energy_coefficient"
					WHERE
						energy_type_id = '8'
					  AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
					  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
									   ) , 0 ) / 1000 +
					COALESCE
						( SUM ( t.crude ) * (
					SELECT
						coefficient
					FROM
						"discharge_energy_coefficient"
					WHERE
						energy_type_id = '10'
					  AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
					  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
															) , 0 ) / 1000 +
					  COALESCE
						 ( SUM ( t.fuel ) * (
					SELECT
						coefficient
					FROM
						"discharge_energy_coefficient"
					WHERE
						energy_type_id = '11'
					  AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
					  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
																				 ) , 0 ) / 1000 +
				  COALESCE
					  ( SUM ( t.kerosene ) * (
					SELECT
						coefficient
					FROM
						"discharge_energy_coefficient"
					WHERE
						energy_type_id = '9'
					  AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
					  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
					  ) , 0 ) / 1000) as DECIMAL(18,2)) AS energy_consumption
			FROM
				discharge_data_oil t
			WHERE
				t.del_flag = '0'
			  AND to_char( t.report_time, 'yyyy' )=#{year}
				<if test="currentMonth != null and currentMonth != ''">
					AND to_char( report_time, 'mm' ) <![CDATA[<]]> #{currentMonth}
				</if>
			  AND t.company_id NOT IN (2600000000, 0)
			GROUP BY company_id) b on a.id = b.company_id
		LEFT JOIN (
			SELECT
				company_id,
				CAST(( COALESCE
					( SUM ( gasoline )  * (
					SELECT
					factor
					FROM
					"discharge_energy_factor"
					WHERE
					energy_type_id = '7'
					AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as TIMESTAMP)
					AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
					) , 0 ) +
					COALESCE
					( SUM ( diesel )  * (
					SELECT
					factor
					FROM
					"discharge_energy_factor"
					WHERE
					energy_type_id = '8'
					AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as TIMESTAMP)
					AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
					) , 0 ) +
					COALESCE
					( SUM ( crude )  * (
					SELECT
					factor
					FROM
					"discharge_energy_factor"
					WHERE
					energy_type_id = '10'
					AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as TIMESTAMP)
					AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
					) , 0 ) +
					COALESCE
					( SUM ( fuel )  * (
					SELECT
					factor
					FROM
					"discharge_energy_factor"
					WHERE
					energy_type_id = '11'
					AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as TIMESTAMP)
					AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
					) , 0 ) +
					COALESCE
					( SUM ( kerosene )  * (
					SELECT
					factor
					FROM
					"discharge_energy_factor"
					WHERE
					energy_type_id = '9'
					AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as TIMESTAMP)
					AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
					) , 0 )
					)/ 1000 AS DECIMAL(18,2)) AS carbon_emissions_last_year
			FROM discharge_data_oil
			WHERE
				del_flag = '0'
				AND to_char( report_time, 'yyyy' )=#{lastYear}
				<if test="currentMonth != null and currentMonth != ''">
					AND to_char( report_time, 'mm' ) <![CDATA[<]]> #{currentMonth}
				</if>
			GROUP BY company_id) c on a.id = c.company_id
		WHERE a.del_flag='0'
			AND a.parent_company_no = '2600000000'
-- 		AND name NOT like '%省本部%'
-- 		AND name NOT like '%省公司%'
	</select>
	<select id="getCoalDataList"
			resultType="com.enrising.ctsc.business.api.vo.CarbonRankingVo">
		SELECT
			a.id as dept_id,
			a.province_code as city_code,
			a.org_name as dept_name,
			0 as telecom_business_total,
			0 as telecom_business_total_last_year,
			0 as business_flow_total,
			b.carbon_emissions,
			c.carbon_emissions_last_year,
			b.energy_consumption
		FROM rmp.sys_organizations a
		LEFT JOIN (
			SELECT
				t.company_id,
				CAST( COALESCE
					( SUM ( t.coal )  * (
					SELECT
					factor
					FROM
					"discharge_energy_factor"
					WHERE
					energy_type_id = '13'
					AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
					AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
					) , 0) as DECIMAL(18,2))  AS carbon_emissions,
				CAST( COALESCE
					( SUM ( t.coal ) * (
							SELECT
								coefficient
							FROM
								"discharge_energy_coefficient"
							WHERE
								energy_type_id = '13'
							  AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
							  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
					) , 0) as DECIMAL(18,2)) AS energy_consumption
			FROM
				discharge_data_coal t
			WHERE
				t.del_flag = '0'
				AND to_char( t.report_time, 'yyyy' )=#{year}
			<if test="currentMonth != null and currentMonth != ''">
				AND to_char( report_time, 'mm' ) <![CDATA[<]]> #{currentMonth}
			</if>
				AND t.company_id NOT IN (2600000000, 0)
			GROUP BY company_id) b on a.id = b.company_id
		LEFT JOIN (
			SELECT
			    company_id,
			    CAST( COALESCE
					( SUM ( coal )  * (
					SELECT
					factor
					FROM
					"discharge_energy_factor"
					WHERE
					energy_type_id = '13'
					AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as TIMESTAMP)
					AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
					) , 0) as DECIMAL(18,2)) AS carbon_emissions_last_year
			FROM
				discharge_data_coal
			WHERE
				del_flag = '0'
				AND to_char( report_time, 'yyyy' )=#{lastYear}
				<if test="currentMonth != null and currentMonth != ''">
					AND to_char( report_time, 'mm' ) <![CDATA[<]]> #{currentMonth}
				</if>
			GROUP BY company_id) c on a.id = c.company_id
		WHERE a.del_flag='0'
			AND a.parent_company_no = '2600000000'
-- 		AND name NOT like '%省本部%'
-- 		AND name NOT like '%省公司%'
	</select>
	<select id="getThermalDataList"
			resultType="com.enrising.ctsc.business.api.vo.CarbonRankingVo">
		SELECT
			a.id as dept_id,
			a.province_code as city_code,
			a.org_name as dept_name,
			0 as telecom_business_total,
			0 as telecom_business_total_last_year,
			0 as business_flow_total,
			b.carbon_emissions,
			c.carbon_emissions_last_year,
			b.energy_consumption
		FROM rmp.sys_organizations a
		LEFT JOIN (
			SELECT
				t.company_id,
				CAST(COALESCE
						 ( SUM ( t.thermal )  * (
							SELECT
								factor
							FROM
								"discharge_energy_factor"
							WHERE
								energy_type_id = '12'
							  AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
							  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
						 ) , 0 ) / 1000 as DECIMAL(18,2)) AS carbon_emissions,
				CAST(COALESCE
						 ( SUM ( t.thermal ) * (
							SELECT
								coefficient
							FROM
								"discharge_energy_coefficient"
							WHERE
								energy_type_id = '12'
							  AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
							  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
						 ) , 0 ) / 1000 as DECIMAL(18,2)) AS energy_consumption
			FROM
				discharge_data_thermal t
			WHERE
				t.del_flag = '0'
			  AND to_char( t.report_time, 'yyyy' )=#{year}
				<if test="currentMonth != null and currentMonth != ''">
					AND to_char( report_time, 'mm' ) <![CDATA[<]]> #{currentMonth}
				</if>
			  AND t.company_id NOT IN (2600000000, 0)
			GROUP BY company_id) b on a.id = b.company_id
		LEFT JOIN (
			SELECT
				company_id,
				CAST(COALESCE
				( SUM ( thermal )  * (
				SELECT
				factor
				FROM
				"discharge_energy_factor"
				WHERE
				energy_type_id = '12'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as TIMESTAMP)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ) / 1000 as DECIMAL(18,2)) AS carbon_emissions_last_year
			FROM
				discharge_data_thermal
			WHERE
				del_flag = '0'
				AND to_char( report_time, 'yyyy' )=#{lastYear}
				<if test="currentMonth != null and currentMonth != ''">
					AND to_char( report_time, 'mm' ) <![CDATA[<]]> #{currentMonth}
				</if>
			GROUP BY company_id) c on a.id = c.company_id
		WHERE a.del_flag='0'
			AND a.parent_company_no = '2600000000'
	</select>
	<select id="getWaterDataList"
			resultType="com.enrising.ctsc.business.api.vo.CarbonRankingVo">
		SELECT
			a.id as dept_id,
			a.province_code as city_code,
			a.org_name as dept_name,
			0 as telecom_business_total,
			0 as telecom_business_total_last_year,
			0 as business_flow_total,
			b.carbon_emissions,
			c.carbon_emissions_last_year,
			b.energy_consumption
		FROM rmp.sys_organizations a
		LEFT JOIN (
			SELECT
				t.company_id,
				CAST(COALESCE
						 ( SUM ( t.water )  * (
							SELECT
								factor
							FROM
								"discharge_energy_factor"
							WHERE
								energy_type_id = '1'
							  AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
							  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
						 ) , 0 ) / 1000 as DECIMAL(18,2)) AS carbon_emissions,
				CAST(COALESCE
						 ( SUM ( t.water ) * (
							SELECT
								coefficient
							FROM
								"discharge_energy_coefficient"
							WHERE
								energy_type_id = '1'
							  AND validity_start <![CDATA[<=]]> cast(CONCAT(#{year}, '-01-01 0:00:00') as TIMESTAMP)
							  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
						 ) , 0 ) / 1000 as DECIMAL(18,2)) AS energy_consumption
			FROM
				discharge_data_water t
			WHERE
				t.del_flag = '0'
			  AND to_char( t.report_time, 'yyyy' )=#{year}
				<if test="currentMonth != null and currentMonth != ''">
					AND to_char( report_time, 'mm' ) <![CDATA[<]]> #{currentMonth}
				</if>
			  AND t.company_id NOT IN (2600000000, 0)
			GROUP BY company_id) b on a.id = b.company_id
		LEFT JOIN (
			SELECT
			    company_id,
			    CAST(COALESCE
				( SUM ( water )  * (
				SELECT
				factor
				FROM
				"discharge_energy_factor"
				WHERE
				energy_type_id = '1'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as TIMESTAMP)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ) / 1000 as DECIMAL(18,2)) AS carbon_emissions_last_year
			FROM
				discharge_data_water
			WHERE
				del_flag = '0'
				AND to_char( report_time, 'yyyy' )=#{lastYear}
				<if test="currentMonth != null and currentMonth != ''">
					AND to_char( report_time, 'mm' ) <![CDATA[<]]> #{currentMonth}
				</if>
			GROUP BY company_id) c on a.id = c.company_id
		WHERE a.del_flag='0'
			AND a.parent_company_no = '2600000000'
	</select>
<!--	<select id="getMonitorSettingList"-->
<!--			resultType="com.enrising.ctsc.discharge.api.entity.DischargeMonitorSetting">-->
<!--		SELECT-->
<!--			*-->
<!--		FROM discharge_monitor_setting-->
<!--		WHERE del_flag='0'-->
<!--		  AND year=#{year}-->
<!--	</select>-->
	<select id="getCompanyBusinessTotalList" resultType="java.util.HashMap">
		SELECT
		    telecom_business_total,
		    to_char(report_time, 'MM月') as data_month
		FROM business_production_data
		WHERE del_flag='0' and company_id=#{companyId} and to_char(report_time, 'yyyy')=#{dataYear}
		ORDER BY data_month asc
	</select>
	<select id="getBusinessTotalView" resultType="java.util.HashMap">
		SELECT
			COALESCE ( SUM ( t.telecom_business_total ), 0 ) AS telecom_business_total,
			COALESCE ( SUM ( t.business_flow_total ), 0 ) AS business_flow_total,
			(SELECT COALESCE ( SUM ( telecom_business_total ), 0 ) from business_production_data
				WHERE del_flag = '0'
				<if test="query.companyId != null">
					AND company_id = #{query.companyId}
				</if>
				<if test="query.preStartTime != null and query.preEndTime != null">
					AND report_time BETWEEN #{query.preStartTime} AND #{query.preEndTime}
				</if>
			) as pre_telecom_business_total,
			(SELECT COALESCE ( SUM ( telecom_business_total ), 0 ) from business_production_data
				WHERE del_flag = '0'
				<if test="query.companyId != null">
					AND company_id = #{query.companyId}
				</if>
				<if test="query.startTime != null and query.endTime != null">
					AND report_time BETWEEN cast(#{query.startTime} as TIMESTAMP) + '-1 YEAR' AND cast(#{query.endTime} as TIMESTAMP) + '-1 YEAR'
				</if>
				) as last_telecom_business_total,
			(SELECT COALESCE ( SUM ( business_flow_total ), 0 ) from business_production_data
				WHERE del_flag = '0'
				<if test="query.companyId != null">
					AND company_id = #{query.companyId}
				</if>
				<if test="query.startTime != null and query.endTime != null">
					AND report_time BETWEEN cast(#{query.startTime} as TIMESTAMP) + '-1 YEAR' AND cast(#{query.endTime} as TIMESTAMP) + '-1 YEAR'
				</if>
			) as last_business_flow_total
		FROM business_production_data t
		WHERE t.del_flag='0'
		<if test="query.companyId != null">
			AND t.company_id = #{query.companyId}
		</if>
		<if test="query.startTime != null and query.endTime != null">
			AND t.report_time BETWEEN #{query.startTime} AND #{query.endTime}
		</if>
	</select>
	<select id="getCompanyNameById" resultType="java.lang.String">
		SELECT
			org_name
		FROM rmp.sys_organizations
		WHERE
		    id = #{companyId}
	</select>
	<select id="getCompanyIdByName" resultType="java.lang.Long">
		SELECT
			id
		FROM rmp.sys_organizations
		WHERE
			del_flag = '0'
			AND org_name = #{companyName}
		LIMIT 1
	</select>
</mapper>