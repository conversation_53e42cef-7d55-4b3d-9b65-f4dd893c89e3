package com.enrising.ctsc.assess.api.bo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 考核二级指标规则
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
public class AssessTargetSecondaryRuleBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 所属二级指标id
     */
    private Long secondaryTargetId;

    /**
     * 考核规则判断
     */
    @NotBlank(message = "考核规则判断不能为空")
    private String ruleJudge;

    /**
     * 考核规则值
     */
    @Max(value = 9999, message = "考核规则值不能超过9999")
    @Min(value = 0, message = "考核规则值不能小于0")
    @NotNull(message = "考核规则值不能为空")
    private Double ruleValue;

    /**
     * 考核规则得分
     */
    @Max(value = 99999, message = "考核规则得分不能超过99999")
    @Min(value = 0, message = "考核规则得分不能小于0")
    @NotNull(message = "考核规则得分不能为空")
    private Double ruleScore;


}
