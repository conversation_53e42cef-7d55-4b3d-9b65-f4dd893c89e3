package com.enrising.ctsc.assess.api.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class AssessScoreVo {

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 模板id
     */
    private Long targetId;

    /**
     * 模板id
     */
    private String targetName;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 模板总分
     */
    private double totalScore;

    /**
     * 考核得分
     */
    private BigDecimal assessScore = new BigDecimal(0);

    /**
     * 得分率
     */
    private String assessRate = "0.0%";

    /**
     * 考核排名
     */
    private String assessRank;

    /**
     * 指标分数
     */
    private Double score;

    /**
     * 指标的周期
     */
    private Integer assessPeriod;

    /**
     * 指标类别，1-减分，2-加分
     */
    private Integer targetCategory;

    /**
     * 指标公式
     */
    private String formula;

    /**
     * 考核方式
     */
    private String assessMethod;

}
