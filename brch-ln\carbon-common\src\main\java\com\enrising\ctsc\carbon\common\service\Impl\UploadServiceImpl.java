package com.enrising.ctsc.carbon.common.service.Impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.enrising.ctsc.carbon.common.config.MinioConfig;
import com.enrising.ctsc.carbon.common.constant.UploadConstant;
import com.enrising.ctsc.carbon.common.entity.Attachments;
import com.enrising.ctsc.carbon.common.entity.CarbonAttachment;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.service.AttachmentsService;
import com.enrising.ctsc.carbon.common.service.UploadService;
import com.enrising.ctsc.carbon.common.utils.AttachmentUtil;
import com.enrising.ctsc.carbon.common.utils.MinioUtil;
import io.minio.StatObjectResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/27
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UploadServiceImpl implements UploadService {

    private final MinioConfig minioConfig;

    private final MinioUtil minioUtil;

    private final AttachmentsService attachmentsService;

    /**
     * 显示文件大小信息单位
     *
     * @param fileS
     * @return
     */
    private static String formatFileSize(long fileS) {
        DecimalFormat df = new DecimalFormat("#.00");
        String fileSizeString = "";
        String wrongSize = "0B";
        if (fileS == 0) {
            return wrongSize;
        }
        if (fileS < 1024) {
            fileSizeString = df.format((double) fileS) + " B";
        } else if (fileS < 1048576) {
            fileSizeString = df.format((double) fileS / 1024) + " KB";
        } else if (fileS < 1073741824) {
            fileSizeString = df.format((double) fileS / 1048576) + " MB";
        } else {
            fileSizeString = df.format((double) fileS / 1073741824) + " GB";
        }
        return fileSizeString;
    }

    @Override
    public List<CarbonAttachment> uploadFiles(MultipartFile[] files) {
        List<CarbonAttachment> list = new ArrayList<>();
        for (MultipartFile file : files) {
            list.add(uploadFile(file));
        }
        return list;
    }

    @Override
    @SneakyThrows
    public CarbonAttachment uploadFile(MultipartFile file) {
        if (ObjectUtil.isEmpty(file)) {
            throw new BusinessException("文件不能为空！");
        }
        String originalFilename = file.getOriginalFilename();
        String fileExtension = getFileExtension(originalFilename);
        if (!isFileTypeAllowed(fileExtension)) {
            throw new BusinessException("不支持的文件类型！");
        }

        String bucketName = minioConfig.getBucketName();
        String result = minioUtil.putObject(bucketName, file);

        log.info("文件上传结果---》{}", result);
        if (UploadConstant.UPLOAD_FAIL.equals(result)) {
            throw new BusinessException("文件上传失败！");
        }

        // 获取桶名称后面的路径作为保存名称
        String savedName = StrUtil.split(result, "/", 5, false, false).get(4);

        // 保存附件信息到数据库
        Attachments attachments = AttachmentUtil.createAttachment(
                file,
                0L, // 默认业务ID，可根据需要调整
                AttachmentUtil.BusiAlias.CARBON_FILE,
                "碳排放文件",
                AttachmentUtil.CategoryCode.FILE,
                "文件",
                result,
                minioConfig.getBucketName(),
                savedName
        );

        // 保存附件信息
        boolean saved = attachmentsService.saveAttachment(attachments);
        if (!saved) {
            throw new BusinessException("保存附件信息失败！");
        }

        //将文件保存到Attachment附件表中（保持向后兼容）
        CarbonAttachment attachment = new CarbonAttachment();
        Long fileId = attachments.getId();
        attachment.setId(fileId);
        // 原始文件名
        attachment.setFileName(originalFilename);
        //扩展名
        attachment.setFileExt(file.getContentType());
        //文件大小
        attachment.setFileSize(formatFileSize(file.getSize()));
        // 文件保存路径
        attachment.setSavedUrl(result);
        attachment.setSavedName(StrUtil.format(savedName));

        log.info("保存的文件名称：{}", savedName);
        log.info("需要保存的附件：{}", attachment);
        log.info("文件上传成功,保存在附件表中的文件id为：{}", fileId);
        return attachment;
//		FileVo fileVo = new FileVo();
//		fileVo.setAttachmentId(fileId.toString());
//		fileVo.setUrl(result);
//		fileVo.setFileName(file.getOriginalFilename());
//		fileVo.setSize(formatFileSize(file.getSize()));
//
//		return fileVo;
    }

    @Override
    @SneakyThrows
    public CarbonAttachment uploadIllustration(MultipartFile file) {
        if (ObjectUtil.isEmpty(file)) {
            throw new BusinessException("文件不能为空！");
        }
        String originalFilename = file.getOriginalFilename();
        String fileExtension = getFileExtension(originalFilename);
        if (!isFileTypeAllowed(fileExtension)) {
            throw new BusinessException("不支持的文件类型！");
        }

        String bucketName = minioConfig.getBucketName();
        String result = minioUtil.putObject(bucketName, file);

        log.info("文件上传结果---》{}", result);
        if (UploadConstant.UPLOAD_FAIL.equals(result)) {
            throw new BusinessException("文件上传失败！");
        }

        // 获取桶名称后面的路径作为保存名称
        String savedName = StrUtil.split(result, "/", 5, false, false).get(4);

        // 保存附件信息到数据库
        Attachments attachments = AttachmentUtil.createAttachment(
                file,
                0L, // 默认业务ID，可根据需要调整
                AttachmentUtil.BusiAlias.CARBON_ILLUSTRATION,
                "碳排放插图",
                AttachmentUtil.CategoryCode.IMAGE,
                "图片",
                result,
                minioConfig.getBucketName(),
                savedName
        );

        // 保存附件信息
        boolean saved = attachmentsService.saveAttachment(attachments);
        if (!saved) {
            throw new BusinessException("保存附件信息失败！");
        }

        //将文件保存到Attachment附件表中（保持向后兼容）
        CarbonAttachment attachment = new CarbonAttachment();
        Long fileId = attachments.getId();
        attachment.setId(fileId);
        // 原始文件名
        attachment.setFileName(originalFilename);
        //扩展名
        attachment.setFileExt(file.getContentType());
        //文件大小
        attachment.setFileSize(formatFileSize(file.getSize()));
        // 文件保存路径
        attachment.setSavedUrl(result);
        attachment.setSavedName(StrUtil.format(savedName));

        log.info("保存的文件名称：{}", savedName);
        log.info("需要保存的附件：{}", attachment);
        log.info("文件上传成功,保存在附件表中的文件id为：{}", fileId);
        return attachment;
//		//预览地址
//		String preUrl = minioUtil.getObjectUrl(bucketName, savedName);
//
//		FileVo fileVo = new FileVo();
//		fileVo.setAttachmentId(fileId.toString());
//		fileVo.setUrl(preUrl);
//		fileVo.setFileName(file.getOriginalFilename());
//		fileVo.setSize(formatFileSize(file.getSize()));
//
//		return fileVo;
    }

    @Override
    @SneakyThrows(Exception.class)
    public void downloadFile(HttpServletResponse response, CarbonAttachment attachment) {

        if (ObjectUtil.isNull(attachment)) {
            throw new BusinessException("文件不能为空！");
        }

        String bucketName = minioConfig.getBucketName();
        StatObjectResponse statObjectResponse = minioUtil.statObject(bucketName, attachment.getSavedName());

        response.setContentType(statObjectResponse.contentType());

        // 设置响应头
        response.setHeader("content-type", statObjectResponse.contentType());

        InputStream is = minioUtil.getObject(bucketName, attachment.getSavedName());
        IOUtils.copy(is, response.getOutputStream());
        is.close();
        // 下载成功后记录下载次数
//		remoteAttachmentService.addDownloadCountByFileId(attachment.getId());

    }

    @Override
    public String getPreviewUrl(String fileName) {
        String bucketName = minioConfig.getBucketName();
        String preUrl = minioUtil.getObjectUrl(bucketName, fileName);
        return preUrl;
    }

    /**
     * 返回文件的后缀
     *
     * @param fileName
     * @return String
     */
    private String getFileExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf(".");
        if (dotIndex >= 0 && dotIndex < fileName.length() - 1) {
            return fileName.substring(dotIndex + 1).toLowerCase();
        }
        return "";
    }

    /**
     * 判断该文件是否支持上传
     *
     * @param fileExtension
     * @return boolean
     */
    private boolean isFileTypeAllowed(String fileExtension) {
        // 判断文件类型是否允许上传
        String fileType = minioConfig.getFileType();
        if (StrUtil.isNotBlank(fileType)) {
            List<String> list = Arrays.asList(fileType.split("、"));
            return list.contains(fileExtension);
        }
        return true;
    }
}
