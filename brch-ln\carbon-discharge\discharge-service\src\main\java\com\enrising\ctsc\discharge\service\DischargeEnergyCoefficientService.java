package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.discharge.api.bo.DischargeEnergyCoefficientBo;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyCoefficient;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyCoefficientQuery;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyFactorOpenBo;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyCoefficientVo;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 碳排放能源转换系数表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
public interface DischargeEnergyCoefficientService extends IService<DischargeEnergyCoefficient> {

	/**
	 * 分页查询
	 *
	 * @param page  分页
	 * @return 列表
	 */
	TableDataInfo<DischargeEnergyCoefficientVo> findList(QueryPage<DischargeEnergyCoefficientQuery> page);

	/**
	 * 导出excel
	 *
	 * @param query 查询条件
	 * @param response 响应
	 */
	void exportExcel(DischargeEnergyCoefficientQuery query, HttpServletResponse response);

	/**
	 * 详情
	 *
	 * @param query 参数
	 * @return 详情
	 */
	DischargeEnergyCoefficientVo detail(DischargeEnergyCoefficientQuery query);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	void add(DischargeEnergyCoefficientBo bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	void edit(DischargeEnergyCoefficientBo bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	void del(Long id);

	/**
	 * 根据时间获取有效Coefficient
	 *
	 * @param energyTypeId 能量类型id
	 * @param reportTime 数据时间
	 * @return 有效Coefficient
	 */
	BigDecimal getCoefficientByTime(Long energyTypeId, Date reportTime);

	/**
	 * 根据时间查询类型列表，判断是否有重叠
	 *
	 * @param query 查询条件
	 * @return 列表
	 */
    List<DischargeEnergyCoefficient> selectTypeByDate(DischargeEnergyCoefficientQuery query);

	List<DischargeEnergyCoefficient> getListByTime(Date reportTime);

	DischargeEnergyCoefficientVo getGainFactor(DischargeEnergyFactorOpenBo bo);
}
