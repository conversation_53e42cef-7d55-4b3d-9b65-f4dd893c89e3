<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.enrising.ctsc.carbon.common.mapper.AttachmentsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.enrising.ctsc.carbon.common.entity.Attachments">
        <id column="id" property="id"/>
        <result column="mongodb_file_id" property="mongodbFileId"/>
        <result column="busi_id" property="busiId"/>
        <result column="category_id" property="categoryId"/>
        <result column="category_name" property="categoryName"/>
        <result column="category_code" property="categoryCode"/>
        <result column="busi_alias_name" property="busiAliasName"/>
        <result column="busi_alias" property="busiAlias"/>
        <result column="file_name" property="fileName"/>
        <result column="database_name" property="databaseName"/>
        <result column="collection" property="collection"/>
        <result column="file_size" property="fileSize"/>
        <result column="file_type" property="fileType"/>
        <result column="area_code" property="areaCode"/>
        <result column="shard_key" property="shardKey"/>
        <result column="save_type" property="saveType"/>
        <result column="litimg_url" property="litimgUrl"/>
        <result column="url" property="url"/>
        <result column="description" property="description"/>
        <result column="creator_id" property="creatorId"/>
        <result column="creator_name" property="creatorName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="year" property="year"/>
        <result column="del_flag" property="delFlag"/>
        <result column="bucket_name" property="bucketName"/>
        <result column="object_name" property="objectName"/>
        <result column="invoice_flag" property="invoiceFlag"/>
        <result column="invoice_supplier" property="invoiceSupplier"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, mongodb_file_id, busi_id, category_id, category_name, category_code,
        busi_alias_name, busi_alias, file_name, database_name, collection,
        file_size, file_type, area_code, shard_key, save_type, litimg_url,
        url, description, creator_id, creator_name, create_time, update_time,
        year, del_flag, bucket_name, object_name, invoice_flag, invoice_supplier
    </sql>

    <!-- 根据业务ID和业务别名查询附件列表 -->
    <select id="selectByBusiIdAndAlias" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM attachments
        WHERE del_flag = '0'
        AND busi_id = #{busiId}
        AND busi_alias = #{busiAlias}
        ORDER BY create_time DESC
    </select>

    <!-- 根据业务ID列表批量查询附件 -->
    <select id="selectByBusiIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM attachments
        WHERE del_flag = '0'
        AND busi_id IN
        <foreach collection="busiIds" item="busiId" open="(" separator="," close=")">
            #{busiId}
        </foreach>
        ORDER BY create_time DESC
    </select>

</mapper>
