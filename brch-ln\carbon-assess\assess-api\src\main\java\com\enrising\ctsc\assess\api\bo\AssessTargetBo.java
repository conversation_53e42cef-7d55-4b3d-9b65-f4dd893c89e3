package com.enrising.ctsc.assess.api.bo;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 考核指标
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
public class AssessTargetBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;


    /**
     * 指标类型
     */
    @NotBlank(message = "指标类型不能为空")
    @Length(max = 50, message = "指标类型字符长度不能超过50")
    private String targetType;

    /**
     * 指标年份
     */
    @NotBlank(message = "指标年份不能为空")
    @Length(min = 4, max = 4, message = "指标年份格式不正确")
    private String targetYear;

    /**
     * 指标类别
     */
    @NotBlank(message = "指标类别不能为空")
    private String targetCategory;

    /**
     * 指标状态
     */
    private String status;


    /**
     * 二级指标
     */
    @Valid
    @NotEmpty(message = "二级指标不能为空")
    private List<AssessTargetSecondaryBo> targetSecondaryList;


    /**
     * 获取二级指标
     *
     * @return 二级指标列表
     */
    public List<AssessTargetSecondaryBo> getTargetSecondaryList() {
        if (targetSecondaryList == null) {
            return new ArrayList<>();
        }
        return targetSecondaryList;
    }
}
