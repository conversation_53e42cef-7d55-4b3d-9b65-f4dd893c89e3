package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.enrising.ctsc.discharge.api.entity.AccountBaseResult;
import com.enrising.ctsc.discharge.api.entity.EnergyCountData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 能耗数据统计
 *
 * <AUTHOR>
 * @since 2024-09-16
 */
@Mapper
public interface EnergyCountDataMapper extends BaseMapper<EnergyCountData> {
    Long getIndicatorIdByCode(@Param("indicatorCode") String indicatorCode);

    List<HashMap<String, Object>> selectListByBudgetSetName(@Param("query") HashMap<String, Object> query);

    List<AccountBaseResult> getAccountBaseByMss(@Param("query") HashMap<String, Object> query);
    List<AccountBaseResult> getAccountBaseByBillId(@Param("billId") Long billId);

    List<AccountBaseResult> getAccountEsBaseByBillId(@Param("billId") Long billId);

    List<AccountBaseResult> getAccountThermalBaseByBillId(@Param("billId") Long billId);

    List<AccountBaseResult> getAccountCoalBaseByBillId(@Param("billId") Long billId);

    List<AccountBaseResult> getAccountOilBaseByBillId(@Param("billId") Long billId);

}