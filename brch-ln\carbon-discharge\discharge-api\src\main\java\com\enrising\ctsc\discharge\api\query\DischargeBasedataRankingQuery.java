package com.enrising.ctsc.discharge.api.query;

import com.enrising.ctsc.discharge.api.entity.DischargeBasedataRanking;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 集团排名情况查询
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DischargeBasedataRankingQuery extends DischargeBasedataRanking {

    private String timePeriodYear;

    private String displayState;

    /**
     * 关键字搜索
     */
    private String keys;
}
