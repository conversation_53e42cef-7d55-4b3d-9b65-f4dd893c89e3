package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 *  碳盘查数据表
 *
 * <AUTHOR>
 * @since 3/13
 */

@Data
@TableName("discharge_data_examine")
public class DischargeDataExamine extends Model<DischargeDataExamine> {

	/**
	 * 主键id,采用雪花id
	 */
		@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 碳盘查数据表主键id
	 */
		private Long energyExamineId;

	/**
	 * 创建者id
	 */
		private Long createBy;

	/**
	 * 创建时间
	 */
		private Date createTime;

	/**
	 * 更新者id
	 */
		private Long updateBy;

	/**
	 * 更新时间
	 */
		private Date updateTime;

	/**
	 * 公司id
	 */
		private Long companyId;

	/**
	 * 使用量
	 */
		private BigDecimal use;

	/**
	 * 填报时间
	 */
		private String reportTime;

	/**
	 * 删除标志：0-正常；1-删除
	 */
		@TableLogic
	private String delFlag;

}
