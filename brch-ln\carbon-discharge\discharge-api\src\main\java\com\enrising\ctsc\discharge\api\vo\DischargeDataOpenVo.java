package com.enrising.ctsc.discharge.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 开放接口返回vo
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeDataOpenVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 填报单位id
	 */
	private Long companyId;

	/**
	 * 数据填报单位名称
	 */
	private String companyName;

	/*  电   */
	/**
	 * 外购绿电
	 */
	private BigDecimal outsourcingGreenPower = new BigDecimal(0);

	/**
	 * 外购火电
	 */
	private BigDecimal outsourcingThermalPower = new BigDecimal(0);

	/**
	 * 自有新能源发电
	 */
	private BigDecimal ownGreenPower = new BigDecimal(0);
	/*  气   */
	/**
	 * 天然气
	 */
	private BigDecimal ng = new BigDecimal(0);

	/**
	 * 液化石油气
	 */
	private BigDecimal lpg = new BigDecimal(0);

	/*  油   */
	/**
	 * 汽油
	 */
	private BigDecimal gasoline = new BigDecimal(0);

	/**
	 * 柴油
	 */
	private BigDecimal diesel = new BigDecimal(0);

	/**
	 * 原油
	 */
	private BigDecimal crude = new BigDecimal(0);

	/**
	 * 燃料油
	 */
	private BigDecimal fuel = new BigDecimal(0);

	/**
	 * 煤油
	 */
	private BigDecimal kerosene = new BigDecimal(0);

	/*  水   */
	/**
	 * 用水总量
	 */
	private BigDecimal water = new BigDecimal(0);

	/*  热力  */
	/**
	 * 热力总量
	 */
	private BigDecimal thermal = new BigDecimal(0);

	/*  煤炭  */
	/**
	 * 煤碳用量
	 */
	private BigDecimal coal = new BigDecimal(0);

	/**
	 * 能源消耗总量
	 */
	private BigDecimal energyConsumption = new BigDecimal(0);

	/**
	 * 碳排总量
	 */
	private BigDecimal carbonEmissions = new BigDecimal(0);

}