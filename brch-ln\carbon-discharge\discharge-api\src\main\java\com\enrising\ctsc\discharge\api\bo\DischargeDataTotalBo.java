package com.enrising.ctsc.discharge.api.bo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 碳排放数据总量
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeDataTotalBo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 填报单位id
	 */
	private Long companyId;

	/**
	 * 填报时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date reportTime;

	/**
	 * 数据年份
	 */
	private Integer dataYear;

	/**
	 * 数据类型
	 */
	private String dataType;

	/**
	 * 统计时间类型
	 */
	private String timeType;

	/**
	 * 填报时间范围
	 */
	private String[] customTime;
}
