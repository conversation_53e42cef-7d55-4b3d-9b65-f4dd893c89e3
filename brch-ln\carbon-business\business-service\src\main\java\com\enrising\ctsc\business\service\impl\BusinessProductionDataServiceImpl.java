package com.enrising.ctsc.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.business.api.bo.BusinessProductionDataBo;
import com.enrising.ctsc.business.api.entity.BusinessProductionData;
import com.enrising.ctsc.business.api.enums.DischargeCarbonTimeType;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.business.api.vo.BusinessProductionDataCompareVo;
import com.enrising.ctsc.business.api.vo.BusinessProductionDataVo;
import com.enrising.ctsc.business.api.vo.CarbonRankingVo;
import com.enrising.ctsc.business.mapper.BusinessProductionDataMapper;
import com.enrising.ctsc.business.service.BusinessProductionDataService;
import com.sccl.common.utils.MathUtils;
import com.sccl.common.utils.NumberFormatUtils;
import com.sccl.common.utils.QueryPage;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生产业务数据表服务接口实现
 *
 * <AUTHOR>
 * @since 1.0.0 2023-03-23
 */
@Service
@AllArgsConstructor
public class BusinessProductionDataServiceImpl extends ServiceImpl<BusinessProductionDataMapper,
		BusinessProductionData> implements BusinessProductionDataService {

	private final BusinessProductionDataMapper businessProductionDataMapper;

	@Override
	public TableDataInfo<BusinessProductionDataVo> getByPage(QueryPage<BusinessProductionDataBo> queryPage) {
		BusinessProductionDataBo businessProductionDataBo = queryPage.getModel();
		return TableDataInfo.build(baseMapper.findList(new Page<>(queryPage.getCurrent(), queryPage.getSize()), businessProductionDataBo));
	}

	@Override
	public List<BusinessProductionDataCompareVo> getDataCompareList(BusinessProductionDataBo businessProductionDataBo) {
		String deptName = "全省";
		if (ObjectUtil.isNotEmpty(businessProductionDataBo.getCompanyId())) {
			deptName = baseMapper.getCompanyNameById(businessProductionDataBo.getCompanyId());
		}
		if (ArrayUtil.isEmpty(businessProductionDataBo.getReportTimeArea())) {
			businessProductionDataBo.setReportTimeArea(genDateByYear(businessProductionDataBo.getDataYear()));
		}
		BusinessProductionData[] businessProductionDataArray = getCompareDataArray(businessProductionDataBo.getCompanyId(),
				businessProductionDataBo.getReportTimeArea(), businessProductionDataBo.getTimeType());
		Date[] lastYearDate = getLastYearDate(businessProductionDataBo.getReportTimeArea());
		BusinessProductionData[] lastDataArray = getCompareDataArray(businessProductionDataBo.getCompanyId(),
				lastYearDate, businessProductionDataBo.getTimeType());
		List<BusinessProductionDataCompareVo> businessProductionDataCompareVoList = new ArrayList<>();
		int[] dataRange = getDataRange(businessProductionDataBo.getDataYear(), businessProductionDataBo.getTimeType(),
				businessProductionDataBo.getReportTimeArea());
		for (int i = dataRange[0]; i < dataRange[1]; i++) {
			BusinessProductionDataCompareVo businessProductionDataCompareVo = new BusinessProductionDataCompareVo();
			if (businessProductionDataBo.getTimeType().equals(DischargeCarbonTimeType.QUARTER.getValue())) {
				businessProductionDataCompareVo.setDateMonth("第" + (i + 1) + "季度");
			} else {
				businessProductionDataCompareVo.setDateMonth((i + 1) + "月");
			}
			BeanUtils.copyProperties(businessProductionDataArray[i], businessProductionDataCompareVo);
			businessProductionDataCompareVo.setCompanyName(deptName);
			businessProductionDataCompareVoList.add(addLastYearData(businessProductionDataCompareVo, lastDataArray[i]));
		}
		return businessProductionDataCompareVoList;
	}

	@Override
	public List<BusinessProductionDataCompareVo> getCompanyBusList(BusinessProductionDataBo businessProductionDataBo) {
		return businessProductionDataMapper.getCompanyBusList(businessProductionDataBo);
	}

	@Override
	public List<CarbonRankingVo> getCarbonRanking(String year) {
		// 获取当前日期
		Date currentDate = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
		String formattedDate = sdf.format(currentDate);
		String currentMonth = formattedDate.substring(5);
		if (StrUtil.isBlank(year)) {
			//获取当前年份
			year = formattedDate.substring(0, 4);
		}
		if ("01".equals(currentMonth)) {
			currentMonth = null;
			year = String.valueOf(Integer.parseInt(year) - 1);
		}
		List<CarbonRankingVo> carbonRankingVoList = getCarbonDataList(year, currentMonth);
		List<CarbonRankingVo> resultList = carbonRankingVoList.stream().sorted(Comparator.comparing(CarbonRankingVo::getCarbonStrength,
				Comparator.reverseOrder()).thenComparing(CarbonRankingVo::getCarbonEmissions, Comparator.reverseOrder()).thenComparing(
								CarbonRankingVo::getTelecomBusinessTotal, Comparator.reverseOrder())).collect(Collectors.toList());
		for (int i = 0; i < resultList.size(); i++) {
			resultList.get(i).setRankNum(NumberFormatUtils.arabicNumToChineseNum(i + 1));
		}
		return resultList;
	}

	@Override
	public List<HashMap<String, Object>> getCompanyBusinessTotalList(Long companyId, String dataYear) {
		return baseMapper.getCompanyBusinessTotalList(companyId, dataYear);
	}

	@Override
	public HashMap<String, Object> getBusinessRanking() {
		HashMap<String, Object> result = new HashMap<>();
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.MONTH,-1);
		String year = String.valueOf(calendar.get(Calendar.YEAR));
		String currentMonth = String.valueOf(calendar.get(Calendar.MONTH) + 1);
		//获取数据列表
		List<CarbonRankingVo> carbonRankingVoList = getCarbonDataList(year, currentMonth);

		//业务总量同比增幅排名
		List<CarbonRankingVo> businessRankingList = carbonRankingVoList.stream().sorted(Comparator
				.comparing(CarbonRankingVo::getTelecomBusinessTotalGrowthRate, Comparator.reverseOrder())
				.thenComparing(CarbonRankingVo::getTelecomBusinessTotal, Comparator.reverseOrder())
				.thenComparing(CarbonRankingVo::getTelecomBusinessTotalLastYear, Comparator.reverseOrder())).collect(Collectors.toList());
		double averageValue = businessRankingList.stream().mapToDouble(CarbonRankingVo::getTelecomBusinessTotalGrowthRate).average().orElse(0D);
		Long underAvgNumber = businessRankingList.stream().filter(data -> data.getTelecomBusinessTotalGrowthRate() < averageValue).count();
		HashMap<String, Object> businessRankingMap = new HashMap<>();
		businessRankingMap.put("businessRankingList", businessRankingList);
		businessRankingMap.put("firstDept", businessRankingList.get(0).getDeptName());
		businessRankingMap.put("lastDept", businessRankingList.get(businessRankingList.size()-1).getDeptName());
		businessRankingMap.put("averageValue", BigDecimal.valueOf(averageValue).setScale(2, BigDecimal.ROUND_HALF_UP));
		businessRankingMap.put("underAvgNumber", underAvgNumber);
		result.put("businessRanking", businessRankingMap);

		//碳排放强度同比降幅排名
		List<CarbonRankingVo> strengthDecreaseRankingList = carbonRankingVoList.stream().sorted(Comparator
				.comparing(CarbonRankingVo::getCarbonStrengthDecreaseRate, Comparator.reverseOrder())
				.thenComparing(CarbonRankingVo::getCarbonStrength, Comparator.reverseOrder())
				.thenComparing(CarbonRankingVo::getTelecomBusinessTotal, Comparator.reverseOrder())
				.thenComparing(CarbonRankingVo::getCarbonEmissions, Comparator.reverseOrder())).collect(Collectors.toList());
		result.put("carbonStrengthDecreaseRanking", strengthDecreaseRankingList);

		//碳排放量同比增幅排名
		List<CarbonRankingVo> carbonEmissionsGrowthRateRankingList = carbonRankingVoList.stream().sorted(Comparator
				.comparing(CarbonRankingVo::getCarbonEmissionsGrowthRate, Comparator.reverseOrder())
				.thenComparing(CarbonRankingVo::getCarbonEmissions, Comparator.reverseOrder())).collect(Collectors.toList());
		result.put("carbonEmissionsGrowthRateRanking", carbonEmissionsGrowthRateRankingList);
		return result;
	}

	@Override
	public HashMap<String, Object> getBusinessOverview(Long companyId) {
		BusinessProductionDataBo query = new BusinessProductionDataBo();
		query.setCompanyId(companyId);
		HashMap<String, Object> result = new HashMap<>();
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.MONTH,-1);
		Date currentDate = calendar.getTime();
		calendar.add(Calendar.MONTH,-1);
		Date preMontDate  = calendar.getTime();
		calendar.add(Calendar.MONTH,-2);
		Date preQuarterDate  = calendar.getTime();
		calendar.add(Calendar.MONTH,-9);

		query.setStartTime(DateUtil.beginOfMonth(currentDate));
		query.setEndTime(DateUtil.endOfMonth(currentDate));
		query.setPreStartTime(DateUtil.beginOfMonth(preMontDate));
		query.setPreEndTime(DateUtil.endOfMonth(preMontDate));
		HashMap<String, Object> monthData = baseMapper.getBusinessTotalView(query);
		monthData.put("YoY", MathUtils.getUpRate(new BigDecimal(monthData.get("telecom_business_total").toString()),
				new BigDecimal(monthData.get("last_telecom_business_total").toString())));
		monthData.put("MoM", MathUtils.getUpRate(new BigDecimal(monthData.get("telecom_business_total").toString()),
				new BigDecimal(monthData.get("pre_telecom_business_total").toString())));
		result.put("monthData", monthData);
		query.setStartTime(DateUtil.beginOfQuarter(currentDate));
		query.setEndTime(DateUtil.endOfQuarter(currentDate));
		query.setPreStartTime(DateUtil.beginOfQuarter(preQuarterDate));
		query.setPreEndTime(DateUtil.endOfQuarter(preQuarterDate));
		HashMap<String, Object> quarterData = baseMapper.getBusinessTotalView(query);
		quarterData.put("YoY", MathUtils.getUpRate(new BigDecimal(quarterData.get("telecom_business_total").toString()),
				new BigDecimal(quarterData.get("last_telecom_business_total").toString())));
		quarterData.put("MoM", MathUtils.getUpRate(new BigDecimal(quarterData.get("telecom_business_total").toString()),
				new BigDecimal(quarterData.get("pre_telecom_business_total").toString())));
		result.put("quarterData", quarterData);
		query.setStartTime(DateUtil.beginOfYear(currentDate));
		query.setEndTime(DateUtil.endOfMonth(currentDate));
		query.setPreStartTime(DateUtil.beginOfYear(currentDate));
		query.setPreEndTime(DateUtil.endOfMonth(currentDate));
		HashMap<String, Object> yearData = baseMapper.getBusinessTotalView(query);
		yearData.put("YoY", MathUtils.getUpRate(new BigDecimal(yearData.get("telecom_business_total").toString()),
				new BigDecimal(yearData.get("last_telecom_business_total").toString())));
		result.put("yearData", yearData);
		return result;
	}

	@Override
	public List<CarbonRankingVo> getCompanyList() {
		return baseMapper.getDeptList();
	}

	@Override
	public boolean saveBusinessProductionData(BusinessProductionDataBo businessProductionDataBo) {
		BusinessProductionData businessProductionData = new BusinessProductionData();
		BeanUtils.copyProperties(businessProductionDataBo, businessProductionData);
		if (ObjectUtil.isEmpty(businessProductionData.getCompanyId())) {
			if (StrUtil.isNotBlank(businessProductionDataBo.getCompanyName())) {
				Long companyId = baseMapper.getCompanyIdByName(businessProductionDataBo.getCompanyName());
				if (ObjectUtil.isNotEmpty(companyId)) {
					businessProductionData.setCompanyId(companyId);
				} else {
					return false;
				}
			} else {
				return false;
			}
		}
		return save(businessProductionData);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean addList(List<BusinessProductionDataBo> businessProductionDataBos) {
		if (CollectionUtil.isNotEmpty(businessProductionDataBos)) {
			businessProductionDataBos.forEach(businessProductionDataBo -> {
				BusinessProductionData businessProductionData = new BusinessProductionData();
				BeanUtils.copyProperties(businessProductionDataBo, businessProductionData);
				if (ObjectUtil.isEmpty(businessProductionData.getCompanyId())) {
					if (StrUtil.isNotBlank(businessProductionDataBo.getCompanyName())) {
						Long companyId = baseMapper.getCompanyIdByName(businessProductionDataBo.getCompanyName());
						if (ObjectUtil.isNotEmpty(companyId)) {
							businessProductionData.setCompanyId(companyId);
						} else {
							throw new RuntimeException("数据部门信息错误！");
						}
					} else {
						throw new RuntimeException("数据部门不能为空！");
					}
				}
				BusinessProductionData oldData = getOne(Wrappers.<BusinessProductionData>lambdaQuery()
						.eq(BusinessProductionData::getCompanyId, businessProductionData.getCompanyId())
						.eq(BusinessProductionData::getReportTime, businessProductionData.getReportTime()));
				if (ObjectUtil.isNotEmpty(oldData)) {
					//数据存在则更新
					businessProductionData.setId(oldData.getId());
					updateById(businessProductionData);
				} else {
					//数据不存在则插入
					save(businessProductionData);
				}

			});
			return true;
		}
		return false;
	}

	/***
	 * 通知实体列表转换为VO列表
	 * @param businessProductionDataList 通知实体列表
	 * @return 返回文章VO页
	 */
	private List<BusinessProductionDataVo> listEntityToVo(List<BusinessProductionData> businessProductionDataList) {
		List<BusinessProductionDataVo> businessProductionDataVoList = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(businessProductionDataList)) {
			businessProductionDataList.forEach(businessProductionData -> {
				BusinessProductionDataVo businessProductionDataVo = new BusinessProductionDataVo();
				BeanUtils.copyProperties(businessProductionData, businessProductionDataVo);
				if (ObjectUtil.isNotEmpty(businessProductionData.getCompanyId())) {
					String companyName = baseMapper.getCompanyNameById(businessProductionData.getCompanyId());
					if (StrUtil.isNotBlank(companyName)) {
						businessProductionDataVo.setCompanyName(companyName);
					}
				} else {
					businessProductionDataVo.setCompanyName("全省");
				}
				businessProductionDataVoList.add(businessProductionDataVo);
			});
		}
		return businessProductionDataVoList;
	}

	private BusinessProductionData[] getCompareDataArray(Long companyId, Date[] reportTimeArea, String timeType) {
		BusinessProductionData[] businessProductionDataArray = initCompareDataArray(12);
		List<BusinessProductionData> dataList;
		LambdaQueryWrapper<BusinessProductionData> qw = Wrappers.lambdaQuery();
		//设置查询条件，上报时间区域
		if (ArrayUtil.isNotEmpty(reportTimeArea)) {
			qw.ge(BusinessProductionData::getReportTime, DateUtil.beginOfMonth(reportTimeArea[0]))
					.le(BusinessProductionData::getReportTime, DateUtil.endOfMonth(reportTimeArea[1]));
		}
		if (ObjectUtil.isNotEmpty(companyId) && companyId != 0) {
			//设置查询条件，公司
			qw.eq(BusinessProductionData::getCompanyId, companyId);
			qw.orderByAsc(BusinessProductionData::getReportTime);
			dataList = list(qw);
		} else {
			//全省
			dataList = baseMapper.getDataCompareList(qw);
		}
		dataList.forEach(businessProductionData -> {
			int index = getMonthIndex(businessProductionData.getReportTime());
			if (ObjectUtil.isNotEmpty(businessProductionData)) {
				BeanUtils.copyProperties(businessProductionData, businessProductionDataArray[index]);
			}
		});
		if (timeType.equals(DischargeCarbonTimeType.QUARTER.getValue())) {
			return transQuarterDataArray(businessProductionDataArray);
		} else {
			return businessProductionDataArray;
		}
	}

	private Date[] getLastYearDate(Date[] curDate) {
		Date[] result = new Date[2];
		Calendar cal = Calendar.getInstance();
		cal.setTime(curDate[0]);
		cal.add(Calendar.YEAR, -1);
		result[0] = cal.getTime();
		cal.setTime(curDate[1]);
		cal.add(Calendar.YEAR, -1);
		result[1] = cal.getTime();
		return result;
	}

	private BusinessProductionDataCompareVo addLastYearData(BusinessProductionDataCompareVo thisYearDataVo,
															BusinessProductionData lastYearData) {
		if (ObjectUtil.isNotEmpty(lastYearData.getBroadbandBusiness()) &&
				lastYearData.getBroadbandBusiness().compareTo(BigDecimal.valueOf(0)) != 0) {
			thisYearDataVo.setBroadbandBusinessLastYear(lastYearData.getBroadbandBusiness());
			thisYearDataVo.setBroadbandBusinessRiseRate(thisYearDataVo.getBroadbandBusiness().subtract(
					thisYearDataVo.getBroadbandBusinessLastYear()).multiply(BigDecimal.valueOf(100)).divide(
					thisYearDataVo.getBroadbandBusinessLastYear(), 2, BigDecimal.ROUND_HALF_UP));
		} else {
			thisYearDataVo.setBroadbandBusinessLastYear(BigDecimal.valueOf(0));
		}
		if (ObjectUtil.isNotEmpty(lastYearData.getBusinessFlowTotal()) &&
				lastYearData.getBusinessFlowTotal().compareTo(BigDecimal.valueOf(0)) != 0) {
			thisYearDataVo.setBusinessFlowTotalLastYear(lastYearData.getBusinessFlowTotal());
			thisYearDataVo.setBusinessFlowTotalRiseRate(thisYearDataVo.getBusinessFlowTotal().subtract(
					thisYearDataVo.getBusinessFlowTotalLastYear()).multiply(BigDecimal.valueOf(100)).divide(
					thisYearDataVo.getBusinessFlowTotalLastYear(), 2, BigDecimal.ROUND_HALF_UP));
		} else {
			thisYearDataVo.setBusinessFlowTotalLastYear(BigDecimal.valueOf(0));
		}
		if (ObjectUtil.isNotEmpty(lastYearData.getIdcBusiness()) &&
				lastYearData.getIdcBusiness().compareTo(BigDecimal.valueOf(0)) != 0) {
			thisYearDataVo.setIdcBusinessLastYear(lastYearData.getIdcBusiness());
			thisYearDataVo.setIdcBusinessRiseRate(thisYearDataVo.getIdcBusiness().subtract(
					thisYearDataVo.getIdcBusinessLastYear()).multiply(BigDecimal.valueOf(100)).divide(
					thisYearDataVo.getIdcBusinessLastYear(), 2, BigDecimal.ROUND_HALF_UP));
		} else {
			thisYearDataVo.setIdcBusinessLastYear(BigDecimal.valueOf(0));
		}
		if (ObjectUtil.isNotEmpty(lastYearData.getIotBusiness()) &&
				lastYearData.getIotBusiness().compareTo(BigDecimal.valueOf(0)) != 0) {
			thisYearDataVo.setIotBusinessLastYear(lastYearData.getIotBusiness());
			thisYearDataVo.setIotBusinessRiseRate(thisYearDataVo.getIotBusiness().subtract(
					thisYearDataVo.getIotBusinessLastYear()).multiply(BigDecimal.valueOf(100)).divide(
					thisYearDataVo.getIotBusinessLastYear(), 2, BigDecimal.ROUND_HALF_UP));
		} else {
			thisYearDataVo.setIotBusinessLastYear(BigDecimal.valueOf(0));
		}
		if (ObjectUtil.isNotEmpty(lastYearData.getFixedPhoneBusiness()) &&
				lastYearData.getFixedPhoneBusiness().compareTo(BigDecimal.valueOf(0)) != 0) {
			thisYearDataVo.setFixedPhoneBusinessLastYear(lastYearData.getFixedPhoneBusiness());
			thisYearDataVo.setFixedPhoneBusinessRiseRate(thisYearDataVo.getFixedPhoneBusiness().subtract(
					thisYearDataVo.getFixedPhoneBusinessLastYear()).multiply(BigDecimal.valueOf(100)).divide(
					thisYearDataVo.getFixedPhoneBusinessLastYear(), 2, BigDecimal.ROUND_HALF_UP));
		} else {
			thisYearDataVo.setFixedPhoneBusinessLastYear(BigDecimal.valueOf(0));
		}
		if (ObjectUtil.isNotEmpty(lastYearData.getIptvBusiness()) &&
				lastYearData.getIptvBusiness().compareTo(BigDecimal.valueOf(0)) != 0) {
			thisYearDataVo.setIptvBusinessLastYear(lastYearData.getIptvBusiness());
			thisYearDataVo.setIptvBusinessRiseRate(thisYearDataVo.getIptvBusiness().subtract(
					thisYearDataVo.getIptvBusinessLastYear()).multiply(BigDecimal.valueOf(100)).divide(
					thisYearDataVo.getIptvBusinessLastYear(), 2, BigDecimal.ROUND_HALF_UP));
		} else {
			thisYearDataVo.setIptvBusinessLastYear(BigDecimal.valueOf(0));
		}
		if (ObjectUtil.isNotEmpty(lastYearData.getMobileInternetBusiness()) &&
				lastYearData.getMobileInternetBusiness().compareTo(BigDecimal.valueOf(0)) != 0) {
			thisYearDataVo.setMobileInternetBusinessLastYear(lastYearData.getMobileInternetBusiness());
			thisYearDataVo.setMobileInternetBusinessRiseRate(thisYearDataVo.getMobileInternetBusiness().subtract(
					thisYearDataVo.getMobileInternetBusinessLastYear()).multiply(BigDecimal.valueOf(100)).divide(
					thisYearDataVo.getMobileInternetBusinessLastYear(), 2, BigDecimal.ROUND_HALF_UP));
		} else {
			thisYearDataVo.setMobileInternetBusinessLastYear(BigDecimal.valueOf(0));
		}
		if (ObjectUtil.isNotEmpty(lastYearData.getOtherBusiness()) &&
				lastYearData.getOtherBusiness().compareTo(BigDecimal.valueOf(0)) != 0) {
			thisYearDataVo.setOtherBusinessLastYear(lastYearData.getOtherBusiness());
			thisYearDataVo.setOtherBusinessRiseRate(thisYearDataVo.getOtherBusiness().subtract(
					thisYearDataVo.getOtherBusinessLastYear()).multiply(BigDecimal.valueOf(100)).divide(
					thisYearDataVo.getOtherBusinessLastYear(), 2, BigDecimal.ROUND_HALF_UP));
		} else {
			thisYearDataVo.setOtherBusinessLastYear(BigDecimal.valueOf(0));
		}
		if (ObjectUtil.isNotEmpty(lastYearData.getMobileSmsBusiness()) &&
				lastYearData.getMobileSmsBusiness().compareTo(BigDecimal.valueOf(0)) != 0) {
			thisYearDataVo.setMobileSmsBusinessLastYear(lastYearData.getMobileSmsBusiness());
			thisYearDataVo.setMobileSmsBusinessRiseRate(thisYearDataVo.getMobileSmsBusiness().subtract(
					thisYearDataVo.getMobileSmsBusinessLastYear()).multiply(BigDecimal.valueOf(100)).divide(
					thisYearDataVo.getMobileSmsBusinessLastYear(), 2, BigDecimal.ROUND_HALF_UP));
		} else {
			thisYearDataVo.setMobileSmsBusinessLastYear(BigDecimal.valueOf(0));
		}
		if (ObjectUtil.isNotEmpty(lastYearData.getMobilePhonBusiness()) &&
				lastYearData.getMobilePhonBusiness().compareTo(BigDecimal.valueOf(0)) != 0) {
			thisYearDataVo.setMobilePhonBusinessLastYear(lastYearData.getMobilePhonBusiness());
			thisYearDataVo.setMobilePhonBusinessRiseRate(thisYearDataVo.getMobilePhonBusiness().subtract(
					thisYearDataVo.getMobilePhonBusinessLastYear()).multiply(BigDecimal.valueOf(100)).divide(
					thisYearDataVo.getMobilePhonBusinessLastYear(), 2, BigDecimal.ROUND_HALF_UP));
		} else {
			thisYearDataVo.setMobilePhonBusinessLastYear(BigDecimal.valueOf(0));
		}
		if (ObjectUtil.isNotEmpty(lastYearData.getSpecialBroadbandBusiness()) &&
				lastYearData.getSpecialBroadbandBusiness().compareTo(BigDecimal.valueOf(0)) != 0) {
			thisYearDataVo.setSpecialBroadbandBusinessLastYear(lastYearData.getSpecialBroadbandBusiness());
			thisYearDataVo.setSpecialBroadbandBusinessRiseRate(thisYearDataVo.getSpecialBroadbandBusiness().subtract(
					thisYearDataVo.getSpecialBroadbandBusinessLastYear()).multiply(BigDecimal.valueOf(100)).divide(
					thisYearDataVo.getSpecialBroadbandBusinessLastYear(), 2, BigDecimal.ROUND_HALF_UP));
		} else {
			thisYearDataVo.setSpecialBroadbandBusinessLastYear(BigDecimal.valueOf(0));
		}
		if (ObjectUtil.isNotEmpty(lastYearData.getTelecomBusinessTotal()) &&
				lastYearData.getTelecomBusinessTotal().compareTo(BigDecimal.valueOf(0)) != 0) {
			thisYearDataVo.setTelecomBusinessTotalLastYear(lastYearData.getTelecomBusinessTotal());
			thisYearDataVo.setTelecomBusinessTotalRiseRate(thisYearDataVo.getTelecomBusinessTotal().subtract(
					thisYearDataVo.getTelecomBusinessTotalLastYear()).multiply(BigDecimal.valueOf(100)).divide(
					thisYearDataVo.getTelecomBusinessTotalLastYear(), 2, BigDecimal.ROUND_HALF_UP));
		} else {
			thisYearDataVo.setTelecomBusinessTotalLastYear(BigDecimal.valueOf(0));
		}
		return thisYearDataVo;
	}

	private Date[] genDateByYear(int dataYear) {
		String startYear = dataYear + "-01-01 0:00:00";
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
		//sdf将字符串转化成java.util.Date
		Date dateStart;
		Date dateEnd;
		try {
			dateStart = sdf.parse(startYear);
			dateEnd = DateUtil.endOfYear(dateStart);
			Date[] result = new Date[2];
			result[0] = dateStart;
			result[1] = dateEnd;
			return result;
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	private int getMonthIndex(Date curDate) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(curDate);
		return cal.get(Calendar.MONTH);
	}

	private BusinessProductionData[] initCompareDataArray(int count) {
		try {
			BusinessProductionData[] businessDataCompares = new BusinessProductionData[count];
			for (int i = 0; i < businessDataCompares.length; i++) {
				businessDataCompares[i] = new BusinessProductionData();
				Field[] fields = businessDataCompares[i].getClass().getDeclaredFields();
				for (Field field : fields) {
					if (field.getType().toString().contains("BigDecimal")) {
						field.setAccessible(true);
						field.set(businessDataCompares[i], BigDecimal.valueOf(0));
					}
				}
			}
			return businessDataCompares;
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		return null;
	}

	private BusinessProductionData[] transQuarterDataArray(BusinessProductionData[] businessProductionDataArray) {
		BusinessProductionData[] quarterDataArray = initCompareDataArray(4);
		for (int i = 0; i < businessProductionDataArray.length; i++) {
			int iQuarter = i / 3;
			if (ObjectUtil.isNotEmpty(businessProductionDataArray[i].getReportTime())) {
				quarterDataArray[iQuarter].setReportTime(businessProductionDataArray[i].getReportTime());
				quarterDataArray[iQuarter].setBroadbandBusiness(quarterDataArray[iQuarter].getBroadbandBusiness().
						add(businessProductionDataArray[i].getBroadbandBusiness()));
				quarterDataArray[iQuarter].setBusinessFlowTotal(quarterDataArray[iQuarter].getBusinessFlowTotal().
						add(businessProductionDataArray[i].getBusinessFlowTotal()));
				quarterDataArray[iQuarter].setIdcBusiness(quarterDataArray[iQuarter].getIdcBusiness().
						add(businessProductionDataArray[i].getIdcBusiness()));
				quarterDataArray[iQuarter].setIotBusiness(quarterDataArray[iQuarter].getIotBusiness().
						add(businessProductionDataArray[i].getIotBusiness()));
				quarterDataArray[iQuarter].setFixedPhoneBusiness(quarterDataArray[iQuarter].getFixedPhoneBusiness().
						add(businessProductionDataArray[i].getFixedPhoneBusiness()));
				quarterDataArray[iQuarter].setIptvBusiness(quarterDataArray[iQuarter].getIptvBusiness().
						add(businessProductionDataArray[i].getIptvBusiness()));
				quarterDataArray[iQuarter].setMobileInternetBusiness(quarterDataArray[iQuarter].
						getMobileInternetBusiness().add(businessProductionDataArray[i].getMobileInternetBusiness()));
				quarterDataArray[iQuarter].setOtherBusiness(quarterDataArray[iQuarter].getOtherBusiness().
						add(businessProductionDataArray[i].getOtherBusiness()));
				quarterDataArray[iQuarter].setMobileSmsBusiness(quarterDataArray[iQuarter].getMobileSmsBusiness().
						add(businessProductionDataArray[i].getMobileSmsBusiness()));
				quarterDataArray[iQuarter].setMobilePhonBusiness(quarterDataArray[iQuarter].getMobilePhonBusiness().
						add(businessProductionDataArray[i].getMobilePhonBusiness()));
				quarterDataArray[iQuarter].setSpecialBroadbandBusiness(quarterDataArray[iQuarter].
						getSpecialBroadbandBusiness().add(businessProductionDataArray[i].getSpecialBroadbandBusiness()));
				quarterDataArray[iQuarter].setTelecomBusinessTotal(quarterDataArray[iQuarter].
						getTelecomBusinessTotal().add(businessProductionDataArray[i].getTelecomBusinessTotal()));
			}
		}
		return quarterDataArray;
	}

	private List<CarbonRankingVo> mergeCarbonRanking(List<CarbonRankingVo> list) {
		List<CarbonRankingVo> result = list.stream()
				// 表示id为key， 接着如果有重复的，那么从BillsNums对象o1与o2中筛选出一个，这里选择o1，
				// 并把id重复，需要将nums和sums与o1进行合并的o2, 赋值给o1，最后返回o1
				.collect(Collectors.toMap(CarbonRankingVo::getDeptId, a -> a, (o1, o2) -> {
					o1.setTelecomBusinessTotal(o1.getTelecomBusinessTotal().add(o2.getTelecomBusinessTotal()));
					o1.setTelecomBusinessTotalLastYear(o1.getTelecomBusinessTotalLastYear().add(o2.getTelecomBusinessTotalLastYear()));
					o1.setBusinessFlowTotal(o1.getBusinessFlowTotal().add(o2.getBusinessFlowTotal()));
					o1.setCarbonEmissions(o1.getCarbonEmissions().add(o2.getCarbonEmissions()));
					o1.setCarbonEmissionsLastYear(o1.getCarbonEmissionsLastYear().add(o2.getCarbonEmissionsLastYear()));
					o1.setEnergyConsumption(o1.getEnergyConsumption().add(o2.getEnergyConsumption()));
					return o1;
				})).values().stream().collect(Collectors.toList());
		return result;
	}

	private int[] getDataRange(int dataYear, String timeType, Date[] reportTimeArea) {
		int[] result = new int[2];
		if (timeType.equals(DischargeCarbonTimeType.CUSTOM.getValue())) {
			result[0] = DateUtil.month(reportTimeArea[0]);
			result[1] = DateUtil.month(reportTimeArea[1]) + 1;
		} else {
			result[0] = 0;
			Calendar calendar = Calendar.getInstance();
			int currentYear = calendar.get(Calendar.YEAR);
			if (dataYear == currentYear) {
				//当前年份
				int currentMonth = calendar.get(Calendar.MONTH) + 1;
				if (timeType.equals(DischargeCarbonTimeType.MONTH.getValue())) {
					result[1] = currentMonth - 1;
				} else {
					result[1] = currentMonth % 3 == 0 ? currentMonth / 3 : currentMonth / 3 + 1;;
				}
			} else {
				//往年
				if (timeType.equals(DischargeCarbonTimeType.MONTH.getValue())) {
					result[1] = 12;
				} else {
					result[1] = 4;
				}
			}
		}
		return result;
	}

	private List<CarbonRankingVo> getCarbonDataList(String year, String currentMonth) {
		String lastYear = String.valueOf(Integer.parseInt(year) - 1);
		//获取部门列表
		List<CarbonRankingVo> deptList = baseMapper.getDeptList();
		//获取营业数据
		List<CarbonRankingVo> businessDataList = baseMapper.getBusinessDataList(year, lastYear, currentMonth);
		//获取电排放数据
		List<CarbonRankingVo> electricDataList = baseMapper.getElectricDataList(year, lastYear, currentMonth);
		//获取气排放数据
		List<CarbonRankingVo> gasDataList = baseMapper.getGasDataList(year, lastYear, currentMonth);
		//获取油排放数据
		List<CarbonRankingVo> oilDataList = baseMapper.getOilDataList(year, lastYear, currentMonth);
		//获取煤碳排放数据
		List<CarbonRankingVo> coalDataList = baseMapper.getCoalDataList(year, lastYear, currentMonth);
		//获取热能排放数据
		List<CarbonRankingVo> thermalDataList = baseMapper.getThermalDataList(year, lastYear, currentMonth);
		//获取水排放数据
		List<CarbonRankingVo> waterDataList = baseMapper.getWaterDataList(year, lastYear, currentMonth);

		deptList.addAll(businessDataList);
		deptList.addAll(electricDataList);
		deptList.addAll(gasDataList);
		deptList.addAll(oilDataList);
		deptList.addAll(coalDataList);
		deptList.addAll(thermalDataList);
		deptList.addAll(waterDataList);
		List<CarbonRankingVo> carbonRankingVoList = mergeCarbonRanking(deptList);
		return carbonRankingVoList;
	}
}