package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.discharge.api.bo.DischargeDataEnergyNotificationBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergyNotification;
import com.enrising.ctsc.discharge.api.enums.DelFlagEnum;
import com.enrising.ctsc.discharge.api.enums.DischargeDataEnergyNotificationStatus;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyNotificationVo;
import com.enrising.ctsc.discharge.mapper.DischargeDataEnergyNotificationMapper;
import com.enrising.ctsc.discharge.service.DischargeDataEnergyNotificationService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 碳排放数据填报（能源）提醒
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-12
 */
@Service
@AllArgsConstructor
public class DischargeDataEnergyNotificationServiceImpl extends ServiceImpl<DischargeDataEnergyNotificationMapper,
        DischargeDataEnergyNotification> implements DischargeDataEnergyNotificationService {
	@Override
	public List<DischargeDataEnergyNotificationVo> getRemindCount(DischargeDataEnergyNotificationBo dischargeDataEnergyNotificationBo) {
		return baseMapper.getAllRemindCountList(dischargeDataEnergyNotificationBo.getReportTime());
	}

	@Override
	public DischargeDataEnergyNotification getCompanyRemind(DischargeDataEnergyNotificationBo dischargeDataEnergyNotificationBo) {
		List<DischargeDataEnergyNotification> dischargeDataEnergyNotificationList = baseMapper.selectList(Wrappers.<DischargeDataEnergyNotification>lambdaQuery().
				eq(DischargeDataEnergyNotification::getDelFlag, DelFlagEnum.NOT_DELETED.getValue()).
				eq(ObjectUtil.isNotEmpty(dischargeDataEnergyNotificationBo.getCompanyId()),
						DischargeDataEnergyNotification::getCompanyId, dischargeDataEnergyNotificationBo.getCompanyId()).
				eq(ObjectUtil.isNotEmpty(dischargeDataEnergyNotificationBo.getReportTime()),
						DischargeDataEnergyNotification::getReportTime, dischargeDataEnergyNotificationBo.getReportTime()).
				eq(StrUtil.isNotBlank(dischargeDataEnergyNotificationBo.getStatus()),
						DischargeDataEnergyNotification::getStatus, dischargeDataEnergyNotificationBo.getStatus()).
				orderByAsc(DischargeDataEnergyNotification::getRemindTime));
		if (CollectionUtil.isNotEmpty(dischargeDataEnergyNotificationList)) {
			return dischargeDataEnergyNotificationList.get(0);
		}
		return null;
	}

	@Override
	public boolean clearCompanyRemind(DischargeDataEnergyNotificationBo dischargeDataEnergyNotificationBo) {
		return this.update(new LambdaUpdateWrapper<DischargeDataEnergyNotification>()
				.set(DischargeDataEnergyNotification::getStatus, DischargeDataEnergyNotificationStatus.READ.getValue())
				.eq(ObjectUtil.isNotEmpty(dischargeDataEnergyNotificationBo.getCompanyId()),
						DischargeDataEnergyNotification::getCompanyId, dischargeDataEnergyNotificationBo.getCompanyId())
				.eq(ObjectUtil.isNotEmpty(dischargeDataEnergyNotificationBo.getReportTime()),
						DischargeDataEnergyNotification::getReportTime, dischargeDataEnergyNotificationBo.getReportTime())
				.eq(DischargeDataEnergyNotification::getStatus, DischargeDataEnergyNotificationStatus.UNREAD.getValue()));
	}
}
