package com.enrising.ctsc.carbon.common.service;


import com.enrising.ctsc.carbon.common.entity.CarbonAttachment;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/27
 */
public interface UploadService {

	/**
	 * 批量上传文件
	 * */
	List<CarbonAttachment> uploadFiles(MultipartFile[] files);

	/**
	 * 单个上传文件
	 * */
	CarbonAttachment uploadFile(MultipartFile file);

	/**
	 * 上传插图
	 * */
	CarbonAttachment uploadIllustration(MultipartFile file);

	/**
	 * 文件下载
	 *
	 * <AUTHOR>
	 * @date 2022/10/1 16:17
	 */
	void downloadFile(HttpServletResponse response, CarbonAttachment attachment);

	String getPreviewUrl(String fileName);
}
