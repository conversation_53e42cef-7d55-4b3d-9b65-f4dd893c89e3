package com.enrising.ctsc.discharge.api.bo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.enrising.ctsc.discharge.api.entity.DischargeDataHeatOil;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 热力燃油数据导入EXCEL
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2024-12-23
 */
@Data
public class DischargeDataHeatOilExcel extends DischargeDataHeatOil implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 年月
	 */
	@Excel(name = "时间周期")
	private String yearMonth;

	/**
	 * 分公司名称
	 */
	@Excel(name = "数据部门")
	private String companyName;

	/**
	 * 股份汽油
	 */
	@Excel(name = "股份（元）", groupName = "汽油")
	private BigDecimal gasolineStock;

	/**
	 * 集团汽油
	 */
	@Excel(name = "固网（元）")
	private BigDecimal gasolineGroup;

	/**
	 * 股份柴油
	 */
	@Excel(name = "股份（元）", groupName = "柴油")
	private BigDecimal dieselStock;

	/**
	 * 集团柴油
	 */
	@Excel(name = "固网（元）")
	private BigDecimal dieselGroup;

	/**
	 * 股份热力
	 */
	@Excel(name = "股份（元）", groupName = "热力")
	private BigDecimal heatStock;

	/**
	 * 集团热力
	 */
	@Excel(name = "固网（元）")
	private BigDecimal heatGroup;

	/**
	 * 股份水
	 */
	@Excel(name = "股份（元）", groupName = "水")
	private BigDecimal waterStock;

	/**
	 * 集团水
	 */
	@Excel(name = "固网（元）")
	private BigDecimal waterGroup;
}
