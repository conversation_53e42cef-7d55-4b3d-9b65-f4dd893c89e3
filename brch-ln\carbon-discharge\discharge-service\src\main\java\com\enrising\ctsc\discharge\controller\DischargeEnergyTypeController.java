package com.enrising.ctsc.discharge.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.bo.DischargeEnergyTypeSave;
import com.enrising.ctsc.discharge.api.bo.DischargeEnergyTypeStatus;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyType;
import com.enrising.ctsc.discharge.api.enums.EnableStatus;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyTypeQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyTypeVo;
import com.enrising.ctsc.discharge.service.DischargeEnergyTypeService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 碳排放能源类型表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@RestController
@RequestMapping("/discharge/energyType")
@AllArgsConstructor
public class DischargeEnergyTypeController {
	private final DischargeEnergyTypeService dischargeEnergyTypeService;

	@GetMapping("/list")
	public TableDataInfo<DischargeEnergyTypeVo> page(Page<DischargeEnergyTypeVo> page, DischargeEnergyTypeQuery query) {
		return dischargeEnergyTypeService.findList(page, query);
	}

	@GetMapping("/typeList")
	public R<List<DischargeEnergyType>> typeList(String type) {
		List<DischargeEnergyType> list = dischargeEnergyTypeService.list(Wrappers.<DischargeEnergyType>lambdaQuery()
				.eq(StrUtil.isNotBlank(type), DischargeEnergyType::getEnergyType, type)
		);
		return R.success(list, "类型列表");
	}

	@GetMapping("/detail")
	public R<DischargeEnergyTypeVo> get(DischargeEnergyTypeQuery query) {
		DischargeEnergyTypeVo detail = dischargeEnergyTypeService.detail(query);
		return R.success(detail, "查询成功");
	}

		@PostMapping(value = "/save")
	public R<String> save(@RequestBody @Valid DischargeEnergyTypeSave typeSave) {
		dischargeEnergyTypeService.add(typeSave);
		return R.success("保存成功");
	}

		@PostMapping(value = "/update")
	public R<String> update(@RequestBody @Valid DischargeEnergyTypeSave typeSave) {
		dischargeEnergyTypeService.edit(typeSave);
		return R.success("修改成功");
	}

		@PostMapping(value = "/setStatus")
	public R<String> setStatus(@RequestBody @Valid DischargeEnergyTypeStatus status) {
		DischargeEnergyType type = new DischargeEnergyType();
		type.setId(status.getId());
		type.setStatus(status.getStatus());
		dischargeEnergyTypeService.updateById(type);
		return R.success(StrUtil.format("{}成功", EnableStatus.getName(status.getStatus())));
	}


		@PostMapping(value = "/delete/{id}")
	public R<String> delete(@PathVariable Long id) {
		dischargeEnergyTypeService.del(id);
		return R.success("删除成功");
	}

	@GetMapping("/getEnergyTypeList")
	public R<List<DischargeEnergyType>> getEnergyTypeList(String energyType) {
		return R.success(dischargeEnergyTypeService.getEnergyTypeList(energyType));
	}
}
