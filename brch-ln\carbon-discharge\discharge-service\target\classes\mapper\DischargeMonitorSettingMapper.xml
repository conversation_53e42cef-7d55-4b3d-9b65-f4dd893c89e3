<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeMonitorSettingMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.company_id,
            t.year,
            t.quota,
            t.quota_selection,
			t.selection_value,
            t.del_flag
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeMonitorSettingVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_monitor_setting t
        ${ew.customSqlSegment}
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.discharge.api.vo.DischargeMonitorSettingVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_monitor_setting t
        ${ew.customSqlSegment}
        limit 1
    </select>
	<select id="getSettingList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeMonitorSettingVo">
		SELECT
		    A.org_name AS companyName,
			A.ID AS companyId,
			b.ID,
			b.quota,
			b.quota_selection,
			C.quota AS preQuota ,
			( COALESCE
			( d.electric_total, 0 ) + COALESCE
			( e.lpg_total, 0 )  + COALESCE
			( e.ng_total, 0 )
			+ COALESCE
			( f.crude_total, 0 ) + COALESCE
			( f.diesel_total, 0 )  + COALESCE
			( f.fuel_total, 0 ) +  COALESCE
			( f.gasoline_total, 0 )
			+ COALESCE
			( g.water_total, 0 )  + COALESCE
			( h.thermal_total, 0 )) / 1000  + COALESCE
			( m.coal_total, 0 ) as preCarbonTotal
		FROM rmp.sys_organizations A
		    LEFT JOIN ( SELECT ID, quota, quota_selection, company_id, `year` FROM discharge_monitor_setting WHERE `year` = #{bo.year} ) b ON A.id = b.company_id
		LEFT JOIN ( SELECT quota, company_id, `year` FROM discharge_monitor_setting WHERE `year` = #{bo.preYear} ) C ON A.id = C.company_id
		LEFT JOIN (
			SELECT COALESCE
			( ( SUM( outsourcing_thermal_power ) - SUM( own_green_power ) ) * (
			SELECT
				factor
			FROM discharge_energy_factor
			WHERE
				energy_type_id = '3'
				and del_flag = '0'
				and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.preYear}
			ORDER BY validity_start DESC
			    LIMIT 1
			) , 0 )  AS electric_total,
			company_id
			FROM
			discharge_data_electric
			WHERE
			DATE_FORMAT( report_time, '%Y' )  = #{bo.preYear}
		    AND del_flag = '0'
			GROUP BY
			company_id
			) d ON A.id = d.company_id
		LEFT JOIN (
			SELECT COALESCE
			( SUM( ng ) * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '5'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.preYear}  ORDER BY validity_start DESC LIMIT 1
			) , 0 ) AS ng_total,
			COALESCE( SUM( lpg ) * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '6'
			and del_flag = '0'
			ORDER BY
			create_time desc
			LIMIT 1
			), 0 )  AS lpg_total,
			company_id
			FROM
			discharge_data_gas
			WHERE
			DATE_FORMAT( report_time, '%Y' )  = #{bo.preYear}
		    AND del_flag = '0'
			GROUP BY
			company_id
			) e ON A.id = e.company_id
		LEFT JOIN (
			SELECT COALESCE
			( SUM( gasoline ) * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '7'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.preYear}  ORDER BY validity_start DESC LIMIT 1
			) , 0 )  AS gasoline_total,
			COALESCE ( SUM( diesel ) * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '8'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.preYear}  ORDER BY validity_start DESC LIMIT 1
			), 0 )  AS diesel_total,
			COALESCE( SUM( kerosene ) * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '9'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.preYear}  ORDER BY validity_start DESC LIMIT 1
			) , 0 ) AS crude_total,
			COALESCE ( SUM( fuel ) * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '11'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.preYear}  ORDER BY validity_start DESC LIMIT 1
			), 0 )  AS fuel_total,
			company_id
			FROM
			discharge_data_oil
			WHERE
			DATE_FORMAT( report_time, '%Y' )  = #{bo.preYear}
		    AND del_flag = '0'
			GROUP BY
			company_id
			) f ON A.id = f.company_id
			LEFT JOIN (
			SELECT COALESCE
			( SUM( water ) * (
			SELECT COALESCE
			( factor, 0 )
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '1'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.preYear}  ORDER BY validity_start DESC LIMIT 1
			), 0 )  AS water_total,
			company_id
			FROM
			discharge_data_water
			WHERE
			DATE_FORMAT( report_time, '%Y' )  = #{bo.preYear}
		    AND del_flag = '0'
			GROUP BY
			company_id
			) G ON A.id = G.company_id
			LEFT JOIN (
			SELECT COALESCE
			( SUM( thermal ) * (
			SELECT COALESCE
			( factor, 0 )
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '12'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.preYear}  ORDER BY validity_start DESC LIMIT 1
			), 0 )  AS thermal_total,
			company_id
			FROM
			discharge_data_thermal
			WHERE
			DATE_FORMAT( report_time, '%Y' )  = #{bo.preYear}
		    AND del_flag = '0'
			GROUP BY
			company_id
			) h on a.id = h.company_id
		LEFT JOIN (
			SELECT COALESCE
			( SUM( coal ) * (
			SELECT COALESCE
			( factor, 0 )
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '13'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.preYear}  ORDER BY validity_start DESC LIMIT 1
			), 0 )  AS coal_total,
			company_id
			FROM
			discharge_data_coal
			WHERE
			DATE_FORMAT( report_time, '%Y' )  = #{bo.preYear}
		    AND del_flag = '0'
			GROUP BY
			company_id
			) m on a.id = m.company_id
		WHERE
		A.parent_company_no = '2600000000'
		and a.del_flag = '0'
		<if test="bo.keyword!=null and bo.keyword!=''">
			and A.org_name like concat('%',#{bo.keyword},'%')
		</if>
	</select>
	<select id="getRecordsList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeMonitorSettingVo">
		SELECT
			A.org_name AS companyName,
			A.ID AS companyId,
			b.ID,
			b.quota,
			b.quota_selection,
			COALESCE
			( d.electric_total, 0 ) + COALESCE
			( e.lpg_total, 0 )  + COALESCE
			( e.ng_total, 0 )
			+ COALESCE
			( f.crude_total, 0 ) + COALESCE
			( f.diesel_total, 0 )  + COALESCE
			( f.fuel_total, 0 ) +  COALESCE
			( f.gasoline_total, 0 )+  COALESCE
		    ( f.kerosene_total, 0 )
			+ COALESCE
			( g.water_total, 0 )  + COALESCE
			( h.thermal_total, 0 )   + COALESCE
			( m.coal_total, 0 ) * 1000 as carbonTotal
		FROM rmp.sys_organizations A
		    LEFT JOIN ( SELECT ID, quota, quota_selection, company_id, `year` FROM discharge_monitor_setting WHERE `year` = #{bo.year} ) b ON A.id = b.company_id
		LEFT JOIN (
			SELECT COALESCE
			( ( SUM( outsourcing_thermal_power ) - SUM( own_green_power ) ) * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '3'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
			) , 0 )  AS electric_total,
			company_id
			FROM
			discharge_data_electric
			WHERE
			DATE_FORMAT( report_time, '%Y' )  = #{bo.year}
		    and del_flag = '0'
			GROUP BY
			company_id
			) d ON A.id = d.company_id
		LEFT JOIN (
			SELECT COALESCE
			( SUM( ng ) * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '5'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
			) , 0 ) AS ng_total,
			COALESCE ( SUM( lpg ) * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '6'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
			), 0 )  AS lpg_total,
			company_id
			FROM
			discharge_data_gas
			WHERE
			DATE_FORMAT( report_time, '%Y' ) = #{bo.year}
		    and del_flag = '0'
			GROUP BY
			company_id
			) e ON A.id = e.company_id
		LEFT JOIN (
			SELECT COALESCE
			( SUM( gasoline ) * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '7'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
			) , 0 )  AS gasoline_total,
			COALESCE( SUM( diesel ) * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '8'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
			), 0 )  AS diesel_total,
			COALESCE( SUM( kerosene ) * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '9'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
			) , 0 ) AS kerosene_total,
			COALESCE( SUM( crude ) * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '10'
			and del_flag = '0'
			and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
			) , 0 ) AS crude_total,
			COALESCE( SUM( fuel ) * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '11'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
			), 0 )  AS fuel_total,
			company_id
			FROM
			discharge_data_oil
			WHERE
			DATE_FORMAT( report_time, '%Y' ) = #{bo.year}
		    and del_flag = '0'
			GROUP BY
			company_id
			) f ON A.id = f.company_id
		LEFT JOIN (
			SELECT COALESCE
			( SUM( water ) * (
			SELECT COALESCE
			( factor, 0 )
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '1'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
			), 0 )  AS water_total,
			company_id
			FROM
			discharge_data_water
			WHERE
			DATE_FORMAT( report_time, '%Y' ) = #{bo.year}
		    and del_flag = '0'
			GROUP BY
			company_id
			) G ON A.id = G.company_id
			LEFT JOIN (
			SELECT COALESCE
			( SUM( thermal ) * (
			SELECT COALESCE
			( factor, 0 )
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '12'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
			), 0 )  AS thermal_total,
			company_id
			FROM
			discharge_data_thermal
			WHERE
			DATE_FORMAT( report_time, '%Y' ) = #{bo.year}
		    and del_flag = '0'
			GROUP BY
			company_id
			) h on a.id = h.company_id
			LEFT JOIN (
			SELECT COALESCE
			( SUM( coal ) * (
			SELECT COALESCE
			( factor, 0 )
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '13'
			and del_flag = '0'
		    and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
			), 0 )  AS coal_total,
			company_id
			FROM
			discharge_data_coal
			WHERE
			DATE_FORMAT( report_time, '%Y' ) = #{bo.year}
		    and del_flag = '0'
			GROUP BY
			company_id
			) m on a.id = m.company_id
		WHERE
		A.parent_company_no = '2600000000'
		and a.del_flag = '0'
		<if test="bo.companyId!=null">
			and A.id = #{bo.companyId}
		</if>
		<if test="bo.keyword!=null and bo.keyword!=''">
			and A.org_name like concat('%',#{bo.keyword},'%')
		</if>
	</select>
	<select id="getRecordInfo" resultType="com.enrising.ctsc.discharge.api.vo.DischargeMonitorSettingVo">
		SELECT
			a.org_name as companyName,
			b.quota,
			b.quota_selection,
			b.`year`
		FROM
			rmp.sys_organizations a
		LEFT JOIN discharge_monitor_setting b on a.id = b.company_id
		WHERE a.id = #{query.companyId}
		and b.id = #{query.id}
	</select>
	<select id="getCarbonTotal" resultType="com.enrising.ctsc.discharge.api.vo.DischargeMonitorSettingVo">
		SELECT
			( COALESCE
			( d.electric_total, 0 ) + COALESCE
			( e.lpg_total, 0 )  + COALESCE
			( e.ng_total, 0 )
			+ COALESCE
			( f.crude_total, 0 ) + COALESCE
			( f.diesel_total, 0 )  + COALESCE
			( f.fuel_total, 0 ) +  COALESCE
			( f.gasoline_total, 0 )
			+ COALESCE
			( g.water_total, 0 )  + COALESCE
			( h.thermal_total, 0 )) / 1000  + COALESCE
			( m.coal_total, 0 ) as carbonTotal
		FROM rmp.sys_organizations A
		LEFT JOIN (
		SELECT COALESCE
		( ( SUM( outsourcing_thermal_power ) - SUM( own_green_power ) ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '3'
		and del_flag = '0'
		and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{query.year}  ORDER BY validity_start DESC LIMIT 1
		) , 0 )  AS electric_total,
		company_id
		FROM
		discharge_data_electric
		WHERE
		DATE_FORMAT( report_time, '%Y' )  = #{query.year}
		AND del_flag = '0'
		and company_id = #{query.companyId}
		<if test="query.searchTime!=null and query.searchTime!=''">
			and (report_time BETWEEN #{query.beginTime}
			and #{query.endTime})
		</if>
		GROUP BY company_id
		) d ON A.id = d.company_id
		LEFT JOIN (
		SELECT COALESCE
		( SUM( ng ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '5'
		and del_flag = '0'
		and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{query.year}  ORDER BY validity_start DESC LIMIT 1
		) , 0 ) AS ng_total,
		COALESCE ( SUM( lpg ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '6'
		and del_flag = '0'
		and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{query.year}  ORDER BY validity_start DESC LIMIT 1
		), 0 )  AS lpg_total,
		company_id
		FROM
		discharge_data_gas
		WHERE
		DATE_FORMAT( report_time, '%Y' ) = #{query.year}
		AND del_flag = '0'
		and company_id = #{query.companyId}
		<if test="query.searchTime!=null and query.searchTime!=''">
			and (report_time BETWEEN #{query.beginTime}
			and #{query.endTime})
		</if>
		GROUP BY company_id
		) e ON A.id = e.company_id
		LEFT JOIN (
		SELECT COALESCE
		( SUM( gasoline ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '7'
		and del_flag = '0'
		and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{query.year}  ORDER BY validity_start DESC LIMIT 1
		) , 0 )  AS gasoline_total,
		COALESCE ( SUM( diesel ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '8'
		and del_flag = '0'
		and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{query.year}  ORDER BY validity_start DESC LIMIT 1
		), 0 )  AS diesel_total,
		COALESCE ( SUM( kerosene ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '9'
		and del_flag = '0'
		and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{query.year}  ORDER BY validity_start DESC LIMIT 1
		) , 0 ) AS crude_total,
		COALESCE ( SUM( fuel ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '11'
		and del_flag = '0'
		and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{query.year}  ORDER BY validity_start DESC LIMIT 1
		), 0 )  AS fuel_total,
		company_id
		FROM
		discharge_data_oil
		WHERE
		DATE_FORMAT( report_time, '%Y' ) = #{query.year}
		AND del_flag = '0'
		and company_id = #{query.companyId}
		<if test="query.searchTime!=null and query.searchTime!=''">
			and (report_time BETWEEN #{query.beginTime}
			and #{query.endTime})
		</if>
		GROUP BY company_id
		) f ON A.id = f.company_id
		LEFT JOIN (
		SELECT COALESCE
		( SUM( water ) * (
		SELECT COALESCE
		( factor, 0 )
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '1'
		and del_flag = '0'
		and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{query.year}  ORDER BY validity_start DESC LIMIT 1
		), 0 )  AS water_total,
		company_id
		FROM
		discharge_data_water
		WHERE
		DATE_FORMAT( report_time, '%Y' )  = #{query.year}
		AND del_flag = '0'
		and company_id = #{query.companyId}
		<if test="query.searchTime!=null and query.searchTime!=''">
			and (report_time BETWEEN #{query.beginTime}
			and #{query.endTime})
		</if>
		GROUP BY company_id
		) G ON A.id = G.company_id
		LEFT JOIN (
		SELECT COALESCE
		( SUM( thermal ) * (
		SELECT COALESCE
		( factor, 0 )
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '12'
		and del_flag = '0'
		and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{query.year}  ORDER BY validity_start DESC LIMIT 1
		), 0 )  AS thermal_total,
		company_id
		FROM
		discharge_data_thermal
		WHERE
		DATE_FORMAT( report_time, '%Y' ) = #{query.year}
		AND del_flag = '0'
		and company_id = #{query.companyId}
		<if test="query.searchTime!=null and query.searchTime!=''">
			and (report_time BETWEEN #{query.beginTime}
			and #{query.endTime})
		</if>
		GROUP BY company_id
		) h on a.id = h.company_id
		LEFT JOIN (
		SELECT COALESCE
		( SUM( coal ) * (
		SELECT COALESCE
		( factor, 0 )
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '13'
		and del_flag = '0'
		and DATE_FORMAT( validity_start, '%Y' ) &lt;= #{query.year}  ORDER BY validity_start DESC LIMIT 1
		), 0 )  AS coal_total,
		company_id
		FROM
		discharge_data_coal
		WHERE
		DATE_FORMAT( report_time, '%Y' ) = #{query.year}
		AND del_flag = '0'
		and company_id = #{query.companyId}
		<if test="query.searchTime!=null and query.searchTime!=''">
			and (report_time BETWEEN #{query.beginTime}
			and #{query.endTime})
		</if>
		GROUP BY company_id
		) m on a.id = m.company_id
		WHERE
		a.id = #{query.companyId}
		and a.del_flag = '0'
	</select>
	<select id="getFossilCarbonTotal"
			resultType="com.enrising.ctsc.discharge.api.vo.DischargeMonitorSettingVo">
		SELECT
		( COALESCE
		( e.lpg_total, 0 )  + COALESCE
		( e.ng_total, 0 )
		+ COALESCE
		( f.crude_total, 0 ) + COALESCE
		( f.diesel_total, 0 )  + COALESCE
		( f.fuel_total, 0 ) +  COALESCE
		( f.gasoline_total, 0 )
		) as carbonTotal
		FROM rmp.sys_organizations A
		LEFT JOIN (
		SELECT COALESCE
		( SUM( ng ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '5'
		and DATE_FORMAT(validity_start, '%Y') &lt;= #{query.year}  ORDER BY validity_start DESC LIMIT 1
		) , 0 ) AS ng_total,
		COALESCE ( SUM( lpg ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '6'
		and DATE_FORMAT(validity_start, '%Y') &lt;= #{query.year}  ORDER BY validity_start DESC LIMIT 1
		), 0 )  AS lpg_total,
		company_id
		FROM
		discharge_data_gas
		WHERE
		DATE_FORMAT(report_time, '%Y') = #{query.year}
		AND del_flag = '0'
		and company_id = #{query.companyId}
		<if test="query.searchTime!=null and query.searchTime!=''">
			and (report_time BETWEEN #{query.beginTime}
			and #{query.endTime})
		</if>
		GROUP BY company_id
		) e ON A.id = e.company_id
		LEFT JOIN (
		SELECT COALESCE
		( SUM( gasoline ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '7'
		and DATE_FORMAT(validity_start, '%Y') &lt;= #{query.year}  ORDER BY validity_start DESC LIMIT 1
		) , 0 )  AS gasoline_total,
		COALESCE ( SUM( diesel ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '8'
		and DATE_FORMAT(validity_start, '%Y') &lt;= #{query.year}  ORDER BY validity_start DESC LIMIT 1
		), 0 )  AS diesel_total,
		COALESCE ( SUM( kerosene ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '9'
		and DATE_FORMAT(validity_start, '%Y') &lt;= #{query.year}  ORDER BY validity_start DESC LIMIT 1
		) , 0 ) AS crude_total,
		COALESCE ( SUM( fuel ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '11'
		and DATE_FORMAT(validity_start, '%Y') &lt;= #{query.year}  ORDER BY validity_start DESC LIMIT 1
		), 0 )  AS fuel_total,
		company_id
		FROM
		discharge_data_oil
		WHERE
		DATE_FORMAT(report_time, '%Y') = #{query.year}
		AND del_flag = '0'
		and company_id = #{query.companyId}
		<if test="query.searchTime!=null and query.searchTime!=''">
			and (report_time BETWEEN #{query.beginTime}
			and #{query.endTime})
		</if>
		GROUP BY company_id
		) f ON A.id = f.company_id
		WHERE
		a.id = #{query.companyId}
		and a.del_flag = '0'
	</select>
	<select id="getYearTotalCarbon" resultType="com.enrising.ctsc.discharge.api.vo.DischargeMonitorSettingVo">
		SELECT
		( COALESCE
		( d.electric_total, 0 ) + COALESCE
		( e.lpg_total, 0 )  + COALESCE
		( e.ng_total, 0 )
		+ COALESCE
		( f.crude_total, 0 ) + COALESCE
		( f.diesel_total, 0 )  + COALESCE
		( f.fuel_total, 0 ) +  COALESCE
		( f.gasoline_total, 0 )
		+ COALESCE
		( g.water_total, 0 )  + COALESCE
		( h.thermal_total, 0 )) / 1000  + COALESCE
		( m.coal_total, 0 ) as carbonTotal,
		a.id as companyId
		FROM rmp.sys_organizations A
		LEFT JOIN (
		SELECT COALESCE
		( ( SUM( outsourcing_thermal_power ) - SUM( own_green_power ) ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '3'
		and del_flag = '0'
		and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		) , 0 )  AS electric_total,
		company_id
		FROM
		discharge_data_electric
		WHERE
		DATE_FORMAT(report_time, '%Y') = #{bo.year}
		AND del_flag = '0'
		and company_id in
		<foreach collection="bo.companyIds" open="(" separator="," item="companyId" close=")">
			#{companyId}
		</foreach>
		GROUP BY company_id
		) d ON A.id = d.company_id
		LEFT JOIN (
		SELECT COALESCE
		( SUM( ng ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '5'
		and del_flag = '0'
		and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		) , 0 ) AS ng_total,
		COALESCE ( SUM( lpg ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '6'
		and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		), 0 )  AS lpg_total,
		company_id
		FROM
		discharge_data_gas
		WHERE
		DATE_FORMAT(report_time, '%Y') = #{bo.year}
		AND del_flag = '0'
		and company_id in
		<foreach collection="bo.companyIds" open="(" separator="," item="companyId" close=")">
			#{companyId}
		</foreach>
		GROUP BY company_id
		) e ON A.id = e.company_id
		LEFT JOIN (
		SELECT COALESCE
		( SUM( gasoline ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '7'
		and del_flag = '0'
		and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		) , 0 )  AS gasoline_total,
		COALESCE ( SUM( diesel ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '8'
		and del_flag = '0'
		and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		), 0 )  AS diesel_total,
		COALESCE ( SUM( kerosene ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '9'
		and del_flag = '0'
		and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		) , 0 ) AS crude_total,
		COALESCE ( SUM( fuel ) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '11'
		and del_flag = '0'
		and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		), 0 )  AS fuel_total,
		company_id
		FROM
		discharge_data_oil
		WHERE
		DATE_FORMAT(report_time, '%Y') = #{bo.year}
		AND del_flag = '0'
		and company_id in
		<foreach collection="bo.companyIds" open="(" separator="," item="companyId" close=")">
			#{companyId}
		</foreach>
		GROUP BY company_id
		) f ON A.id = f.company_id
		LEFT JOIN (
		SELECT COALESCE
		( SUM( water ) * (
		SELECT COALESCE
		( factor, 0 )
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '1'
		and del_flag = '0'
		and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		), 0 )  AS water_total,
		company_id
		FROM
		discharge_data_water
		WHERE
		DATE_FORMAT(report_time, '%Y') = #{bo.year}
		AND del_flag = '0'
		and company_id in
		<foreach collection="bo.companyIds" open="(" separator="," item="companyId" close=")">
			#{companyId}
		</foreach>
		GROUP BY company_id
		) G ON A.id = G.company_id
		LEFT JOIN (
		SELECT COALESCE
		( SUM( thermal ) * (
		SELECT COALESCE
		( factor, 0 )
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '12'
		and del_flag = '0'
		and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		), 0 )  AS thermal_total,
		company_id
		FROM
		discharge_data_thermal
		WHERE
		DATE_FORMAT(report_time, '%Y') = #{bo.year}
		AND del_flag = '0'
		and company_id in
		<foreach collection="bo.companyIds" open="(" separator="," item="companyId" close=")">
			#{companyId}
		</foreach>
		GROUP BY company_id
		) h on a.id = h.company_id
		LEFT JOIN (
		SELECT COALESCE
		( SUM( coal ) * (
		SELECT COALESCE
		( factor, 0 )
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '13'
		and del_flag = '0'
		and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		), 0 )  AS coal_total,
		company_id
		FROM
		discharge_data_coal
		WHERE
		DATE_FORMAT(report_time, '%Y') = #{bo.year}
		AND del_flag = '0'
		and company_id in
		<foreach collection="bo.companyIds" open="(" separator="," item="companyId" close=")">
			#{companyId}
		</foreach>
		GROUP BY company_id
		) m on a.id = m.company_id
		WHERE
		a.id in
		<foreach collection="bo.companyIds" open="(" separator="," item="companyId" close=")">
		  #{companyId}
	    </foreach>
		and a.del_flag = '0'

	</select>
	<select id="getTelecomBusinessTotal"
			resultType="com.enrising.ctsc.discharge.api.vo.DischargeEmissionTrendVo">
		SELECT
			company_id,
			COALESCE(SUM( telecom_business_total ), 0 ) AS carbonIntensity,
			COALESCE(SUM( business_flow_total ), 0 )  as busCarbonIntensity
		FROM
			business_production_data
		WHERE
			DATE_FORMAT(report_time, '%Y') = #{nowYear}
		GROUP BY
			company_id
	</select>
	<select id="getEnergyCompanyList"
			resultType="com.enrising.ctsc.discharge.api.vo.DischargeMonitorSettingVo">

		SELECT
		    A.org_name AS companyName,
			A.ID AS companyId,
			CAST (
				(
					COALESCE ( b.power_energy, 0 ) + COALESCE ( C.oil_energy, 0 ) + COALESCE ( d.gas_energy, 0 ) + COALESCE ( e.thermal_energy, 0 ) + COALESCE ( f.water_energy, 0 ) + COALESCE ( G.coal_energy, 0 )
				) / 10000 AS DECIMAL ( 18, 2 )
			) AS energy
		FROM
			rmp.sys_organizations A
			    LEFT JOIN (
			SELECT
				company_id,
				CAST (
					COALESCE (
						( SUM( outsourcing_thermal_power ) - SUM( own_green_power ) ) * (
						SELECT
							coefficient
						FROM
							"discharge_energy_coefficient"
						WHERE
							energy_type_id = '3'
							AND to_char( validity_start, 'yyyy' ) &lt;=#{bo.year}
							AND del_flag = '0'
						ORDER BY
							validity_start DESC
							LIMIT 1
						),
						0
					) / 1000 AS DECIMAL ( 18, 4 )
				) AS power_energy
			FROM
				discharge_data_electric
			WHERE
				DATE_FORMAT(report_time, '%Y') =#{bo.year}
			AND del_flag = '0'
			GROUP BY
				company_id
			) b ON A.ID = b.company_id
			LEFT JOIN (
			SELECT
				company_id,
				CAST (
					(
						COALESCE (
							SUM( gasoline ) * (
							SELECT
								coefficient
							FROM
								discharge_energy_coefficient
							WHERE
								energy_type_id = '7'
								AND DATE_FORMAT(validity_start, '%Y') &lt;=#{bo.year}
								AND del_flag = '0'
							ORDER BY
								validity_start DESC
								LIMIT 1
							),
							0
							) / 1000 + COALESCE (
							SUM( diesel ) * (
							SELECT
								coefficient
							FROM
								discharge_energy_coefficient
							WHERE
								energy_type_id = '8'
								AND DATE_FORMAT(validity_start, '%Y') &lt;=#{bo.year}
								AND del_flag = '0'
							ORDER BY
								validity_start DESC
								LIMIT 1
							),
							0
							) / 1000 + COALESCE (
							SUM( crude ) * (
							SELECT
								coefficient
							FROM
								discharge_energy_coefficient
							WHERE
								energy_type_id = '10'
								AND DATE_FORMAT(validity_start, '%Y') &lt;=#{bo.year}
								AND del_flag = '0'
							ORDER BY
								validity_start DESC
								LIMIT 1
							),
							0
							) / 1000 + COALESCE (
							SUM( fuel ) * (
							SELECT
								coefficient
							FROM
								discharge_energy_coefficient
							WHERE
								energy_type_id = '11'
								AND DATE_FORMAT(validity_start, '%Y') &lt;=#{bo.year}
								AND del_flag = '0'
							ORDER BY
								validity_start DESC
								LIMIT 1
							),
							0
							) / 1000 + COALESCE (
							SUM( kerosene ) * (
							SELECT
								coefficient
							FROM
								discharge_energy_coefficient
							WHERE
								energy_type_id = '9'
								AND DATE_FORMAT(validity_start, '%Y') &lt;=#{bo.year}
								AND del_flag = '0'
							ORDER BY
								validity_start DESC
								LIMIT 1
							),
							0
						) / 1000
					) AS DECIMAL ( 18, 4 )
				) AS oil_energy
			FROM
				discharge_data_oil
			WHERE
				DATE_FORMAT(report_time, '%Y') =#{bo.year}
			AND del_flag = '0'
			GROUP BY
				company_id
			) C ON A.ID = C.company_id
			LEFT JOIN (
			SELECT
				company_id,
				CAST (
					(
						COALESCE (
							SUM( ng ) * (
							SELECT
								coefficient
							FROM
								discharge_energy_coefficient
							WHERE
								energy_type_id = '5'

								AND DATE_FORMAT(validity_start, '%Y') &lt;=#{bo.year}
								AND del_flag = '0'
							ORDER BY
								validity_start DESC
								LIMIT 1
							),
							0
							) / 1000 + COALESCE (
							SUM( lpg ) * (
							SELECT
								coefficient
							FROM
								discharge_energy_coefficient
							WHERE
								energy_type_id = '6'
								AND DATE_FORMAT(validity_start, '%Y') &lt;=#{bo.year}
								AND del_flag = '0'
							ORDER BY
								validity_start DESC
								LIMIT 1
							),
							0
						) / 1000
					) AS DECIMAL ( 18, 4 )
				) AS gas_energy
			FROM
				discharge_data_gas
			WHERE
				DATE_FORMAT(report_time, '%Y') =#{bo.year}
			AND del_flag = '0'
			GROUP BY
				company_id
			) d ON A.ID = d.company_id
			LEFT JOIN (
			SELECT
				company_id,
				CAST (
					COALESCE (
						SUM( thermal ) * (
						SELECT
							coefficient
						FROM
							discharge_energy_coefficient
						WHERE
							energy_type_id = '12'
							AND DATE_FORMAT(validity_start, '%Y') &lt;=#{bo.year}
							AND del_flag = '0'
						ORDER BY
							validity_start DESC
							LIMIT 1
						),
						0
					) / 1000 AS DECIMAL ( 18, 4 )
				) AS thermal_energy
			FROM
				discharge_data_thermal
			WHERE
				DATE_FORMAT(report_time, '%Y') =#{bo.year}
			AND del_flag = '0'
			GROUP BY
				company_id
			) e ON A.ID = e.company_id
			LEFT JOIN (
			SELECT
				company_id,
				CAST (
					COALESCE (
						SUM( water ) * (
						SELECT
							coefficient
						FROM
							discharge_energy_coefficient
						WHERE
							energy_type_id = '1'
							AND DATE_FORMAT(validity_start, '%Y') &lt;=#{bo.year}
							AND del_flag = '0'
						ORDER BY
							validity_start DESC
							LIMIT 1
						),
						0
					) / 1000 AS DECIMAL ( 18, 4 )
				) AS water_energy
			FROM
				discharge_data_water
			WHERE
				DATE_FORMAT(report_time, '%Y') =#{bo.year}
			AND del_flag = '0'
			GROUP BY
				company_id
			) f ON A.ID = f.company_id
			LEFT JOIN (
			SELECT
				company_id,
				CAST (
					COALESCE (
						SUM( coal ) * (
						SELECT
							coefficient
						FROM
							discharge_energy_coefficient
						WHERE
							energy_type_id = '13'
							AND DATE_FORMAT(validity_start, '%Y') &lt;=#{bo.year}
							AND del_flag = '0'
						ORDER BY
							validity_start DESC
							LIMIT 1
						),
						0
					) AS DECIMAL ( 18, 4 )
				) AS coal_energy
			FROM
				discharge_data_coal
			WHERE
				DATE_FORMAT(report_time, '%Y') =#{bo.year}
			AND del_flag = '0'
			GROUP BY
				company_id
			) G ON A.ID = G.company_id
		WHERE
			A.parent_company_no IN ( ( SELECT ID FROM rmp.sys_organizations WHERE parent_company_no = '0000000000' ), '0000000000' )
			AND A.del_flag = '0'
	</select>
	<select id="getCompanyCount" resultType="java.lang.Integer">
		SELECT
			count(*)
		FROM rmp.sys_organizations
		WHERE del_flag='0'
		  AND parent_company_no = '2600000000'
	</select>
</mapper>