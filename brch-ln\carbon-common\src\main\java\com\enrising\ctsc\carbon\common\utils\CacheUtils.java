package com.enrising.ctsc.carbon.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * redis 工具类封装
 *
 * <AUTHOR>
 * @Date 2019/3/11 10:08
 */
public class CacheUtils {
    private static final Logger logger = LoggerFactory.getLogger(CacheUtils.class);

    /**
     * 获取 StringRedisTemplate
     *
     * @return org.springframework.data.redis.core.StringRedisTemplate
     * <AUTHOR>
     * @Date 2019/3/11 10:18
     */
    public static StringRedisTemplate getStringRedisTemplate() {
        return SpringUtils.getBean("stringRedisTemplate");
    }

    /**
     * 获取  RedisTemplate<'String, Object>
     *
     * @return org.springframework.data.redis.core.RedisTemplate<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @Date 2019/3/11 10:41
     */
    @SuppressWarnings("unchecked")
    public static RedisTemplate<String, Object> getRedisTemplate() {
        return SpringUtils.getBean("redisTemplate");
    }

    /**
     * 删除缓存
     * 根据key精确匹配删除
     *
     * @param key
     */
    public static void del(String... key) {
        if (key == null || key.length == 0) {
            return;
        }
        if (key.length == 1) {
            getRedisTemplate().delete(key[0]);
        } else {
            getRedisTemplate().delete(Arrays.asList(key));
        }
    }

    /**
     * 批量删除 （该操作会执行模糊查询，请尽量不要使用，以免影响性能或误删）
     *
     * @param pattern 键
     */
    public static void batchDel(String... pattern) {
        for (String kp : pattern) {
            Set<String> keys = getKeys(kp + "*");
            if (keys != null && !keys.isEmpty()) {
                int i = 1;
                for (String key : keys) {
                    logger.info("删除key" + i + "==" + key);
                    i++;
                }
                getRedisTemplate().delete(keys);
            }
        }
    }

    /**
     * 取得缓存（int型）
     *
     * @param key 键
     * @return Integer类型的value
     */
    public static Integer getInt(String key) {
        String value = getStringRedisTemplate().boundValueOps(key).get();
        if (StringUtils.isNotBlank(value)) {
            return Integer.valueOf(value);
        }
        return null;
    }



    /**
     * 取得缓存（字符串类型）
     *
     * @param key 键
     * @return String 值
     */
    public static String getStr(String key) {
        return getStringRedisTemplate().boundValueOps(key).get();
    }


    /**
     * 取得缓存（字符串类型）
     *
     * @param key    键
     * @param retain 获取后是否保留
     * @return 字符类型的 值
     */
    public static String getStr(String key, boolean retain) {
        String value = getStringRedisTemplate().boundValueOps(key).get();
        if (!retain) {
            getRedisTemplate().delete(key);
        }
        return value;
    }

    /**
     * 获取缓存<br>
     * 注：基本数据类型(Character除外)，请直接使用get(String key, Class<T> clazz)取值
     *
     * @param key 键
     * @return Object
     */
    public static Object getObj(String key) {
        return getRedisTemplate().boundValueOps(key).get();
    }
    public static void setObj(String key, Object value) {
        getRedisTemplate().opsForValue().set(key, value);
    }



    /**
     * 获取缓存<br>
     * 注：java 8种基本类型的数据请直接使用get(String key, Class<T> clazz)取值
     *
     * @param key    键
     * @param retain 获取后 是否保留
     * @return Object
     */
    public static Object getObj(String key, boolean retain) {
        Object obj = getRedisTemplate().boundValueOps(key).get();
        if (!retain) {
            getRedisTemplate().delete(key);
        }
        return obj;
    }

    /**
     * 获取缓存
     * 注：该方法暂不支持Character数据类型
     *
     * @param key 键
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T get(String key) {
        return (T) getRedisTemplate().boundValueOps(key).get();
    }

    /**
     * 获取缓存json对象<br>
     *
     * @param key   key
     * @param clazz 类型
     * @return
     */
    public static <T> T getJson(String key, Class<T> clazz) {
        return JsonMapper.fromJsonString(getStringRedisTemplate().boundValueOps(key).get(), clazz);
    }

    /**
     * 将value对象写入缓存
     *
     * @param key   键
     * @param value 值
     */
    public static void set(String key, Object value) {

        if (value.getClass().equals(String.class) || value.getClass().equals(Integer.class) || value.getClass().equals(Double.class) || value.getClass().equals(Float.class) || value.getClass().equals(Short.class) || value.getClass().equals(Long.class) || value.getClass().equals(Boolean.class)) {
            getStringRedisTemplate().opsForValue().set(key, value.toString());
        } else {
            getRedisTemplate().opsForValue().set(key, value);
        }
    }

    /**
     * 将value对象以JSON格式写入缓存
     *
     * @param key   键
     * @param value 值
     */
    public static void setJson(String key, Object value) {
        getStringRedisTemplate().opsForValue().set(key, JsonMapper.toJsonString(value));
    }


    /**
     * 更新key对象field的值
     *
     * @param key   缓存key
     * @param field 缓存对象field
     * @param value 缓存对象field值
     */
    public static void setJsonField(String key, String field, String value) {
        JSONObject obj = JSON.parseObject(getStringRedisTemplate().boundValueOps(key).get());
        obj.put(field, value);
        getStringRedisTemplate().opsForValue().set(key, obj.toJSONString());
    }

    /**
     * 递减操作
     *
     * @param key 键
     * @param by  具体数值
     * @return 递减后的值
     */
    public static double decr(String key, double by) {
        return getRedisTemplate().opsForValue().increment(key, -by);
    }

    /**
     * 递增操作
     *
     * @param key 键
     * @param by  具体数值
     * @return 递增后的值
     */
    public static double incr(String key, double by) {
        return getRedisTemplate().opsForValue().increment(key, by);
    }

    /**
     * 获取double类型值
     *
     * @param key 键
     * @return double类似的数据
     */
    public static double getDouble(String key) {
        String value = getStringRedisTemplate().boundValueOps(key).get();
        if (StringUtils.isNotBlank(value)) {
            return Double.parseDouble(value);
        }
        return 0d;
    }

    /**
     * 设置double类型值
     *
     * @param key   键
     * @param value 值
     */
    public static void setDouble(String key, double value) {
        getStringRedisTemplate().opsForValue().set(key, String.valueOf(value));
    }

    /**
     * 设置Int类型值
     *
     * @param key   键
     * @param value 值
     */
    public static void setInt(String key, int value) {
        getStringRedisTemplate().opsForValue().set(key, String.valueOf(value));
    }

    /**
     * 将map写入缓存
     *
     * @param key 键
     * @param map map对象
     */
    public static <T> void setMap(String key, Map<String, T> map) {
        getRedisTemplate().opsForHash().putAll(key, map);
    }

    /**
     * 将map写入缓存
     *
     * @param key 键
     * @param obj
     */
    @SuppressWarnings("unchecked")
    public static <T> void setMap(String key, T obj) {
        Map<String, String> map = (Map<String, String>) JsonMapper.parseObject(obj, Map.class);
        getRedisTemplate().opsForHash().putAll(key, map);
    }

    /**
     * 向key对应的map中添加缓存对象
     *
     * @param key 键
     * @param map map对象
     */
    public static <T> void addMap(String key, Map<String, T> map) {
        getRedisTemplate().opsForHash().putAll(key, map);
    }

    /**
     * 向key对应的map中添加缓存对象
     *
     * @param key   cache对象key
     * @param field map对应的key
     * @param value 值
     */
    public static void addMap(String key, String field, String value) {
        getRedisTemplate().opsForHash().put(key, field, value);
    }

    /**
     * 向key对应的map中添加缓存对象
     *
     * @param key   cache对象key
     * @param field map对应的key
     * @param obj   对象
     */
    public static <T> void addMap(String key, String field, T obj) {
        getRedisTemplate().opsForHash().put(key, field, obj);
    }

    /**
     * 获取map缓存
     *
     * @param key 键
     * @return map对象
     */
    public static <T> Map<String, T> mapGet(String key) {
        BoundHashOperations<String, String, T> boundHashOperations = getRedisTemplate().boundHashOps(key);
        return boundHashOperations.entries();
    }

    /**
     * 获取map缓存
     *
     * @param key   键
     * @param clazz 类型
     * @return
     */
    public static <T> T getMap(String key, Class<T> clazz) {
        BoundHashOperations<String, String, String> boundHashOperations = getRedisTemplate().boundHashOps(key);
        Map<String, String> map = boundHashOperations.entries();
        return JsonMapper.parseObject(map, clazz);
    }

    /**
     * 获取map缓存中的某个对象
     *
     * @param key   键
     * @param field map对应的key
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T getMapField(String key, String field) {
        return (T) getRedisTemplate().boundHashOps(key).get(field);
    }

    /**
     * 删除map中的某个对象
     *
     * @param key   缓存的键
     * @param field map中该对象的key
     */
    public void delMapField(String key, String... field) {
        BoundHashOperations<String, String, ?> boundHashOperations = getRedisTemplate().boundHashOps(key);
        boundHashOperations.delete(field);
    }

    /**
     * 添加set
     *
     * @param key   键
     * @param value 值
     */
    public static void sadd(String key, String... value) {
        getRedisTemplate().boundSetOps(key).add(value);
    }

    /**
     * 删除set集合中的对象
     *
     * @param key   键
     * @param value 值
     */
    public static void sremove(String key, String... value) {
        getRedisTemplate().boundSetOps(key).remove(value);
    }

    /**
     * set重命名
     *
     * @param oldkey 原来的键
     * @param newkey 新命名的键
     */
    public static void srename(String oldkey, String newkey) {
        getRedisTemplate().boundSetOps(oldkey).rename(newkey);
    }

    /**
     * 短信缓存
     *
     * @param key   键
     * @param value
     * @param time
     */
    public static void setIntForPhone(String key, Object value, int time) {
        getStringRedisTemplate().opsForValue().set(key, JsonMapper.toJsonString(value));
        if (time > 0) {
            getStringRedisTemplate().expire(key, time, TimeUnit.SECONDS);
        }
    }

    /**
     * 模糊查询keys
     *
     * @param pattern 键
     * @return 符合条件的key 集合
     */
    public static Set<String> keys(String pattern) {
        return getRedisTemplate().keys(pattern);
    }

    /**
     * expire:(使缓存在指定时间过期失效)
     *
     * @param key 键
     */
    public static void expire(String key) {
        if (StringUtils.isBlank(key)) {
            return;
        }
        getRedisTemplate().expire(key, 1, TimeUnit.SECONDS);
    }

    /**
     * getConnection:(获取jedis连接). <br/>
     *
     * @return RedisConnection
     */
    public static RedisConnection getConnection() {
        RedisConnection redisConnection = null;
        RedisConnectionFactory redisConnectionFactory = getRedisTemplate().getConnectionFactory();
        if (redisConnectionFactory != null) {
            redisConnection = redisConnectionFactory.getConnection();
        }
        return redisConnection;
    }

    /**
     * 返回Keys
     *
     * @param partern
     * @return
     */
    public static Set<String> getKeys(String partern) {
        if (StringUtils.isBlank(partern)) {
            return null;
        }
        return getRedisTemplate().keys(partern);
    }

    /**
     * 数据查询，默认从MC中获取，失败改成DB获取，并将查询结果加入缓存（缓存永远生效）
     *
     * @param key     键
     * @param handler handler
     * @return Object
     */
    public static Object getValue(String key, CacheHandler handler) {
        return CacheUtils.getValue(key, 0, handler);
    }

    /**
     * 数据查询，默认从缓存中获取，失败改成DB获取，并将查询结果加入缓存
     *
     * @param key     键
     * @param exp     失效时间
     * @param handler handler
     * @return java.lang.Object
     */
    public static Object getValue(String key, int exp, CacheHandler handler) {
        // 优先从缓存中获取
        Object value = null;
        try {
            value = CacheUtils.getObj(key);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (value == null) {
                value = handler.getDataFromDB();

                CacheUtils.set(key, value);

            }
        }
        return value;
    }

    /**
     * 将对象添加到缓存的列表中 采用末尾添加的方式
     *
     * @param key    键
     * @param object 需要添加的对象
     */
    public static void listPush(String key, Object object) {
        getRedisTemplate().opsForList().rightPush(key, object);
    }

    /**
     * 批量把一个集合插入到列表中 采用末尾添加
     *
     * @param key    键
     * @param values 待插入的列表
     */
    public static void listPushAll(String key, Collection<Object> values) {
        getRedisTemplate().opsForList().rightPushAll(key, values);
    }

    /**
     * 批量把一个数组插入到列表中
     *
     * @param key    键
     * @param values 数组
     */
    public static void listPushAll(String key, String[] values) {
        getRedisTemplate().opsForList().rightPushAll(key, values);
    }

    /**
     * 只有存在key对应的列表才能将这个value值插入到key所对应的列表中
     * 采用末尾添加
     *
     * @param key   键
     * @param value 对象
     * @return java.lang.Long
     */
    public static Long listPushIfPresent(String key, Object value) {
        return getRedisTemplate().opsForList().rightPushIfPresent(key, value);
    }

    /**
     * 在列表中index的位置设置value值
     *
     * @param key   键
     * @param index 索引
     * @param value 对象
     */
    public static void listSet(String key, long index, Object value) {
        getRedisTemplate().opsForList().set(key, index, value);
    }

    /**
     * 从存储在键中的列表中删除等于值的元素的第一个计数事件。
     * 计数参数以下列方式影响操作：
     * count> 0：删除等于从头到尾移动的值的元素。
     * count <0：删除等于从尾到头移动的值的元素。
     * count = 0：删除等于value的所有元素。
     *
     * @param key   键
     * @param count 计数参数
     * @param value 需要删除的值
     * @return java.lang.Long
     */
    public static Long listRemove(String key, long count, Object value) {
        return getRedisTemplate().opsForList().remove(key, count, value);
    }

    /**
     * 根据下标获取列表中的值，下标是从0开始的
     *
     * @param key   键
     * @param index 索引
     * @return java.lang.Object
     */
    public static Object listIndex(String key, long index) {
        return getRedisTemplate().opsForList().index(key, index);
    }


    /**
     * 获取缓存中的列表 (全量返回)
     *
     * @param key
     * @return java.util.List<java.lang.Object>
     */
    public static List<Object> getList(String key) {
        return getRedisTemplate().opsForList().range(key, 0, -1);
    }

    /**
     * 修剪现有列表，使其只包含指定的指定范围的元素，起始和停止都是基于0的索引
     *
     * @param key   键
     * @param start 起始索引
     * @param end   停止索引
     */
    public static void listTrim(String key, long start, long end) {
        getRedisTemplate().opsForList().trim(key, start, end);
    }

    /**
     * 返回存储在键中的列表的长度。如果键不存在，则将其解释为空列表，并返回0。当key存储的值不是列表时会返回错误。
     *
     * @param key 键
     * @return java.lang.Long
     */
    public static Long listSize(String key) {
        return getRedisTemplate().opsForList().size(key);
    }
}
