package com.enrising.ctsc.discharge.api.query;

import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergy;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 碳排放数据填报表（能源）查询
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DischargeDataEnergyQuery extends DischargeDataEnergy {

	/**
	 * 上报年月
	 */
	private Date reportTime;

	/**
	 * 数据部门
	 */
	private Long companyId;

	/**
	 * 已上报的公司id
	 */
	private List<Long> companyIds;

	/**
	 * 上报公司
	 */
	private String reportCompany;

	/**
	 * 上报年月
	 */
	private String reportMonth;

	/**
	 * 下载类型
	 */
	private String downType;

	/**
	 * 查询年度
	 */
	private String year;
}
