package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergyCalculate;
import com.enrising.ctsc.discharge.api.query.DischargeDataEnergyQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 按规则计算能源数据表 服务接口
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
public interface DischargeDataEnergyCalculateService extends IService<DischargeDataEnergyCalculate> {
    int saveData(DischargeDataEnergyCalculate dischargeDataEnergyCalculate);

    List<DischargeDataEnergyVo> getAllDataList(DischargeDataEnergyCalculate query);

    boolean saveList(List<DischargeDataEnergyCalculate> dischargeDataEnergyCalculateList);

    int removeCalculateData(Long companyId, Date reportTime);

    void exportExcel(HttpServletRequest request, HttpServletResponse response, DischargeDataEnergyQuery query);

    List<DischargeDataEnergyVo> getCalcReportList(DischargeDataEnergyQuery query);
}