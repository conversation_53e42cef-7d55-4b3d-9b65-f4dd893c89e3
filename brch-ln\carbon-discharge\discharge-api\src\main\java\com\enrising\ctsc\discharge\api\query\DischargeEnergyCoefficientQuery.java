package com.enrising.ctsc.discharge.api.query;

import com.enrising.ctsc.discharge.api.entity.DischargeEnergyCoefficient;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 碳排放能源转换系数表查询
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DischargeEnergyCoefficientQuery extends DischargeEnergyCoefficient {

	/**
	 * 关键字
	 */
	private String keys;

	/**
	 * 时间周期
	 */
	private List<String> daterange;

	/**
	 * 更新时候传入的id，不做查询使用，用于更新对比查询出数据库的数据是否是更新这一条
	 */
	private Long boId;
}
