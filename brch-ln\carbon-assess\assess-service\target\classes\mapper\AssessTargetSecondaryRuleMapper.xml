<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.assess.mapper.AssessTargetSecondaryRuleMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.secondary_target_id,
            t.rule_judge,
            t.rule_value,
            t.rule_score,
            t.primary_target_id
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.assess.api.vo.AssessTargetSecondaryRuleVo">
        SELECT
        <include refid="baseColumns" />
        FROM assess_target_secondary_rule t
        ${ew.customSqlSegment}
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.assess.api.vo.AssessTargetSecondaryRuleVo">
        SELECT
        <include refid="baseColumns" />
        FROM assess_target_secondary_rule t
        ${ew.customSqlSegment}
        limit 1
    </select>
</mapper>