package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.discharge.api.bo.DischargeDataWaterBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataWater;
import com.enrising.ctsc.discharge.api.vo.DischargeDataWaterVo;

import java.util.Date;
import java.util.List;

/**
 * 碳排放数据填报表（水）
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-01-03
 */
public interface DischargeDataWaterService extends IService<DischargeDataWater> {

	/**
	 * 列表查询
	 *
	 * @param queryPage 查询参数
	 * @return 列表
	 */
	Page<DischargeDataWaterVo> getWaterListPage(QueryPage<DischargeDataWaterBo> queryPage);

	/**
	 * 列表查询
	 *
	 * @param dischargeDataWaterBo 查询参数
	 * @return 列表
	 */
	List<DischargeDataWaterVo> getWaterListToExcel(DischargeDataWaterBo dischargeDataWaterBo);

	/**
	 * 列表查询
	 *
	 * @param dataYear 数据年份
	 * @return 列表
	 */
	List<DischargeDataWaterVo> getDataList(Integer dataYear, Long companyId);

	/**
	 * 列表查询
	 *
	 * @param dateStart 数据开始时间
	 * @return 列表
	 */
	List<DischargeDataWaterVo> getDataListByDate(Date dateStart, Date dateEnd, Long companyId);

	/**
	 * 详情
	 *
	 * @param id 参数
	 * @return 详情
	 */
	DischargeDataWaterVo detail(Long id);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	String add(DischargeDataWaterBo bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	void edit(DischargeDataWaterBo bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	void del(Long id);

	/**
	 * 统计数据列表查询
	 *
	 * @param dateStart 自定义数据开始时间
	 * @param dateEnd 自定义数据结束时间
	 * @return 列表
	 */
	List<DischargeDataWaterVo> countCompanyData(Date dateStart, Date dateEnd, Long companyId);
}
