package com.enrising.ctsc.assess.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.assess.api.bo.AssessTemplateBo;
import com.enrising.ctsc.assess.api.entity.AssessTemplate;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.assess.api.vo.AssessTemplateVo;
import com.enrising.ctsc.assess.api.vo.DeptTreeVo;

import java.util.List;

/**
 * 考核模板管理服务接口
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-07
 */
public interface AssessTemplateService extends IService<AssessTemplate> {

	/**
	 * 详情
	 *
	 * @param id 参数
	 * @return 详情
	 */
	AssessTemplateVo getDetailById(Long id);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	boolean add(AssessTemplateBo bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	boolean edit(AssessTemplateBo bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	boolean del(Long id);

	/**
	 * 考核模板分页查询
	 * @param queryPage  分页查询参数
	 * @return 分页查询结果
	 */
	Page<AssessTemplateVo> getAssessTemplatePage(QueryPage<AssessTemplateBo> queryPage);

	/**
	 * 统计考核任务
	 * @return 查询结果
	 */
	Integer countTotalTask();

	/**
	 * 考核任务管理--省级列表
	 * @param queryPage  分页查询参数
	 * @return 分页查询结果
	 */
	Page<AssessTemplateVo> getDeliveredTaskByPage(QueryPage<AssessTemplateBo> queryPage);

	/**
	 * 考核任务管理--省级详情列表
	 * @return 查询结果
	 */
	List<AssessTemplate> getDeliveredTaskList();

	/**
	 * 考核任务管理--市州公司/部门列表
	 * @return 查询结果
	 */
	List<AssessTemplateVo> getAllAssessTemplateByYear();

	/**
	 * 市州公司/部门列表--所有考核模板列表
	 * @return 查询结果
	 */
	List<AssessTemplateVo> getAllAssessTemplate(AssessTemplateBo bo);

	/**
	 * 根据 市州公司/部门列表--所有考核模板列表 获取指标数量
	 * @return	指标数量
	 */
	long countTargetByAssessTemplate(AssessTemplateBo bo);

	/**
	 * 模板是否有上报数据
	 *
	 * @param id 参数
	 * @return 模板是否有上报数据
	 */
	boolean isHasReportData(Long id);

	/**
	 * 设置考核模板下发状态
	 * @param id 参数
	 * @param sendStatus 考核模板下发状态，1-已下发 2-未下发 3-已撤回
	 * @return 操作是否成功
	 */
	boolean setTemplateSendStatus(Long id, String sendStatus);

	/**
	 * 考核任务上报-检查是否到上报时间
	 * @return 操作是否成功
	 * @param id 模板id
	 */
	Boolean checkReportTime(Long id);

	List<DeptTreeVo> listAssessDeptTrees(DeptTreeVo deptTree);
}
