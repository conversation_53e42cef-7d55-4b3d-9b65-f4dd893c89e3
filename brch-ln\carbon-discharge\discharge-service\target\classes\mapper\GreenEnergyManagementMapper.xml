<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.GreenEnergyManagementMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.transaction_month,
            t.company_branch,
            t.monthly_contract_power,
            t.subject_entity,
            t.power_source_location,
            t.energy_type,
            t.price,
            t.supporting_document,
            t.remarks,
            t.created_by,
            t.created_time,
            t.updated_time
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.discharge.api.vo.GreenEnergyManagementVo">
        SELECT
        <include refid="baseColumns" />
        FROM green_energy_management t
        ${ew.customSqlSegment}
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.discharge.api.vo.GreenEnergyManagementVo">
        SELECT
        <include refid="baseColumns" />
        FROM green_energy_management t
        ${ew.customSqlSegment}
        limit 1
    </select>
</mapper>