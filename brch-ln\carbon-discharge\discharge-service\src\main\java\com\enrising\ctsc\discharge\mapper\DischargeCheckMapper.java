package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.entity.DischargeCheck;
import com.enrising.ctsc.discharge.api.query.DischargeCheckQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeCheckVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 碳排放核查
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Mapper
public interface DischargeCheckMapper extends BaseMapper<DischargeCheck> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
	IPage<DischargeCheckVo> findList(Page<DischargeCheckVo> page, @Param(Constants.WRAPPER) Wrapper<DischargeCheckQuery> wrapper);

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
	List<DischargeCheckVo> findList(@Param(Constants.WRAPPER) Wrapper<DischargeCheckQuery> wrapper);

	/**
	 * 详情查询
	 *
	 * @param wrapper 条件
	 * @return 结果
	 */
	DischargeCheckVo detail(@Param(Constants.WRAPPER) Wrapper<DischargeCheckQuery> wrapper);
}