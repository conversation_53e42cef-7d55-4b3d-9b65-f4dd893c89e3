package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.enrising.ctsc.discharge.api.bo.DischargeDataExamineBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataExamine;
import com.enrising.ctsc.discharge.api.vo.DischargeDataExamineVo;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyExamineVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 碳排放能源指标表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Mapper
public interface DischargeDataExamineMapper extends BaseMapper<DischargeDataExamine> {


	/**
	 * 查询列表
	 * */
	List<DischargeDataExamineVo> getList(@Param("bo") DischargeDataExamineBo bo);

	/**
	 * 查询使用量列表
	 * */
	@InterceptorIgnore(tenantLine = "true")
	List<DischargeEnergyExamineVo> getUseDataByExamineIds(@Param("bo") DischargeDataExamineBo bo);

	/**
	 * 通过公司ID查询公司名称
	 * */
	String getCompanyNameById(Long companyId);
}