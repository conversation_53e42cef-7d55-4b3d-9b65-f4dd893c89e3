package com.enrising.ctsc.assess.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.assess.api.bo.AssessReportScoreBo;
import com.enrising.ctsc.assess.api.bo.AssessTaskReportBo;
import com.enrising.ctsc.assess.api.entity.AssessTaskReport;
import com.enrising.ctsc.assess.api.query.AssessTaskReportQuery;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.assess.api.vo.AssessTaskReportScoreDetailVo;
import com.enrising.ctsc.assess.api.vo.AssessTaskReportScoreVo;
import com.enrising.ctsc.assess.api.vo.AssessTaskReportVo;
import com.enrising.ctsc.assess.service.AssessTaskReportService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 考核任务上报表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@RestController
@RequestMapping("/assess/taskReport")
@AllArgsConstructor
public class AssessTaskReportController {
	private final AssessTaskReportService assessTaskReportService;

	@GetMapping("/scoreList")
	public TableDataInfo<AssessTaskReportScoreVo> page(Page<AssessTaskReportVo> page, AssessTaskReportQuery query) {
		return assessTaskReportService.scoreList(page, query);
	}

	@GetMapping(value = "/scoreDetail/{reportId}")
	public R<AssessTaskReportScoreDetailVo> scoreDetail(@PathVariable Long reportId) {
		AssessTaskReportScoreDetailVo detail = assessTaskReportService.scoreDetail(reportId);
		return R.success(detail, "打分详情");
	}

	/**
	 * 根据考核模板对象查询上报列表
	 *
	 * @param templateObjectId 考核模板对象id
	 * @return 列表
	 */
	@GetMapping(value = "/listByTemplateObjectId/{templateObjectId}")
	public R<List<AssessTaskReportVo>> listByTemplateObjectId(@PathVariable Long templateObjectId) {
		List<AssessTaskReportVo> list = assessTaskReportService.listByTemplateObjectId(templateObjectId);
		return R.success(list, "考核模板对象上报列表");
	}

	@PostMapping(value = "/score")
	public R<AssessTaskReport> score(@RequestBody @Valid AssessReportScoreBo bo) {
		AssessTaskReport taskReport = assessTaskReportService.score(bo);
		return R.success(taskReport, "操作成功");
	}

	@GetMapping("/detail")
	public R<AssessTaskReportVo> get(AssessTaskReportQuery query) {
		AssessTaskReportVo detail = assessTaskReportService.detail(query);
		return R.success(detail, "查询成功");
	}

	@PostMapping(value = "/save")
	public R<String> save(@RequestBody AssessTaskReportBo bo) {
		assessTaskReportService.add(bo);
		return R.success("保存成功");
	}

	@PostMapping(value = "/update")
	public R<String> update(@RequestBody @Validated({AssessTaskReportBo.report.class}) AssessTaskReportBo bo) {
		assessTaskReportService.edit(bo);
		return R.success("修改成功");
	}

	@PostMapping(value = "/delete/{id}")
	public R<String> delete(@PathVariable Long id) {
		assessTaskReportService.del(id);
		return R.success("删除成功");
	}

	@PostMapping(value = "/reporte")
	public R<String> reporte(@RequestBody @Validated({AssessTaskReportBo.report.class}) AssessTaskReportBo bo) {
		assessTaskReportService.reporte(bo);
		return R.success("保存成功");
	}

}
