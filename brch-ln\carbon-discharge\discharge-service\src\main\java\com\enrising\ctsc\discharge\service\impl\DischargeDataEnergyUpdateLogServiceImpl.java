package com.enrising.ctsc.discharge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergyUpdateLog;
import com.enrising.ctsc.discharge.mapper.DischargeDataEnergyUpdateLogMapper;
import com.enrising.ctsc.discharge.service.DischargeDataEnergyUpdateLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 碳排放数据填报表（能源）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DischargeDataEnergyUpdateLogServiceImpl extends ServiceImpl<DischargeDataEnergyUpdateLogMapper, DischargeDataEnergyUpdateLog> implements DischargeDataEnergyUpdateLogService {




}
