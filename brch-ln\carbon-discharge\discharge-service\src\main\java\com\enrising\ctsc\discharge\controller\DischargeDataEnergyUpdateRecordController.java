package com.enrising.ctsc.discharge.controller;

import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.discharge.api.bo.DischargeDataEnergyBo;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyUpdateRecordVo;
import com.enrising.ctsc.discharge.service.DischargeDataEnergyUpdateRecordService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 碳排放数据填报表（能源）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@RestController
@RequestMapping("/discharge/udpRecord")
@AllArgsConstructor
public class DischargeDataEnergyUpdateRecordController {

	private final DischargeDataEnergyUpdateRecordService dischargeDataEnergyUpdateRecordService;

	@PostMapping("/getUpdateRecordList")
		public R<List<DischargeDataEnergyUpdateRecordVo>> getDataList(@RequestBody DischargeDataEnergyBo bo) {
		List<DischargeDataEnergyUpdateRecordVo> list = dischargeDataEnergyUpdateRecordService.getUpdateRecordList(bo);
		return R.success(list);
	}

}
