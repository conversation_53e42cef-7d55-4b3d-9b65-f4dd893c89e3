<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeEnergyIndicatorMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.indicator_name,
            t.unit,
            t.status,
            t.parent_id,
            t.sort,
            t.del_flag
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_energy_indicator t
        ${ew.customSqlSegment}
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_energy_indicator t
        ${ew.customSqlSegment}
        limit 1
    </select>

	<!-- 查询模板指标列表 -->
	<select id="getIndicatorList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorVo">
		SELECT
			*
		FROM (
				 SELECT discharge_energy_indicator.id,
						discharge_energy_indicator.create_by,
						discharge_energy_indicator.create_time,
						discharge_energy_indicator.update_by,
						discharge_energy_indicator.update_time,
						discharge_energy_indicator.indicator_name,
						discharge_energy_indicator.unit,
						discharge_energy_indicator.status,
						discharge_energy_indicator.parent_id,
						discharge_energy_indicator.sort,
						discharge_energy_indicator.del_flag,
						sys_dict_item.description AS unit_description,
						sys_dict_item.label AS unit_name
				 FROM (discharge_energy_indicator
					 LEFT JOIN sys_dict_item ON (((sys_dict_item.type = 'energy_type_unit') AND (sys_dict_item.value = discharge_energy_indicator.unit))))) as t
		WHERE t.del_flag='0' and t.status='1'
		order by t.sort asc
	</select>
	<!-- 查询模板指标列表 -->
	<select id="getIndicatorTreeList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorVo">
		SELECT discharge_energy_indicator.id,
			discharge_energy_indicator.create_by,
			discharge_energy_indicator.create_time,
			discharge_energy_indicator.update_by,
			discharge_energy_indicator.update_time,
			discharge_energy_indicator.indicator_name,
			discharge_energy_indicator.unit,
			discharge_energy_indicator.status,
			discharge_energy_indicator.parent_id,
			discharge_energy_indicator.sort,
			discharge_energy_indicator.del_flag,
			sys_dict_item.description AS unit_description,
			sys_dict_item.label AS unit_name
		FROM discharge_energy_indicator
		LEFT JOIN sys_dict_item ON (((sys_dict_item.type = 'energy_type_unit') AND (sys_dict_item.value = discharge_energy_indicator.unit)))
		WHERE discharge_energy_indicator.del_flag='0'
		<choose>
			<when test="parentId == null">
				AND discharge_energy_indicator.parent_id is not NULL
			</when>
			<otherwise>
				AND discharge_energy_indicator.parent_id = #{parentId}
			</otherwise>
		</choose>
		<if test="status != null"> AND discharge_energy_indicator.status = #{status}</if>
		order by discharge_energy_indicator.sort asc
	</select>
</mapper>