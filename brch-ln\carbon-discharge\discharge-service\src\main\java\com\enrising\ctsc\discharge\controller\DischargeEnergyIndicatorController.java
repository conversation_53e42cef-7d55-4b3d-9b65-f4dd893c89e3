package com.enrising.ctsc.discharge.controller;

import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.bo.DischargeEnergyIndicatorBo;
import com.enrising.ctsc.discharge.api.bo.DischargeEnergyIndicatorStatus;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyIndicator;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyIndicatorQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorVo;
import com.enrising.ctsc.discharge.service.DischargeEnergyIndicatorService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 碳排放能源指标表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@RestController
@RequestMapping("/discharge/indicator")
@AllArgsConstructor
public class DischargeEnergyIndicatorController {
	private final DischargeEnergyIndicatorService dischargeEnergyIndicatorService;

	@GetMapping("/list")
	public TableDataInfo<DischargeEnergyIndicatorVo> page(DischargeEnergyIndicatorQuery query) {
		return dischargeEnergyIndicatorService.findList(query);
	}

	@GetMapping("/getIndicatorList")
	public R<List<DischargeEnergyIndicatorVo>> getIndicatorList() {
		return R.success(dischargeEnergyIndicatorService.getIndicatorList());
	}

	/**
	 * 返回树形菜单集合
	 *
	 * @param lazy     是否是懒加载
	 * @param parentId 父节点ID
	 * @return 树形菜单
	 */
	@GetMapping(value = "/getTree")
	public R getTree(boolean lazy, Long parentId) {
		return R.success(dischargeEnergyIndicatorService.getTree(lazy, parentId));
	}

	@GetMapping("/detail")
	public R<DischargeEnergyIndicatorVo> get(DischargeEnergyIndicatorQuery query) {
		DischargeEnergyIndicatorVo detail = dischargeEnergyIndicatorService.detail(query);
		return R.success(detail, "查询成功");
	}

		@PostMapping(value = "/save")
		public R<String> save(@RequestBody @Valid DischargeEnergyIndicatorBo bo) {
		dischargeEnergyIndicatorService.add(bo);
		return R.success("保存成功");
	}

		@PostMapping(value = "/update")
		public R<String> update(@RequestBody @Valid DischargeEnergyIndicatorBo bo) {
		dischargeEnergyIndicatorService.edit(bo);
		return R.success("修改成功");
	}

		@PostMapping(value = "/setStatus")
		public R<String> setStatus(@RequestBody @Valid DischargeEnergyIndicatorStatus status) {
		DischargeEnergyIndicator indicator = new DischargeEnergyIndicator();
		indicator.setId(status.getId());
		indicator.setStatus(status.getStatus());
		dischargeEnergyIndicatorService.updateById(indicator);
		return R.success("修改成功");
	}

		@PostMapping(value = "/delete/{id}")
		public R<String> delete(@PathVariable Long id) {
		dischargeEnergyIndicatorService.del(id);
		return R.success("删除成功");
	}
}
