package com.enrising.ctsc.discharge.controller;

import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergyCalculate;
import com.enrising.ctsc.discharge.api.query.DischargeDataEnergyQuery;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo;
import com.enrising.ctsc.discharge.service.DischargeDataEnergyCalculateService;
import lombok.AllArgsConstructor;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.WebRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
* 按规则计算能源数据表 接口
*
* @<NAME_EMAIL>
* @since 2024-09-18
*/
@RestController
@RequestMapping("/discharge/energyCalculate")
@AllArgsConstructor
public class DischargeDataEnergyCalculateController {
    private final DischargeDataEnergyCalculateService dischargeDataEnergyCalculateService;

    @InitBinder
    public void initBinder(WebDataBinder binder, WebRequest request) {
        //转换日期
        DateFormat dateFormat=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
    }

    @PostMapping("/getAllDataList")
    public R<List<DischargeDataEnergyVo>> getAllDataList(@RequestBody DischargeDataEnergyCalculate query) {
        return R.success(dischargeDataEnergyCalculateService.getAllDataList(query));
    }

    /**
     * 保存数据
     * @param dischargeDataEnergyCalculate 数据
     * @return 结果
     */
    @PostMapping
    public R save(@RequestBody @Valid DischargeDataEnergyCalculate dischargeDataEnergyCalculate){
        return R.success(dischargeDataEnergyCalculateService.saveData(dischargeDataEnergyCalculate), "操作成功");
    }

    /**
     * 保存数据列表
     * @param dischargeDataEnergyCalculateList 数据列表
     * @return 结果
     */
    @PostMapping(value = "/saveList")
    public R<String> saveList(@RequestBody List<DischargeDataEnergyCalculate> dischargeDataEnergyCalculateList) {
        boolean iSuc = dischargeDataEnergyCalculateService.saveList(dischargeDataEnergyCalculateList);
        if (iSuc) {
            return R.success("保存成功");
        } else {
            return R.success("保存失败");
        }
    }

    @PostMapping("/getCalcReportList")
    public R<List<DischargeDataEnergyVo>> getCalcReportList(@RequestBody DischargeDataEnergyQuery query) {
        List<DischargeDataEnergyVo> dischargeDataEnergyVoList = dischargeDataEnergyCalculateService.getCalcReportList(query);
        Set<DischargeDataEnergyVo> dataSet = new HashSet<>(dischargeDataEnergyVoList);
        List<DischargeDataEnergyVo> list = new ArrayList<>(dataSet);
        return R.success(list);
    }

    /**
     * 删除
     * @param id 数据ID
     * @return 结果
     */
    @DeleteMapping(value = "/{id}")
    public R delete(@PathVariable Long id){
        return R.success(dischargeDataEnergyCalculateService.removeById(id), "操作成功");
    }

    @PostMapping("/exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, @RequestBody DischargeDataEnergyQuery query) {
        dischargeDataEnergyCalculateService.exportExcel(request,response,query);
    }
}
