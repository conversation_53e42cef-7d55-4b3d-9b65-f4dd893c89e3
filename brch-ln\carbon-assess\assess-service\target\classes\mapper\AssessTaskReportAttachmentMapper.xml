<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.assess.mapper.AssessTaskReportAttachmentMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.task_report_id,
            t.attachment_id,
            t.download_count
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.assess.api.vo.AssessTaskReportAttachmentVo">
        SELECT
        <include refid="baseColumns" />
        FROM assess_task_report_attachment t
        ${ew.customSqlSegment}
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.assess.api.vo.AssessTaskReportAttachmentVo">
        SELECT
        <include refid="baseColumns" />
        FROM assess_task_report_attachment t
        ${ew.customSqlSegment}
        limit 1
    </select>
</mapper>