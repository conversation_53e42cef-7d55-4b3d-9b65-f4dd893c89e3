package com.enrising.ctsc.assess.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.assess.api.bo.AssessTemplateTargetObjectBo;
import com.enrising.ctsc.assess.api.entity.AssessTemplateTarget;
import com.enrising.ctsc.assess.api.vo.AssessTemplateTargetVo;

import java.util.List;

/**
 * 考核模板指标
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-08
 */
public interface AssessTemplateTargetService extends IService<AssessTemplateTarget> {


	/**
	 * 根据模板id查询出所有的指标的考核方式 和 指标公式
	 */
	List<AssessTemplateTargetVo> getTargetAndTemInfo(AssessTemplateTargetObjectBo bo);
}