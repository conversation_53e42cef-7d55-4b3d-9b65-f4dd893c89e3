package com.enrising.ctsc.assess.api.vo;

import com.enrising.ctsc.assess.api.entity.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 考核任务上报表 打分
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2022-12-21
 */
@Data
public class AssessTaskReportScoreDetailVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 上报的任务
     */
    private AssessTaskReportVo taskReport;

    /**
     * 考核模板对象
     */
    private AssessTemplateTargetObject templateTargetObject;

    /**
     * 考核公司
     */
    private String companyName;

    /**
     * 考核部门
     */
    private String deptName;

    /**
     * 模板指标
     */
    private AssessTemplateTarget templateTarget;

    /**
     * 考核模板
     */
    private AssessTemplate assessTemplate;

    /**
     * 考核指标
     */
    private AssessTarget assessTarget;

    /**
     * 考核二级指标
     */
    private AssessTargetSecondary assessTargetSecondary;

    /**
     * 二级指标规则
     */
    private List<AssessTargetSecondaryRule> secondaryRuleList;
}
