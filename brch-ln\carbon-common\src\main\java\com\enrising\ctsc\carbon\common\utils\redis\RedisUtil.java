package com.enrising.ctsc.carbon.common.utils.redis;

import com.enrising.ctsc.carbon.common.utils.CacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.types.Expiration;

import java.util.Map;
import java.util.Random;
import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;

/**
 * Redis的工具类，继承于前辈写的工具类，额外实现了对Redis击穿、雪崩、穿透的保护方法
 *
 * @Auther <PERSON>
 * @Date 2021/09/27 10:36
 */
@Slf4j
public class RedisUtil extends CacheUtils {
    private static final String REDIS_LOCK_PREFIX = "redis_lock_";//redis锁的前缀

    /**
     * 指定缓存有效时间
     *
     * @param key  缓存名
     * @param time 时间，单位为毫秒
     * @return boolean 是否设置成功
     * <AUTHOR> Yongxiang
     * @date 2021/9/27 15:02
     */
    public static boolean setValidTime(String key, long time) {
        try {
            if (time > 0) {
                getRedisTemplate().expire(key, time, TimeUnit.MILLISECONDS);
            }
            return true;
        } catch (Exception e) {
            log.error("错误！设置缓存" + key + "有效时间失败！", e);
            return false;
        }
    }

    public static Boolean hasStringKey(String key) {
        Boolean flag = getStringRedisTemplate().hasKey(key);
        return flag != null && flag;
    }


    /**
     * 获取缓存的有效时间：毫秒
     *
     * @param key 缓存名
     * @return long -1：不存在有效时间;-2：不存在该key;-3：特殊异常
     * <AUTHOR> Yongxiang
     * @date 2021/9/27 15:11
     */
    public static long getValidTime(String key) {
        RedisTemplate<String, Object> redisTemplate = getRedisTemplate();
        Long value = redisTemplate.getExpire(key, TimeUnit.MILLISECONDS);
        return value != null ? value : -1;
    }

    public static boolean hasKey(String key) {
        Boolean flag = getRedisTemplate().hasKey(key);
        return flag != null && flag;
    }

    public static void setList(Map<String, String> values) {
        getRedisTemplate()
                .opsForValue()
                .multiSet(values);
    }

    public static void setList(Map<String, String> values, long expiringTime, TimeUnit unit) {
        getRedisTemplate().executePipelined(new RedisCallback<Object>() {
            @Override
            public Object doInRedis(RedisConnection connection) throws DataAccessException {
                try {
                    connection.openPipeline();
                    for (Map.Entry<String, String> entry : values.entrySet()) {
                        connection.set(entry
                                .getKey()
                                .getBytes(), entry
                                .getValue()
                                .getBytes(), Expiration.from(expiringTime, unit), RedisStringCommands.SetOption.UPSERT);
                    }
                } finally {
                    connection.closePipeline();
                }
                return null;
            }
        });
    }

    public static boolean setValidTimeForString(String key, long time) {
        try {
            if (time > 0) {
                getStringRedisTemplate().expire(key, time, TimeUnit.MILLISECONDS);
            }
            return true;
        } catch (Exception e) {
            log.error("错误！设置缓存{}有效时间失败！", key);
            e.printStackTrace();
            return false;
        }
    }


    /**
     * 给Redis上锁
     *
     * @param keyName        锁名
     * @param systemTime     系统时间戳
     * @param validTime      锁生存时间：毫秒
     * @param maxTryTime     上锁失败后尝试获取最大时间
     * @param sleepBeforeTry 获取失败后休眠时间
     * @return boolean
     * <AUTHOR> Yongxiang
     * @date 2021/9/30 14:25
     */
    public static boolean lock(String keyName, long systemTime, long validTime, long maxTryTime, long sleepBeforeTry) {

        keyName = REDIS_LOCK_PREFIX + keyName;
        while (true) {
            StringRedisTemplate stringRedisTemplate = getStringRedisTemplate();
            Boolean flag = stringRedisTemplate
                    .opsForValue()
                    .setIfAbsent(keyName, String.valueOf(validTime));
            if (flag != null && flag) {
                if (setValidTime(keyName, validTime)) {
                    log.debug("加锁成功，锁名：{}", keyName);
                    return true;
                }
            }
            log.debug(Thread
                    .currentThread()
                    .getName() + "加锁失败！{}ms后进行重新获取...", sleepBeforeTry);
            try {
                Thread.sleep(sleepBeforeTry);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            if (systemTime + maxTryTime < System.currentTimeMillis()) {
                log.debug(Thread
                        .currentThread()
                        .getName() + "加锁最大尝试时间结束，未获取到锁！");
                break;
            }
        }
        return false;
    }

    /**
     * 释放锁
     *
     * @param key 锁名
     * @return boolean
     * <AUTHOR> Yongxiang
     * @date 2021/9/27 17:09
     */
    public static boolean release(String key) {
        try {
            if (setValidTime(key, -1)) {
                getStringRedisTemplate().delete(REDIS_LOCK_PREFIX + key);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        log.debug("Redis锁:{}已被删除！", key);
        return true;
    }

    /**
     * 更新缓存值但不刷新过期时间
     *
     * @param key   缓存名
     * @param value 缓存值
     * @return boolean
     * <AUTHOR> Yongxiang
     * @date 2021/9/29 11:10
     */
    public static boolean setNoRefreshValid(String key, String value) {
        try {
            getStringRedisTemplate()
                    .opsForValue()
                    .set(key, value, getValidTime(key), TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 更新缓存值同时刷新过期时间
     *
     * @param key   缓存名
     * @param value 缓存值
     * @param time  刷新到的时间：毫秒
     * @return boolean
     * <AUTHOR> Yongxiang
     * @date 2021/9/29 11:12
     */
    public static boolean setAndRefreshValid(String key, String value, long time) {
        try {
            getStringRedisTemplate()
                    .opsForValue()
                    .set(key, value, time, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 开辟一个子线程对指定Redis锁进行监测，当其存活时间到达某个阈值时进行存活时间重置
     * 监测线程和业务线程不冲突，监测时业务线程可照常运行，本方法将会返回一个FutureTask对象
     * 该对象在监测结束后将会返回一个标志作为是否监测成功的判断。监听时间应该大于锁的生命
     * 周期，重置阈值请根据实际指定，由于重置时间会消耗一定时间，阈值过小时将会导致锁过期
     *
     * @param lockName   监测的锁名
     * @param percent    重置阈值：0-1，当剩余存活时间低于该阈值时进行存活时间重置
     * @param listenTime 监听时长：毫秒
     * @return java.util.concurrent.FutureTask
     * <AUTHOR> Yongxiang
     * @date 2021/9/29 11:25
     */
    public static FutureTask<Object> listenLock(String lockName, double percent, long listenTime) {

        Callable<Object> callable = () -> {
            log.info("监听器启动，监听lock：{}，预计监听时长：{}ms...", lockName, listenTime);
            long start = System.currentTimeMillis();
            int count = 0;
            while (true) {
                //log.info("监听器第{}轮监听...", count++);
                long now = System.currentTimeMillis();
                if (start + listenTime < now) {
                    log.debug("监听结束，本次监听时长：{}ms", listenTime);
                    return true;
                }
                //目前生存时间
                long valid = getValidTime(REDIS_LOCK_PREFIX + lockName);
                //如果锁已过期或锁已被释放则不再处理
                if (valid == -1 || valid == -2) {
                    log.error("锁已过期，监听失败");
                    return false;
                }
                //原始生存时间
                long time = Long.parseLong(getStr(REDIS_LOCK_PREFIX + lockName));

                if (getValidTime(REDIS_LOCK_PREFIX + lockName) <= percent * time && getValidTime(REDIS_LOCK_PREFIX + lockName) > 0) {
                    if (setAndRefreshValid(REDIS_LOCK_PREFIX + lockName, time + "", time)) {
                        log.debug("达到阈值,对lock:{}进行过期时间重置到{}ms，目前剩余生命时间{}ms!", lockName, time, getValidTime(REDIS_LOCK_PREFIX + lockName));
                    }
                }
            }

        };
        FutureTask<Object> futureTask = new FutureTask<>(callable);
        Thread thread = new Thread(futureTask);
        thread.start();
        return futureTask;
    }

    /**
     * 随机休眠时间后进行get操作
     *
     * @param key          缓存名
     * @param minSleepTime 随机最小时间：毫秒
     * @param maxSleepTime 随机最长时间：毫秒
     * @return java.lang.String
     * <AUTHOR> Yongxiang
     * @date 2021/10/2 14:37
     */
    public static String randomGet(String key, int minSleepTime, int maxSleepTime) {
        Random random = new Random();
        long sleep = random.nextInt(minSleepTime + 1) + (maxSleepTime - minSleepTime);
        try {
            Thread.sleep(sleep);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return getStr("key");
    }

    /**
     * 随机休眠时间后进行set操作
     *
     * @param key          缓存名
     * @param value        缓存值
     * @param minSleepTime 随机最小时间：毫秒
     * @param maxSleepTime 随机最大时间：毫秒
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2021/10/2 17:01
     */
    public static void randomSet(String key, Object value, int minSleepTime, int maxSleepTime) {
        Random random = new Random();
        long sleep = random.nextInt(minSleepTime + 1) + (maxSleepTime - minSleepTime);
        try {
            Thread.sleep(sleep);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        set(key, value);
    }


}
