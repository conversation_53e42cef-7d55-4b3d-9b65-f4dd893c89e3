{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basic\\login\\login.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basic\\login\\login.vue", "mtime": 1753853977878}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": 1741317456427}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,OAAA,MAAA,MAAA,gBAAA;AACA,OAAA,SAAA,MAAA,eAAA;AACA,SAAA,UAAA,EAAA,YAAA,QAAA,MAAA;AAEA,eAAA;AACA,EAAA,UAAA,EAAA;AACA,IAAA,SAAA,EAAA;AADA,GADA;AAIA,EAAA,OAJA,qBAIA;AAAA;;AAEA,SAAA,SAAA;AACA,QAAA,iBAAA,OAAA,CAAA,GAAA,CAAA,QAAA,IAAA,UAAA,MAAA,CAAA,UAAA,EACA,KAAA,SAAA,GAAA,IAAA;AAEA,SAAA,OAAA;AACA,IAAA,MAAA,CAAA,gBAAA,CAAA,QAAA,EAAA,YAAA;AACA,MAAA,KAAA,CAAA,OAAA;AACA,KAFA;AAGA,GAdA;AAeA,EAAA,IAfA,kBAeA;AACA,WAAA;AACA,MAAA,UAAA,EAAA,EADA;AAEA,MAAA,OAAA,EAAA,KAFA;AAGA,MAAA,SAAA,EAAA;AAHA,KAAA;AAKA,GArBA;AAsBA,EAAA,OAAA,oBACA,YAAA,CAAA,CAAA,eAAA,EAAA,QAAA,CAAA,CADA,EAEA,UAAA,CAAA,CAAA,aAAA,EAAA,aAAA,EAAA,cAAA,EAAA,UAAA,CAAA,CAFA;AAGA,IAAA,YAHA,8BAGA,MAHA,EAGA;AAAA;;AAAA,UAAA,QAAA,QAAA,QAAA;AAAA,UAAA,QAAA,QAAA,QAAA;AACA,WAAA,YAAA,CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,YAAA,CAAA,OAAA,CAAA,UAAA,EAAA,GAAA,CAAA,IAAA,CAAA,QAAA;AACA,QAAA,YAAA,CAAA,OAAA,CAAA,SAAA,EAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,YAAA,MAAA,GAAA,IAAA,QAAA,CAAA,UAAA,CAAA,GAAA,CAAA,IAAA,CAAA,QAAA,EAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA;AACA,YAAA,WAAA,GAAA,QAAA,CAAA,KAAA,CAAA,EAAA,EAAA,OAAA,GAAA,IAAA,CAAA,EAAA,CAAA;AACA,YAAA,WAAA,GAAA,QAAA,CAAA,eAAA,CAAA,MAAA,EAAA,WAAA,CAAA;AAEA,YAAA,IAAA,GAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,IAAA;;AAEA,QAAA,MAAA,CAAA,WAAA,CAAA;AACA,UAAA,QAAA,EAAA,QADA;AAEA,UAAA,WAAA,EAAA,WAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA;;AACA,YAAA,MAAA,CAAA,WAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,IAAA,EAAA;AACA,gBAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AACA,kBAAA,IAAA,EAAA,MAAA,CAAA,OAAA,CAAA,QADA;AAEA,kBAAA,MAAA,EAAA;AACA,oBAAA,IAAA,EAAA,YADA;AAEA,oBAAA,SAAA,EAAA;AAFA;AAFA,iBAAA;AAOA,eARA,MAQA;AACA,gBAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AACA,kBAAA,IAAA,EAAA,MAAA,CAAA,OAAA,CAAA,QADA;AAEA,kBAAA,MAAA,EAAA;AACA,oBAAA,SAAA,EAAA;AADA;AAFA,iBAAA;AAMA;;AAEA,cAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA,gBAAA,MAAA,CAAA,aAAA,CAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,MAAA,CAAA;AACA,kBAAA,KAAA,EAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AADA,iBAAA;AAGA,eALA;AAMA,aAxBA;AAyBA,WA3BA,MA2BA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA,UAAA,CAAA,KAAA;AACA;AACA,SAnCA;AAoCA,OA7CA;AA8CA,KAlDA;AAmDA,IAAA,OAnDA,qBAmDA;AAAA;;AACA,WAAA,SAAA,CAAA,YAAA;AACA,YAAA,YAAA,GAAA,QAAA,CAAA,eAAA,CAAA,WAAA;AACA,YAAA,aAAA,GAAA,QAAA,CAAA,eAAA,CAAA,YAAA;AACA,YAAA,kBAAA,GAAA,CAAA,YAAA,GAAA,CAAA,CAAA,YAAA,CAAA,CAAA,KAAA,EAAA,IAAA,CAAA;AACA,YAAA,iBAAA,GAAA,CAAA,aAAA,GAAA,CAAA,CAAA,YAAA,CAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAEA,QAAA,MAAA,CAAA,UAAA,GAAA,UAAA,kBAAA,GAAA,IAAA;AACA,OAPA;AAQA,KA5DA;AA6DA,IAAA,SA7DA,uBA6DA;AAAA;;AACA;AACA,UAAA,IAAA,GAAA,KAAA,MAAA,CAAA,KAAA,CAAA,IAAA;AACA,UAAA,QAAA,GAAA,KAAA,MAAA,CAAA,KAAA,CAAA,QAAA;AACA,UAAA,MAAA,GAAA,KAAA,MAAA,CAAA,KAAA,CAAA,MAAA;AACA,UAAA,IAAA,GAAA,KAAA,MAAA,CAAA,KAAA,CAAA,IAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,EAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,aAAA,EADA,CAEA;;AACA,aAAA,SAAA,GAAA,KAAA;AACA,aAAA,OAAA,GAAA,IAAA;AACA,aAAA,WAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,WAAA,EAAA,IAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAAA,EAAA,IAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kCACA,iBAAA,GAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAAA,MAAA,CAAA,OAAA,GAAA,KAAA;;AACA,wBAAA,GAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,sBAAA,MAAA,CAAA,aAAA,CAAA,IAAA;AACA,qBAFA,MAEA;AACA,sBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA,UAAA,CAAA,KAAA;AACA;;AANA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WADA;;AAAA;AAAA;AAAA;AAAA;AAUA,OAfA,MAeA,IAAA,QAAA,IAAA,IAAA,IAAA,QAAA,IAAA,EAAA,IAAA,MAAA,IAAA,IAAA,IAAA,MAAA,IAAA,EAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,QAAA,EADA,CAEA;;AACA,aAAA,SAAA,GAAA,KAAA;AACA,aAAA,OAAA,GAAA,IAAA;AACA,aAAA,YAAA,CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,MAAA,GAAA,IAAA,QAAA,CAAA,UAAA,CAAA,GAAA,CAAA,IAAA,CAAA,QAAA,EAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA;AACA,cAAA,WAAA,GAAA,MAAA,CAAA,KAAA,CAAA,EAAA,EAAA,OAAA,GAAA,IAAA,CAAA,EAAA,CAAA;AACA,cAAA,WAAA,GAAA,QAAA,CAAA,eAAA,CAAA,MAAA,EAAA,WAAA,CAAA;;AAEA,UAAA,MAAA,CAAA,WAAA,CAAA;AACA,YAAA,QAAA,EAAA,QADA;AAEA,YAAA,WAAA,EAAA,WAFA;AAGA,YAAA,SAAA,EAAA;AAHA,WAAA,EAIA,IAJA;AAAA;AAAA;AAAA;AAAA;AAAA,oCAIA,kBAAA,GAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAAA,MAAA,CAAA,OAAA,GAAA,KAAA;;AACA,0BAAA,GAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,wBAAA,MAAA,CAAA,aAAA,CAAA,IAAA;AACA,uBAFA,MAEA;AACA,wBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA,UAAA,CAAA,KAAA;AACA;;AANA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAJA;;AAAA;AAAA;AAAA;AAAA;AAYA,SAjBA;AAkBA,OAvBA,MAuBA;AACA,aAAA,SAAA,GAAA,IAAA,CADA,CAEA;AACA;AACA;AACA;;AACA;;;AAGA;AACA,KAnHA;AAoHA,IAAA,aApHA,yBAoHA,IApHA,EAoHA;AAAA;;AACA,WAAA,QAAA;AACA,WAAA,WAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AACA,YAAA,IAAA,EAAA,MAAA,CAAA,OAAA,CAAA,QADA;AAEA,YAAA,MAAA,EAAA;AACA,cAAA,IAAA,EAAA,IADA;AAEA,cAAA,SAAA,EAAA;AAFA;AAFA,WAAA;AAOA,SARA,MAQA;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AACA,YAAA,IAAA,EAAA,MAAA,CAAA,OAAA,CAAA,QADA;AAEA,YAAA,MAAA,EAAA;AACA,cAAA,SAAA,EAAA;AADA;AAFA,WAAA;AAMA;;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA,UAAA,MAAA,CAAA,aAAA,CAAA,EAAA;;AACA,UAAA,MAAA,CAAA,MAAA,CAAA;AACA,YAAA,KAAA,EAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AADA,WAAA;AAGA,SALA;AAMA,OAvBA;AAwBA;AA9IA;AAtBA,CAAA", "sourcesContent": ["<style lang=\"less\">\r\n@import \"login.less\";\r\n@import \"login.css\";\r\n</style>\r\n<template>\r\n  <div class=\"login satic-area\">\r\n    <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n    <!--<div class=\"dynamic-area1\"></div>-->\r\n    <!--<div class=\"dynamic-area2\"></div>-->\r\n    <img src=\"../../../assets/images/login_logo_new.png\" class=\"login_logo\" />\r\n    <img src=\"../../../assets/images/login_bottom_bg.png\" class=\"login_bottom_bg\" />\r\n    <img src=\"../../../assets/images/login_bottom_img1.png\" class=\"login_bottom_img1\" />\r\n    <img src=\"../../../assets/images/login_bottom_img2.png\" class=\"login_bottom_img2\" />\r\n    <p class=\"login_copyright\">中通服创立信息科技有限责任公司</p>\r\n    <div v-if=\"showLogin\" class=\"login-con\" id=\"login-con\" :style=\"loginStyle\">\r\n      <!--<img src=\"../../../assets/images/login_fly.png\" class=\"login_fly\"/>\r\n            <img src=\"../../../assets/images/login_fly_light.png\" class=\"login_fly_light\"/>-->\r\n      <!--<Card dis-hover class=\"login-card\" icon=\"log-in\" title=\"欢迎登录\" :bordered=\"false\">-->\r\n      <div class=\"form-con\">\r\n        <login-form ref=\"loginForm\" @on-success-valid=\"handleSubmit\"></login-form>\r\n        <!--<p class=\"login-tip\">中通服创立信息科技有限责任公司</p>-->\r\n      </div>\r\n      <!-- </Card> -->\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport config from \"@/config/index\";\r\nimport LoginForm from \"_c/login-form\";\r\nimport { mapActions, mapMutations } from \"vuex\";\r\n\r\nexport default {\r\n  components: {\r\n    LoginForm,\r\n  },\r\n  mounted() {\r\n\r\n    this.login_sso();\r\n    if (\"development\" == process.env.NODE_ENV || \"dev\" === config.deploy_env)\r\n      this.showLogin = true;\r\n\r\n    this.reStyle();\r\n    window.addEventListener(\"resize\", () => {\r\n      this.reStyle();\r\n    });\r\n  },\r\n  data() {\r\n    return {\r\n      loginStyle: \"\",\r\n      loading: false,\r\n      showLogin: false,\r\n    };\r\n  },\r\n  methods: {\r\n    ...mapMutations([\"setTagNavList\", \"addTag\"]),\r\n    ...mapActions([\"handleLogin\", \"getUserInfo\", \"getPublicKey\", \"dictInit\"]),\r\n    handleSubmit({ userName, password }, button) {\r\n      this.getPublicKey(userName).then((res) => {\r\n        localStorage.setItem(\"exponent\", res.data.exponent);\r\n        localStorage.setItem(\"modulus\", res.data.modulus);\r\n        const pwdKey = new RSAUtils.getKeyPair(res.data.exponent, \"\", res.data.modulus);\r\n        const reversedPwd = password.split(\"\").reverse().join(\"\");\r\n        const encrypedPwd = RSAUtils.encryptedString(pwdKey, reversedPwd);\r\n\r\n        let page = this.$route.query.page;\r\n\r\n        this.handleLogin({\r\n          userName: userName,\r\n          encrypedPwd: encrypedPwd,\r\n          loginType: \"0\",\r\n        }).then((res) => {\r\n          if (res.code == 0) {\r\n            this.dictInit();\r\n            this.getUserInfo().then((res) => {\r\n              if (page) {\r\n                this.$router.push({\r\n                  name: this.$config.homeName,\r\n                  params: {\r\n                    page: \"wfProcInst\",\r\n                    fromLogin: true,\r\n                  },\r\n                });\r\n              } else {\r\n                this.$router.push({\r\n                  name: this.$config.homeName,\r\n                  params: {\r\n                    fromLogin: true,\r\n                  },\r\n                });\r\n              }\r\n\r\n              this.$nextTick(() => {\r\n                this.setTagNavList([]);\r\n                this.addTag({\r\n                  route: this.$store.state.app.homeRoute,\r\n                });\r\n              });\r\n            });\r\n          } else {\r\n            this.$refs.loginForm.buttonLoad(false);\r\n          }\r\n        });\r\n      });\r\n    },\r\n    reStyle() {\r\n      this.$nextTick(() => {\r\n        var window_width = document.documentElement.clientWidth;\r\n        var window_height = document.documentElement.clientHeight;\r\n        var login_content_left = (window_width - $(\".login-con\").width()) / 2;\r\n        var login_content_top = (window_height - $(\".login-con\").height()) / 2;\r\n\r\n        this.loginStyle = \"left:\" + login_content_left + \"px\";\r\n      });\r\n    },\r\n    login_sso() {\r\n      // 辽宁 单点登录\r\n      let code = this.$route.query.code;\r\n      let userName = this.$route.query.UserName;\r\n      let appKey = this.$route.query.AppKey;\r\n      let page = this.$route.query.page;\r\n      if (code != null && code != \"\") {\r\n        console.log(\"省认证中心统一认证登录\")\r\n        //统一认证单点登录\r\n        this.showLogin = false;\r\n        this.loading = true;\r\n        this.handleLogin({ userName: code, encrypedPwd: code, loginType: \"ln\" }).then(\r\n          async (res) => {\r\n            this.loading = false;\r\n            if (res.code == 0) {\r\n              this.login_success(null);\r\n            } else {\r\n              this.$refs.loginForm.buttonLoad(false);\r\n            }\r\n          }\r\n        );\r\n      } else if (userName != null && userName != \"\" && appKey != null && appKey != \"\") {\r\n        console.log(\"OA单点登录\")\r\n        //原有OA单点登录\r\n        this.showLogin = false;\r\n        this.loading = true;\r\n        this.getPublicKey(userName).then((res) => {\r\n          const pwdKey = new RSAUtils.getKeyPair(res.data.exponent, \"\", res.data.modulus);\r\n          const reversedPwd = appKey.split(\"\").reverse().join(\"\");\r\n          const encrypedPwd = RSAUtils.encryptedString(pwdKey, reversedPwd);\r\n\r\n          this.handleLogin({\r\n            userName: userName,\r\n            encrypedPwd: encrypedPwd,\r\n            loginType: \"1\",\r\n          }).then(async (res) => {\r\n            this.loading = false;\r\n            if (res.code == 0) {\r\n              this.login_success(page);\r\n            } else {\r\n              this.$refs.loginForm.buttonLoad(false);\r\n            }\r\n          });\r\n        });\r\n      } else {\r\n        this.showLogin = true;\r\n        // if (\"production\" == process.env.NODE_ENV && \"dev\" !== config.deploy_env)\r\n        //   window.location.href =\r\n        //     \"http://eam.sc.ctc.com:8002/eam-apps/oauth/authorize?client_id=CTSCNHXT20210819&response_type=code&redirect_uri=http://172.16.47.127:80/login\";\r\n        //alert(\"pc href:\"+window.location.href);\r\n        /*if(\"production\"==process.env.NODE_ENV&&\"dev\"!==config.deploy_env)\r\n                        window.location.href=\"http://uamportal.paas.sc.ctc.com:22002？redirect_uri=http://172.16.47.127:80/login\"\r\n*/\r\n      }\r\n    },\r\n    login_success(page) {\r\n      this.dictInit();\r\n      this.getUserInfo().then((res) => {\r\n        if (page) {\r\n          this.$router.push({\r\n            name: this.$config.homeName,\r\n            params: {\r\n              page: page,\r\n              fromLogin: true,\r\n            },\r\n          });\r\n        } else {\r\n          this.$router.push({\r\n            name: this.$config.homeName,\r\n            params: {\r\n              fromLogin: true,\r\n            },\r\n          });\r\n        }\r\n        this.$nextTick(() => {\r\n          this.setTagNavList([]);\r\n          this.addTag({\r\n            route: this.$store.state.app.homeRoute,\r\n          });\r\n        });\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style></style>\r\n"], "sourceRoot": "src/view/basic/login"}]}