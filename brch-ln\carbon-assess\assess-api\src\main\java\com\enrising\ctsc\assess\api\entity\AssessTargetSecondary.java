package com.enrising.ctsc.assess.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 考核二级指标
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */

@Data
@TableName("assess_target_secondary")
public class AssessTargetSecondary extends Model<AssessTargetSecondary> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 所属一级指标id
     */
    private Long primaryTargetId;

    /**
     * 指标名称
     */
    private String targetName;

    /**
     * 指标分值
     */
    private Double score;

    /**
     * 指标说明
     */
    private String targetDescription;

    /**
     * 指标算法
     */
    private String algorithm;

    /**
     * 考核规则说明
     */
    private String rulesDescription;

    /**
     * 指标公式
     */
    private String formula;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 考核周期
     */
    private String assessPeriod;
}
