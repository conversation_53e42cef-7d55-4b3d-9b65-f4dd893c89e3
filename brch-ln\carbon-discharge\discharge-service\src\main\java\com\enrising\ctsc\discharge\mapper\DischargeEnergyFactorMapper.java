package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyFactor;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyFactorOpenBo;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyFactorQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyFactorVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 碳排放能源转换因子表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Mapper
public interface DischargeEnergyFactorMapper extends BaseMapper<DischargeEnergyFactor> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
	IPage<DischargeEnergyFactorVo> findList(Page<DischargeEnergyFactorVo> page, @Param(Constants.WRAPPER) Wrapper<DischargeEnergyFactorQuery> wrapper);

	/**
	 * 列表查询
	 *
	 * @param wrapper 条件
	 * @return 列表
	 */
	List<DischargeEnergyFactorVo> findList(@Param(Constants.WRAPPER) Wrapper<DischargeEnergyFactorQuery> wrapper);

	/**
	 * 详情查询
	 *
	 * @param wrapper 条件
	 * @return 结果
	 */
	DischargeEnergyFactorVo detail(@Param(Constants.WRAPPER) Wrapper<DischargeEnergyFactorQuery> wrapper);

	DischargeEnergyFactorVo getGainFactor(@Param("bo") DischargeEnergyFactorOpenBo bo);
}