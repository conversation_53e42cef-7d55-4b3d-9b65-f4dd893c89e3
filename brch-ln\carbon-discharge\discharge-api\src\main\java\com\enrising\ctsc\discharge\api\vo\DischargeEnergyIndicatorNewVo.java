package com.enrising.ctsc.discharge.api.vo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放能源指标表
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-23
 */
@Data
public class DischargeEnergyIndicatorNewVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
	private Long id;

	/**
	 * 创建者id
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date createTime;

	/**
	 * 更新者id
	 */
	private Long updateBy;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date updateTime;

	/**
	 * 能源指标类型编号
	 */
	private String indicatorCode;

	/**
	 * 能源指标类型名称
	 */
	private String indicatorName;

	/**
	 * 能源类型id
	 */
	private Long energyTypeId;

	/**
	 * 能源类型名称
	 */
	private String energyTypeName;

	/**
	 * 集团数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String groupInputType;

	/**
	 * 股份数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String stockInputType;

	/**
	 * 大型数据中心数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String largeInputType;

	/**
	 * 中小型数据中心数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String mediumInputType;

	/**
	 * 移动业务数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String mobileInputType;

	/**
	 * 状态，1-启用，2-禁用
	 */
	private String status;

	/**
	 * 父指标id
	 */
	private Long parentId;

	/**
	 * 父指标编号
	 */
	private String parentCode;

	/**
	 * 父指标名称
	 */
	private String parentName;
	/**
	 * 排序值
	 */
	private Integer sort;

	/**
	 * 单位名称
	 */
	private String unitName;

	/**
	 * 单位描述
	 */
	private String unitDescription;

	/**
	 * 集团数据
	 */
		private BigDecimal groupData;

	/**
	 * 股份数据
	 */
		private BigDecimal stockData;

	/**
	 * 大型数据中心数据
	 */
		private BigDecimal largeData;

	/**
	 * 中小型数据中心数据
	 */
		private BigDecimal mediumData;

	/**
	 * 移动业务数据
	 */
		private BigDecimal mobileData;

	/**
	 * 填报时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
		private Date reportTime;

	/**
	 * 填报单位id
	 */
		private Long companyId;

	/**
	 * 上报标志
	 */
		private String reportFlag;
}
