package com.enrising.ctsc.discharge.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.discharge.api.bo.DischargeDataExamineBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataExamine;
import com.enrising.ctsc.discharge.api.enums.DischargeCarbonDataType;
import com.enrising.ctsc.carbon.common.utils.ExcelUtil;
import com.enrising.ctsc.carbon.common.utils.JwtUtils;
import com.enrising.ctsc.carbon.common.utils.NumberFormatUtils;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.discharge.api.vo.DischargeDataExamineVo;
import com.enrising.ctsc.discharge.mapper.DischargeDataExamineMapper;
import com.enrising.ctsc.discharge.service.DischargeDataExamineService;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 碳排放能源指标表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Service
@Slf4j
@AllArgsConstructor
public class DischargeDataExamineServiceImpl extends ServiceImpl<DischargeDataExamineMapper, DischargeDataExamine> implements DischargeDataExamineService {

    private DischargeDataExamineMapper dischargeDataExamineMapper;

//    private RemoteAdminService remoteAdminService;

	@Override
	public List<DischargeDataExamineVo> getList(DischargeDataExamineBo bo) {
		List<DischargeDataExamineVo> list = dischargeDataExamineMapper.getList(bo);
		if(CollectionUtil.isEmpty(list)){
			return new ArrayList<>();
		}
	    list.forEach(node->{
		   node.setUnitName(node.getUnitDescription()+"("+node.getUnitName()+")");
		   switch (node.getExamineName()) {
			   case "煤炭":
				   node.setCarbonTotal(node.getFactor().multiply(node.getUseTotal()));
				   break;
			   case "外购火电":
				   // 外购火电对=1.3耗电量总-1.3.3可再生能源使用量  自有新能源对应1.3.3可再生能源使用量
				   DischargeDataExamineVo dischargeDataExamineVo = list.stream().filter(item -> {
					   return item.getExamineName().equals(DischargeCarbonDataType.OWN_GREEN_POWER.getName());
				   }).findFirst().orElse(new DischargeDataExamineVo());

				   BigDecimal subtract = node.getUseTotal()
						   .subtract(dischargeDataExamineVo.getUseTotal());
				   node.setCarbonTotal(NumberFormatUtils.formatValue(subtract
						   .multiply(node.getFactor())
				   ));
				   break;
			   case "外购热力":
				   // 热力*1000
				   node.setCarbonTotal(node.getFactor().multiply(node.getUseTotal()).multiply(BigDecimal.valueOf(1000)));
				   break;
			   case "固定源—液化石油气":
				   // 1.9液化石油气*1000，单位改成kg（千克）
				   node.setCarbonTotal(NumberFormatUtils.formatValue(node.getFactor()
						   .multiply(node.getUseTotal())));
				   node.setUnitName("kg(千克)");
				   break;
			   default:
				   node.setCarbonTotal(NumberFormatUtils.formatValue(node.getFactor()
						   .multiply(node.getUseTotal()))
				   );
		   }
		   node.setCarbonTotal(node.getCarbonTotal().setScale(2,RoundingMode.HALF_UP));
	    });
		return list;
	}

	@Override
	public void saveList(List<DischargeDataExamineBo> boList) {
		List<DischargeDataExamine> list = new ArrayList<>();
//		Long companyId = Objects.requireNonNull(SecurityUtils.getSysUser()).getDeptId();
		Long companyId = Objects.requireNonNull(JwtUtils.getCurrentUserCompanyId());
		int year = DateUtil.year(new Date());
		List<Long> energyExamineIds = boList.stream().map(DischargeDataExamineBo::getId).collect(Collectors.toList());
		// 先删除后添加
		dischargeDataExamineMapper.delete(new LambdaQueryWrapper<DischargeDataExamine>()
		         .eq(DischargeDataExamine::getCompanyId,companyId)
				 .eq(DischargeDataExamine::getReportTime,String.valueOf(year))
				 .in(DischargeDataExamine::getEnergyExamineId,energyExamineIds)
		);
		boList.forEach(node->{
			DischargeDataExamine dataExamine = new DischargeDataExamine();
			dataExamine.setUse(node.getUse());
			dataExamine.setCompanyId(companyId);
			dataExamine.setReportTime(String.valueOf(year));
			dataExamine.setEnergyExamineId(node.getId());
			list.add(dataExamine);
		});
		saveBatch(list);
	}

	@Override
	@SneakyThrows
	public void download(HttpServletRequest request, HttpServletResponse response, DischargeDataExamineBo bo) {
		List<DischargeDataExamineVo> list = getList(bo);
		if(CollectionUtil.isEmpty(list)){
			return;
		}
		String companyName = ObjectUtil.isNull(bo.getCompanyId()) ? "全省" :
				dischargeDataExamineMapper.getCompanyNameById(bo.getCompanyId());
		String year = StrUtil.isBlank(bo.getReportTime())?String.valueOf(DateUtil.year(new Date())):
				 bo.getReportTime();
		final BigDecimal[] total = {new BigDecimal("0")};

		list.forEach(node->{
			if(ObjectUtil.isNotNull(node.getCarbonTotal())){
				total[0] = total[0].add(node.getCarbonTotal());
			}
		});
		ExportParams exportParams = new ExportParams();
		exportParams.setTitle(year+"年"+companyName+"碳盘查数据 "+"碳排放总量： "+ total[0] +"tCO₂");
		exportParams.setSheetName("碳盘查数据");
		Workbook workbook = ExcelExportUtil.exportExcel(exportParams,
				DischargeDataExamineVo.class, list);
		//实现页面下载
		ExcelUtil.setResponseHeader(request,response,"碳盘查数据.xls");
		//创建页面输出流对象
		ServletOutputStream outputStream = response.getOutputStream();
		//把文件写入输出流的对象中
		workbook.write(outputStream);
		outputStream.close();
	}
}
