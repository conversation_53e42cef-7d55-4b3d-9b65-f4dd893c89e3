package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.discharge.api.bo.DischargeDataHeatOilBo;
import com.enrising.ctsc.discharge.api.bo.DischargeDataHeatOilExcel;
import com.enrising.ctsc.discharge.api.entity.DischargeDataHeatOil;
import com.enrising.ctsc.discharge.api.vo.DischargeDataHeatOilVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 热力燃油数据表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2024-12-20
 */
public interface DischargeDataHeatOilService extends IService<DischargeDataHeatOil> {

	/**
	 * 列表查询
	 *
	 * @param dischargeDataHeatOilBo 查询参数
	 * @return 列表
	 */
	List<DischargeDataHeatOilVo> getDataList(DischargeDataHeatOilBo dischargeDataHeatOilBo);

	/**
	 * 详情
	 *
	 * @param id 参数
	 * @return 详情
	 */
	DischargeDataHeatOilVo detail(Long id);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	String add(DischargeDataHeatOilBo bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	void edit(DischargeDataHeatOilBo bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	void del(Long id);

	List<DischargeDataHeatOilExcel> importExcel(MultipartFile file) throws Exception;

	String addBatch(List<DischargeDataHeatOilBo> boList);

	void downloadTemplate(HttpServletRequest request, HttpServletResponse response);
}
