package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.entity.DischargeDataHeatOil;
import com.enrising.ctsc.discharge.api.query.DischargeDataGasQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeDataGasVo;
import com.enrising.ctsc.discharge.api.vo.DischargeDataHeatOilVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 热力燃油数据
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2024-12-20
 */
@Mapper
public interface DischargeDataHeatOilMapper extends BaseMapper<DischargeDataHeatOil> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param dischargeDataHeatOil 查询件
	 * @return 列表
	 */
	IPage<DischargeDataHeatOilVo> findList(Page<DischargeDataHeatOilVo> page, DischargeDataHeatOil dischargeDataHeatOil);

	/**
	 * 列表查询
	 *
	 * @param dischargeDataHeatOil 查询条件
	 * @return 列表
	 */
	List<DischargeDataHeatOilVo> findList(DischargeDataHeatOil dischargeDataHeatOil);

	/**
	 * 详情查询
	 *
	 * @param dischargeDataHeatOil 条件
	 * @return 结果
	 */
	DischargeDataHeatOilVo detail(DischargeDataHeatOil dischargeDataHeatOil);

	Long getCompanyIdByName(@Param("companyName") String companyName);
}