package com.enrising.ctsc.discharge.api.vo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放能源转换因子表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeEnergyFactorVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
	private Long id;

	/**
	 * 创建者id
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date createTime;

	/**
	 * 更新者id
	 */
	private Long updateBy;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date updateTime;

	/**
	 * 能源类型id
	 */
	private Long energyTypeId;

	/**
	 * 数据有效期起始日期
	 */
	@JsonFormat(pattern = DateUtils.YYYY)
	private Date validityStart;

	/**
	 * 数据有效期结束日期
	 */
	@JsonFormat(pattern = DateUtils.YYYY)
	private Date validityEnd;

	/**
	 * 转换因子
	 */
	private BigDecimal factor;

	/**
	 * 数据标准来源
	 */
	private String source;

	/**
	 * 能源类型
	 */
	private String energyType;

	/**
	 * 能源类型单位
	 */
	private String energyTypeUnit;

	/**
	 * 能源细类名称
	 */
	private String energySecondName;


	/**
	 * 低位发热量
	 */
	private BigDecimal netCalorificPower;

	/**
	 * 单位热值含碳量
	 */
	private BigDecimal carbonPerUnitCalorificValue;

	/**
	 * 碳氧化率
	 */
	private BigDecimal carbonOxidationRate;
}
