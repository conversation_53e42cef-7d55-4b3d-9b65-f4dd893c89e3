package com.enrising.ctsc.assess.api.bo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 考核任务上报表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
public class AssessTaskReportBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 考核任务id
     */
    @NotNull(message = "模板id不能为空！", groups = {report.class})
    private Long templateObjectId;

    /**
     * 考核任务id
     */
    private Long templateId;

    /**
     * 考核指标id
     */
    private Long targetId;

    /**
     * 上报名称
     */
    @NotBlank(message = "上报名称不能为空！", groups = {report.class})
    private String reportName;

    /**
     * 上报说明
     */
    @NotBlank(message = "上报说明不能为空！", groups = {report.class})
    private String reportDescription;

    /**
     * 考核评分
     */
    private Double assessScore;

    /**
     * 考核评价
     */
    private String assessEvaluate;

    /**
     * 按规则得分
     */
    private Double ruleScore;

    /**
     * 考评人
     */
    private Long assesser;

    /**
     * 考评时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date assessTime;

    /**
     * 上报状态：0-未上报；1-已上报
     */
    private String reportStatus;

    /**
     * 文件ids
     */
    @NotEmpty(message = "相关资料不能为空！", groups = {report.class})
    private List<Long> fileIds;

    /**
     * 上报校验
     */
    public interface report {
    }

    ;

    /**
     * 报告类型 1-1份报告 2-3份报告 3-4份报告  4-12份报告
     */
    private String reportType;

    /**
     * 上报时间
     */
    private Integer reportTime;

}
