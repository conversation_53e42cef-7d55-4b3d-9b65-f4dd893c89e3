package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.discharge.api.bo.DischargeEnergyTypeSave;
import com.enrising.ctsc.discharge.api.enums.DelFlagEnum;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.bo.DischargeEnergyTypeBo;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyType;
import com.enrising.ctsc.discharge.api.enums.EnableStatus;
import com.enrising.ctsc.discharge.api.enums.EnergySizeType;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyTypeQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyTypeVo;
import com.enrising.ctsc.discharge.mapper.DischargeEnergyTypeMapper;
import com.enrising.ctsc.discharge.service.DischargeEnergyTypeService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 碳排放能源类型表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Service
@AllArgsConstructor
public class DischargeEnergyTypeServiceImpl extends ServiceImpl<DischargeEnergyTypeMapper, DischargeEnergyType> implements DischargeEnergyTypeService {

	@Override
	public TableDataInfo<DischargeEnergyTypeVo> findList(Page<DischargeEnergyTypeVo> page, DischargeEnergyTypeQuery query) {
		QueryWrapper<DischargeEnergyTypeQuery> wrapper = this.getWrapper(query);
		IPage<DischargeEnergyTypeVo> resultPage = baseMapper.findList(page, wrapper);
		int i = 0;
		while (i < resultPage.getRecords().size()) {
			String energyTpe = resultPage.getRecords().get(i).getEnergyType();
			int index = i;
			for (int j = i + 1; j < resultPage.getRecords().size(); j ++) {
				DischargeEnergyTypeVo dischargeEnergyTypeVo = resultPage.getRecords().get(j);
				if (energyTpe.equals(dischargeEnergyTypeVo.getEnergyType())) {
					index ++;
					resultPage.getRecords().remove(j);
					resultPage.getRecords().add(index, dischargeEnergyTypeVo);
				}
			}
			i = index + 1;
		}
		return TableDataInfo.build(resultPage);
	}

	@Override
	public DischargeEnergyTypeVo detail(DischargeEnergyTypeQuery query) {
		if (ObjectUtil.allFieldIsNull(query)) {
			throw new BusinessException("查询参数不能为空");
		}
		QueryWrapper<DischargeEnergyTypeQuery> wrapper = this.getWrapper(query);
		return baseMapper.detail(wrapper);
	}

	private QueryWrapper<DischargeEnergyTypeQuery> getWrapper(DischargeEnergyTypeQuery query) {
		QueryWrapper<DischargeEnergyTypeQuery> wrapper = new QueryWrapper<>();
		wrapper.eq(query.getId() != null, "t.id", query.getId());
		wrapper.eq("t.del_flag", DelFlagEnum.NOT_DELETED.getValue());
		wrapper.like(StrUtil.isNotBlank(query.getKeys()), "t.second_name", query.getKeys());
		wrapper.eq(StrUtil.isNotBlank(query.getStatus()), "t.status", query.getStatus());
		wrapper.orderByDesc("t.create_time");
		return wrapper;
	}

	@Override
	public void add(DischargeEnergyTypeSave bo) {
		for (int i = 0; i < bo.getList().size(); i++) {
			DischargeEnergyTypeBo typeBo;
			// 只有大类的时候
			if (i == 0 && bo.getList().size() == 1) {
				typeBo = bo.getList().get(0);
				this.checkUnit(typeBo);
				this.setTypeBo(typeBo);
				typeBo.setSizeType(EnergySizeType.BIG.getValue());
			}
			// 设置了小类
			else {
				if (i > 0) {
					typeBo = bo.getList().get(i);
					this.checkUnit(typeBo);
					this.setTypeBo(typeBo);
					typeBo.setSizeType(EnergySizeType.LITTLE.getValue());
				}
			}
		}
	}

	/**
	 * 属性赋值
	 *
	 * @param typeBo 参数
	 */
	private void setTypeBo(DischargeEnergyTypeBo typeBo) {
		typeBo.setStatus(EnableStatus.ENABLE.getValue());
		DischargeEnergyType entity = new DischargeEnergyType();
		BeanUtils.copyProperties(typeBo, entity);
		this.save(entity);
	}

	/**
	 * 属性值校验
	 *
	 * @param bo 参数
	 */
	private void checkUnit(DischargeEnergyTypeBo bo) {
		if (StrUtil.isBlank(bo.getUnit())) {
			throw new BusinessException("能源单位不能为空");
		}
		// todo 记得改	升的密度不能为空
		if ("升".equals(bo.getUnit()) && null == bo.getDensity()) {
			throw new BusinessException("密度不能为空");
		}
	}

	@Override
	public void edit(DischargeEnergyTypeSave bo) {
		String energyType = bo.getList().get(0).getEnergyType();
		DischargeEnergyType first = this.getOne(Wrappers.<DischargeEnergyType>lambdaQuery()
				.eq(DischargeEnergyType::getEnergyType, energyType)
				.orderByAsc(DischargeEnergyType::getCreateTime)
				.last("limit 1")
		);
		this.remove(Wrappers.<DischargeEnergyType>lambdaQuery().eq(DischargeEnergyType::getEnergyType, energyType));
		// 保留之前的创建时间	——需求
		bo.getList().forEach(item -> {
			item.setCreateTime(first.getCreateTime());
		});
		this.add(bo);
	}

	@Override
	public void del(Long id) {
		baseMapper.deleteById(id);
	}

	/**
	 * 能源类型列表查询
	 *
	 * @param energyType 字典能源类型
	 * @return 列表
	 */
	@Override
	public List<DischargeEnergyType> getEnergyTypeList(String energyType) {
		List<DischargeEnergyType> dischargeEnergyTypeList = this.list(new LambdaQueryWrapper<DischargeEnergyType>()
				.ge(DischargeEnergyType::getEnergyType, energyType)
				.orderByAsc(DischargeEnergyType::getId));
		return dischargeEnergyTypeList;
	}

	/**
	 * 能源类型密度查询
	 *
	 * @param energyTypeId 能源类型id
	 * @return 能源类型密度
	 */
	public BigDecimal getEnergyDensity(Long energyTypeId) {
		DischargeEnergyType dischargeEnergyType = baseMapper.selectById(energyTypeId);
		if (ObjectUtil.isNotEmpty(dischargeEnergyType)) {
			return dischargeEnergyType.getDensity();
		}
		return new BigDecimal(0);
	}
}
