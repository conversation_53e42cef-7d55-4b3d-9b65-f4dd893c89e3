/*
 * Copyright (c) 2021 ctsc-cloudx Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.enrising.ctsc.discharge.api.utils;

import com.enrising.ctsc.discharge.api.bo.TreeNode;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-5-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorTree extends TreeNode implements Serializable {

	/**
	 * 能源指标类型编号
	 */
	private String indicatorCode;

	/**
	 * 能源指标类型名称
	 */
	private String indicatorName;

	/**
	 * 能源类型id
	 */
	private Long energyTypeId;

	/**
	 * 能源类型名称
	 */
	private String energyTypeName;

	/**
	 * 集团数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String groupInputType;

	/**
	 * 股份数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String stockInputType;

	/**
	 * 大型数据中心数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String largeInputType;

	/**
	 * 中小型数据中心数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String mediumInputType;

	/**
	 * 移动业务数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String mobileInputType;

	/**
	 * 状态，1-启用，2-禁用
	 */
	private String status;

	/**
	 * 排序值
	 */
		private Integer sort;

	/**
	 * 单位名称
	 */
	private String unitName;

	/**
	 * 单位描述
	 */
	private String unitDescription;

	private boolean spread = true;
	/**
	 * 上级指标id
	 */
	private Long parentId;

	/**
	 * 上级指标编号
	 */
	private String parentCode;

	/**
	 * 上级指标名称
	 */
	private String parentName;

	/**
	 * 是否包含子节点
	 *
	 */
	private Boolean hasChildren;

	public IndicatorTree() {
	}

	public IndicatorTree(Long id, String name, Long parentId) {
		this.id = id;
		this.parentId = parentId;
		this.indicatorName = name;
	}

	public IndicatorTree(Long id, String name, IndicatorTree parent) {
		this.id = id;
		this.parentId = parent.getId();
		this.indicatorName = name;
	}

	public IndicatorTree(DischargeEnergyIndicatorVo dischargeEnergyIndicatorVo) {
		this.id = dischargeEnergyIndicatorVo.getId();
		this.parentId = dischargeEnergyIndicatorVo.getParentId();
		this.indicatorName = dischargeEnergyIndicatorVo.getIndicatorName();
		this.sort = dischargeEnergyIndicatorVo.getSort();
		this.status = dischargeEnergyIndicatorVo.getStatus();
//		this.energyTypeId = dischargeEnergyIndicatorVo.gete();
		this.unitName = dischargeEnergyIndicatorVo.getUnitName();
		this.unitDescription = dischargeEnergyIndicatorVo.getUnitDescription();
		this.hasChildren = false;
	}

}
