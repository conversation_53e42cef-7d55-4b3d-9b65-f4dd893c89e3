package com.enrising.ctsc.assess.api.vo;


import lombok.Data;

import java.util.List;

@Data
public class RankRateInfoVo {


    /**
     * 责任部门
     */
    private Long deptId;

    /**
     * 考核对象
     */
    private String companyName;

    /**
     * 考核对象
     */
    private String deptName;

    /**
     * 指标类型
     */
    private Long targetCategory;

    /**
     * 指标名称
     */
    private String targetName;

    /**
     * 指标分值
     */
    private double score = 0;

    /**
     * 考核成绩
     */
    private double assessScore = 0;

    /**
     * 排名
     */
    private String rankNum;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 二级指标id
     */
    private Long secondaryTargetId;

    /**
     * 二级指标id
     */
    private List<Long> secondaryTargetIds;

    /**
     * 指标公式
     */
    private String formula;

    /**
     * 考核方式
     */
    private String assessMethod;

}
