package com.enrising.ctsc.carbon.common.service;

import com.enrising.ctsc.carbon.common.entity.CarbonAttachment;
import com.enrising.ctsc.carbon.common.entity.User;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 通用服务接口
 * <AUTHOR>
 */
public interface CommonService {
	/**
	 * 获取当前用户
	 * @return 返回当前用户
	 */
	User getCurrentUser();

	/**
	 * 批量下载附件
	 * @param attachmentList 附件列表
	 * @return
	 */
	void downloadAttachmentFile(HttpServletResponse response, List<CarbonAttachment> attachmentList);

	/**
	 * 下载单个附件
	 * @param attachment 附件
	 * @return
	 */
	void downloadSingleAttachmentFile(HttpServletResponse response, CarbonAttachment attachment);

	/**
	 * 附件预览
	 * @param attachment 附件
	 * @return 附件预览url
	 */
	String previewAttachmentFile(CarbonAttachment attachment);

	/**
	 * 根据附件URL获取附件预览
	 * @param fileUrl 附件URL
	 * @return 附件预览url
	 */
	String getPreviewUrlByUrl(String fileUrl);

	User getUserById(Long id);
}
