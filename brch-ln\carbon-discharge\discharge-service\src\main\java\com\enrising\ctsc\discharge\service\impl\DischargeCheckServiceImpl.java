package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.discharge.api.bo.DischargeCheckBo;
import com.enrising.ctsc.discharge.api.entity.DischargeCheck;
import com.enrising.ctsc.discharge.api.enums.DelFlagEnum;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.discharge.api.query.DischargeCheckQuery;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.vo.DischargeCheckVo;
import com.enrising.ctsc.discharge.mapper.DischargeCheckMapper;
import com.enrising.ctsc.discharge.service.DischargeCheckService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 碳排放核查
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Service
@AllArgsConstructor
public class DischargeCheckServiceImpl extends ServiceImpl<DischargeCheckMapper, DischargeCheck> implements DischargeCheckService {

//	private final RemoteAdminService remoteAdminService;

	@Override
	public TableDataInfo<DischargeCheckVo> findList(Page<DischargeCheckVo> page, DischargeCheckQuery query) {
		QueryWrapper<DischargeCheckQuery> wrapper = this.getWrapper(query);
		IPage<DischargeCheckVo> resultPage = baseMapper.findList(page, wrapper);
        if(CollectionUtil.isNotEmpty(resultPage.getRecords())){
			List<DischargeCheckVo> records = resultPage.getRecords();
//			todo
//			records.forEach(node -> {
//				List<Attachment> attachmentList = remoteAdminService.getListByIds(Collections.singletonList(node.getReportAttachmentId()));
//
//				if(CollectionUtil.isNotEmpty(attachmentList)){
//					node.setFileName(attachmentList.get(0).getFileName());
//					node.setUrl(attachmentList.get(0).getSavedUrl());
//				}
//			});
		}

		return TableDataInfo.build(resultPage);
	}

	@Override
	public DischargeCheckVo detail(DischargeCheckQuery query) {
		if (ObjectUtil.allFieldIsNull(query)) {
			throw new BusinessException("查询参数不能为空");
		}
		QueryWrapper<DischargeCheckQuery> wrapper = this.getWrapper(query);
		DischargeCheckVo detail = baseMapper.detail(wrapper);
//			todo
//		List<Attachment> attachmentList = remoteAdminService.getListByIds(Collections.singletonList(detail.getReportAttachmentId()));
//		detail.setAttachmentList(attachmentList);
		return detail;
	}

	private QueryWrapper<DischargeCheckQuery> getWrapper(DischargeCheckQuery query) {
		QueryWrapper<DischargeCheckQuery> wrapper = new QueryWrapper<>();
		wrapper.eq(query.getId() != null, "t.id", query.getId());
		if(query.getSearchTimeArea() != null){
			wrapper.between(CollectionUtil.isNotEmpty(query.getSearchTimeArea()),"t.check_time"
					, DateUtil.parse(query.getSearchTimeArea().get(0),"yyyy-MM-dd HH:mm:ss"),
					DateUtil.parse(query.getSearchTimeArea().get(1),"yyyy-MM-dd HH:mm:ss"));
		}
		wrapper.eq("t.del_flag", DelFlagEnum.NOT_DELETED.getValue());
		if(StrUtil.isNotBlank(query.getKeyword())){
			wrapper.and(t->{
				t.like(StrUtil.isNotBlank(query.getKeyword()),"t.check_company",query.getKeyword())
						.or()
						.like(StrUtil.isNotBlank(query.getKeyword()),"t.check_director",query.getKeyword());
			});
		}
		wrapper.orderByDesc("t.create_time");
		return wrapper;
	}

	@Override
	public void add(DischargeCheckBo bo) {
		DischargeCheck entity = new DischargeCheck();
		BeanUtils.copyProperties(bo, entity);
        if(CollectionUtil.isNotEmpty(bo.getFileIds())){
			entity.setReportAttachmentId(bo.getFileIds().get(0));
		}
		baseMapper.insert(entity);
	}

	@Override
	public void edit(DischargeCheckBo bo) {
		DischargeCheck entity = new DischargeCheck();
		BeanUtils.copyProperties(bo, entity);
		if(CollectionUtil.isNotEmpty(bo.getFileIds())){
			entity.setReportAttachmentId(bo.getFileIds().get(0));
		}
		baseMapper.updateById(entity);
	}

	@Override
	public void del(Long id) {
		baseMapper.deleteById(id);
	}

}
