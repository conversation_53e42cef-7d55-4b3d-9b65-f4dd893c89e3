package com.enrising.ctsc.discharge.api.bo;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 碳排放能源转换因子表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeEnergyFactorBo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
		private Long id;


	/**
	 * 能源类型id
	 */
		private Long energyTypeId;

	/**
	 * 时间区间
	 */
	@NotNull(message = "有效期不能为空")
	private List<String> daterange;

	/**
	 * 转换因子
	 */
		@Max(value = 999999, message = "低碳排放因子数值过大")
	@NotBlank(message = "碳排放因子不能为空")
	private BigDecimal factor;

	/**
	 * 数据标准来源
	 */
	@Length(min = 1, max = 255, message = "数据标准来源过长")
		private String source;

	/**
	 * 低位发热量
	 */
	@Max(value = 999999, message = "低位发热量数值过大")
		private BigDecimal netCalorificPower;

	/**
	 * 单位热值含碳量
	 */
	@Max(value = 999999, message = "单位热值含碳量数值过大")
		private BigDecimal carbonPerUnitCalorificValue;

	/**
	 * 碳氧化率
	 */
	@Max(value = 999999, message = "碳氧化率数值过大")
		private BigDecimal carbonOxidationRate;
}
