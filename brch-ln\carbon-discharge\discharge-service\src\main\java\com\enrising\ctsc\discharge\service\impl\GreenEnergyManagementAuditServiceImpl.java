package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.zhxu.bs.BeanSearcher;
import cn.zhxu.bs.FieldOps;
import cn.zhxu.bs.SearchResult;
import cn.zhxu.bs.util.MapBuilder;
import cn.zhxu.bs.util.MapUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.carbon.common.entity.Attachments;
import com.enrising.ctsc.carbon.common.entity.Organization;
import com.enrising.ctsc.carbon.common.entity.User;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.Bs;
import com.enrising.ctsc.carbon.common.utils.JwtUtils;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.bo.GreenEnergyManagementAuditBo;
import com.enrising.ctsc.discharge.api.entity.GreenAuditUserId;
import com.enrising.ctsc.discharge.api.entity.GreenEnergyManagement;
import com.enrising.ctsc.discharge.api.entity.GreenEnergyManagementAudit;
import com.enrising.ctsc.discharge.api.enums.GreenEnergyAudit;
import com.enrising.ctsc.discharge.api.query.GreenEnergyManagementAuditQuery;
import com.enrising.ctsc.discharge.api.vo.GreenEnergyManagementAuditVo;
import com.enrising.ctsc.discharge.api.vo.GreenEnergyManagementVo;
import com.enrising.ctsc.discharge.mapper.GreenEnergyManagementAuditMapper;
import com.enrising.ctsc.discharge.service.GreenEnergyManagementAuditService;
import com.enrising.ctsc.discharge.service.GreenEnergyManagementService;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 绿电管理审核
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2024-09-18
 */
@Slf4j
@Service
@AllArgsConstructor
public class GreenEnergyManagementAuditServiceImpl extends ServiceImpl<GreenEnergyManagementAuditMapper, GreenEnergyManagementAudit> implements GreenEnergyManagementAuditService {

    @Autowired
    private BeanSearcher beanSearcher;


    @Autowired
    private GreenEnergyManagementService managementService;

    /**
     * 获取机构名称
     *
     * @param detail 详情
     */
    private void getAllInfo(GreenEnergyManagementAuditVo detail) {
        Map<String, Object> companyBranch = Maps.newHashMap();
        companyBranch.put("id", detail.getCompanyBranch());
        Organization organization = beanSearcher.searchFirst(Organization.class, companyBranch);
        if (organization != null) {
            detail.setCompanyBranchName(organization.getOrgName());
        }
        Map<String, Object> companies = Maps.newHashMap();
        companies.put("id", detail.getCompanies());
        Organization companiesOrg = beanSearcher.searchFirst(Organization.class, companies);
        if (companiesOrg != null) {
            detail.setCompaniesName(companiesOrg.getOrgName());
        }
        // 获取附件
        if (detail.getSupportingDocument() != null) {
            Attachments attachments = beanSearcher.searchFirst(Attachments.class, MapUtils.of("id", detail.getSupportingDocument()));
            detail.setAttachments(attachments);
        }
        // 绿电信息
        GreenEnergyManagement energyManagement = new GreenEnergyManagement().selectById(detail.getGreenId());
        GreenEnergyManagementVo energyManagementVo = new GreenEnergyManagementVo();
        BeanUtils.copyProperties(energyManagement, energyManagementVo);
        detail.setGreenEnergyManagement(energyManagementVo);
    }

    @Override
    public TableDataInfo<GreenEnergyManagementAuditVo> findList(GreenEnergyManagementAuditQuery query) {
        MapBuilder params = Bs.params();
        // 关键字查询
        if (StrUtil.isNotBlank(query.getKeyword())) {
            params.field(
                            GreenEnergyManagementAuditVo::getSubjectEntity,
                            GreenEnergyManagementAuditVo::getPowerSourceLocation,
                            GreenEnergyManagementAuditVo::getOrgName
                    )
                    .sql("$1 like ? or $2 like ? or $3 like ?", query.getKeyword(), query.getKeyword(), query.getKeyword());
        }
        // 已审核默认查询的列表
        if (CollUtil.isNotEmpty(query.getAuditResultList())) {
            params.field(GreenEnergyManagementAuditVo::getAuditResult, query.getAuditResultList()).op(FieldOps.InList);
        }
        // 当前登录人的审核记录
        params.field(GreenEnergyManagementAuditVo::getAuditUser, JwtUtils.getUser().getId());
        SearchResult<GreenEnergyManagementAuditVo> result = Bs.getBean().search(GreenEnergyManagementAuditVo.class, params.build());
        result.getDataList().stream().peek(this::getAllInfo).collect(Collectors.toList());
        return TableDataInfo.build(result);
    }

    @Override
    public GreenEnergyManagementAuditVo detail(GreenEnergyManagementAuditQuery query) {
        if (ObjectUtil.allFieldIsNull(query)) {
            throw new BusinessException("查询参数不能为空");
        }
        GreenEnergyManagementAuditVo result = Bs.getBean().searchFirst(GreenEnergyManagementAuditVo.class, Bs.params().build());
        getAllInfo(result);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(GreenEnergyManagementAuditBo bo) {
        if (bo.getAuditUser() == null) {
            throw new BusinessException("审核人[auditUser]不能为空");
        }
        if (bo.getCompanies() == null) {
            throw new BusinessException("所属分公司[companies]不能为空");
        }
        Long greenId;
        // 更新绿电位待审核
        GreenEnergyManagement energyManagement = new GreenEnergyManagement();
        // 编辑提交
        if (bo.getGreenId() != null) {
            greenId = bo.getGreenId();
            BeanUtils.copyProperties(bo.getGreenEnergyManagement(), energyManagement);
            energyManagement.setAuditResult(GreenEnergyAudit.PENDING.getCode());
            BigDecimal deduction = managementService.getDeduction(energyManagement.getMonthlyContractPower());
            energyManagement.setDeduction(deduction);
            energyManagement.setId(bo.getGreenId());
            energyManagement.updateById();
        }
        // 新增并提交
        else {
            BeanUtils.copyProperties(bo.getGreenEnergyManagement(), energyManagement);
            energyManagement.setAuditResult(GreenEnergyAudit.PENDING.getCode());
            BigDecimal deduction = managementService.getDeduction(energyManagement.getMonthlyContractPower());
            energyManagement.setDeduction(deduction);
            energyManagement.insert();
            greenId = energyManagement.getId();
        }
        /*-----------------------------------*/

        GreenEnergyManagementAudit entity = new GreenEnergyManagementAudit();
        BeanUtils.copyProperties(bo, entity);
        // 默认待审核
        entity.setGreenId(greenId);
        entity.setAuditResult(GreenEnergyAudit.PENDING.getCode());
        entity.setSubmitTime(new Date());
        entity.setSubmitUser(JwtUtils.getUser().getId());
        baseMapper.insert(entity);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(GreenEnergyManagementAuditBo bo) {
        if (bo.getId() == null) {
            throw new BusinessException("id不能为空");
        }
        if (bo.getGreenId() == null) {
            throw new BusinessException("绿电id不能为空");
        }
        if (StrUtil.isBlank(bo.getAuditResult())) {
            throw new BusinessException("审核结果不能为空");
        }
        GreenEnergyManagementAudit entity = new GreenEnergyManagementAudit();
        BeanUtils.copyProperties(bo, entity);
        entity.setAuditTime(new Date());
        baseMapper.updateById(entity);
        // 更新绿电状态
        GreenEnergyManagement energyManagement = new GreenEnergyManagement();
        energyManagement.setId(bo.getGreenId());
        energyManagement.setAuditResult(bo.getAuditResult());
        energyManagement.updateById();
    }

    @Override
    public void del(Long id) {
        baseMapper.deleteById(id);
    }

    @Override
    public List<User> auditUserList() {
        List<GreenAuditUserId> greenAuditUserIdList = beanSearcher.searchAll(GreenAuditUserId.class);
        if (CollUtil.isEmpty(greenAuditUserIdList)) {
            throw new BusinessException("没有绿电审核权限的用户");
        }
        List<Long> userIds = greenAuditUserIdList.stream().map(GreenAuditUserId::getUserId).collect(Collectors.toList());
        Map<String, Object> params = MapUtils.builder()
                .field(User::getId, userIds).op(FieldOps.InList)
                .build();
        return beanSearcher.searchAll(User.class, params);
    }

}
