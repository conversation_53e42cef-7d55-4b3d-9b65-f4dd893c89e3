package com.enrising.ctsc.assess.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.assess.api.bo.AssessReportScoreBo;
import com.enrising.ctsc.assess.api.bo.AssessTaskReportBo;
import com.enrising.ctsc.assess.api.bo.AssessTemplateBo;
import com.enrising.ctsc.assess.api.entity.AssessTaskReport;
import com.enrising.ctsc.assess.api.query.AssessTaskReportQuery;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.assess.api.vo.AssessTaskReportScoreDetailVo;
import com.enrising.ctsc.assess.api.vo.AssessTaskReportScoreVo;
import com.enrising.ctsc.assess.api.vo.AssessTaskReportVo;

import java.util.List;

/**
 * 考核任务上报表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
public interface AssessTaskReportService extends IService<AssessTaskReport> {

	/**
	 * 分页查询
	 *
	 * @param page  分页
	 * @param query 参数
	 * @return 列表
	 */
	TableDataInfo<AssessTaskReportScoreVo> scoreList(Page<AssessTaskReportVo> page, AssessTaskReportQuery query);

	/**
	 * 详情
	 *
	 * @param query 参数
	 * @return 详情
	 */
	AssessTaskReportVo detail(AssessTaskReportQuery query);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	void add(AssessTaskReportBo bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	void edit(AssessTaskReportBo bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	void del(Long id);

	/**
	 * 上报
	 *
	 * @param bo 参数
	 */
	void reporte(AssessTaskReportBo bo);

	/**
	 * 打分详情
	 *
	 * @param reportId 上报id
	 * @return
	 */
	AssessTaskReportScoreDetailVo scoreDetail(Long reportId);

	/**
	 * 考核打分
	 *
	 * @param bo 分数
	 * @return
	 */
	AssessTaskReport score(AssessReportScoreBo bo);

	/**
	 * 根据考核模板对象查询上报列表
	 * @param templateObjectId	考核模板对象id
	 * @return	列表
	 */
	List<AssessTaskReportVo> listByTemplateObjectId(Long templateObjectId);


	List<AssessTaskReportVo> getAllAssessTemplateByYear(List<Long> templateIds, AssessTemplateBo bo);
}
