package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.bo.DischargeEnergyFactorBo;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyFactor;
import com.enrising.ctsc.discharge.api.entity.SysDictItem;
import com.enrising.ctsc.discharge.api.enums.DelFlagEnum;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyFactorOpenBo;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyFactorQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyCoefficientVo;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyFactorExport;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyFactorOpenVo;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyFactorVo;
import com.enrising.ctsc.discharge.mapper.DischargeEnergyFactorMapper;
import com.enrising.ctsc.discharge.service.DischargeEnergyCoefficientService;
import com.enrising.ctsc.discharge.service.DischargeEnergyFactorService;
import com.enrising.ctsc.discharge.service.SysDictItemService;
import com.github.liaochong.myexcel.core.DefaultExcelBuilder;
import com.github.liaochong.myexcel.utils.AttachmentExportUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.CharEncoding;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * 碳排放能源转换因子表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Slf4j
@Service
@AllArgsConstructor
public class DischargeEnergyFactorServiceImpl extends ServiceImpl<DischargeEnergyFactorMapper, DischargeEnergyFactor> implements DischargeEnergyFactorService {

	private final SysDictItemService sysDictItemService;

	private final DischargeEnergyCoefficientService dischargeEnergyCoefficientService;

	@Override
	public TableDataInfo<DischargeEnergyFactorVo> findList(QueryPage<DischargeEnergyFactorQuery> page) {
		QueryWrapper<DischargeEnergyFactorQuery> wrapper = this.getWrapper(page.getModel());
		IPage<DischargeEnergyFactorVo> resultPage = baseMapper.findList(new Page<>(page.getCurrent(), page.getSize()), wrapper);
		return TableDataInfo.build(resultPage);
	}

	@Override
	public List<DischargeEnergyFactor> selectTypeByDate(DischargeEnergyFactorQuery query) {
		if (CollUtil.isEmpty(query.getDaterange())) {
			throw new BusinessException("时间区间不能为空");
		}
		Date startTime = DateUtil.parse(query.getDaterange().get(0), "yyyy-MM-dd");
		Date endTime = DateUtil.parse(query.getDaterange().get(1), "yyyy-MM-dd");
		List<DischargeEnergyFactor> list = this.list(Wrappers.<DischargeEnergyFactor>lambdaQuery()
				.select(DischargeEnergyFactor::getId, DischargeEnergyFactor::getEnergyTypeId, DischargeEnergyFactor::getSource,
						DischargeEnergyFactor::getFactor, DischargeEnergyFactor::getValidityStart, DischargeEnergyFactor::getValidityEnd
				)
				.ge(DischargeEnergyFactor::getValidityEnd, startTime)
				.le(DischargeEnergyFactor::getValidityStart, endTime)
				.eq(DischargeEnergyFactor::getEnergyTypeId, query.getEnergyTypeId())
				.ne(ObjectUtil.isNotEmpty(query.getId()), DischargeEnergyFactor::getId, query.getId())
		);
//		for (DischargeEnergyFactor factor : list) {
//			String startTimeFormat = DateUtil.format(startTime, "yyyy");
//			String dbStartTimeFormat = DateUtil.format(factor.getValidityStart(), "yyyy");
//			if (startTimeFormat.equals(dbStartTimeFormat) && !Objects.equals(query.getBoId(), factor.getId())) {
//				throw new BusinessException("存在多条数据，请删除其余数据后操作！");
//			}
//			if (factor.getValidityStart().getTime() > startTime.getTime() && !Objects.equals(query.getBoId(), factor.getId())) {
//				throw new BusinessException("存在多条数据，请删除其余数据后操作！");
//			}
//		}
		return list;
	}

    @Override
    public void exportExcel(DischargeEnergyFactorQuery query, HttpServletResponse response) {
		QueryWrapper<DischargeEnergyFactorQuery> wrapper = this.getWrapper(query);
		List<DischargeEnergyFactorVo> list = baseMapper.findList(wrapper);
		List<DischargeEnergyFactorExport> exportList = Lists.newArrayList();

		List<SysDictItem> energyTypeList = sysDictItemService.listItems("energy_type");
		List<SysDictItem> energyTypeUnitList = sysDictItemService.listItems("energy_type_unit");
		list.forEach(it -> {
			DischargeEnergyFactorExport export = new DischargeEnergyFactorExport();
			BeanUtil.copyProperties(it, export);
			// 设置转换系数单位

			String energyTypeStr = energyTypeList.stream().filter(dict -> dict.getValue().equals(it.getEnergyType())).findFirst().orElse(new SysDictItem()).getDescription();
			String energyTypeUnitStr = energyTypeUnitList.stream().filter(dict -> dict.getValue().equals(it.getEnergyType())).findFirst().orElse(new SysDictItem()).getDescription();

			export.setFactorStr(StrUtil.format("{}{}/{}", it.getFactor(), energyTypeStr, energyTypeUnitStr));
			exportList.add(export);
		});
		Workbook workbook = DefaultExcelBuilder.of(DischargeEnergyFactorExport.class)
				.sheetName("碳排放能源转换系数")
				.build(exportList);
		response.setCharacterEncoding(CharEncoding.UTF_8);
		AttachmentExportUtil.export(workbook, "碳排放因子库", response);
    }

    @Override
	public DischargeEnergyFactorVo detail(DischargeEnergyFactorQuery query) {
		if (ObjectUtil.allFieldIsNull(query)) {
			throw new BusinessException("查询参数不能为空");
		}
		QueryWrapper<DischargeEnergyFactorQuery> wrapper = this.getWrapper(query);
		return baseMapper.detail(wrapper);
	}

	private QueryWrapper<DischargeEnergyFactorQuery> getWrapper(DischargeEnergyFactorQuery query) {
		QueryWrapper<DischargeEnergyFactorQuery> wrapper = new QueryWrapper<>();
		wrapper.eq(query.getId() != null, "t.id", query.getId());
		wrapper.eq("t.del_flag", DelFlagEnum.NOT_DELETED.getValue());
		// 关键字
		if (StrUtil.isNotBlank(query.getKeys())) {
			wrapper.and(w -> w.like("energy_type.second_name", query.getKeys()).or().like("t.source", query.getKeys()));
		}
		// 时间周期
//		if (CollUtil.isNotEmpty(query.getDaterange())) {
//			Date startTime = DateUtil.parse(query.getDaterange().get(0), "yyyy-MM-dd");
//			Date endTime = DateUtil.parse(query.getDaterange().get(1), "yyyy-MM-dd");
//			String endTimeFormat = DateUtil.format(endTime, "yyyy-12-31 23:59:59");
//			wrapper.and(w ->
//					w.between("t.validity_start", startTime, DateUtil.parse(endTimeFormat)).or().between("t.validity_end", startTime, DateUtil.parse(endTimeFormat))
//			);
//
//		}
		wrapper.ge(ObjectUtil.isNotEmpty(query.getValidityStart()), "t.validity_start", query.getValidityStart());
		if (ObjectUtil.isNotEmpty(query.getValidityEnd())) {
			wrapper.le("t.validity_end", DateUtil.endOfYear(query.getValidityEnd()));
		}
		wrapper.orderByDesc("t.create_time");
		return wrapper;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void add(DischargeEnergyFactorBo bo) {
		// 获取时间重叠的数据，进行更新
		this.updateTimeOverlapData(bo);

		DischargeEnergyFactor entity = getDischargeEnergyFactor(bo);
		baseMapper.insert(entity);
	}

	private void updateTimeOverlapData(DischargeEnergyFactorBo bo) {
		Date startTime = DateUtil.parse(bo.getDaterange().get(0), "yyyy-MM-dd");
		Date endTime = DateUtil.parse(bo.getDaterange().get(1).substring(0, 5) + "12-31 23:59:59", "yyyy-MM-dd HH:mm:ss");
		DischargeEnergyFactorQuery query = new DischargeEnergyFactorQuery();
		query.setEnergyTypeId(bo.getEnergyTypeId());
		query.setDaterange(bo.getDaterange());
		query.setId(bo.getId());
		List<DischargeEnergyFactor> overlappingData = this.selectTypeByDate(query);
		for (DischargeEnergyFactor factor : overlappingData) {
			// 开始时间减一秒 如:2023-01-01 00:00:00 结果：2022-12-31 23:59:59
			if (factor.getValidityStart().before(startTime)) {
				factor.setValidityEnd(new DateTime(startTime).offset(DateField.SECOND, -1));
				factor.updateById();
			} else {
				if (factor.getValidityEnd().after(endTime)) {
					factor.setValidityStart(new DateTime(DateUtil.parse(bo.getDaterange().get(1), "yyyy-MM-dd")).offset(DateField.YEAR, 1));
					factor.updateById();
				} else {
					factor.deleteById();
				}
			}
		}
	}

	private static DischargeEnergyFactor getDischargeEnergyFactor(DischargeEnergyFactorBo bo) {
		DischargeEnergyFactor entity = new DischargeEnergyFactor();
		BeanUtils.copyProperties(bo, entity);
		Date startTime = DateUtil.parse(bo.getDaterange().get(0), "yyyy-MM-dd");
		Date endTime = DateUtil.parse(bo.getDaterange().get(1), "yyyy-MM-dd");
		String endTimeFormat = DateUtil.format(endTime, "yyyy-12-31 23:59:59");
		entity.setValidityStart(startTime);
		entity.setValidityEnd(DateUtil.parse(endTimeFormat));
		return entity;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void edit(DischargeEnergyFactorBo bo) {
		// 获取时间重叠的数据，进行更新
		this.updateTimeOverlapData(bo);

		DischargeEnergyFactor entity = getDischargeEnergyFactor(bo);
		baseMapper.updateById(entity);
	}

	@Override
	public void del(Long id) {
		// 查询数据记录
		DischargeEnergyFactor factor = this.getById(id);
		if (factor == null) {
			throw new BusinessException("要删除的数据不存在");
		}

		// 校验有效期开始时间
		Date currentDate = new Date();
		if (factor.getValidityStart().before(currentDate)) {
			throw new BusinessException("只能删除有效期开始时间在当前时间及之后的数据");
		}

		baseMapper.deleteById(id);
	}


	@Override
	public BigDecimal getFactorByTime(Long energyTypeId, Date reportTime) {
		List<DischargeEnergyFactor> dischargeEnergyFactorList = this.list(new LambdaQueryWrapper<DischargeEnergyFactor>()
				.select(DischargeEnergyFactor::getEnergyTypeId, DischargeEnergyFactor::getValidityStart,
						DischargeEnergyFactor::getFactor)
				.eq(DischargeEnergyFactor::getEnergyTypeId, energyTypeId)
				.le(DischargeEnergyFactor::getValidityStart, reportTime)
				.orderByDesc(DischargeEnergyFactor::getValidityStart)
				.last("limit 1"));
		if (CollectionUtil.isNotEmpty(dischargeEnergyFactorList)) {
			return dischargeEnergyFactorList.get(0).getFactor();
		}
		return new BigDecimal("0.0");
	}

	@Override
	public DischargeEnergyFactorOpenVo getGainFactor(DischargeEnergyFactorOpenBo bo) {
		String match = "^\\d{4}";
		if(!Pattern.matches(match,bo.getYear())){
			throw new BusinessException("时间格式不正确，请确认后查询（如 2023 ）！");
		}
		DischargeEnergyFactorOpenVo result = new DischargeEnergyFactorOpenVo();
		DischargeEnergyFactorVo vo = baseMapper.getGainFactor(bo);
		Optional.ofNullable(vo).ifPresent(node->result.setFactor(node.getFactor()));
		DischargeEnergyCoefficientVo  coefficientVo = dischargeEnergyCoefficientService.getGainFactor(bo);
		Optional.ofNullable(coefficientVo).ifPresent(node->result.setCoefficient(node.getCoefficient()));
		return result;
	}
}
