package com.enrising.ctsc.discharge.api.interceptor;

import cn.zhxu.bs.boot.NamedDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

@Component
public class BsConfig {

    // ecm 数据源配置信息
    @Bean(name = "ecmDsProps")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.dynamic.datasource.ecm")
    public DataSourceProperties ecmDsProps() {
        return new DataSourceProperties();
    }

    // rmp 数据源配置信息
    @Bean(name = "rmpDsProps")
    @ConfigurationProperties(prefix = "spring.datasource.dynamic.datasource.rmp")
    public DataSourceProperties rmpDsProps() {
        return new DataSourceProperties();
    }

    @Bean
    public NamedDataSource userNamedDataSource(@Qualifier("ecmDsProps") DataSourceProperties dataSourceProperties) {
        DataSource dataSource = dataSourceProperties.initializeDataSourceBuilder().build();
        return new NamedDataSource("ecmDs", dataSource);
    }

    @Bean
    public NamedDataSource orderNamedDataSource(@Qualifier("rmpDsProps") DataSourceProperties dataSourceProperties) {
        DataSource dataSource = dataSourceProperties.initializeDataSourceBuilder().build();
        return new NamedDataSource("rmpDs", dataSource);
    }
}
