package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 碳排放能源指标表
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-23
 */

@Data
@TableName("discharge_energy_indicator_new")
public class DischargeEnergyIndicatorNew extends Model<DischargeEnergyIndicatorNew> {

	/**
	 * 主键id,采用雪花id
	 */
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 创建者id
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date createTime;

	/**
	 * 更新者id
	 */
	private Long updateBy;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date updateTime;

	/**
	 * 能源指标类型编号
	 */
	private String indicatorCode;

	/**
	 * 能源指标类型名称
	 */
	private String indicatorName;

	/**
	 * 能源类型id
	 */
	private Long energyTypeId;

	/**
	 * 集团数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String groupInputType;

	/**
	 * 股份数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String stockInputType;

	/**
	 * 大型数据中心数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String largeInputType;

	/**
	 * 中小型数据中心数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String mediumInputType;

	/**
	 * 移动业务数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String mobileInputType;

	/**
	 * 状态，1-启用，2-禁用
	 */
	private String status;

	/**
	 * 父指标id
	 */
	private Long parentId;

	/**
	 * 排序值
	 */
	private Integer sort;

	/**
	 * 删除标志：0-正常；1-删除
	 */
	@TableLogic
	private String delFlag;

}
