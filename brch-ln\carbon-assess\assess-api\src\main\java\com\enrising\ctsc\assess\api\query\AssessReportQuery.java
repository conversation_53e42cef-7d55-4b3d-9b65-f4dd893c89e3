package com.enrising.ctsc.assess.api.query;

import com.enrising.ctsc.assess.api.entity.AssessReport;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 考核报告查询
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AssessReportQuery extends AssessReport {

    /*
     * 关键字
     * */
    private String keyword;

    /*
     * 生成状态
     * */
    private String generateStatus;

    /*
     * 是否通知公告，1-否，2-是
     * */
    private String isNotification;

}
