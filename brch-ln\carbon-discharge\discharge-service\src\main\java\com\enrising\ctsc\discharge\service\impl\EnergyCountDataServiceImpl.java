package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.discharge.api.bo.EnergyCountDataBo;
import com.enrising.ctsc.discharge.api.entity.AccountBaseResult;
import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergyCalculate;
import com.enrising.ctsc.discharge.api.entity.EnergyCalculatePrice;
import com.enrising.ctsc.discharge.api.entity.EnergyCountData;
import com.enrising.ctsc.discharge.api.enums.DischargeDataTotalEnum;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.enrising.ctsc.discharge.api.vo.SysDeptVO;
import com.enrising.ctsc.discharge.mapper.DischargeDataTotalMapper;
import com.enrising.ctsc.discharge.mapper.EnergyCountDataMapper;
import com.enrising.ctsc.discharge.service.DischargeDataEnergyCalculateService;
import com.enrising.ctsc.discharge.service.EnergyCalculatePriceService;
import com.enrising.ctsc.discharge.service.EnergyCountDataService;
import com.sccl.common.lang.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 能耗统计数据
 *
 * <AUTHOR>
 * @since 2024-09-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EnergyCountDataServiceImpl extends ServiceImpl<EnergyCountDataMapper, EnergyCountData> implements EnergyCountDataService {


    private final DischargeDataTotalMapper dischargeDataTotalMapper;

    private final EnergyCalculatePriceService energyCalculatePriceService;

    private final DischargeDataEnergyCalculateService dischargeDataEnergyCalculateService;

    private List<EnergyCalculatePrice> energyCalculatePriceList;

    @Override
    public void countEnergyData(EnergyCountData energyCountData) {
        if (ObjectUtil.isEmpty(energyCountData) || ObjectUtil.isEmpty(energyCountData.getReportTime())) {
            throw new BusinessException("统计时间参数不能为空");
        }
        if (StringUtils.isBlank(energyCountData.getCountType())) {
            //原始数据0、按规则计算1 同时统计
            energyCountData.setCountType("01");
        }
        if (ObjectUtil.isEmpty(energyCountData.getCompanyId())) {
            List<Long> companyIdList = dischargeDataTotalMapper.getSysDeptList().stream().map(SysDeptVO::getId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(companyIdList)) {
                for (Long companyId : companyIdList) {
                    CompletableFuture.runAsync(() -> {
                        countCompanyEnergyData(companyId, energyCountData.getReportTime(), energyCountData.getCountType());
                    });
                }
            }
        } else {
            countCompanyEnergyData(energyCountData.getCompanyId(), energyCountData.getReportTime(), energyCountData.getCountType());
        }
    }

    private void countCompanyEnergyData(Long companyId, Date reportTime, String countType) {
        if (DischargeDataTotalEnum.EnergyCountType.按规则计算.getValue().equals(countType)) {
            //按规则计算，删除已经统计数据
            dischargeDataEnergyCalculateService.removeCalculateData(companyId, reportTime);
        }
        List<HashMap<String, Object>> mssAccountBillList = getMssAccountList(companyId, reportTime);
        if (CollectionUtil.isNotEmpty(mssAccountBillList)) {
            log.info("查询报账期间{}，获取账单条数{}", DateUtils.parseDateToStr("yyyy-MM", reportTime), mssAccountBillList.size());
            List<EnergyCountDataBo> energyCountDataBoList = new ArrayList<>();
            for (HashMap<String, Object> mssAccountBill : mssAccountBillList) {
                String preType = "1";
                if (ObjectUtil.isNotEmpty(mssAccountBill.get("preBillType"))) {
                    preType = mssAccountBill.get("preBillType").toString().trim();
                }
                Long billId = new BigDecimal(mssAccountBill.get("ID").toString()).longValue();
                String companyNameText = mssAccountBill.get("COMPANY_NAME_TXT").toString().trim();
                List<AccountBaseResult> accountBaseResultList = null;
                //类型 1自有 2预估 3铁塔 4自有合并,5预估合并 6铁塔合并 7 铁塔预估 8铁塔预估合并 9铁塔包干 10铁塔包干合并 11 自有挂账
                // 12自有挂账合并 13自有预付 14自有预付合并 15铁塔挂账 16铁塔挂账合并 17铁塔预付 18铁塔预付合并 19热力  20煤  21用油
                if ("19".equals(preType)) {
                    //热力
                    accountBaseResultList = getThermalAccountList(billId);
                } else if ("20".equals(preType)) {
                    //煤
                    accountBaseResultList = getCoalAccountList(billId);
                } else if ("21".equals(preType)) {
                    //用油
                    accountBaseResultList = getOilAccountList(billId);
                }else if ("1".equals(preType) || "3".equals(preType) || "4".equals(preType) || "6".equals(preType)
                        || "9".equals(preType) || "10".equals(preType)) {// 非预估
                    accountBaseResultList = getPowerAccountList(billId);
                } else if ("2".equals(preType) || "5".equals(preType) || "7".equals(preType) || "8".equals(preType)) {// 预估
                    accountBaseResultList = getPowerAccountEsList(billId);
                } else if ("11".equals(preType) || "12".equals(preType) || "13".equals(preType) || "14".equals(preType)
                        || "15".equals(preType) || "16".equals(preType) || "17".equals(preType) || "18".equals(preType)) {
                    // 新增的 11 自有挂账 12自有挂账合并 13自有预付 14自有预付合并 15铁塔挂账 16铁塔挂账合并
                    // 都是 查询 预估台账表 es
                    accountBaseResultList = getPowerAccountEsList(billId);
                } else {
                    accountBaseResultList = getPowerAccountList(billId);
                }
                if (CollectionUtil.isNotEmpty(accountBaseResultList)) {
                    for (AccountBaseResult accountBaseResult : accountBaseResultList) {
                        if (countType.contains(DischargeDataTotalEnum.EnergyCountType.原始数据.getValue())) {
                            //统计原始数据
                            EnergyCountDataBo energyCountDataBo = getEnergyData(companyId,
                                    StringUtils.isNotBlank(companyNameText) ? companyNameText.substring(0, 1) : DischargeDataTotalEnum.EnergyDataType.股份数据.getValue(),
                                            DischargeDataTotalEnum.EnergyCountType.原始数据.getValue(), reportTime, accountBaseResult);
                            if (ObjectUtil.isNotEmpty(energyCountDataBo)) {
                                energyCountDataBoList.add(energyCountDataBo);
                            }
                        }
                        if (countType.contains(DischargeDataTotalEnum.EnergyCountType.按规则计算.getValue())) {
                            //统计按规则计算数据
                            EnergyCountDataBo energyCountDataBo = getEnergyData(companyId,
                                    StringUtils.isNotBlank(companyNameText) ? companyNameText.substring(0, 1) : DischargeDataTotalEnum.EnergyDataType.股份数据.getValue(),
                                    DischargeDataTotalEnum.EnergyCountType.按规则计算.getValue(), reportTime, accountBaseResult);
                            if (ObjectUtil.isNotEmpty(energyCountDataBo)) {
                                energyCountDataBoList.add(energyCountDataBo);
                            }
                        }
                    }
                }
            }
            List<EnergyCountDataBo> dataList;
            if (CollectionUtil.isNotEmpty(energyCountDataBoList)) {
                dataList = mergeDataByCompany(energyCountDataBoList);
            } else {
                dataList = new ArrayList<>();
                EnergyCountDataBo energyCountDataBoA = new EnergyCountDataBo();
                energyCountDataBoA.setCompanyId(companyId);
                energyCountDataBoA.setReportTime(reportTime);
                energyCountDataBoA.setDataType(DischargeDataTotalEnum.EnergyDataType.股份数据.getValue());
                energyCountDataBoA.setCountType(countType);
                dataList.add(energyCountDataBoA);
                EnergyCountDataBo energyCountDataBoB = new EnergyCountDataBo();
                energyCountDataBoB.setCompanyId(companyId);
                energyCountDataBoB.setReportTime(reportTime);
                energyCountDataBoB.setDataType(DischargeDataTotalEnum.EnergyDataType.集团数据.getValue());
                energyCountDataBoB.setCountType(countType);
                dataList.add(energyCountDataBoB);
            }
            saveEnergyDataList(dataList);
        }
    }

    private List<HashMap<String, Object>> getMssAccountList(Long companyId, Date reportTime) {
        HashMap<String, Object> query = new HashMap<>();
        query.put("companyCode", Long.toString(companyId));
//        query.put("budgetSetName", DateUtils.parseDateToStr("yyyy-MM", reportTime));
        query.put("YEAR", DateUtils.parseDateToStr("yyyy", reportTime));
        query.put("BIZ_ENTRY_CODE", DateUtils.parseDateToStr("MM", reportTime));
        return baseMapper.selectListByBudgetSetName(query);
    }

    private Long getIndicatorIdByCode(String code) {
        return baseMapper.getIndicatorIdByCode(code);
    }

    private List<AccountBaseResult> getPowerAccountList(Long billId) {
        return baseMapper.getAccountBaseByBillId(billId);
    }

    private List<AccountBaseResult> getPowerAccountEsList(Long billId) {
        return baseMapper.getAccountEsBaseByBillId(billId);
    }

    private List<AccountBaseResult> getThermalAccountList(Long billId) {
        return baseMapper.getAccountThermalBaseByBillId(billId);
    }

    private List<AccountBaseResult> getCoalAccountList(Long billId) {
        return baseMapper.getAccountCoalBaseByBillId(billId);
    }

    private List<AccountBaseResult> getOilAccountList(Long billId) {
        return baseMapper.getAccountOilBaseByBillId(billId);
    }

    private EnergyCalculatePrice getEnergyPrice(Date reportTime) {
        EnergyCalculatePrice result = null;
        if (CollectionUtil.isEmpty(energyCalculatePriceList)) {
            energyCalculatePriceList = new ArrayList<>();
        }
        for (EnergyCalculatePrice energyCalculatePrice : energyCalculatePriceList) {
            if (reportTime.equals(energyCalculatePrice.getReportTime())) {
                result = energyCalculatePrice;
                break;
            }
        }
        if (ObjectUtil.isEmpty(result)) {
            EnergyCalculatePrice query = new EnergyCalculatePrice();
            query.setReportTime(reportTime);
            result = energyCalculatePriceService.getLastOne(query);
            if (ObjectUtil.isNotEmpty(result)) {
                energyCalculatePriceList.add(result);
            }
        }
        return result;
    }

    private int saveEnergyCountData(Long companyId, Date reportTime, String dataType, String countType,
                                    Long energyIndicatorId, BigDecimal energyData, BigDecimal amount) {
        if (DischargeDataTotalEnum.EnergyCountType.按规则计算.getValue().equals(countType)) {
            //按规则计算数据保存
            DischargeDataEnergyCalculate dischargeDataEnergyCalculate = new DischargeDataEnergyCalculate();
            dischargeDataEnergyCalculate.setCompanyId(companyId);
            dischargeDataEnergyCalculate.setReportTime(reportTime);
            dischargeDataEnergyCalculate.setEnergyIndicatorId(energyIndicatorId);
            if (DischargeDataTotalEnum.EnergyDataType.集团数据.getValue().equals(dataType)) {
                dischargeDataEnergyCalculate.setGroupData(energyData);
            } else {
                dischargeDataEnergyCalculate.setStockData(energyData);
            }
            return dischargeDataEnergyCalculateService.saveData(dischargeDataEnergyCalculate);
        } else {
            return saveOriginEnergyData(companyId, reportTime, dataType, energyIndicatorId, energyData, amount);
        }
    }

    private int saveOriginEnergyData(Long companyId, Date reportTime, String dataType,
                                     Long energyIndicatorId, BigDecimal energyData, BigDecimal amount) {
        int ret = 0;
        EnergyCountData energyCountData = baseMapper.selectOne(new LambdaQueryWrapper<EnergyCountData>()
                .eq(EnergyCountData::getCompanyId, companyId)
                .eq(EnergyCountData::getReportTime, reportTime)
//                .eq(EnergyCountData::getCountType, countType)
                .eq(EnergyCountData::getEnergyIndicatorId, energyIndicatorId));
        if (ObjectUtil.isNotEmpty(energyCountData)) {
            if (DischargeDataTotalEnum.EnergyDataType.集团数据.getValue().equals(dataType)) {
                energyCountData.setGroupData(energyData);
            } else {
                energyCountData.setStockData(energyData);
            }
            ret = baseMapper.updateById(energyCountData);
        } else {
            energyCountData = new EnergyCountData();
            energyCountData.setCompanyId(companyId);
            energyCountData.setReportTime(reportTime);
//            energyCountData.setCountType(countType);
            energyCountData.setAmount(amount);
            energyCountData.setEnergyIndicatorId(energyIndicatorId);
            if (DischargeDataTotalEnum.EnergyDataType.集团数据.getValue().equals(dataType)) {
                energyCountData.setGroupData(energyData);
            } else {
                energyCountData.setStockData(energyData);
            }
            ret = baseMapper.insert(energyCountData);
        }
        return ret;
    }

    /**
     * 保存统计数据列表
     */
    @SneakyThrows
    private void saveEnergyDataList(List<EnergyCountDataBo> dataList) {
        Long totalPowerDataIndicatorId = getIndicatorIdByCode("1.3");
        Long productionRoomDataIndicatorId = getIndicatorIdByCode("1.3.1");
        Long communicationIndicatorId = getIndicatorIdByCode("1.3.1.1");
        Long baseStationDataIndicatorId = getIndicatorIdByCode("1.3.1.2");
        Long towerIndicatorId = getIndicatorIdByCode("1.3.1.2.1");
        Long leasingIndicatorId = getIndicatorIdByCode("1.3.1.2.2");
        Long ownIndicatorId = getIndicatorIdByCode("1.3.1.2.3");
        Long centerIndicatorId = getIndicatorIdByCode("1.3.1.3");
        Long externalCenterIndicatorId = getIndicatorIdByCode("1.3.1.3.2");
        Long selfCenterIndicatorId = getIndicatorIdByCode("1.3.1.3.3");
        Long accessIndicatorId = getIndicatorIdByCode("1.3.1.4");
        Long nonProductionBuildingsDataIndicatorId = getIndicatorIdByCode("1.3.2");
        Long managerIndicatorId = getIndicatorIdByCode("1.3.2.1");
        Long canalIndicatorId = getIndicatorIdByCode("1.3.2.2");
        Long coalIndicatorId = getIndicatorIdByCode("1.1");
        Long powerCoalIndicatorId = getIndicatorIdByCode("1.1.1");
        Long cokeIndicatorId = getIndicatorIdByCode("1.2");
        //汽油、柴油、热力数据改为导入，取消报账计算
//        Long gasolineIndicatorId = getIndicatorIdByCode("1.5");
//        Long gasolineMovableIndicatorId = getIndicatorIdByCode("1.5.1");
//        Long gasolineStationaryIndicatorId = getIndicatorIdByCode("1.5.2");
//        Long dieselIndicatorId = getIndicatorIdByCode("1.7");
//        Long dieselMovableIndicatorId = getIndicatorIdByCode("1.7.1");
//        Long dieselStationaryIndicatorId = getIndicatorIdByCode("1.7.2");
//        Long thermalIndicatorId = getIndicatorIdByCode("2.1");
        for (EnergyCountDataBo energyCountDataBo : dataList) {
            if (ObjectUtil.isNotEmpty(energyCountDataBo.getTotalPowerData()) &&
                    ObjectUtil.isNotEmpty(totalPowerDataIndicatorId)) {
                //总耗电量1.3
                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), totalPowerDataIndicatorId,
                        energyCountDataBo.getTotalPowerData(), energyCountDataBo.getAmount());
            }if (ObjectUtil.isNotEmpty(energyCountDataBo.getProductionRoomData()) &&
                    ObjectUtil.isNotEmpty(productionRoomDataIndicatorId)) {
                //1.3.1、生产用房耗电量(千瓦时)
                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), productionRoomDataIndicatorId,
                        energyCountDataBo.getProductionRoomData(), energyCountDataBo.getAmount());
            }
            if (ObjectUtil.isNotEmpty(energyCountDataBo.getCommunicationRoomData()) &&
                    ObjectUtil.isNotEmpty(communicationIndicatorId)) {
                //保存通信机房耗电量
                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), communicationIndicatorId,
                        energyCountDataBo.getCommunicationRoomData(), energyCountDataBo.getAmount());
            }
            if (ObjectUtil.isNotEmpty(energyCountDataBo.getBaseStationData()) &&
                    ObjectUtil.isNotEmpty(baseStationDataIndicatorId)) {
                //1.3.1.2、其中：基站耗电量(千瓦时)
                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), baseStationDataIndicatorId,
                        energyCountDataBo.getBaseStationData(), energyCountDataBo.getAmount());
            }
            if (ObjectUtil.isNotEmpty(energyCountDataBo.getTowerBaseStationData()) &&
                    ObjectUtil.isNotEmpty(towerIndicatorId)) {
                //铁塔公司基站耗电量
                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), towerIndicatorId,
                        energyCountDataBo.getTowerBaseStationData(), energyCountDataBo.getAmount());
            }
            if (ObjectUtil.isNotEmpty(energyCountDataBo.getLeasingBaseStationData()) &&
                    ObjectUtil.isNotEmpty(leasingIndicatorId)) {
                //第三方租赁基站耗电量
                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), leasingIndicatorId,
                        energyCountDataBo.getLeasingBaseStationData(), energyCountDataBo.getAmount());
            }
            if (ObjectUtil.isNotEmpty(energyCountDataBo.getOwnBaseStationData()) &&
                    ObjectUtil.isNotEmpty(ownIndicatorId)) {
                //自有产权基站耗电量
                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), ownIndicatorId,
                        energyCountDataBo.getOwnBaseStationData(), energyCountDataBo.getAmount());
            }
            if (ObjectUtil.isNotEmpty(energyCountDataBo.getExternalDataCenterData()) &&
                    ObjectUtil.isNotEmpty(externalCenterIndicatorId)) {
                //对外数据中心耗电量
                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), externalCenterIndicatorId,
                        energyCountDataBo.getExternalDataCenterData(), energyCountDataBo.getAmount());
            }
            if (ObjectUtil.isNotEmpty(energyCountDataBo.getSelfDataCenterData()) &&
                    ObjectUtil.isNotEmpty(selfCenterIndicatorId)) {
                //对外数据中心耗电量
                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), selfCenterIndicatorId,
                        energyCountDataBo.getSelfDataCenterData(), energyCountDataBo.getAmount());
            }
            if (ObjectUtil.isNotEmpty(energyCountDataBo.getDataCenterData()) &&
                    ObjectUtil.isNotEmpty(centerIndicatorId)) {
                //数据中心耗电量
                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), centerIndicatorId,
                        energyCountDataBo.getDataCenterData(), energyCountDataBo.getAmount());
            }
            if (ObjectUtil.isNotEmpty(energyCountDataBo.getAccessRoomData()) &&
                    ObjectUtil.isNotEmpty(accessIndicatorId)) {
                //接入局所及室外机柜耗电量
                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), accessIndicatorId,
                        energyCountDataBo.getAccessRoomData(), energyCountDataBo.getAmount());
            }
            if (ObjectUtil.isNotEmpty(energyCountDataBo.getNonProductionBuildingsData()) &&
                    ObjectUtil.isNotEmpty(nonProductionBuildingsDataIndicatorId)) {
                //1.3.2、非生产用房耗电量(千瓦时)
                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), nonProductionBuildingsDataIndicatorId,
                        energyCountDataBo.getNonProductionBuildingsData(), energyCountDataBo.getAmount());
            }
            if (ObjectUtil.isNotEmpty(energyCountDataBo.getManagerRoomData()) &&
                    ObjectUtil.isNotEmpty(accessIndicatorId)) {
                //管理用房耗电量
                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), managerIndicatorId,
                        energyCountDataBo.getManagerRoomData(), energyCountDataBo.getAmount());
            }
            if (ObjectUtil.isNotEmpty(energyCountDataBo.getCanalRoomData()) &&
                    ObjectUtil.isNotEmpty(ObjectUtil.isNotEmpty(canalIndicatorId))) {
                //渠道用房耗电量
                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), canalIndicatorId,
                        energyCountDataBo.getCanalRoomData(), energyCountDataBo.getAmount());
            }
            if (ObjectUtil.isNotEmpty(energyCountDataBo.getCoalData()) &&
                    ObjectUtil.isNotEmpty(coalIndicatorId)) {
                //煤炭数据
                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), coalIndicatorId,
                        energyCountDataBo.getCoalData(), energyCountDataBo.getAmount());
            }
            if (ObjectUtil.isNotEmpty(energyCountDataBo.getPowerCoalData()) &&
                    ObjectUtil.isNotEmpty(powerCoalIndicatorId)) {
                //发电用煤数据
                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), powerCoalIndicatorId,
                        energyCountDataBo.getPowerCoalData(), energyCountDataBo.getAmount());
            }
            if (ObjectUtil.isNotEmpty(energyCountDataBo.getCokeData()) &&
                    ObjectUtil.isNotEmpty(cokeIndicatorId)) {
                //焦炭数据
                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), cokeIndicatorId,
                        energyCountDataBo.getCokeData(), energyCountDataBo.getAmount());
            }
            //汽油、柴油、热力数据改为导入，取消报账计算
//            if (ObjectUtil.isNotEmpty(energyCountDataBo.getGasolineData()) &&
//                    ObjectUtil.isNotEmpty(gasolineIndicatorId)) {
//                //汽油数据
//                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
//                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), gasolineIndicatorId,
//                        energyCountDataBo.getGasolineData(), energyCountDataBo.getAmount());
//            }
//            if (ObjectUtil.isNotEmpty(energyCountDataBo.getGasolineMovableData()) &&
//                    ObjectUtil.isNotEmpty(gasolineMovableIndicatorId)) {
//                //汽油移动数据
//                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
//                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), gasolineMovableIndicatorId,
//                        energyCountDataBo.getGasolineMovableData(), energyCountDataBo.getAmount());
//            }
//            if (ObjectUtil.isNotEmpty(energyCountDataBo.getGasolineStationaryData()) &&
//                    ObjectUtil.isNotEmpty(gasolineStationaryIndicatorId)) {
//                //汽油固定数据
//                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
//                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), gasolineStationaryIndicatorId,
//                        energyCountDataBo.getGasolineStationaryData(), energyCountDataBo.getAmount());
//            }
//            if (ObjectUtil.isNotEmpty(energyCountDataBo.getDieselData()) &&
//                    ObjectUtil.isNotEmpty(dieselIndicatorId)) {
//                //柴油数据
//                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
//                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), dieselIndicatorId,
//                        energyCountDataBo.getDieselData(), energyCountDataBo.getAmount());
//            }
//            if (ObjectUtil.isNotEmpty(energyCountDataBo.getDieselMovableData()) &&
//                    ObjectUtil.isNotEmpty(dieselMovableIndicatorId)) {
//                //柴油移动数据
//                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
//                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), dieselMovableIndicatorId,
//                        energyCountDataBo.getDieselMovableData(), energyCountDataBo.getAmount());
//            }
//            if (ObjectUtil.isNotEmpty(energyCountDataBo.getDieselStationaryData()) &&
//                    ObjectUtil.isNotEmpty(dieselStationaryIndicatorId)) {
//                //柴油固定数据
//                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
//                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), dieselStationaryIndicatorId,
//                        energyCountDataBo.getDieselStationaryData(), energyCountDataBo.getAmount());
//            }
//            if (ObjectUtil.isNotEmpty(energyCountDataBo.getThermalData()) &&
//                    ObjectUtil.isNotEmpty(thermalIndicatorId)) {
//                //热力数据
//                saveEnergyCountData(energyCountDataBo.getCompanyId(), energyCountDataBo.getReportTime(),
//                        energyCountDataBo.getDataType(), energyCountDataBo.getCountType(), thermalIndicatorId,
//                        energyCountDataBo.getThermalData(), energyCountDataBo.getAmount());
//            }
        }
    }

    /**
     * 按关键字分组合并数据
     */
    private List<EnergyCountDataBo> mergeDataByCompany(List<EnergyCountDataBo> list) {
        List<EnergyCountDataBo> result = list.stream()
                .collect(Collectors.toMap(EnergyCountDataBo::getUniqueKey, a -> a, (o1, o2) -> {
                    //管理用房耗电量
                    o1.setManagerRoomData(o1.getManagerRoomData().add(o2.getManagerRoomData()));
                    //渠道用房耗电量
                    o1.setCanalRoomData(o1.getCanalRoomData().add(o2.getCanalRoomData()));
                    //通信机房耗电量
                    o1.setCommunicationRoomData(o1.getCommunicationRoomData().add(o2.getCommunicationRoomData()));
                    //对外数据中心耗电量
                    o1.setExternalDataCenterData(o1.getExternalDataCenterData().add(o2.getExternalDataCenterData()));
                    //自有数据中心耗电量
                    o1.setSelfDataCenterData(o1.getSelfDataCenterData().add(o2.getSelfDataCenterData()));
                    //接入局所及室外机柜耗电量
                    o1.setAccessRoomData(o1.getAccessRoomData().add(o2.getAccessRoomData()));
                    //铁塔公司基站耗电量
                    o1.setTowerBaseStationData(o1.getTowerBaseStationData().add(o2.getTowerBaseStationData()));
                    //第三方租赁基站耗电量
                    o1.setLeasingBaseStationData(o1.getLeasingBaseStationData().add(o2.getLeasingBaseStationData()));
                    //自有产权基站耗电量
                    o1.setOwnBaseStationData(o1.getOwnBaseStationData().add(o2.getOwnBaseStationData()));
                    //热力数据
                    o1.setThermalData(o1.getThermalData().add(o2.getThermalData()));
                    //煤炭数据
                    o1.setCoalData(o1.getCoalData().add(o2.getCoalData()));
                    //其中发电用煤
                    o1.setPowerCoalData(o1.getPowerCoalData().add(o2.getPowerCoalData()));
                    //焦炭数据
                    o1.setCokeData(o1.getCokeData().add(o2.getCokeData()));
                    //汽油数据
                    o1.setGasolineData(o1.getGasolineData().add(o2.getGasolineData()));
                    //移动源
                    o1.setGasolineMovableData(o1.getGasolineMovableData().add(o2.getGasolineMovableData()));
                    //固定源
                    o1.setGasolineStationaryData(o1.getGasolineStationaryData().add(o2.getGasolineStationaryData()));
                    //汽柴数据
                    o1.setDieselData(o1.getDieselData().add(o2.getDieselData()));
                    //移动源
                    o1.setDieselMovableData(o1.getDieselMovableData().add(o2.getDieselMovableData()));
                    //固定源
                    o1.setDieselStationaryData(o1.getDieselStationaryData().add(o2.getDieselStationaryData()));
                    //金额
                    o1.setAmount(o1.getAmount().add(o2.getAmount()));
                    return o1;
                })).values().stream().collect(Collectors.toList());
        return result;
    }

    /**
     * 账单数据转为双碳系统数据
     *
     */
    private EnergyCountDataBo getEnergyData(Long companyId, String companyNameText, String countType, Date reportTime, AccountBaseResult accountBaseResult) {
        EnergyCountDataBo energyCountDataBo = new EnergyCountDataBo();
        energyCountDataBo.setCompanyId(companyId);
        energyCountDataBo.setReportTime(reportTime);
        energyCountDataBo.setDataType(companyNameText);
        energyCountDataBo.setCountType(countType);
        if (ObjectUtil.isEmpty(energyCountDataBo.getCompanyId()) ||
                ObjectUtil.isEmpty(accountBaseResult.getCurusedreadings()) ||
                ObjectUtil.isEmpty(accountBaseResult.getElectrotype())) {
            return null;
        }
        EnergyCalculatePrice energyCalculatePrice = getEnergyPrice(energyCountDataBo.getReportTime());
        energyCountDataBo.setAmount(ObjectUtil.isNotEmpty(accountBaseResult.getAccountmoney()) ?
                accountBaseResult.getAccountmoney() : BigDecimal.ZERO);
        switch (accountBaseResult.getElectrotype()) {
            case 2:  //管理用房耗电量
                if (DischargeDataTotalEnum.EnergyCountType.原始数据.getValue().equals(countType)) {
                    //原始数据
                    energyCountDataBo.setManagerRoomData(energyCountDataBo.getManagerRoomData().
                            add(accountBaseResult.getCurusedreadings()));
                } else {
                    //按规则计算
                    if (ObjectUtil.isNotEmpty(energyCalculatePrice) &&
                            energyCalculatePrice.getElectricityPrice().compareTo(BigDecimal.ZERO) > 0 &&
                            ObjectUtil.isNotEmpty(energyCountDataBo.getAmount())) {
                        energyCountDataBo.setManagerRoomData(energyCountDataBo.getManagerRoomData().
                                add(energyCountDataBo.getAmount().divide(energyCalculatePrice.getElectricityPrice(), 6, BigDecimal.ROUND_HALF_UP)));
                    }
                }
                break;
            case 31://渠道用房耗电量
            case 32://渠道用房耗电量
            case 33://渠道用房耗电量
                if (DischargeDataTotalEnum.EnergyCountType.原始数据.getValue().equals(countType)) {
                    //原始数据
                    energyCountDataBo.setCanalRoomData(energyCountDataBo.getCanalRoomData().
                            add(accountBaseResult.getCurusedreadings()));
                } else {
                    //按规则计算
                    if (ObjectUtil.isNotEmpty(energyCalculatePrice) &&
                            energyCalculatePrice.getElectricityPrice().compareTo(BigDecimal.ZERO) > 0 &&
                            ObjectUtil.isNotEmpty(energyCountDataBo.getAmount())) {
                        energyCountDataBo.setCanalRoomData(energyCountDataBo.getCanalRoomData().
                                add(energyCountDataBo.getAmount().divide(energyCalculatePrice.getElectricityPrice(), 6, BigDecimal.ROUND_HALF_UP)));
                    }
                }
                break;
            case 111://通信机房耗电量
            case 112://通信机房耗电量
            case 113://通信机房耗电量
                if (DischargeDataTotalEnum.EnergyCountType.原始数据.getValue().equals(countType)) {
                    //原始数据
                    energyCountDataBo.setCommunicationRoomData(energyCountDataBo.getCommunicationRoomData().
                            add(accountBaseResult.getCurusedreadings()));
                } else {
                    //按规则计算
                    if (ObjectUtil.isNotEmpty(energyCalculatePrice) &&
                            energyCalculatePrice.getElectricityPrice().compareTo(BigDecimal.ZERO) > 0 &&
                            ObjectUtil.isNotEmpty(energyCountDataBo.getAmount())) {
                        energyCountDataBo.setCommunicationRoomData(energyCountDataBo.getCommunicationRoomData().
                                add(energyCountDataBo.getAmount().divide(energyCalculatePrice.getElectricityPrice(), 6, BigDecimal.ROUND_HALF_UP)));
                    }
                }
                break;
            case 121://对外数据中心耗电量
                if (DischargeDataTotalEnum.EnergyCountType.原始数据.getValue().equals(countType)) {
                    //原始数据
                    energyCountDataBo.setExternalDataCenterData(energyCountDataBo.getExternalDataCenterData().
                            add(accountBaseResult.getCurusedreadings()));
                } else {
                    //按规则计算
                    if (ObjectUtil.isNotEmpty(energyCalculatePrice) &&
                            energyCalculatePrice.getElectricityPrice().compareTo(BigDecimal.ZERO) > 0 &&
                            ObjectUtil.isNotEmpty(energyCountDataBo.getAmount())) {
                        energyCountDataBo.setExternalDataCenterData(energyCountDataBo.getExternalDataCenterData().
                                add(energyCountDataBo.getAmount().divide(energyCalculatePrice.getElectricityPrice(), 6, BigDecimal.ROUND_HALF_UP)));
                    }
                }
                break;
            case 122://自用数据中心耗电量
                if (DischargeDataTotalEnum.EnergyCountType.原始数据.getValue().equals(countType)) {
                    //原始数据
                    energyCountDataBo.setSelfDataCenterData(energyCountDataBo.getSelfDataCenterData().
                            add(accountBaseResult.getCurusedreadings()));
                } else {
                    //按规则计算
                    if (ObjectUtil.isNotEmpty(energyCalculatePrice) &&
                            energyCalculatePrice.getElectricityPrice().compareTo(BigDecimal.ZERO) > 0 &&
                            ObjectUtil.isNotEmpty(energyCountDataBo.getAmount())) {
                        energyCountDataBo.setSelfDataCenterData(energyCountDataBo.getSelfDataCenterData().
                                add(energyCountDataBo.getAmount().divide(energyCalculatePrice.getElectricityPrice(), 6, BigDecimal.ROUND_HALF_UP)));
                    }
                }
                break;
            case 4:
            case 131://接入局所及室外机柜耗电量
            case 132://接入局所及室外机柜耗电量
            case 133://接入局所及室外机柜耗电量
                if (DischargeDataTotalEnum.EnergyCountType.原始数据.getValue().equals(countType)) {
                    //原始数据
                    energyCountDataBo.setAccessRoomData(energyCountDataBo.getAccessRoomData().
                            add(accountBaseResult.getCurusedreadings()));
                } else {
                    //按规则计算
                    if (ObjectUtil.isNotEmpty(energyCalculatePrice) &&
                            energyCalculatePrice.getElectricityPrice().compareTo(BigDecimal.ZERO) > 0 &&
                            ObjectUtil.isNotEmpty(energyCountDataBo.getAmount())) {
                        energyCountDataBo.setAccessRoomData(energyCountDataBo.getAccessRoomData().
                                add(energyCountDataBo.getAmount().divide(energyCalculatePrice.getElectricityPrice(), 6, BigDecimal.ROUND_HALF_UP)));
                    }
                }
                break;
            case 1411://铁塔公司基站耗电量
            case 1412://铁塔公司基站耗电量
                if (DischargeDataTotalEnum.EnergyCountType.原始数据.getValue().equals(countType)) {
                    //原始数据
                    energyCountDataBo.setTowerBaseStationData(energyCountDataBo.getTowerBaseStationData().
                            add(accountBaseResult.getCurusedreadings()));
                } else {
                    //按规则计算
                    if (ObjectUtil.isNotEmpty(energyCalculatePrice) &&
                            energyCalculatePrice.getElectricityPrice().compareTo(BigDecimal.ZERO) > 0 &&
                            ObjectUtil.isNotEmpty(energyCountDataBo.getAmount())) {
                        energyCountDataBo.setTowerBaseStationData(energyCountDataBo.getTowerBaseStationData().
                                add(energyCountDataBo.getAmount().divide(energyCalculatePrice.getElectricityPrice(), 6, BigDecimal.ROUND_HALF_UP)));
                    }
                }
                break;
            case 1421://第三方租赁基站耗电量
            case 1422://第三方租赁基站耗电量
                if (DischargeDataTotalEnum.EnergyCountType.原始数据.getValue().equals(countType)) {
                    //原始数据
                    energyCountDataBo.setLeasingBaseStationData(energyCountDataBo.getLeasingBaseStationData().
                            add(accountBaseResult.getCurusedreadings()));
                } else {
                    //按规则计算
                    if (ObjectUtil.isNotEmpty(energyCalculatePrice) &&
                            energyCalculatePrice.getElectricityPrice().compareTo(BigDecimal.ZERO) > 0 &&
                            ObjectUtil.isNotEmpty(energyCountDataBo.getAmount())) {
                        energyCountDataBo.setLeasingBaseStationData(energyCountDataBo.getLeasingBaseStationData().
                                add(energyCountDataBo.getAmount().divide(energyCalculatePrice.getElectricityPrice(), 6, BigDecimal.ROUND_HALF_UP)));
                    }
                }
                break;
            case 1431://自有产权基站耗电量
            case 1432://自有产权基站耗电量
                if (DischargeDataTotalEnum.EnergyCountType.原始数据.getValue().equals(countType)) {
                    //原始数据
                    energyCountDataBo.setOwnBaseStationData(energyCountDataBo.getOwnBaseStationData().
                            add(accountBaseResult.getCurusedreadings()));
                } else {
                    //按规则计算
                    if (ObjectUtil.isNotEmpty(energyCalculatePrice) &&
                            energyCalculatePrice.getElectricityPrice().compareTo(BigDecimal.ZERO) > 0 &&
                            ObjectUtil.isNotEmpty(energyCountDataBo.getAmount())) {
                        energyCountDataBo.setOwnBaseStationData(energyCountDataBo.getOwnBaseStationData().
                                add(energyCountDataBo.getAmount().divide(energyCalculatePrice.getElectricityPrice(), 6, BigDecimal.ROUND_HALF_UP)));
                    }
                }
                break;
//            case 1900://热力数据
//                if (DischargeDataTotalEnum.EnergyCountType.原始数据.getValue().equals(countType)) {
//                    //原始数据
//                    energyCountDataBo.setThermalData(energyCountDataBo.getThermalData().
//                            add(accountBaseResult.getCurusedreadings()));
//                } else {
//                    //按规则计算
//                    if (ObjectUtil.isNotEmpty(energyCalculatePrice) &&
//                            energyCalculatePrice.getThermalPrice().compareTo(BigDecimal.ZERO) > 0 &&
//                            ObjectUtil.isNotEmpty(energyCountDataBo.getAmount())) {
//                        energyCountDataBo.setThermalData(energyCountDataBo.getThermalData().
//                                add(energyCountDataBo.getAmount().divide(energyCalculatePrice.getThermalPrice(), 6, BigDecimal.ROUND_HALF_UP)));
//                    }
//                }
//                break;
            case 2000://用煤数据
                if (DischargeDataTotalEnum.EnergyCountType.原始数据.getValue().equals(countType)) {
                    //原始数据
                    if (2 == accountBaseResult.getCategory()) {
                        //煤炭
                        energyCountDataBo.setCoalData(energyCountDataBo.getCoalData().add(accountBaseResult.getCurusedreadings()));
                        if (2 == accountBaseResult.getAmmetertype()) {
                            //发电用煤
                            energyCountDataBo.setPowerCoalData(energyCountDataBo.getPowerCoalData().add(accountBaseResult.getCurusedreadings()));
                        }
                    } else {
                        //焦炭
                        energyCountDataBo.setCokeData(energyCountDataBo.getCokeData().add(accountBaseResult.getCurusedreadings()));
                    }
                } else {
                    //按规则计算
                    if (ObjectUtil.isNotEmpty(energyCalculatePrice) &&
                            energyCalculatePrice.getCoalPrice().compareTo(BigDecimal.ZERO) > 0 &&
                            ObjectUtil.isNotEmpty(energyCountDataBo.getAmount())) {
                        if (2 == accountBaseResult.getCategory()) {
                            //煤炭
                            energyCountDataBo.setCoalData(energyCountDataBo.getCoalData().add(energyCountDataBo.getAmount().divide(
                                    energyCalculatePrice.getCoalPrice(), 6, BigDecimal.ROUND_HALF_UP)));
                            if (2 == accountBaseResult.getAmmetertype()) {
                                //发电用煤
                                energyCountDataBo.setPowerCoalData(energyCountDataBo.getPowerCoalData().add(energyCountDataBo.getAmount().divide(
                                        energyCalculatePrice.getCoalPrice(), 6, BigDecimal.ROUND_HALF_UP)));
                            }
                        } else {
                            //焦炭
                            energyCountDataBo.setCokeData(energyCountDataBo.getCokeData().add(energyCountDataBo.getAmount().divide(
                                    energyCalculatePrice.getCoalPrice(), 6, BigDecimal.ROUND_HALF_UP)));
                        }
                    }
                }
                break;
//            case 2100://用油数据
//                if (DischargeDataTotalEnum.EnergyCountType.原始数据.getValue().equals(countType)) {
//                    //原始数据
//                    if (5 == accountBaseResult.getAmmetertype()) {
//                        //柴油
//                        energyCountDataBo.setDieselData(energyCountDataBo.getDieselData().add(accountBaseResult.getCurusedreadings()));
//                        if (2 == accountBaseResult.getCategory()) {
//                            //固定源
//                            energyCountDataBo.setDieselStationaryData(energyCountDataBo.getDieselStationaryData().add(accountBaseResult.getCurusedreadings()));
//                        } else {
//                            //移动源
//                            energyCountDataBo.setDieselMovableData(energyCountDataBo.getDieselMovableData().add(accountBaseResult.getCurusedreadings()));
//                        }
//                    } else {
//                        //汽油
//                        energyCountDataBo.setGasolineData(energyCountDataBo.getGasolineData().add(accountBaseResult.getCurusedreadings()));
//                        if (2 == accountBaseResult.getCategory()) {
//                            //固定源
//                            energyCountDataBo.setGasolineStationaryData(energyCountDataBo.getGasolineStationaryData().add(accountBaseResult.getCurusedreadings()));
//                        } else {
//                            //移动源
//                            energyCountDataBo.setGasolineMovableData(energyCountDataBo.getGasolineMovableData().add(accountBaseResult.getCurusedreadings()));
//                        }
//                    }
//                } else if (ObjectUtil.isNotEmpty(energyCalculatePrice) &&
//                        ObjectUtil.isNotEmpty(energyCountDataBo.getAmount())) {
//                    //按规则计算
//                    if (5 == accountBaseResult.getAmmetertype()) {
//                        //柴油
//                        if (energyCalculatePrice.getDieselPrice().compareTo(BigDecimal.ZERO) > 0) {
//                            energyCountDataBo.setDieselData(energyCountDataBo.getDieselData().add(energyCountDataBo.getAmount().divide(
//                                    energyCalculatePrice.getDieselPrice(), 6, BigDecimal.ROUND_HALF_UP)));
//                            if (2 == accountBaseResult.getCategory()) {
//                                //固定源
//                                energyCountDataBo.setDieselStationaryData(energyCountDataBo.getDieselStationaryData().add(
//                                        energyCountDataBo.getAmount().divide(energyCalculatePrice.getDieselPrice(), 6, BigDecimal.ROUND_HALF_UP)));
//                            } else {
//                                //移动源
//                                energyCountDataBo.setDieselMovableData(energyCountDataBo.getDieselMovableData().add(
//                                        energyCountDataBo.getAmount().divide(energyCalculatePrice.getDieselPrice(), 6, BigDecimal.ROUND_HALF_UP)));
//                            }
//                        }
//                    } else {
//                        BigDecimal energyData = BigDecimal.ZERO;
//                        switch (accountBaseResult.getAmmetertype()) {
//                            case 2:  //92号汽油
//                                if (energyCalculatePrice.getGasolinePriceTwo().compareTo(BigDecimal.ZERO) > 0) {
//                                    energyData = energyCountDataBo.getAmount().divide(energyCalculatePrice.getGasolinePriceTwo(), 6, BigDecimal.ROUND_HALF_UP);
//                                }
//                                break;
//                            case 3:  //95号汽油
//                                if (energyCalculatePrice.getGasolinePriceFive().compareTo(BigDecimal.ZERO) > 0) {
//                                    energyData = energyCountDataBo.getAmount().divide(energyCalculatePrice.getGasolinePriceFive(), 6, BigDecimal.ROUND_HALF_UP);
//                                }
//                                break;
//                            case 4:  //98号汽油
//                                if (energyCalculatePrice.getGasolinePriceEight().compareTo(BigDecimal.ZERO) > 0) {
//                                    energyData = energyCountDataBo.getAmount().divide(energyCalculatePrice.getGasolinePriceEight(), 6, BigDecimal.ROUND_HALF_UP);
//                                }
//                                break;
//                        }
//                        //汽油
//                        energyCountDataBo.setGasolineData(energyCountDataBo.getGasolineData().add(energyData));
//                        if (2 == accountBaseResult.getCategory()) {
//                            //固定源
//                            energyCountDataBo.setGasolineStationaryData(energyCountDataBo.getGasolineStationaryData().add(energyData));
//                        } else {
//                            //移动源
//                            energyCountDataBo.setGasolineMovableData(energyCountDataBo.getGasolineMovableData().add(energyData));
//                        }
//                    }
//                }
//                break;
        }
        return energyCountDataBo;
    }

    /**
     * 账单账期转为日期格式
     *
     */
    private Date formatAccountNo(String accountNo) {
        if (StringUtils.isBlank(accountNo) || accountNo.length() < 6) {
            return null;
        }
        String sDate = accountNo.substring(0, 4) + "-" + accountNo.substring(4, 6) + "-01";
        return DateUtil.parse(sDate);
    }
}
