<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.assess.mapper.AssessTargetMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.target_type,
            t.target_year,
            t.target_category,
            t.status,
            t.del_flag
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.assess.api.vo.AssessTargetVo">
        SELECT
			t.id,
			t.create_by,
			t.create_time,
			t.update_by,
			t.update_time,
			t.target_type,
			t.target_year,
			t.target_category,
			t.status,
			t.del_flag,
			target_secondary.assess_period,
			target_secondary.id as secondaryId,
			target_secondary.target_name as secondary_target_name,
			target_secondary.score as secondary_target_score,
			target_secondary.formula as secondary_target_formula,
			su.user_name as create_by_name
        FROM assess_target t
		RIGHT JOIN assess_target_secondary target_secondary on target_secondary.primary_target_id = t.id and target_secondary.del_flag = '0'
		LEFT JOIN rmp.sys_user su on su.id = t.create_by
        ${ew.customSqlSegment}
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.assess.api.vo.AssessTargetVo">
        SELECT
        <include refid="baseColumns" />
        FROM assess_target t
        ${ew.customSqlSegment}
        limit 1
    </select>
</mapper>