package com.enrising.ctsc.assess.api.bo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 考核模板对象
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-08
 */

@Data
public class AssessTemplateTargetObjectBo {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 考核模板id
     */
    @NotNull(message = "任务模板id不能为空！", groups = query.class)
    private Long templateId;

    /**
     * 考核指标id
     */
    private Long targetId;

    /**
     * 二级考核指标id
     */
    private List<Long> targetIds;

    /**
     * 二级考核指标id
     */
    private Long secondaryTargetId;

    /**
     * 公司id
     */
    @NotNull(message = "分公司id不能为空！", groups = query.class)
    private Long companyId;

    /**
     * 公司ids
     */
    private List<Long> companyIds;

    /**
     * 公司id
     */
    private String companyName;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 关键字搜索
     */
    private String keyWord;

    /**
     * 指标类别
     */
    private String targetCategory;

    /**
     * 考核方式
     */
    private String assessMethod;

    /*
     * 查询详情 1--省级 2--市州/公司
     * */
    private Integer queryType;

    /*
     * 考核排名
     * */
    private String assessRank;

    /*
     * 查询某个分公司的考核任务
     * */
    public interface query {
    }

    ;

}
