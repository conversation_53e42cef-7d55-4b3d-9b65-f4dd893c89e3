package com.enrising.ctsc.assess.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.enrising.ctsc.assess.api.bo.AssessTemplateTargetObjectBo;
import com.enrising.ctsc.assess.api.entity.AssessTemplateTarget;
import com.enrising.ctsc.assess.api.vo.AssessTemplateTargetVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * 考核模板指标
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-08
 */
@Mapper
public interface AssessTemplateTargetMapper extends BaseMapper<AssessTemplateTarget> {
	/**
	 * 查询模板指标列表
	 * @param id 模板id
	 * @return 模板指标列表
	 */
	List<HashMap<String,String>> getTemplateTargetList(Long id);

	/**
	 * 查询模板指标对象列表
	 * @param id 模板指标id
	 * @return 模板指标对象列表
	 */
	List<HashMap<String,String>> getTemplateTargetObjectList(Long id);


	List<AssessTemplateTargetVo> getTargetAndTemInfo(@Param("bo") AssessTemplateTargetObjectBo bo);
}