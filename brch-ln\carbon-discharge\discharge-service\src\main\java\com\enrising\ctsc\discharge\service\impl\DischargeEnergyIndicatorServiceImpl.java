package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.carbon.common.constant.CommonConstants;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.enums.DelFlagEnum;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.discharge.api.utils.*;
import com.enrising.ctsc.discharge.api.bo.DischargeEnergyIndicatorBo;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyIndicator;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyIndicatorQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorVo;
import com.enrising.ctsc.discharge.mapper.DischargeEnergyIndicatorMapper;
import com.enrising.ctsc.discharge.service.DischargeEnergyIndicatorService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 碳排放能源指标表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Service
@AllArgsConstructor
public class DischargeEnergyIndicatorServiceImpl extends ServiceImpl<DischargeEnergyIndicatorMapper, DischargeEnergyIndicator> implements DischargeEnergyIndicatorService {

	private List<DischargeEnergyIndicatorVo> indicatorVoList;

	@Override
	public TableDataInfo<DischargeEnergyIndicatorVo> findList(DischargeEnergyIndicatorQuery query) {
		QueryWrapper<DischargeEnergyIndicatorQuery> wrapper = this.getWrapper(query);
		wrapper.orderByAsc("t.create_time");
		List<DischargeEnergyIndicatorVo> resultPage = baseMapper.findList(wrapper);
		return TableDataInfo.build(resultPage);
	}

	@Override
	public List<DischargeEnergyIndicatorVo> getIndicatorList() {
//		List<DischargeEnergyIndicatorVo> rootList = baseMapper.getIndicatorTreeList(-1L, "1");
//		indicatorVoList = new ArrayList<>();
//		this.mapTree(rootList);
		return baseMapper.getIndicatorList();
	}

	private void mapTree(List<DischargeEnergyIndicatorVo> rootList) {
		if (rootList.size() > 1) {
			for (int i = 0; i < rootList.size(); i ++) {
				indicatorVoList.add(rootList.get(i));
				List<DischargeEnergyIndicatorVo> childrenList = baseMapper.getIndicatorTreeList(rootList.get(i).getId(), "1");
				if (CollectionUtil.isNotEmpty(childrenList) && childrenList.size() > 0) {
					this.mapTree(childrenList);
				}
			}
		} else {
			indicatorVoList.add(rootList.get(0));
			List<DischargeEnergyIndicatorVo> childrenList = baseMapper.getIndicatorTreeList(rootList.get(0).getId(), "1");
			if (CollectionUtil.isNotEmpty(childrenList) && childrenList.size() > 0) {
				this.mapTree(childrenList);
			}
		}
	}

	/**
	 * 构建树查询 1. 不是懒加载情况，查询全部 2. 是懒加载，根据parentId 查询 2.1 父节点为空，则查询ID -1
	 *
	 * @param lazy     是否是懒加载
	 * @param parentId 父节点ID
	 * @return
	 */
	@Override
	public List<IndicatorTree> getTree(boolean lazy, Long parentId) {
		if (!lazy) {
			List<DischargeEnergyIndicatorVo> list = baseMapper.getIndicatorTreeList(null, null);
			return buildTree(list, CommonConstants.MENU_TREE_ROOT_ID);
		}

		Long parent = parentId == null ? CommonConstants.MENU_TREE_ROOT_ID : parentId;
		return buildTree(baseMapper.getIndicatorTreeList(parent, null), parent);
	}

	@Override
	public DischargeEnergyIndicatorVo detail(DischargeEnergyIndicatorQuery query) {
		if (ObjectUtil.allFieldIsNull(query)) {
			throw new BusinessException("查询参数不能为空");
		}
		QueryWrapper<DischargeEnergyIndicatorQuery> wrapper = this.getWrapper(query);
		return baseMapper.detail(wrapper);
	}

	private QueryWrapper<DischargeEnergyIndicatorQuery> getWrapper(DischargeEnergyIndicatorQuery query) {
		QueryWrapper<DischargeEnergyIndicatorQuery> wrapper = new QueryWrapper<>();
		wrapper.eq(query.getId() != null, "t.id", query.getId());
		wrapper.eq(StrUtil.isNotBlank(query.getStatus()), "t.status", query.getStatus());
		wrapper.eq("t.del_flag", DelFlagEnum.NOT_DELETED.getValue());
		wrapper.like(StrUtil.isNotBlank(query.getKeys()), "t.indicator_name", query.getKeys());
		return wrapper;
	}

	@Override
	public void add(DischargeEnergyIndicatorBo bo) {
		DischargeEnergyIndicator entity = new DischargeEnergyIndicator();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.insert(entity);
	}

	@Override
	public void edit(DischargeEnergyIndicatorBo bo) {
		DischargeEnergyIndicator entity = new DischargeEnergyIndicator();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.updateById(entity);
	}

	@Override
	public void del(Long id) {
		baseMapper.deleteById(id);
	}

	/**
	 * 通过IndicatorTree创建树形节点
	 *
	 * @param dischargeEnergyIndicatorVoList
	 * @param root
	 * @return
	 */
	private List<IndicatorTree> buildTree(List<DischargeEnergyIndicatorVo> dischargeEnergyIndicatorVoList, Long root) {
		List<IndicatorTree> trees = new ArrayList<>();
		IndicatorTree node;
		for (DischargeEnergyIndicatorVo dischargeEnergyIndicatorVo : dischargeEnergyIndicatorVoList) {
			node = new IndicatorTree();
			node.setId(dischargeEnergyIndicatorVo.getId());
			node.setParentId(dischargeEnergyIndicatorVo.getParentId());
			node.setIndicatorName(dischargeEnergyIndicatorVo.getIndicatorName());
			node.setStatus(dischargeEnergyIndicatorVo.getStatus());
//			node.setUnit(dischargeEnergyIndicatorVo.getUnit());
			node.setSort(dischargeEnergyIndicatorVo.getSort());
			node.setUnitName(dischargeEnergyIndicatorVo.getUnitName());
			node.setUnitDescription(dischargeEnergyIndicatorVo.getUnitDescription());
			node.setHasChildren(false);
			trees.add(node);
		}
		return buildIndicatorTree(trees, root);
	}

	private List<IndicatorTree> buildIndicatorTree(List<IndicatorTree> treeNodes, Long root) {
		List<IndicatorTree> trees = new ArrayList<>();
		for (IndicatorTree treeNode : treeNodes) {
			//如果传入的节点是已有树的父节点
			if (root.equals(treeNode.getParentId())) {
				trees.add(treeNode);
			}
			//两层循环，将子父节点关联
			for (IndicatorTree it : treeNodes) {
				if (it.getParentId().equals(treeNode.getId())) {
					if (treeNode.getChildren() == null) {
						treeNode.setChildren(new ArrayList<>());
					}
					treeNode.add(it);
					treeNode.setHasChildren(true);
				}
			}
		}
		//设置没有孩子节点， 设置hasChildren 为false
		for (IndicatorTree tree : treeNodes) {
			if (CollectionUtils.isEmpty(tree.getChildren())) {
				tree.setHasChildren(false);
			}
		}
		return trees;
	}
}
