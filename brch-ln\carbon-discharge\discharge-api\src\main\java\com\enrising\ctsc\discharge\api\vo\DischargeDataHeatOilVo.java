package com.enrising.ctsc.discharge.api.vo;

import com.enrising.ctsc.discharge.api.entity.DischargeDataHeatOil;
import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 热力燃油数据表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2024-12-20
 */
@Data
public class DischargeDataHeatOilVo extends DischargeDataHeatOil implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 分公司名称
	 */
	private String companyName;
}
