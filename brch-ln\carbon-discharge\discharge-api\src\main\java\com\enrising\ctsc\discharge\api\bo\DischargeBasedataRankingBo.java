package com.enrising.ctsc.discharge.api.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * 集团排名情况
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-10-13
 */
@Data
public class DischargeBasedataRankingBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id,采用雪花id
     */
    private Long id;


    /**
     * 时间周期-年
     */
    private String timePeriodYear;

    /**
     * 季度
     */
    private String timePeriodQuarter;

    /**
     * 碳排放量同比增幅
     */
    private Double carbonQuantityAmplify;

    /**
     * 排名
     */
    private String carbonQuantityAmplifyRanking;

    /**
     * 碳排放强度同比降幅
     */
    private Double carbonStrengthReduction;

    /**
     * 排名
     */
    private String carbonStrengthReductionRanking;

    /**
     * 电信业务总量同比增幅
     */
    private Double carbonBusinessGrowth;

    /**
     * 排名
     */
    private String carbonBusinessGrowthRanking;

    /**
     * 展示状态
     */
    private String displayState;


}
