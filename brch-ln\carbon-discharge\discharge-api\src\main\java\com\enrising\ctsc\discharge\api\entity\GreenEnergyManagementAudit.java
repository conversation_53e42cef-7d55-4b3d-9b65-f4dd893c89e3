package com.enrising.ctsc.discharge.api.entity;

import cn.zhxu.bs.bean.SearchBean;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 绿电管理审核
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2024-09-18
 */

@Data
@TableName("green_energy_management_audit")
@SearchBean(tables = "green_energy_management_audit")
public class GreenEnergyManagementAudit extends Model<GreenEnergyManagementAudit> {

    /**
     * 审核id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 绿电id
     */
    private Long greenId;

    /**
     * 审核人
     */
    private Long auditUser;

    /**
     * 证明材料文件id
     */
    private Long supportingDocument;

    /**
     * 审核意见
     */
    private String auditRemark;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 提交人
     */
    private Long submitUser;

    /**
     * 审核结果
     */
    private String auditResult;


    /**
     * 所属分公司
     */
    private Long companies;

    /**
     * 所属部门
     */
    private Long companyBranch;

}
