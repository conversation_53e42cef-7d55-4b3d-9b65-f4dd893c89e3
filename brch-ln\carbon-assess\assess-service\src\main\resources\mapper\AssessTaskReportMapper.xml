<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.assess.mapper.AssessTaskReportMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.template_object_id,
            t.report_name,
            t.report_description,
            t.assess_score,
            t.assess_evaluate,
            t.rule_score,
            t.assesser,
            t.assess_time,
            t.del_flag,
            t.report_date
    </sql>

    <!-- 查询列表 -->
	<select id="scoreList" resultType="com.enrising.ctsc.assess.api.vo.AssessTaskReportScoreVo">
		SELECT t.id as task_report_id,
			   t.create_by,
			   t.create_time,
			   t.update_by,
			   t.update_time,
			   t.template_object_id,
			   t.report_name,
			   t.report_description,
			   t.assess_score,
			   t.assess_evaluate,
			   t.rule_score,
			   t.assesser,
			   t.assess_time,
			   t.del_flag,
			   t.report_date,
			   assess_template.template_name,
			   target_secondary.target_name,
			   target_secondary.score as target_secondary_score,
			   assess_target.target_category,
			   target_secondary.assess_period,
			   target_object.company_id,
			   so.org_name as company_name,
			   target_object.dept_id,
			   target_object.object_type,
			   su.user_name as assesserName
		FROM assess_task_report t
				 LEFT JOIN assess_template_target_object target_object on target_object.id = t.template_object_id
				 LEFT JOIN assess_template_target template_target on template_target.id = target_object.template_target_id
				 LEFT JOIN assess_template on assess_template.id = template_target.template_id
				 LEFT JOIN assess_target_secondary target_secondary on target_secondary.id = template_target.secondary_target_id
				 LEFT JOIN assess_target on assess_target.id = target_secondary.primary_target_id
				 LEFT JOIN rmp.sys_user su on su.id = t.assesser
				 LEFT JOIN rmp.sys_organizations so on so.id = target_object.company_id
			${ew.customSqlSegment}
	</select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.assess.api.vo.AssessTaskReportVo">
        SELECT
        <include refid="baseColumns" />
        FROM assess_task_report t
        ${ew.customSqlSegment}
        limit 1
    </select>
	<select id="getAllAssessTemplateByYear" resultType="com.enrising.ctsc.assess.api.vo.AssessTaskReportVo">
		SELECT
			a.id as template_target_id,
			a.template_id,
			a.secondary_target_id,
			b.assess_period as targetPeriod,
			d.id as template_object_id,
			d.company_id,
			d.dept_id
		FROM assess_template_target a
		LEFT JOIN assess_target_secondary b on a.secondary_target_id = b.id
		LEFT JOIN assess_template_target_object d on d.template_target_id = a.id
		WHERE
			a.template_id IN
			<foreach collection="templateIds" open="(" separator="," item="id" close=")">
				#{id}
			</foreach>
			<if test="bo.companyId!=null">
				and d.company_id = #{bo.companyId}
			</if>
			<if test="bo.deptId!=null">
				and d.dept_id = #{bo.deptId}
			</if>
			and a.assess_method = '2'
	</select>
	<select id="getOrganizationById" resultType="com.enrising.ctsc.carbon.common.entity.Organization">
		SELECT
			*
		FROM rmp.sys_organizations
		WHERE
			id = #{id}
	</select>
	<update id="updateAttachment">
		UPDATE
			attachments
		SET busi_id = #{reportId}
		<where>
			id IN
			<foreach collection="attachmentIds" open="(" separator="," item="id" close=")">
				#{id}
			</foreach>
		</where>
	</update>
	<select id="getScoreDetail" resultType="com.enrising.ctsc.assess.api.vo.AssessTaskReportVo">
		SELECT
		    t.*,
		    rsu.user_name as assesser_name
		FROM assess_task_report t
		    LEFT JOIN rmp.sys_user rsu on rsu.id = t.assesser
		WHERE
		    t.del_flag = '0'
		  AND t.id = #{reportId}
	</select>
	<select id="getAttachmentListByIds" resultType="com.enrising.ctsc.assess.api.entity.Attachment">
		SELECT
			t.*
		FROM attachments t
		<where>
			t.id IN
			<foreach collection="attachmentIds" open="(" separator="," item="id" close=")">
				#{id}
			</foreach>
		</where>
	</select>
</mapper>