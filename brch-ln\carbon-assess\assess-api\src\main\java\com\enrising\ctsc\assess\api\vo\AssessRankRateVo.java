package com.enrising.ctsc.assess.api.vo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AssessRankRateVo {


    /*
     * 模板名称
     * */
    private String templateName;

    /*
     * 任务时间
     * */
    private String period;

    /*
     * 任务时间
     * */
    private String templateTaskTime;

    /**
     * 任务开始时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date templateStartTime;

    /*
     * 模板内容
     * */
    private String content;

    /*
     * 考核对象
     * */
    private Long companyId;

    /*
     * 指标总分
     * */
    private double targetTotal = 0;

    /*
     * 考核总分
     * */
    private double assessTotal = 0;

    /*
     * 得分率
     * */
    private String scoreRate = "0";

    /*
     * 考核排名
     * */
    private String assessRank;

    private Integer rankNum;

    /*
     * 考核详情列表
     * */
    private List<RankRateInfoVo> rankDetail;
}
