package com.enrising.ctsc.business.controller;

import com.enrising.ctsc.business.api.bo.BusinessProductionDataBo;
import com.enrising.ctsc.business.api.entity.BusinessProductionData;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.business.api.vo.BusinessProductionDataCompareVo;
import com.enrising.ctsc.business.api.vo.BusinessProductionDataVo;
import com.enrising.ctsc.business.api.vo.CarbonRankingVo;
import com.enrising.ctsc.business.service.BusinessProductionDataService;
import com.sccl.common.utils.QueryPage;
import com.sccl.common.utils.R;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 生产业务数据管理接口
 *
 * <AUTHOR>
 * @since 1.0.0 2023-03-23
 */
@RestController
@RequestMapping("/business")
@AllArgsConstructor
public class BusinessProductionDataController {
	private final BusinessProductionDataService businessProductionDataService;

	@PostMapping("/getByPage")
	public TableDataInfo<BusinessProductionDataVo> getByPage(@RequestBody QueryPage<BusinessProductionDataBo> queryPage) {
		return businessProductionDataService.getByPage(queryPage);
	}

	@PostMapping("/getDataCompareList")
	public R<List<BusinessProductionDataCompareVo>> getDataCompareList(@RequestBody BusinessProductionDataBo businessProductionDataBo) {
		return R.success(businessProductionDataService.getDataCompareList(businessProductionDataBo));
	}


	/**
	 * 新增生产业务数据
	 *
	 * @param businessProductionDatabo 生产业务数据
	 * @return 操作是否成功
	 */
	@PostMapping("/add")
	public R<Boolean> add(@RequestBody BusinessProductionDataBo businessProductionDatabo) {
		return R.success(businessProductionDataService.saveBusinessProductionData(businessProductionDatabo));
	}

	/**
	 * 批量新增生产业务数据
	 *
	 * @param businessProductionDataBos 生产业务数据列表
	 * @return 操作是否成功
	 */
	@PostMapping("/addList")
	public R<Boolean> addList(@RequestBody List<BusinessProductionDataBo> businessProductionDataBos) {
		return R.success(businessProductionDataService.addList(businessProductionDataBos));
	}

	/**
	 * 根据ID删除生产业务数据
	 *
	 * @param id 数据ID
	 * @return 返回操作是否成功
	 */
	@GetMapping(value = "/deleteById/{id}")
	public R<Boolean> deleteById(@PathVariable(name = "id") Long id) {
		return R.success(businessProductionDataService.removeById(id));
	}

	/**
	 * 更新生产业务数据
	 *
	 * @param businessProductionData 生产业务数据
	 * @return 返回是否更新成功
	 */
	@PostMapping(value = "/update")
	public R<Boolean> update(@RequestBody BusinessProductionData businessProductionData) {
		return R.success(businessProductionDataService.updateById(businessProductionData));
	}

	@PostMapping("/getCompanyBusList")
	public R<List<BusinessProductionDataCompareVo>> getCompanyBusList(@RequestBody BusinessProductionDataBo bo) {
		return R.success(businessProductionDataService.getCompanyBusList(bo));
	}

	@GetMapping("/getCompanyBusinessTotalList")
	public R<List<HashMap<String, Object>>> getCompanyBusinessTotalList(Long companyId, String dataYear) {
		return R.success(businessProductionDataService.getCompanyBusinessTotalList(companyId, dataYear));
	}

	@GetMapping("/getCompanyList")
	public R<List<CarbonRankingVo>> getCompanyList() {
		return R.success(businessProductionDataService.getCompanyList());
	}


	/**
	 * 查询碳排强度排名
	 *
	 * @return 碳排强度排名
	 */
	@GetMapping(value = "/getCarbonRanking")
	public R<List<CarbonRankingVo>> getCarbonRanking(String year) {
		return R.success(businessProductionDataService.getCarbonRanking(year));
	}

	/**
	 * 双碳云脑--双碳全景图--数据排名
	 *
	 * @return 数据排名
	 */
	@GetMapping(value = "/getBusinessRanking")
	public R getBusinessRanking() {
		return R.success(businessProductionDataService.getBusinessRanking());
	}

	@GetMapping("/getBusinessOverview")
	public R getBusinessOverview(Long companyId) {
		return R.success(businessProductionDataService.getBusinessOverview(companyId));
	}
}