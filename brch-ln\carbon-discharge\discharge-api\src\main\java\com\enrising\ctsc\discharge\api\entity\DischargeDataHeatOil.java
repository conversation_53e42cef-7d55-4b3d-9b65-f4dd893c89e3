package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 热力燃油数据表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2024-12-20
 */

@Data
@TableName("discharge_data_heat_oil")
public class DischargeDataHeatOil extends Model<DischargeDataHeatOil> {

	/**
	 * 主键id,采用雪花id
	 */
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 创建者id
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date createTime;

	/**
	 * 更新者id
	 */
	private Long updateBy;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date updateTime;

	/**
	 * 填报单位id
	 */
	private Long companyId;

	/**
	 * 数据年份
	 */
	private String year;

	/**
	 * 数据月份
	 */
	private String month;

	/**
	 * 股份汽油
	 */
	private BigDecimal gasolineStock;

	/**
	 * 集团汽油
	 */
	private BigDecimal gasolineGroup;

	/**
	 * 股份柴油
	 */
	private BigDecimal dieselStock;

	/**
	 * 集团柴油
	 */
	private BigDecimal dieselGroup;

	/**
	 * 股份热力
	 */
	private BigDecimal heatStock;

	/**
	 * 集团热力
	 */
	private BigDecimal heatGroup;

	/**
	 * 股份水
	 */
	private BigDecimal waterStock;

	/**
	 * 集团水
	 */
	private BigDecimal waterGroup;

	/**
	 * 删除标志：0-正常；1-删除
	 */
	@TableLogic
	private String delFlag;

}
