package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.bo.DischargeCheckBo;
import com.enrising.ctsc.discharge.api.entity.DischargeCheck;
import com.enrising.ctsc.discharge.api.query.DischargeCheckQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeCheckVo;

/**
 * 碳排放核查
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
public interface DischargeCheckService extends IService<DischargeCheck> {

	/**
	 * 分页查询
	 *
	 * @param page  分页
	 * @param query 参数
	 * @return 列表
	 */
	TableDataInfo<DischargeCheckVo> findList(Page<DischargeCheckVo> page, DischargeCheckQuery query);

	/**
	 * 详情
	 *
	 * @param query 参数
	 * @return 详情
	 */
	DischargeCheckVo detail(DischargeCheckQuery query);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	void add(DischargeCheckBo bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	void edit(DischargeCheckBo bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	void del(Long id);
}
