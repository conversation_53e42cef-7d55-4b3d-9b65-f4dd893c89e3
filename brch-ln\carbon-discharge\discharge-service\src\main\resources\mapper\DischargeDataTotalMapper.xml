<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeDataTotalMapper">
	<select id="getSysDeptList"
			resultType="com.enrising.ctsc.discharge.api.vo.SysDeptVO">
		SELECT
			*
		FROM rmp.sys_organizations
		WHERE del_flag='0'
		  AND `status`='1'
		  AND org_type='1'
		  AND parent_company_no = '2600000000'
-- 		  AND org_name NOT like '%省本部%'
-- 		  AND org_name NOT like '%省公司%'
	</select>
	<select id="getCompanyList"
			resultType="com.enrising.ctsc.discharge.api.vo.DataReportVo">
		SELECT
			id as company_id,
			parent_group_no as city_code,
			org_name as company_name,
			0 as telecom_business_total,
			0 as telecom_business_total_last_year,
			0 as business_flow_total,
			0 as carbon_emissions,
			0 as carbon_emissions_last_year,
			0 as energy_consumption
		FROM rmp.sys_organizations
		WHERE del_flag='0'
		  AND `status`='1'
		  AND org_type='1'
		  AND parent_company_no = '2600000000'
-- 		  AND org_name NOT like '%省本部%'
-- 		  AND org_name NOT like '%省公司%'
	</select>
	<select id="getBusinessDataList" resultType="com.enrising.ctsc.discharge.api.vo.DataReportVo">
		SELECT
			id as company_id,
			parent_group_no as city_code,
			org_name as company_name,
			b.telecom_business_total,
			c.telecom_business_total_last_year,
			b.business_flow_total,
			0 as carbon_emissions,
			0 as carbon_emissions_last_year,
			0 as energy_consumption
		FROM rmp.sys_organizations a
		LEFT JOIN (
			SELECT
			    company_id,
			    COALESCE(SUM(telecom_business_total ), 0 ) AS telecom_business_total,
			    COALESCE(SUM(business_flow_total ), 0 ) AS business_flow_total,
			    sum(0) as carbon_emissions,
			    sum(0) as carbon_emissions_last_year,
			    sum(0) as energy_consumption
			FROM business_production_data
			WHERE del_flag = '0'
			  AND DATE_FORMAT( report_time, '%Y' ) = #{thisYear}
			<if test="startMonth != null and endMonth != null">
				AND DATE_FORMAT( report_time, '%m' ) <![CDATA[>=]]> #{startMonth}
				AND DATE_FORMAT( report_time, '%m' ) <![CDATA[<=]]> #{endMonth}
			</if>
			  AND company_id NOT IN (SELECT id from rmp.sys_organizations WHERE del_flag='0' AND parent_company_no='0000000000' UNION SELECT 0)
			GROUP BY company_id) b on a.id=b.company_id
		LEFT JOIN (
			SELECT
			    company_id,
			    COALESCE(SUM( telecom_business_total ), 0 ) as telecom_business_total_last_year
			FROM business_production_data
			WHERE del_flag = '0'
			  AND DATE_FORMAT( report_time, '%Y' ) = #{lastYear}
			<if test="startMonth != null and endMonth != null">
				AND DATE_FORMAT( report_time, '%m' ) <![CDATA[>=]]> #{startMonth}
				AND DATE_FORMAT( report_time, '%m' ) <![CDATA[<=]]> #{endMonth}
			</if>
			GROUP BY company_id) c on c.company_id=a.id
		WHERE del_flag='0'
			AND `status`='1'
			AND org_type='1'
			AND parent_company_no = '2600000000'
-- 			AND org_name NOT like '%省本部%'
-- 			AND org_name NOT like '%省公司%'
	</select>
	<select id="getElectricDataList"
			resultType="com.enrising.ctsc.discharge.api.vo.DataReportVo">
		SELECT
			id as company_id,
			parent_group_no as city_code,
			org_name as company_name,
			0 as telecom_business_total,
			0 as telecom_business_total_last_year,
			0 as business_flow_total,
			b.carbon_emissions,
			c.carbon_emissions_last_year,
			b.energy_consumption
		FROM rmp.sys_organizations a
		LEFT JOIN (
			SELECT
				t.company_id,
				CAST(COALESCE((sum(t.outsourcing_thermal_power) - sum(t.own_green_power)) * (
				SELECT
					factor
				FROM
					discharge_energy_factor
				WHERE
					energy_type_id = '3'
					AND validity_start <![CDATA[<=]]> CAST(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
					AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ) / 1000 as DECIMAL(18,2)) AS carbon_emissions,
			CAST(COALESCE((SUM( t.outsourcing_thermal_power ) - SUM( t.own_green_power )) * (
			SELECT
			coefficient
			FROM
			discharge_energy_coefficient
			WHERE
			energy_type_id = '3'
			AND validity_start <![CDATA[<=]]> CAST(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0 ) / 1000 as DECIMAL(18,2)) AS energy_consumption
			FROM
			discharge_data_electric t
			WHERE
				t.del_flag = '0'
				AND DATE_FORMAT( t.report_time, '%Y' )=#{thisYear}
				<if test="startMonth != null and endMonth != null">
					AND DATE_FORMAT( report_time, '%m' ) <![CDATA[>=]]> #{startMonth}
					AND DATE_FORMAT( report_time, '%m' ) <![CDATA[<=]]> #{endMonth}
				</if>
				AND t.company_id NOT IN (SELECT id from rmp.sys_organizations WHERE del_flag='0' AND parent_company_no='0000000000' UNION SELECT 0)
			GROUP BY company_id) b on a.id = b.company_id
		LEFT JOIN (
			SELECT company_id, CAST(COALESCE((sum(outsourcing_thermal_power) - sum(own_green_power)) * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '3'
			AND validity_start <![CDATA[<=]]> CAST(CONCAT(#{lastYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0 ) / 1000 as DECIMAL(18,2)) as carbon_emissions_last_year
			FROM discharge_data_electric
			WHERE
			del_flag = '0'
			AND DATE_FORMAT( report_time, '%Y' )=#{lastYear}
			<if test="startMonth != null and endMonth != null">
				AND DATE_FORMAT( report_time, '%m' ) <![CDATA[>=]]> #{startMonth}
				AND DATE_FORMAT( report_time, '%m' ) <![CDATA[<=]]> #{endMonth}
			</if>
			GROUP BY company_id
		) c on a.id = c.company_id
		WHERE del_flag='0'
			AND `status`='1'
			AND org_type='1'
			AND parent_company_no = '2600000000'
-- 			AND org_name NOT like '%省本部%'
-- 			AND org_name NOT like '%省公司%'
	</select>
	<select id="getGasDataList"
			resultType="com.enrising.ctsc.discharge.api.vo.DataReportVo">
		SELECT
			id as company_id,
			parent_group_no as city_code,
			org_name as company_name,
			0 as telecom_business_total,
			0 as telecom_business_total_last_year,
			0 as business_flow_total,
			b.carbon_emissions,
			c.carbon_emissions_last_year,
			b.energy_consumption
		FROM rmp.sys_organizations a
		LEFT JOIN (
			SELECT
			t.company_id,
			CAST(( COALESCE(SUM( t.ng )  * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '5'
			AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0 ) +
			COALESCE(SUM( t.lpg )  * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '6'
			AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0 ))/ 1000 AS DECIMAL(18,2)) AS carbon_emissions,
			CAST((COALESCE(SUM( t.ng ) * (
			SELECT
			coefficient
			FROM
			discharge_energy_coefficient
			WHERE
			energy_type_id = '5'
			AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0 ) / 1000 +
			COALESCE(SUM( t.lpg ) * (
			SELECT
			coefficient
			FROM
			discharge_energy_coefficient
			WHERE
			energy_type_id = '6'
			AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0 ) / 1000) as DECIMAL(18,2)) AS energy_consumption
			FROM
			discharge_data_gas t
			WHERE
			t.del_flag = '0'
			AND DATE_FORMAT( t.report_time, '%Y' )=#{thisYear}
			<if test="startMonth != null and endMonth != null">
				AND DATE_FORMAT( report_time, '%m' ) <![CDATA[>=]]> #{startMonth}
				AND DATE_FORMAT( report_time, '%m' ) <![CDATA[<=]]> #{endMonth}
			</if>
			AND t.company_id NOT IN (SELECT id from rmp.sys_organizations WHERE del_flag='0' AND parent_company_no='0000000000' UNION SELECT 0)
			GROUP BY company_id) b on a.id = b.company_id
		LEFT JOIN (
			SELECT
				company_id, CAST(
				( COALESCE( SUM( ng )  * (
				SELECT
				factor
				FROM
				discharge_energy_factor
				WHERE
				energy_type_id = '5'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as datetime)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ) +
				COALESCE( SUM( lpg )  * (
				SELECT
				factor
				FROM
				discharge_energy_factor
				WHERE
				energy_type_id = '6'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as datetime)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ))/ 1000 AS DECIMAL(18,2)) as carbon_emissions_last_year
			FROM discharge_data_gas
			WHERE
				del_flag = '0'
				AND DATE_FORMAT( report_time, '%Y' )=#{lastYear}
				<if test="startMonth != null and endMonth != null">
					AND DATE_FORMAT( report_time, '%m' ) <![CDATA[>=]]> #{startMonth}
					AND DATE_FORMAT( report_time, '%m' ) <![CDATA[<=]]> #{endMonth}
				</if>
			GROUP BY company_id
		) c on a.id = c.company_id
		WHERE del_flag='0'
			AND `status`='1'
			AND org_type='1'
			AND parent_company_no = '2600000000'
-- 			AND org_name NOT like '%省本部%'
-- 			AND org_name NOT like '%省公司%'
	</select>
	<select id="getOilDataList"
			resultType="com.enrising.ctsc.discharge.api.vo.DataReportVo">
		SELECT
			id as company_id,
			parent_group_no as city_code,
			org_name as company_name,
			0 as telecom_business_total,
			0 as telecom_business_total_last_year,
			0 as business_flow_total,
			b.carbon_emissions,
			c.carbon_emissions_last_year,
			b.energy_consumption
		FROM rmp.sys_organizations a
		LEFT JOIN (
			SELECT
			t.company_id,
			CAST(( COALESCE(SUM( t.gasoline )  * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '7'
			AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0 ) +
			COALESCE(SUM( t.diesel )  * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '8'
			AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0 ) +
			COALESCE(SUM( t.crude )  * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '10'
			AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0 ) +
			COALESCE(SUM( t.fuel )  * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '11'
			AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0 ) +
			COALESCE(SUM( t.kerosene )  * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '9'
			AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0 )
			)/ 1000 AS DECIMAL(18,2)) AS carbon_emissions,
			CAST((COALESCE(SUM( t.gasoline ) * (
			SELECT
			coefficient
			FROM
			discharge_energy_coefficient
			WHERE
			energy_type_id = '7'
			AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0 ) / 1000 +
			COALESCE( SUM( t.diesel ) * (
			SELECT
			coefficient
			FROM
			discharge_energy_coefficient
			WHERE
			energy_type_id = '8'
			AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0 ) / 1000 +
			COALESCE( SUM( t.crude ) * (
			SELECT
			coefficient
			FROM
			discharge_energy_coefficient
			WHERE
			energy_type_id = '10'
			AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0 ) / 1000 +
			COALESCE( SUM( t.fuel ) * (
			SELECT
			coefficient
			FROM
			discharge_energy_coefficient
			WHERE
			energy_type_id = '11'
			AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0 ) / 1000 +
			COALESCE( SUM( t.kerosene ) * (
			SELECT
			coefficient
			FROM
			discharge_energy_coefficient
			WHERE
			energy_type_id = '9'
			AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0 ) / 1000) as DECIMAL(18,2)) AS energy_consumption
			FROM
			discharge_data_oil t
			WHERE
			t.del_flag = '0'
			AND DATE_FORMAT( t.report_time, '%Y' )=#{thisYear}
			<if test="startMonth != null and endMonth != null">
				AND DATE_FORMAT( report_time, '%m' ) <![CDATA[>=]]> #{startMonth}
				AND DATE_FORMAT( report_time, '%m' ) <![CDATA[<=]]> #{endMonth}
			</if>
			AND t.company_id NOT IN (SELECT id from rmp.sys_organizations WHERE del_flag='0' AND parent_company_no='0000000000' UNION SELECT 0)
			GROUP BY company_id) b on a.id = b.company_id
		LEFT JOIN (
			SELECT company_id,
		       CAST(( COALESCE( SUM( gasoline )  * (
				SELECT
				factor
				FROM
				discharge_energy_factor
				WHERE
				energy_type_id = '7'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as datetime)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ) +
				COALESCE( SUM( diesel )  * (
				SELECT
				factor
				FROM
				discharge_energy_factor
				WHERE
				energy_type_id = '8'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as datetime)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ) +
				COALESCE( SUM( crude )  * (
				SELECT
				factor
				FROM
				discharge_energy_factor
				WHERE
				energy_type_id = '10'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as datetime)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ) +
				COALESCE( SUM( fuel )  * (
				SELECT
				factor
				FROM
				discharge_energy_factor
				WHERE
				energy_type_id = '11'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as datetime)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ) +
				COALESCE( SUM( kerosene )  * (
				SELECT
				factor
				FROM
				discharge_energy_factor
				WHERE
				energy_type_id = '9'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as datetime)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 )
				)/ 1000 AS DECIMAL(18,2)) AS carbon_emissions_last_year
				FROM discharge_data_oil
				WHERE
				del_flag = '0'
				AND DATE_FORMAT( report_time, '%Y' )=#{lastYear}
				<if test="startMonth != null and endMonth != null">
					AND DATE_FORMAT( report_time, '%m' ) <![CDATA[>=]]> #{startMonth}
					AND DATE_FORMAT( report_time, '%m' ) <![CDATA[<=]]> #{endMonth}
				</if>
				GROUP BY company_id
		) c on a.id = c.company_id
		WHERE del_flag='0'
			AND `status`='1'
			AND org_type='1'
			AND parent_company_no = '2600000000'
-- 			AND org_name NOT like '%省本部%'
-- 			AND org_name NOT like '%省公司%'
	</select>
	<select id="getCoalDataList"
			resultType="com.enrising.ctsc.discharge.api.vo.DataReportVo">
		SELECT
			id as company_id,
			parent_group_no as city_code,
			org_name as company_name,
			0 as telecom_business_total,
			0 as telecom_business_total_last_year,
			0 as business_flow_total,
			b.carbon_emissions,
			c.carbon_emissions_last_year,
			b.energy_consumption
		FROM rmp.sys_organizations a
		LEFT JOIN (
			SELECT
			t.company_id,
			CAST( COALESCE( SUM( t.coal )  * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '13'
			AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0) as DECIMAL(18,2))  AS carbon_emissions,
			CAST( COALESCE( SUM( t.coal ) * (
			SELECT
			coefficient
			FROM
			discharge_energy_coefficient
			WHERE
			energy_type_id = '13'
			AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) , 0) as DECIMAL(18,2)) AS energy_consumption
			FROM
			discharge_data_coal t
			WHERE
			t.del_flag = '0'
			AND DATE_FORMAT( t.report_time, '%Y' )=#{thisYear}
			<if test="startMonth != null and endMonth != null">
				AND DATE_FORMAT( report_time, '%m' ) <![CDATA[>=]]> #{startMonth}
				AND DATE_FORMAT( report_time, '%m' ) <![CDATA[<=]]> #{endMonth}
			</if>
			AND t.company_id NOT IN (SELECT id from rmp.sys_organizations WHERE del_flag='0' AND parent_company_no='0000000000' UNION SELECT 0)
			GROUP BY company_id) b on a.id = b.company_id
		LEFT JOIN (
			SELECT company_id,
			       CAST( COALESCE( SUM( coal )  * (
				SELECT
				factor
				FROM
				discharge_energy_factor
				WHERE
				energy_type_id = '13'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as datetime)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0) as DECIMAL(18,2)) AS carbon_emissions_last_year
				FROM
				discharge_data_coal
				WHERE
				del_flag = '0'
				AND DATE_FORMAT( report_time, '%Y' )=#{lastYear}
				<if test="startMonth != null and endMonth != null">
					AND DATE_FORMAT( report_time, '%m' ) <![CDATA[>=]]> #{startMonth}
					AND DATE_FORMAT( report_time, '%m' ) <![CDATA[<=]]> #{endMonth}
				</if>
				GROUP BY company_id
		) c on a.id = c.company_id
		WHERE del_flag='0'
			AND `status`='1'
			AND org_type='1'
			AND parent_company_no = '2600000000'
-- 			AND org_name NOT like '%省本部%'
-- 			AND org_name NOT like '%省公司%'
	</select>
	<select id="getThermalDataList"
			resultType="com.enrising.ctsc.discharge.api.vo.DataReportVo">
		SELECT
			id as company_id,
			parent_group_no as city_code,
			org_name as company_name,
			0 as telecom_business_total,
			0 as telecom_business_total_last_year,
			0 as business_flow_total,
			b.carbon_emissions,
			c.carbon_emissions_last_year,
			b.energy_consumption
		FROM rmp.sys_organizations a
		LEFT JOIN (
			SELECT
				t.company_id,
				CAST(COALESCE( SUM( t.thermal )  * (
				SELECT
				factor
				FROM
				discharge_energy_factor
				WHERE
				energy_type_id = '12'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ) / 1000 as DECIMAL(18,2)) AS carbon_emissions,
				CAST(COALESCE( SUM( t.thermal ) * (
				SELECT
				coefficient
				FROM
				discharge_energy_coefficient
				WHERE
				energy_type_id = '12'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ) / 1000 as DECIMAL(18,2)) AS energy_consumption
				FROM
				discharge_data_thermal t
				WHERE
				t.del_flag = '0'
				AND DATE_FORMAT( t.report_time, '%Y' )=#{thisYear}
				<if test="startMonth != null and endMonth != null">
					AND DATE_FORMAT( report_time, '%m' ) <![CDATA[>=]]> #{startMonth}
					AND DATE_FORMAT( report_time, '%m' ) <![CDATA[<=]]> #{endMonth}
				</if>
				AND t.company_id NOT IN (SELECT id from rmp.sys_organizations WHERE del_flag='0' AND parent_company_no='0000000000' UNION SELECT 0)
				GROUP BY company_id) b on a.id = b.company_id
		LEFT JOIN (
			SELECT company_id,
		       CAST(COALESCE( SUM( thermal )  * (
				SELECT
				factor
				FROM
				discharge_energy_factor
				WHERE
				energy_type_id = '12'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as datetime)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ) / 1000 as DECIMAL(18,2)) AS carbon_emissions_last_year
				FROM
				discharge_data_thermal
			WHERE
				del_flag = '0'
				AND DATE_FORMAT( report_time, '%Y' )=#{lastYear}
				<if test="startMonth != null and endMonth != null">
					AND DATE_FORMAT( report_time, '%m' ) <![CDATA[>=]]> #{startMonth}
					AND DATE_FORMAT( report_time, '%m' ) <![CDATA[<=]]> #{endMonth}
				</if>
			GROUP BY company_id
		) c on a.id = c.company_id
		WHERE del_flag='0'
			AND `status`='1'
			AND org_type='1'
			AND parent_company_no = '2600000000'
-- 			AND org_name NOT like '%省本部%'
-- 			AND org_name NOT like '%省公司%'
	</select>
	<select id="getWaterDataList"
			resultType="com.enrising.ctsc.discharge.api.vo.DataReportVo">
		SELECT
			id as company_id,
			parent_group_no as city_code,
			org_name as company_name,
			0 as telecom_business_total,
			0 as telecom_business_total_last_year,
			0 as business_flow_total,
			b.carbon_emissions,
			c.carbon_emissions_last_year,
			b.energy_consumption
		FROM rmp.sys_organizations a
		LEFT JOIN (
			SELECT
				t.company_id,
				CAST(COALESCE( SUM( t.water )  * (
				SELECT
				factor
				FROM
				discharge_energy_factor
				WHERE
				energy_type_id = '1'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ) / 1000 as DECIMAL(18,2)) AS carbon_emissions,
				CAST(COALESCE( SUM( t.water ) * (
				SELECT
				coefficient
				FROM
				discharge_energy_coefficient
				WHERE
				energy_type_id = '1'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{thisYear}, '-01-01 0:00:00') as datetime)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ) / 1000 as DECIMAL(18,2)) AS energy_consumption
				FROM
				discharge_data_water t
				WHERE
				t.del_flag = '0'
				AND DATE_FORMAT( t.report_time, '%Y' )=#{thisYear}
				<if test="startMonth != null and endMonth != null">
					AND DATE_FORMAT( report_time, '%m' ) <![CDATA[>=]]> #{startMonth}
					AND DATE_FORMAT( report_time, '%m' ) <![CDATA[<=]]> #{endMonth}
				</if>
				AND t.company_id NOT IN (SELECT id from rmp.sys_organizations WHERE del_flag='0' AND parent_company_no='0000000000' UNION SELECT 0)
				GROUP BY company_id) b on a.id = b.company_id
		LEFT JOIN (
			SELECT company_id,
			       CAST(COALESCE( SUM( water )  * (
				SELECT
				factor
				FROM
				discharge_energy_factor
				WHERE
				energy_type_id = '1'
				AND validity_start <![CDATA[<=]]> cast(CONCAT(#{lastYear}, '-01-01 0:00:00') as datetime)
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
				) , 0 ) / 1000 as DECIMAL(18,2)) AS carbon_emissions_last_year
				FROM
				discharge_data_water
			WHERE
				del_flag = '0'
				AND DATE_FORMAT( report_time, '%Y' )=#{lastYear}
				<if test="startMonth != null and endMonth != null">
					AND DATE_FORMAT( report_time, '%m' ) <![CDATA[>=]]> #{startMonth}
					AND DATE_FORMAT( report_time, '%m' ) <![CDATA[<=]]> #{endMonth}
				</if>
			GROUP BY company_id
		) c on a.id = c.company_id
		WHERE del_flag='0'
			AND `status`='1'
			AND org_type='1'
			AND parent_company_no = '2600000000'
-- 			AND org_name NOT like '%省本部%'
-- 			AND org_name NOT like '%省公司%'
	</select>
	<select id="getProvinceBusinessData" resultType="com.enrising.ctsc.discharge.api.vo.DataReportVo">
		SELECT
			id as company_id,
			parent_group_no as city_code,
			org_name as company_name,
			b.telecom_business_total,
			b.business_flow_total,
			(
				SELECT
					COALESCE( SUM( b.telecom_business_total ), 0 )
				from business_production_data b
				WHERE b.del_flag = '0'
					AND b.company_id=a.id
					AND DATE_FORMAT( b.report_time, '%Y' ) = #{lastYear}
					<if test="startMonth != null and endMonth != null">
						AND DATE_FORMAT( b.report_time, '%m' ) <![CDATA[>=]]> #{startMonth}
						AND DATE_FORMAT( b.report_time, '%m' ) <![CDATA[<=]]> #{endMonth}
					</if>
			) as telecom_business_total_last_year
		FROM rmp.sys_organizations a
		LEFT JOIN (
			SELECT
				t.company_id,
				COALESCE( SUM( t.telecom_business_total ), 0 ) AS telecom_business_total,
				COALESCE( SUM( t.business_flow_total ), 0 ) AS business_flow_total
			FROM business_production_data t
			WHERE
				t.del_flag = '0'
				AND DATE_FORMAT( t.report_time, '%Y' ) = #{thisYear}
				<if test="startMonth != null and endMonth != null">
					AND DATE_FORMAT( t.report_time, '%m' ) <![CDATA[>=]]> #{startMonth}
					AND DATE_FORMAT( t.report_time, '%m' ) <![CDATA[<=]]> #{endMonth}
				</if>
				AND t.company_id = 2600000000
			GROUP BY company_id) b on a.id = b.company_id
		WHERE del_flag='0'
			AND id = 2600000000
	</select>
	<select id="getCompanyBusinessTotalList" resultType="java.util.HashMap">
		SELECT
			telecom_business_total,
			DATE_FORMAT(report_time, '%m月') as data_month
		FROM business_production_data
		WHERE del_flag='0'
		  and company_id=#{companyId}
		  and DATE_FORMAT(report_time, '%Y') = #{dataYear}
		ORDER BY data_month asc
	</select>
</mapper>