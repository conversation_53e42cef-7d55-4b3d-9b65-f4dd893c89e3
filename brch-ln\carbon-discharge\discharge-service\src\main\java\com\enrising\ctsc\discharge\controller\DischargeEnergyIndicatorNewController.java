package com.enrising.ctsc.discharge.controller;

import com.enrising.ctsc.discharge.api.bo.DischargeEnergyIndicatorNewBo;
import com.enrising.ctsc.discharge.api.bo.DischargeEnergyIndicatorStatus;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyIndicatorNew;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyIndicatorQuery;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorNewVo;
import com.enrising.ctsc.discharge.service.DischargeEnergyIndicatorNewService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 碳排放能源指标表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@RestController
@RequestMapping("/discharge/indicatorNew")
@AllArgsConstructor
public class DischargeEnergyIndicatorNewController {
	private final DischargeEnergyIndicatorNewService dischargeEnergyIndicatorNewService;

	@GetMapping("/list")
	public TableDataInfo<DischargeEnergyIndicatorNewVo> page(DischargeEnergyIndicatorQuery query) {
		return dischargeEnergyIndicatorNewService.findList(query);
	}

	@GetMapping("/getIndicatorList")
	public R<List<DischargeEnergyIndicatorNewVo>> getIndicatorList() {
		return R.success(dischargeEnergyIndicatorNewService.getIndicatorList());
	}

	/**
	 * 返回树形菜单集合
	 *
	 * @param lazy     是否是懒加载
	 * @param parentId 父节点ID
	 * @return 树形菜单
	 */
	@GetMapping(value = "/getTree")
	public R getTree(boolean lazy, Long parentId, String status, String indicatorName) {
		return R.success(dischargeEnergyIndicatorNewService.getTree(lazy, parentId, status, indicatorName));
	}

	@GetMapping("/detail")
	public R<DischargeEnergyIndicatorNewVo> get(DischargeEnergyIndicatorQuery query) {
		DischargeEnergyIndicatorNewVo detail = dischargeEnergyIndicatorNewService.detail(query);
		return R.success(detail, "查询成功");
	}

		@PostMapping(value = "/save")
		public R<String> save(@RequestBody @Valid DischargeEnergyIndicatorNewBo bo) {
			dischargeEnergyIndicatorNewService.add(bo);
		return R.success("保存成功");
	}

		@PostMapping(value = "/update")
		public R<String> update(@RequestBody @Valid DischargeEnergyIndicatorNewBo bo) {
			dischargeEnergyIndicatorNewService.edit(bo);
		return R.success("修改成功");
	}

		@PostMapping(value = "/setStatus")
		public R<String> setStatus(@RequestBody @Valid DischargeEnergyIndicatorStatus status) {
		DischargeEnergyIndicatorNew indicator = new DischargeEnergyIndicatorNew();
		indicator.setId(status.getId());
		indicator.setStatus(status.getStatus());
			dischargeEnergyIndicatorNewService.updateById(indicator);
		return R.success("修改成功");
	}

		@PostMapping(value = "/delete/{id}")
		public R<String> delete(@PathVariable Long id) {
			dischargeEnergyIndicatorNewService.del(id);
		return R.success("删除成功");
	}
}
