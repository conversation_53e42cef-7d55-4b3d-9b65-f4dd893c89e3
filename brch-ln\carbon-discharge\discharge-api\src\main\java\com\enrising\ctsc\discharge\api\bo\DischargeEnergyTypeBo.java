package com.enrising.ctsc.discharge.api.bo;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放能源类型表
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-1-4
 */
@Data
public class DischargeEnergyTypeBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id,采用雪花id
     */
    private Long id;


    /**
     * 能源类型：1-水、2-电、3-气、4-油、5-热力
     */
    @NotBlank(message = "能源类型不嫩为空")
    private String energyType;

    /**
     * 单位id：使用字典
     */
    private String unit;

    /**
     * 细类
     */
    @Length(max = 50, message = "细类字符不能超过50")
    private String secondName;

    /**
     * 启用状态
     */
    private String status;

    /**
     * 密度
     */
    @Max(value = 9999, message = "密度不能大于9999")
    private BigDecimal density;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 能源类型大小类
     */
    private String sizeType;
}
