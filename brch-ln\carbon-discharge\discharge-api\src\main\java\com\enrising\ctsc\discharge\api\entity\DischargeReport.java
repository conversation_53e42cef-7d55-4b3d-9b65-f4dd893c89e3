package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 碳排放报告
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */

@Data
@TableName("discharge_report")
public class DischargeReport extends Model<DischargeReport> {

	/**
	 * 主键id,采用雪花id
	 */
		@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 创建者id
	 */
		private Long createBy;

	/**
	 * 创建时间
	 */
		private Date createTime;

	/**
	 * 更新者id
	 */
		private Long updateBy;

	/**
	 * 更新时间
	 */
		private Date updateTime;

	/**
	 * 报告年份
	 */
		private String year;

	/**
	 * 报告名称
	 */
		private String reportName;

	/**
	 * 报告内容
	 */
		private String content;

	/**
	 * 是否通知公告，1-否，2-是
	 */
		private String isNotification;

	/**
	 * 报告落款人
	 */
		private String reporter;

	/**
	 * 报告时间
	 */
		private Date reportTime;

	/**
	 * 删除标志：0-正常；1-删除
	 */
		@TableLogic
	private String delFlag;

}
