package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.JwtUtils;
import com.enrising.ctsc.discharge.api.bo.DischargeDataCoalBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataCoal;
import com.enrising.ctsc.discharge.api.enums.EnergyType;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.discharge.api.vo.DischargeDataCoalVo;
import com.enrising.ctsc.discharge.mapper.DischargeDataCoalMapper;
import com.enrising.ctsc.discharge.service.DischargeDataCoalService;
import com.enrising.ctsc.discharge.service.DischargeEnergyCoefficientService;
import com.enrising.ctsc.discharge.service.DischargeEnergyFactorService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 碳排放数据填报表（热）
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Service
@AllArgsConstructor
public class DischargeDataCoalServiceImpl extends ServiceImpl<DischargeDataCoalMapper, DischargeDataCoal> implements DischargeDataCoalService {

	private final DischargeEnergyCoefficientService dischargeEnergyCoefficientService;

	private final DischargeEnergyFactorService dischargeEnergyFactorService;

//	private final RemoteUserService remoteUserService;

	@Override
	public Page<DischargeDataCoalVo> getCoalListPage(QueryPage<DischargeDataCoalBo> queryPage) {
		LambdaQueryWrapper<DischargeDataCoal> qw = Wrappers.lambdaQuery();
		DischargeDataCoalBo dischargeDataCoalBo = queryPage.getModel();
		dischargeDataCoalBo.setSize(queryPage.getSize());
		dischargeDataCoalBo.setOffset((queryPage.getCurrent() - 1 ) * queryPage.getSize());
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			Page<DischargeDataCoal> DischargeDataCoalPage = new Page<>(queryPage.getCurrent(),
					queryPage.getSize(), true);
			Page<DischargeDataCoalVo> DischargeDataCoalVoPage = new Page<>();
			BeanUtils.copyProperties(DischargeDataCoalPage, DischargeDataCoalVoPage);
			if (ObjectUtil.isNotEmpty(dischargeDataCoalBo)) {
				if (ObjectUtil.isEmpty(dischargeDataCoalBo.getCompanyId())) {
					dischargeDataCoalBo.setCompanyId(0L);
				}
				if (ObjectUtil.isNotEmpty(dischargeDataCoalBo.getReportTime())) {
					//查询条件 填报时间
					qw.eq(DischargeDataCoal::getReportTime, dischargeDataCoalBo.getReportTime());
					dischargeDataCoalBo.setQueryStartTime(DateUtil.beginOfMonth(dischargeDataCoalBo.getReportTime()));
					dischargeDataCoalBo.setQueryEndTime(DateUtil.endOfMonth(dischargeDataCoalBo.getReportTime()));
				} else {
					//查询条件 数据年份
					String startYear = dischargeDataCoalBo.getDataYear() + "-01-01 0:00:00";
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
					dateStart = sdf.parse(startYear);
					dateEnd = DateUtil.endOfYear(dateStart);
					dischargeDataCoalBo.setQueryStartTime(dateStart);
					dischargeDataCoalBo.setQueryEndTime(dateEnd);
					qw.ge(DischargeDataCoal::getReportTime, dateStart).le(DischargeDataCoal::getReportTime, dateEnd);
				}
				//查询条件，公司
				qw.eq(dischargeDataCoalBo.getCompanyId() != 0, DischargeDataCoal::getCompanyId,
						dischargeDataCoalBo.getCompanyId());
			}
			DischargeDataCoalVoPage.setTotal(this.count(qw));
			if (ObjectUtil.isEmpty(dischargeDataCoalBo) || ObjectUtil.isEmpty(dischargeDataCoalBo.getCompanyId()) ||
					dischargeDataCoalBo.getCompanyId().equals(0L)) {
				DischargeDataCoalVoPage.setRecords(baseMapper.countCompanyData(dischargeDataCoalBo));
			} else {
				DischargeDataCoalVoPage.setRecords(baseMapper.getCompanyDataList(dischargeDataCoalBo));
			}
			return DischargeDataCoalVoPage;
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public List<DischargeDataCoalVo> getCoalListToExcel(DischargeDataCoalBo dischargeDataCoalBo) {
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			if (ObjectUtil.isNotEmpty(dischargeDataCoalBo)) {
				if (ObjectUtil.isEmpty(dischargeDataCoalBo.getCompanyId())) {
					dischargeDataCoalBo.setCompanyId(0L);
				}
				if (ObjectUtil.isNotEmpty(dischargeDataCoalBo.getReportTime())) {
					dischargeDataCoalBo.setQueryStartTime(DateUtil.beginOfMonth(dischargeDataCoalBo.getReportTime()));
					dischargeDataCoalBo.setQueryEndTime(DateUtil.endOfMonth(dischargeDataCoalBo.getReportTime()));
				} else {
					//查询条件 数据年份
					String startYear = dischargeDataCoalBo.getDataYear() + "-01-01 0:00:00";
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
					dateStart = sdf.parse(startYear);
					dateEnd = DateUtil.endOfYear(dateStart);
					dischargeDataCoalBo.setQueryStartTime(dateStart);
					dischargeDataCoalBo.setQueryEndTime(dateEnd);
				}
			}
			return baseMapper.countCompanyData(dischargeDataCoalBo);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public List<DischargeDataCoalVo> getDataList(Integer dataYear, Long companyId) {
		if (ObjectUtil.isEmpty(dataYear)) {
			throw new BusinessException("数据年份不能为空！");
		}
		if (ObjectUtil.isEmpty(companyId)) {
//			companyId = remoteUserService.getCityDeptId();
			companyId = JwtUtils.getCurrentUserCompanyId();
		}
		String startYear = dataYear + "-01-01 0:00:00";
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			dateStart = sdf.parse(startYear);
			dateEnd = DateUtil.endOfYear(dateStart);
			return this.countCompanyData(dateStart, dateEnd, companyId);

		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}


	@Override
	public List<DischargeDataCoalVo> getDataListByDate(Date dateStart, Date dateEnd, Long companyId) {
		if (ObjectUtil.isEmpty(companyId)) {
//			companyId = remoteUserService.getCityDeptId();
			companyId = JwtUtils.getCurrentUserCompanyId();
		}
		return this.countCompanyData(dateStart, dateEnd, companyId);
	}

	@Override
	public DischargeDataCoalVo detail(Long id) {
		if (ObjectUtil.isEmpty(id)) {
			throw new BusinessException("查询参数不能为空");
		}
		DischargeDataCoal DischargeDataCoal = baseMapper.selectById(id);
		if (ObjectUtil.isEmpty(DischargeDataCoal)) {
			return null;
		}
		DischargeDataCoalVo DischargeDataCoalVo = new DischargeDataCoalVo();
		BeanUtils.copyProperties(DischargeDataCoal ,DischargeDataCoalVo);
		Calendar cal = Calendar.getInstance();
		cal.setTime(DischargeDataCoalVo.getReportTime());
		Integer year=cal.get(Calendar.YEAR);//获取年
		Integer month = cal.get(Calendar.MONTH) + 1;//获取月（月份从0开始，需要加一）
		DischargeDataCoalVo.setDataMonth(month + "月");
		DischargeDataCoalVo.setDataYear(year + "年");
		DischargeDataCoalVo.setCarbonEmissions(DischargeDataCoalVo.getCoal().
				multiply(dischargeEnergyFactorService.getFactorByTime(EnergyType.COAL.getId(),
						DischargeDataCoalVo.getReportTime())).
				setScale(4, RoundingMode.HALF_UP));
		DischargeDataCoalVo.setEnergyConsumption(DischargeDataCoalVo.getCoal().
				multiply(dischargeEnergyCoefficientService.getCoefficientByTime(EnergyType.COAL.getId(),
						DischargeDataCoalVo.getReportTime())).setScale(4,
						RoundingMode.HALF_UP));
		return  DischargeDataCoalVo;
	}

	@Override
	public String add(DischargeDataCoalBo bo) {
		DischargeDataCoal entity = new DischargeDataCoal();
		BeanUtils.copyProperties(bo, entity);
//		entity.setCompanyId(remoteUserService.getCityDeptId());
		if (ObjectUtil.isEmpty(entity.getCompanyId())) {
			entity.setCompanyId(JwtUtils.getCurrentUserCompanyId());
		}
		//查询已有数据是否重复
		List<DischargeDataCoal> DischargeDataCoalList = list(
				new LambdaQueryWrapper<DischargeDataCoal>()
						.eq(DischargeDataCoal::getCompanyId, entity.getCompanyId())
						.eq(DischargeDataCoal::getReportTime, entity.getReportTime())
						.select(DischargeDataCoal::getId));
		if (CollectionUtil.isNotEmpty(DischargeDataCoalList) && DischargeDataCoalList.size() > 0) {
			return "所选月份数据已填报";
		}
		if (baseMapper.insert(entity) == 1) {
			return "";
		} else {
			return "保存失败";
		}
	}

	@Override
	public void edit(DischargeDataCoalBo bo) {
		DischargeDataCoal entity = new DischargeDataCoal();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.updateById(entity);
	}

	@Override
	public void del(Long id) {
		baseMapper.deleteById(id);
	}

	@Override
	public List<DischargeDataCoalVo> countCompanyData(Date dateStart, Date dateEnd, Long companyId) {
		DischargeDataCoalBo queryBo = new DischargeDataCoalBo();
		queryBo.setCompanyId(companyId);
		queryBo.setQueryStartTime(dateStart);
		queryBo.setQueryEndTime(dateEnd);
		if (ObjectUtil.isEmpty(companyId) || companyId.equals(0L)) {
			return baseMapper.countCompanyData(queryBo);
		} else {
			return baseMapper.getCompanyDataList(queryBo);
		}
	}

}
