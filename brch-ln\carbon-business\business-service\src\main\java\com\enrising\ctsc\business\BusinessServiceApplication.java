package com.enrising.ctsc.business;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import javax.sql.DataSource;
import java.util.Arrays;

@SpringBootApplication(scanBasePackages = {"com.enrising.ctsc.business", "com.enrising.ctsc.carbon.common"})
@MapperScan(basePackages = {"com.enrising.ctsc.business.mapper", "com.enrising.ctsc.carbon.common.mapper"})
public class BusinessServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(BusinessServiceApplication.class, args);
        System.out.println("                                                                    \n" +
                "                   $$$   $                  $$$                     \n" +
                "                   $$$  $$$                 $$$                     \n" +
                "        $$$$$$$$$$$$$$$$$$$$     $$$$$$$ $$$$$$$$$$$$        $$$    \n" +
                "        $$$$$$$$$$$$$$$$$$$$     $$$$$$$ $$$$$$$$$$$$        $$$    \n" +
                "        $$$        $$$             $$$      $$$   $$$        $$$    \n" +
                "        $$$        $$$  $$$        $$$      $$$   $$$        $$$    \n" +
                "        $$$$$$$$$$ $$$  $$$        $$$      $$$   $$$        $$$    \n" +
                "        $$$    $$$ $$$ $$$         $$$     $$$    $$$        $$$    \n" +
                "        $$$    $$  $$$$$$        $$$$$$$  $$$     $$$               \n" +
                "        $$$ $ $$$  $$$$$    $    $$$$$$$ $$$      $$$        $$$    \n" +
                "       $$$$ $$$$ $$$$$$$   $$           $$$  $$   $$$        $$$    \n" +
                "      $$$$      $$$$  $$$$$$$          $$$    $$$$$$                ");
        System.out.println("\n\n\n");
        System.out.println("           (♥◠‿◠)ﾉﾞ  系统启动成功   ლ(´ڡ`ლ)ﾞ  ");
        System.out.println("\n\n\n");
    }

    /**
     * @description 解决Long类型精度类型丢失问题
     * @param mapperBuilder
     * @return com.fasterxml.jackson.databind.ObjectMapper
     * <AUTHOR>
     * @date 2024-09-30
     */
    @Bean
    public ObjectMapper jacksonObjectMapper(Jackson2ObjectMapperBuilder mapperBuilder) {
        ObjectMapper build = mapperBuilder.createXmlMapper(false).build();
        build.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        SimpleModule module = new SimpleModule();
        module.addSerializer(Long.class, ToStringSerializer.instance);
//		module.addSerializer(Long.TYPE, ToStringSerializer.instance);
        build.registerModule(module);
        return build;

    }

    /**
     * 打印当前激活 profile 和多数据源信息
     */
    @Bean
    public CommandLineRunner printProfileAndDataSource(Environment env, DataSource dataSource) {
        return args -> {
            // 打印激活的 profiles
            String[] activeProfiles = env.getActiveProfiles();
            System.out.println("当前激活的 Profile: " + Arrays.toString(activeProfiles));

            // 获取数据源 URL（适用于默认数据源）
            String url = dataSource.getConnection().getMetaData().getURL();

            // 解析数据库地址和端口
            if (url != null && url.contains("//")) {
                String[] hostPort = url.split("//")[1].split("/")[0].split(":");
                String dbHost = hostPort[0];
                String dbPort = (hostPort.length > 1) ? hostPort[1] : "3306"; // 默认端口

                System.out.println("连接的数据库: IP=" + dbHost + ", 端口=" + dbPort);
            }
        };
    }
}
