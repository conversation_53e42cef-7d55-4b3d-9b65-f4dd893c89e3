package com.enrising.ctsc.business.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.business.api.bo.BusinessProductionDataBo;
import com.enrising.ctsc.business.api.entity.BusinessProductionData;
import com.enrising.ctsc.business.api.vo.BusinessProductionDataCompareVo;
import com.enrising.ctsc.business.api.vo.BusinessProductionDataVo;
import com.enrising.ctsc.business.api.vo.CarbonRankingVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * 生产业务数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-03-23
 */
@Mapper
public interface BusinessProductionDataMapper extends BaseMapper<BusinessProductionData> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param bo 条件
	 * @return 列表
	 */
	IPage<BusinessProductionDataVo> findList(Page<BusinessProductionDataVo> page, @Param("bo") BusinessProductionDataBo bo);

	/**
	 * 列表查询
	 *
	 * @param bo 条件
	 * @return 列表
	 */
	List<BusinessProductionDataVo> findList(@Param("bo") BusinessProductionDataBo bo);

	/**
	 * 查询全省数据对比列表
	 *
	 * @param wrapper 查询条件
	 * @return 列表
	 */
	List<BusinessProductionData> getDataCompareList(@Param(Constants.WRAPPER) LambdaQueryWrapper<BusinessProductionData> wrapper);

	List<BusinessProductionDataCompareVo> getCompanyBusList(@Param("bo") BusinessProductionDataBo bo);

	List<CarbonRankingVo> getDeptList();

	List<CarbonRankingVo> getBusinessDataList(@Param("year") String year,
											  @Param("lastYear") String lastYear,
											  @Param("currentMonth") String currentMonth);

	List<CarbonRankingVo> getElectricDataList(@Param("year") String year,
											  @Param("lastYear") String lastYear,
											  @Param("currentMonth") String currentMonth);

	List<CarbonRankingVo> getGasDataList(@Param("year") String year,
										 @Param("lastYear") String lastYear,
										 @Param("currentMonth") String currentMonth);

	List<CarbonRankingVo> getOilDataList(@Param("year") String year,
										 @Param("lastYear") String lastYear,
										 @Param("currentMonth") String currentMonth);

	List<CarbonRankingVo> getCoalDataList(@Param("year") String year,
										  @Param("lastYear") String lastYear,
										  @Param("currentMonth") String currentMonth);

	List<CarbonRankingVo> getThermalDataList(@Param("year") String year,
											 @Param("lastYear") String lastYear,
											 @Param("currentMonth") String currentMonth);

	List<CarbonRankingVo> getWaterDataList(@Param("year") String year,
										   @Param("lastYear") String lastYear,
										   @Param("currentMonth") String currentMonth);

//	List<DischargeMonitorSetting> getMonitorSettingList(@Param("year") String year);

	List<HashMap<String, Object>> getCompanyBusinessTotalList(@Param("companyId") Long companyId,
															  @Param("dataYear") String dataYear);

	HashMap<String, Object> getBusinessTotalView(@Param("query") BusinessProductionDataBo query);

	String getCompanyNameById(@Param("companyId") Long companyId);


	Long getCompanyIdByName(@Param("companyName") String companyName);
}