package com.enrising.ctsc.assess.api.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * 考核报告
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
public class AssessReportBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;


    /**
     * 考核任务id
     */
    private Long templateId;

    /**
     * 报告名称
     */
    private String reportName;

    /**
     * 报告内容
     */
    private String content;

    /**
     * 意见建议
     */
    private String suggestion;

    /**
     * 是否通知公告
     */
    private String isNotification;

    /**
     * 生成状态
     */
    private String generateStatus;

    /**
     * 报告落款人
     */
    private String reporter;

    /**
     * 报告时间
     */
    private String reportTime;

    /**
     * 预览时的类型
     */
    private String viewType;


}
