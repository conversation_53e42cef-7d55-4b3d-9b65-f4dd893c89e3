package com.enrising.ctsc.discharge.api.bo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放数据填报表（热）
 *
 * <AUTHOR>
 * @since 1.0.0 2023-03-06
 */
@Data
public class DischargeDataCoalBo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
		private Long id;


	/**
	 * 填报单位id
	 */
		private Long companyId;

	/**
	 * 填报时间
	 */
		@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date reportTime;

	/**
	 * 热力总量
	 */
		private BigDecimal coal;

	/**
	 * 数据年份
	 */
	private Integer dataYear;

	/**
	 * 查询起始时间
	 */
		@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date queryStartTime;

	/**
	 * 查询结束时间
	 */
		@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date queryEndTime;

	/**
	 * 分页数据：每页条数
	 */
	private Long size;

	/**
	 * 分页数据：起始条数
	 */
	private Long offset;
}
