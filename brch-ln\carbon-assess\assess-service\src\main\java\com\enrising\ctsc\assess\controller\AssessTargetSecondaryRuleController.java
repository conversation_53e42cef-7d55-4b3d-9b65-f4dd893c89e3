package com.enrising.ctsc.assess.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.assess.api.bo.AssessTargetSecondaryRuleBo;
import com.enrising.ctsc.assess.api.query.AssessTargetSecondaryRuleQuery;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.assess.api.vo.AssessTargetSecondaryRuleVo;
import com.enrising.ctsc.assess.service.AssessTargetSecondaryRuleService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 考核二级指标规则
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@RestController
@RequestMapping("/assess/secondaryRule")
@AllArgsConstructor
public class AssessTargetSecondaryRuleController {
	private final AssessTargetSecondaryRuleService assessTargetSecondaryRuleService;

	@GetMapping("/list")
	public TableDataInfo<AssessTargetSecondaryRuleVo> page(Page<AssessTargetSecondaryRuleVo> page, AssessTargetSecondaryRuleQuery query) {
		return assessTargetSecondaryRuleService.findList(page, query);
	}

	@GetMapping("/detail")
	public R<AssessTargetSecondaryRuleVo> get(AssessTargetSecondaryRuleQuery query) {
		AssessTargetSecondaryRuleVo detail = assessTargetSecondaryRuleService.detail(query);
		return R.success(detail, "查询成功");
	}

	@PostMapping(value = "/save")
	public R<String> save(@RequestBody AssessTargetSecondaryRuleBo bo) {
		assessTargetSecondaryRuleService.add(bo);
		return R.success("保存成功");
	}

	@PostMapping(value = "/update")
	public R<String> update(@RequestBody AssessTargetSecondaryRuleBo bo) {
		assessTargetSecondaryRuleService.edit(bo);
		return R.success("修改成功");
	}

	@PostMapping(value = "/delete/{id}")
	public R<String> delete(@PathVariable Long id) {
		assessTargetSecondaryRuleService.del(id);
		return R.success("删除成功");
	}
}
