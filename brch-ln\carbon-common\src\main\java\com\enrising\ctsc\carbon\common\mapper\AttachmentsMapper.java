package com.enrising.ctsc.carbon.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.enrising.ctsc.carbon.common.entity.Attachments;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 附件信息 Mapper 接口
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Mapper
public interface AttachmentsMapper extends BaseMapper<Attachments> {

    /**
     * 根据业务ID和业务别名查询附件列表
     *
     * @param busiId    业务ID
     * @param busiAlias 业务别名
     * @return 附件列表
     */
    List<Attachments> selectByBusiIdAndAlias(@Param("busiId") Long busiId, @Param("busiAlias") String busiAlias);

    /**
     * 根据业务ID列表批量查询附件
     *
     * @param busiIds 业务ID列表
     * @return 附件列表
     */
    List<Attachments> selectByBusiIds(@Param("busiIds") List<Long> busiIds);
} 
