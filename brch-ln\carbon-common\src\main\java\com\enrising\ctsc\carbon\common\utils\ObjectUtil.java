package com.enrising.ctsc.carbon.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 实体类工具
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-10-17
 */
@Slf4j
public class ObjectUtil extends cn.hutool.core.util.ObjectUtil {

	/**
	 * 判断对象中的属性是否全为null
	 *
	 * @param o 对象
	 * @return 全为空返回true
	 */
	public static boolean allFieldIsNull(Object o) {
		try {
			List<Field> fieldList = new ArrayList<>();
			Class<?> superclass = o.getClass().getSuperclass();
			fieldList.addAll(Arrays.asList(superclass.getDeclaredFields()));
			fieldList.addAll(Arrays.asList(o.getClass().getDeclaredFields()));
			for (Field field : fieldList) {
				field.setAccessible(true);
				Object value = field.get(o);
				if (value instanceof CharSequence) {
					if (!ObjectUtil.isNull(value)) {
						return false;
					}
				} else {
					if (null != (value)) {
						return false;
					}
				}

			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return true;

	}
}
