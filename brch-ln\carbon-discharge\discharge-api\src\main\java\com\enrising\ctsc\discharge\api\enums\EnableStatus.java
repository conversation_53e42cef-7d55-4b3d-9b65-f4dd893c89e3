package com.enrising.ctsc.discharge.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
* 启用状态
*
* <AUTHOR>
* @since 1.0.0 2023-1-3
*/
@Getter
@AllArgsConstructor
public enum EnableStatus {
	/***/
	ENABLE("1", "启用"),
	DISABLE("2", "禁用"),
	;
	private final String value;
	private final String name;

	public static String getName(String value) {
		return Arrays.stream(values()).filter(it -> it.getValue().equals(value)).findFirst().get().getName();
	}
}
