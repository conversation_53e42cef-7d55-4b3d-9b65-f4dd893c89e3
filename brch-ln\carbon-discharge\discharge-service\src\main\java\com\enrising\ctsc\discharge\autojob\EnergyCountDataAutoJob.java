package com.enrising.ctsc.discharge.autojob;

import com.enrising.ctsc.discharge.api.entity.EnergyCountData;
import com.enrising.ctsc.discharge.service.EnergyCountDataService;
import com.sccl.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 双碳能耗数据定时统计任务
 *
 * <AUTHOR>
 * @date 2024-09-16
 */

@Slf4j
@Component
@EnableScheduling
public class EnergyCountDataAutoJob {

    private final EnergyCountDataService energyCountDataService;

    public EnergyCountDataAutoJob(EnergyCountDataService energyCountDataService) {
        this.energyCountDataService = energyCountDataService;
    }

    /**
     * 定时同步pue数据任务，默认每月1日1点运行
     *
     */
//    @Scheduled(cron = "${spring.energy.count.cron:0 0/5 * * * ?}")
    @Scheduled(cron = "${spring.energy.count.cron:0 0 1 1 * ?}")
    public void doSyncEnergyData() {
        log.info("自动统计能耗双炭数据开始...");
        try {
            Date lastMonth = DateUtils.addMonths(new Date(), -1);
//            log.info("统计时间{}", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", lastMonth));
            EnergyCountData energyCountData = new EnergyCountData();
            energyCountData.setReportTime(lastMonth);
            //原始数据0、按规则计算1 同时统计
            energyCountData.setCountType("10");
            energyCountDataService.countEnergyData(energyCountData);
        } catch (Exception e) {
            log.info("自动统计能耗双硕数据错误：{}", e.getMessage());
            e.printStackTrace();
        }
        log.info("自动统计能耗双炭数据结束。");
    }

}
