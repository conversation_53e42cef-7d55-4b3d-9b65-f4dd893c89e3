package com.enrising.ctsc.carbon.common.entity;

import cn.zhxu.bs.bean.DbIgnore;
import cn.zhxu.bs.bean.SearchBean;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 部门对象 sys_Organization
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@SearchBean(dataSource = "rmpDs", tables = "sys_organizations")
public class Organization extends Model<Organization> {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 组织机构唯一编码
     */
    private String orgCode;
    /**
     * 组织机构名称
     */
    private String orgName;
    /**
     * 组织类型 Z01-单位级,Z02-部门级,Z03-部门内设级（不含班组）,Z04-班组级
     */
    private String orgType;
    /**
     * 上级公司编码
     */
    private String parentCompanyNo;
    /**
     * 上级部门编码
     */
    private String parentGroupNo;
    /**
     * 排序编号
     */
    private Integer idxNum;
    /**
     * 状态（0正常 1停用）
     */
    private String status;
    /**
     * 组织正职领导
     */
    private String orgMainLeader;
    /**
     * 组织副职领导，多值用,隔开
     */
    private String orgOtherLeader;
    /**
     * 公司类型
     */
    private String companyType;
    /**
     * 部门类型
     */
    private String groupType;
    /**
     * 省份
     */
    private String provinceCode;
    /**
     * 部门层级
     */
    private String deptLevel;
    /**
     * 组织机构英文名称
     */
    private String orgNameEng;

    /** 以下为虚拟列*/
    /**
     * 父节点ID
     */
    @DbIgnore
    private Long parentId;
    @DbIgnore
    private String parentName;
    /**
     * 下级数量
     */
    @DbIgnore
    private String childNum;
    /**
     * 用户-组织机构信息
     */
    @DbIgnore
    private UserOrganization userOrganization;


}
