package com.enrising.ctsc.discharge.controller;

import com.enrising.ctsc.discharge.api.bo.GreenEnergyManagementAuditBo;
import com.enrising.ctsc.discharge.api.query.GreenEnergyManagementAuditQuery;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.vo.GreenEnergyManagementAuditVo;
import com.enrising.ctsc.discharge.service.GreenEnergyManagementAuditService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 绿电管理审核
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2024-09-18
 */
@Slf4j
@RestController
@RequestMapping("/greenEnergyManagementAudit")
@AllArgsConstructor
public class GreenEnergyManagementAuditController {
    private final GreenEnergyManagementAuditService greenEnergyManagementAuditService;

    /**
     * 查询审核管理员列表
     */
    @GetMapping(value = "/auditUserList")
    public R auditUserList() {
        return R.success(greenEnergyManagementAuditService.auditUserList());
    }

    /**
     * 分页
     *
     * @param query 查询参数
     * @return 分页数据
     */
    @PostMapping("/list")
    public TableDataInfo<GreenEnergyManagementAuditVo> page(@RequestBody GreenEnergyManagementAuditQuery query) {
        return greenEnergyManagementAuditService.findList(query);
    }

    /**
     * 详情
     *
     * @param query 查询参数
     * @return 结果
     */
    @PostMapping("/detail")
    public R<GreenEnergyManagementAuditVo> get(@RequestBody GreenEnergyManagementAuditQuery query) {
        GreenEnergyManagementAuditVo detail = greenEnergyManagementAuditService.detail(query);
        return R.success(detail, "查询成功");
    }

    /**
     * 保存
     *
     * @param bo 数据
     * @return 结果
     */
    @PostMapping(value = "/save")
    public R<String> save(@RequestBody @Valid GreenEnergyManagementAuditBo bo) {
        greenEnergyManagementAuditService.add(bo);
        return R.success("保存成功");
    }

    /**
     * 修改
     *
     * @param bo 数据
     * @return 结果
     */
    @PostMapping(value = "/update")
    public R<String> update(@RequestBody @Valid GreenEnergyManagementAuditBo bo) {
        greenEnergyManagementAuditService.edit(bo);
        return R.success("修改成功");
    }

    /**
     * 删除
     *
     * @param id 数据ID
     * @return 结果
     */
    @PostMapping(value = "/delete/{id}")
    public R<String> delete(@PathVariable Long id) {
        greenEnergyManagementAuditService.del(id);
        return R.success("删除成功");
    }
}
