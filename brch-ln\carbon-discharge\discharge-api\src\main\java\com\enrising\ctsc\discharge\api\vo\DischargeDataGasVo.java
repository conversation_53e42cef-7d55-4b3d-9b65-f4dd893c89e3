package com.enrising.ctsc.discharge.api.vo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放数据填报表（气）
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeDataGasVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
	private Long id;

	/**
	 * 创建者id
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date createTime;

	/**
	 * 更新者id
	 */
	private Long updateBy;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date updateTime;

	/**
	 * 填报单位id
	 */
	private Long companyId;

	/**
	 * 填报时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date reportTime;

	/**
	 * 天然气
	 */
	private BigDecimal ng = BigDecimal.ZERO;

	/**
	 * 液化石油气
	 */
	private BigDecimal lpg = BigDecimal.ZERO;

	/**
	 * 数据月份
	 */
	private String dataMonth;

	/**
	 * 数据年份
	 */
	private String dataYear;

	/**
	 * 碳排放量
	 */
	private BigDecimal carbonEmissions;

	/**
	 * 能源消耗总量
	 */
	private BigDecimal energyConsumption;

	/**
	 * 天然气碳排放量
	 */
	private BigDecimal carbonNg = BigDecimal.ZERO;

	/**
	 * 液化石油气碳排放量
	 */
	private BigDecimal carbonLpg = BigDecimal.ZERO;

	/**
	 * 天然气能耗
	 */
	private BigDecimal consumptionNg = BigDecimal.ZERO;

	/**
	 * 液化石油气能耗
	 */
	private BigDecimal consumptionLpg = BigDecimal.ZERO;

	public BigDecimal getCarbonEmissions() {
		return carbonNg.add(carbonLpg);
	}

	public BigDecimal getEnergyConsumption() {
		return consumptionNg.add(consumptionLpg);
	}
}
