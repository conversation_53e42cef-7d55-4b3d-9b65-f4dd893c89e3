<template>
  <div>
    <div class="tableCard">
      <div class="tableTitle">
        <div>{{titleName}}</div>
        <Button type="text" @click="exportCsv">导出</Button>
      </div>
      <Table
          border
          height="500"
          :loading="loading"
          :columns="columns"
          :data="tableData"
      ></Table>
      <Page
          size="small"
          :total="pageTotal"
          :current="pageNum"
          :page-size="pageSize"
          show-elevator
          show-sizer
          show-total
          placement="top"
          @on-change="handlePage"
          @on-page-size-change="handlePageSize"
      ></Page>
    </div>
  </div>
</template>

<script>
import {getPowerError2} from "@/api/account";
import axios from "@/libs/api.request";

export default {
  name: "query",
  activeName:'局站与电表关联异常项',
  data(){
    return{
      pageTotal:0,
      pageNum:1,
      pageSize:10,
      exportName:'',
      operationsBranch:'',
      cityName:'',
      cityCode:"1000373",
      countyCompanies:"1000406",
      siteType:"1",
      month:"",
      titleName:'局站与电表关联异常项',
      activeName:'电价合理性',
      loading:false,
      columns:[],
      //局站与电表关联异常表
      stationError:[
        { title: "序号", type: 'index', minWidth:70,align: "center"},
        { title: "所属部门", minWidth:150,key: 'city', align: "center"},
        { title: "运营分局", minWidth:150,key: 'operationsBranch', align: "center"},
        { title: "期号", minWidth:150,key: 'accountNo', align: "center"},
        { title: "类型", minWidth:150,key: "errorType", align: "center" ,},
        { title: "电表户名/协议号码",minWidth:150, key: "ammeterid", align: "center" },
        { title: "集团站址编码", minWidth:150,key: "stationcode", align: "center" },
        { title: "铁塔站址编码", minWidth:150,key: "towerSiteCode", align: "center" },
      ],
      //电价合理性表
      priceValid:[
        { title: "序号", type: 'index', minWidth:70,align: "center"},
        { title: "所属部门", minWidth:150,key: 'city', align: "center"},
        { title: "运营分局", minWidth:150,key: 'operationsBranch', align: "center"},
        { title: "台账期号", minWidth:150,key: 'accountNo', align: "center"},
        { title: "集团站址编码", minWidth:150,key: "stationcode", align: "center" },
        { title: "电表户名/协议号码",minWidth:150, key: "ammeterid", align: "center" },
        // { title: "类型", minWidth:150,key: "errorType", align: "center" ,},
        { title: "铁塔站址编码", minWidth:150,key: "towerSiteCode", align: "center" },
        { title: "台账单价", minWidth:150,key: "accountPrice", align: "center" ,},
        { title: "基础信息单价(合同单价)", minWidth:150,key: "meterPrice", align: "center" ,},
      ],
      //电表站址一致性
      stationSame:[
        { title: "序号", type: 'index', minWidth:70,align: "center"},
        { title: "所属部门", minWidth:150,key: 'city', align: "center"},
        { title: "运营分局", minWidth:150,key: 'operationsBranch', align: "center"},
        { title: "期号", minWidth:150,key: 'accountNo', align: "center"},
        { title: "电表户名/协议号码",minWidth:150, key: "ammeterid", align: "center" },
        { title: "集团站址编码", minWidth:150,key: "stationcode", align: "center" },
        { title: "铁塔站址编码", minWidth:150,key: "towerSiteCode", align: "center" },
      ],
      //台账周期连续性
      accountZQ:[
        { title: "序号", type: 'index', minWidth:70,align: "center"},
        { title: "所属部门", minWidth:150,key: 'city', align: "center"},
        { title: "运营分局", minWidth:150,key: 'operationsBranch', align: "center"},
        // { title: "类型", minWidth:150,key: "errorType", align: "center" ,},
        { title: "电表户名/协议号码",minWidth:150, key: "ammeterid", align: "center" },
        // { title: "期号", minWidth:150,key: 'ledgerPeriod', align: "center"},
        { title: "期号", minWidth:150,key: 'accountNo', align: "center"},
        { title: "集团站址编码", minWidth:150,key: "stationcode", align: "center" },
        { title: "铁塔站址编码", minWidth:150,key: "towerSiteCode", align: "center" },
        { title: "上次起始日期", minWidth:150,key: "lastStartTime", align: "center"},
        { title: "上次截止日期", minWidth:150,key: "lastStopTime", align: "center"},
        { title: "本次起始日期", minWidth:150,key: "startTime", align: "center"},
        { title: "本次截止日期", minWidth:150,key: "stopTime", align: "center"},
        { title: "本次台账录入时间", minWidth:150,key: "lasteditdate", align: "center"},
      //   { title: "上次台账录入时间", minWidth:150,key: "auditTimeLast", align: "center"},
      //   { title: "报账周期差异（天）", minWidth:150,key: "differencesDay", align: "center"},
      ],
      //电表度数连续性
      dbds:[
        { title: "序号", type: 'index', minWidth:70,align: "center"},
        { title: "所属部门", minWidth:150,key: 'city', align: "center"},
        { title: "运营分局", minWidth:150,key: 'operationsBranch', align: "center"},
        { title: "类型", minWidth:150,key: "errorType", align: "center" ,},
        { title: "电表户名/协议号码",minWidth:150, key: "ammeterid", align: "center" },
        { title: "台账期号", minWidth:150,key: 'accountNo', align: "center"},
        { title: "集团站址编码", minWidth:150,key: "stationcode", align: "center" },
        { title: "铁塔站址编码", minWidth:150,key: "towerSiteCode", align: "center" },
        { title: "上次起始度数", minWidth:150,key: "lastStartDegrees", align: "center"},
        { title: "上次截止度数", minWidth:150,key: "lastStopDegrees", align: "center"},
        { title: "本次起始度数", minWidth:150,key: "startDegrees", align: "center"},
        { title: "本次截止度数", minWidth:150,key: "stopDegrees", align: "center"},
        { title: "本次台账录入时间", minWidth:150,key: "lasteditdate", align: "center"},
        // { title: "本次台账录入时间", minWidth:150,key: "auditTime", align: "center"},
      ],
      //日均电量及台账波动性异常查看
      tzdl:[
        { title: "序号", type: 'index', minWidth:70,align: "center"},
        { title: "所属部门", minWidth:150,key: 'city', align: "center"},
        { title: "运营分局", minWidth:150,key: 'operationsBranch', align: "center"},
        { title: "类型", minWidth:150,key: "errorType", align: "center" ,},
        { title: "电表户名/协议号码",minWidth:150, key: "ammeterid", align: "center" },
        { title: "台账期号", minWidth:150,key: 'accountNo', align: "center"},
        { title: "集团站址编码", minWidth:150,key: "stationcode", align: "center" },
        { title: "铁塔站址编码", minWidth:150,key: "towerSiteCode", align: "center" },
        { title: "台账电量", minWidth:150,key: "towerDegrees", align: "center"},
        { title: "标准日均电量", minWidth:150,key: "useDay", align: "center"},
        { title: "波动幅度", minWidth:150,key: "degreesFluctuate", align: "center"},
      ],
      //日均电量及台账波动性异常查看 
      tzdl2:[
        { title: "序号", type: 'index', minWidth:70,align: "center"},
        { title: "所属部门", minWidth:150,key: 'city', align: "center"},
        { title: "运营分局", minWidth:150,key: 'operationsBranch', align: "center"},
        { title: "电表户名/协议号码",minWidth:150, key: "ammeterid", align: "center" },
        { title: "台账期号", minWidth:150,key: 'accountNo', align: "center"},
        { title: "集团站址编码", minWidth:150,key: "stationcode", align: "center" },
        { title: "铁塔站址编码", minWidth:150,key: "towerSiteCode", align: "center" },
        { title: "报账开始时间", minWidth:150,key: 'ledgerPeriod', align: "center"},
        { title: "报账结束时间", minWidth:150,key: 'ledgerPeriod', align: "center"},
        { title: "台账电量", minWidth:150,key: "towerDegrees", align: "center"},
        { title: "台账日均电量", minWidth:150,key: "towerDegrees", align: "center"},
        { title: "集团5gr日均电量(标准电量)", minWidth:150,key: "degreesDay", align: "center"},
        // { title: "集团5gr日均电量(标准电量)", minWidth:150,key: "useDay", align: "center" },
        { title: "波动幅度", minWidth:150,key: "degreesFluctuate", align: "center"},
      ],
      //日均电量及台账波动性异常查看 
      tzdl22:[
        { title: "序号", type: 'index', minWidth:70,align: "center"},
        { title: "所属部门", minWidth:150,key: 'city', align: "center"},
        { title: "运营分局", minWidth:150,key: 'operationsBranch', align: "center"},
        { title: "电表户名/协议号码",minWidth:150, key: "ammeterid", align: "center" },
        { title: "台账期号", minWidth:150,key: 'accountNo', align: "center"},
        { title: "集团站址编码", minWidth:150,key: "stationcode", align: "center" },
        { title: "铁塔站址编码", minWidth:150,key: "towerSiteCode", align: "center" },
        { title: "报账开始时间", minWidth:150,key: 'startTime', align: "center"},
        { title: "报账结束时间", minWidth:150,key: 'stopTime', align: "center"},
        { title: "本期总电量", minWidth:150,key: "degrees", align: "center"},
        { title: "台账日均电量", minWidth:150,key: "useDay", align: "center"},
        { title: "集团5gr日均电量(标准电量)", minWidth:150,key: "degreesDay", align: "center"},
        // { title: "集团5gr日均电量(标准电量)", minWidth:150,key: "useDay", align: "center" },
        { title: "波动幅度", minWidth:150,key: "degreesFluctuate", align: "center"},
      ],
      //日均耗电量
      tzdl3:[
        { title: "序号", type: 'index', minWidth:70,align: "center"},
        { title: "所属部门", minWidth:150,key: 'city', align: "center"},
        { title: "运营分局", minWidth:150,key: 'operationsBranch', align: "center"},
        { title: "电表户名/协议号码",minWidth:150, key: "ammeterid", align: "center" },
        { title: "台账期号", minWidth:150,key: 'accountNo', align: "center"},
        // { title: "台账期号", minWidth:150,key: 'ledgerPeriod', align: "center"},
        { title: "集团站址编码", minWidth:150,key: "stationcode", align: "center" },
        { title: "铁塔站址编码", minWidth:150,key: "towerSiteCode", align: "center" },
        { title: "台账电量", minWidth:150,key: "towerDegrees", align: "center"},
        { title: "台账日均耗电量", minWidth:150,key: "degreesDay", align: "center"},
        { title: "标准日均电量", minWidth:150,key: "useDay", align: "center"},
      ],
      //分摊比列准确性查看
      gxzyc:[
        { title: "序号", type: 'index', minWidth:70,align: "center"},
        { title: "所属部门", minWidth:150,key: 'city', align: "center"},
        { title: "运营分局", minWidth:150,key: 'operationsBranch', align: "center"},
        // { title: "类型", minWidth:150,key: "errorType", align: "center" ,},
        { title: "类型", minWidth:150,key: "ratioErrorType", align: "center" ,},
        { title: "电表户名/协议号码",minWidth:150, key: "ammeterid", align: "center" },
        { title: "期号", minWidth:150,key: 'accountNo', align: "center"},
        { title: "集团站址编码", minWidth:150,key: "stationcode", align: "center" },
        { title: "铁塔站址编码", minWidth:150,key: "towerSiteCode", align: "center" },
        // { title: "维护共享家数", minWidth:150,key: "shareNumber", align: "center" },
        { title: "维护共享家数", minWidth:150,key: "shareNum", align: "center" },
        { title: "协议管理能耗比例", minWidth:150,key: "meterPercent", align: "center" },
        // { title: "电信", minWidth:150,key: "percent", align: "center" },
        { title: "电信", minWidth:150,key: "dxApportionmentratio", align: "center" },
        { title: "移动", minWidth:150,key: "mobileApportionmentratio", align: "center" },
        { title: "联通", minWidth:150,key: "unicomApportionmentratio", align: "center" },
        { title: "拓展", minWidth:150,key: "expandApportionmentratio", align: "center" },
        { title: "能源", minWidth:150,key: "energyApportionmentratio", align: "center" },
        // { title: "合计", minWidth:150,key: "", align: "center" },
        { title: "合计", minWidth:150,key: "totalApportionmentratio", align: "center" },
      ],
      //独享站分摊比例异常日均电量异常查看
      dxzft:[
        { title: "序号", type: 'index', minWidth:70,align: "center"},
        { title: "所属部门", minWidth:150,key: 'city', align: "center"},
        { title: "运营分局", minWidth:150,key: 'operationsBranch', align: "center"},
        { title: "类型", minWidth:150,key: "ratioErrorType", align: "center" ,},
        // { title: "类型", minWidth:150,key: "errorType", align: "center" ,},
        // { title: "期号", minWidth:150,key: 'ledgerPeriod', align: "center"}, 
        { title: "期号", minWidth:150,key: 'accountNo', align: "center"},
        { title: "电表户名/协议号码",minWidth:150, key: "ammeterid", align: "center" },
        { title: "集团站址编码", minWidth:150,key: "stationcode", align: "center" },
        { title: "铁塔站址编码", minWidth:150,key: "towerSiteCode", align: "center" },
        // { title: "维护共享家数", minWidth:150,key: "shareNumber", align: "center" },
        { title: "维护共享家数", minWidth:150,key: "shareNum", align: "center" },
        // { title: "电信", minWidth:150,key: "percent", align: "center" },
        { title: "电信", minWidth:150,key: "dxApportionmentratio", align: "center" },
        { title: "移动", minWidth:150,key: "mobileApportionmentratio", align: "center" },
        { title: "联通", minWidth:150,key: "unicomApportionmentratio", align: "center" },
        { title: "拓展", minWidth:150,key: "expandApportionmentratio", align: "center" },
        { title: "能源", minWidth:150,key: "energyApportionmentratio", align: "center" },
        // { title: "合计", minWidth:150,key: "", align: "center" },
        { title: "合计", minWidth:150,key: "totalApportionmentratio", align: "center" },
      ],
      //台账周期异常查看
      tzyczq:[
        { title: "序号", type: 'index', minWidth:70,align: "center"},
        { title: "所属部门", minWidth:150,key: 'city', align: "center"},
        { title: "运营分局", minWidth:150,key: 'operationsBranch', align: "center"},
        { title: "电表户名/协议号码",minWidth:150, key: "ammeterid", align: "center" },
        { title: "台账期号", minWidth:150,key: 'accountNo', align: "center"},
        // { title: "台账期号", minWidth:150,key: 'ledgerPeriod', align: "center"},
        { title: "集团站址编码", minWidth:150,key: "stationcode", align: "center" },
        { title: "铁塔站址编码", minWidth:150,key: "towerSiteCode", align: "center" },
        { title: "本次台账录入时间", minWidth:150,key: "lasteditdate", align: "center"},
        { title: "上次台账录入时间", minWidth:150,key: "auditTimeLast", align: "center"},
        { title: "报账周期差异", minWidth:150,key: "differencesDay", align: "center"},
      ],
      //总数
      zs:[
        { title: "序号", type: 'index', minWidth:70,align: "center"},
        { title: "所属部门", minWidth:150,key: 'city', align: "center"},
        { title: "运营分局", minWidth:150,key: 'operationsBranch', align: "center"},
        { title: "电表户名/协议号码",minWidth:150, key: "ammeterid", align: "center" },
        { title: "集团站址编码", minWidth:150,key: "stationcode", align: "center" },
        { title: "铁塔站址编码", minWidth:150,key: "towerSiteCode", align: "center" },
        { title: "状态", minWidth:150,key: "status", align: "center" },
        { title: "异常项", minWidth:150,key: "abnormal", align: "center" },
        { title: "电表负责人", minWidth:150,key: "headPeople", align: "center" },

      ],
      //电量合理性
      dlhlx:[
        { title: "序号", type: 'index', minWidth:70,align: "center"},
        { title: "所属部门", minWidth:150,key: 'city', align: "center"},
        { title: "运营分局", minWidth:150,key: 'operationsBranch', align: "center"},
        { title: "类型", minWidth:150,key: "errorType", align: "center" ,},
        { title: "电表户名/协议号码",minWidth:150, key: "ammeterid", align: "center" },
        // { title: "期号", minWidth:150,key: 'ledgerPeriod', align: "center"},
        { title: "期号", minWidth:150,key: 'accountNo', align: "center"},
        { title: "集团站址编码", minWidth:150,key: "stationcode", align: "center" },
        { title: "铁塔站址编码", minWidth:150,key: "towerSiteCode", align: "center" },
        { title: "上期报账台账电量", minWidth:150,key: "lastDegrees", align: "center" },
        { title: "本期报账台账电量", minWidth:150,key: "degrees", align: "center" },
      ],
      //电量合理性(省内大数据)
      dlhlxda:[
        { title: "序号", type: 'index', minWidth:70,align: "center"},
        { title: "所属部门", minWidth:150,key: 'city', align: "center"},
        { title: "运营分局", minWidth:150,key: 'operationsBranch', align: "center"},
        // { title: "类型", minWidth:150,key: "type", align: "center" ,},
        // { title: "类型", minWidth:150,key: "errorType", align: "center" ,},
        { title: "台账期号", minWidth:150,key: 'accountNo', align: "center"},
        { title: "集团站址编码", minWidth:150,key: "stationcode", align: "center" },
        { title: "电表户名/协议号码",minWidth:150, key: "ammeterid", align: "center" },
        { title: "铁塔站址编码", minWidth:150,key: "towerSiteCode", align: "center" },
        // { title: "上期报账台账电量", minWidth:150,key: "lastDegrees", align: "center" },
        // { title: "本期报账台账电量", minWidth:150,key: "degrees", align: "center" },
        { title: "报账开始时间", minWidth:150,key: "startTime", align: "center" },
        { title: "报账结束时间", minWidth:150,key: "stopTime", align: "center" },
        { title: "台账电量", minWidth:150,key: "degrees", align: "center" },
        // { title: "省内大数据平台电量", minWidth:150,key: "lastDegrees", align: "center" },
      ],
      tableData:[
      ]
    }
  },
  methods:{
    handlePage(value){
      console.log(value);
      this.pageNum=value;
      this.query();
    },
    handlePageSize(value){
      console.log(value);
      this.pageSize=value;
      this.query();
    },
    exportCsv() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const day = now.getDate();
      let params = {
        city: this.cityName,
        cityCode:this.cityCode,
        countyCompaniesCode:this.countyCompanies,
        operationsBranch:this.operationsBranch,
        siteType:this.siteType,
        month:this.month,
        exportName:this.exportName,
        fileName:this.exportName
      };
      let req = {
        url: "/business/poweraudit/exportAuditDetails",
        method: "post",
        data: params,
      };
      axios.file(req).then((res) => {
        const content = res;
        const blob = new Blob([content]);
        const fileName = this.exportName+`导出.xlsx`;
        if ("download" in document.createElement("a")) {
          // 非IE下载
          const elink = document.createElement("a");
          elink.download = fileName;
          elink.style.display = "none";
          elink.href = URL.createObjectURL(blob);
          document.body.appendChild(elink);
          elink.click();
          URL.revokeObjectURL(elink.href); // 释放URL 对象
          document.body.removeChild(elink);
        } else {
          // IE10+下载
          navigator.msSaveBlob(blob, fileName);
        }
      })
      .catch((err) => {
        console.log(err);
      });
    },
    checktable(){
      // this.priceValid[2].title=this.type==='tz'?"台账期号":"报账期号";
      // this.priceValid[2].key=this.type==='tz'?"tzqh":'bzdqh';
      // this.accountZQ[2].key=this.type==='tz'?"tzqh":'bzdqh';
      // this.dbds[2].key=this.type==='tz'?"tzqh":'bzdqh';
      // this.tzdl[2].key=this.type==='tz'?"tzqh":'bzdqh';
      // this.gxzyc[2].key=this.type==='tz'?"tzqh":'bzdqh';
      // this.dxzft[2].key=this.type==='tz'?"tzqh":'bzdqh';
      // this.tzyczq[2].key=this.type==='tz'?"tzqh":'bzdqh';
      switch (this.activeName){
        case "一表多站":
          this.titleName="一表多站";
          this.columns=this.stationError;
          this.menu='A';
          break;
        case "一站多表/多站多表":
          this.titleName="一站多表/多站多表";
          this.columns=this.stationError;
          this.menu='A';
          break;
        case "电价合理性":
          this.titleName="电价合理性"
          this.columns=this.priceValid
          this.menu='B';
          break;
        case "电表站址一致性":
          this.titleName="电表站址一致性"
          this.columns=this.stationSame
          this.menu='C';
          break;
        case "台账周期连续性":
          this.titleName="台账周期异常"
          this.columns=this.accountZQ
          this.menu='D';
          break;
        case "电表度数连续性":
          this.titleName="电表度数连续性"
          this.columns=this.dbds
          this.menu='E';
          break;
        case "日均电量的波动合理性(集团5gr)":
          this.titleName="日均电量的波动合理性(集团5gr)"
          this.columns=this.tzdl22
          this.menu='F';
          break;
        case "日均电量的波动合理性":
          this.titleName="日均电量的波动合理性"
          this.columns=this.tzdl2
          this.menu='F';
          break;
        case "日均耗电量合理性":
          this.titleName="日均耗电量合理性"
          this.columns=this.tzdl3
          this.menu='F';
          break;
        case "分摊比例准确性":
          this.titleName="分摊比例准确性"
          this.columns=this.gxzyc
          this.menu='G';
          break;
        // case "局站独享共享设置":
        //   this.titleName="局站独享共享设置"
        //   this.columns=this.dxzft
        //   this.menu='H';
        //   break;
        case "台账周期合理性":
          this.titleName="台账周期合理性"
          this.columns=this.tzyczq
          this.menu='I';
          break;
        case "电量合理性":
          this.titleName="电量合理性"
          this.columns=this.dlhlx
          this.menu='I';
          break;
        // case "电量合理性(省内大数据)":
        //   this.titleName="电量合理性(省内大数据)"
        //   this.columns=this.dlhlxda
        //   this.menu='I';
        //   break;
        case "地市和运营分局":
          this.titleName="总数详表"
          this.columns=this.zs
          this.menu='I';
          break;
      }
      this.query();
    },
    query(){
      this.loading=true;
      let data={
        cityCode:this.cityCode,
        countyCompaniesCode:this.countyCompanies,
        operationsBranch:this.operationsBranch,
        city: this.cityName,
        siteType:this.siteType,
        month:this.month,
        exportName:this.exportName,
        pageNum:this.pageNum,
        pageSize:this.pageSize
      }
      getPowerError2(data).then((res) => {
        this.loading=false;
        if(res.data){
          this.tableData=res.data.list
          this.pageTotal=res.data.total
        }
      })
    }
  },
  mounted() {
  },
  // watch:{
  //   activeName(val) {
  //     if (val) {
  //       this.checktable();
  //     }
  //   },
  // }
}
</script>
<style scoped>
.tableCard{
  width: 100%;
  margin-bottom: 20px;
  padding:10px;
  height:auto;
  background-color: white
}
.tableTitle{
  position: relative;
  left: 5px;
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  font-size: 14px;
  margin-bottom:7px;
  padding-right: 5px;
  font-weight: bolder;
}
.tableTitle2{
  position: relative;
  left: 0px;
  font-size: 14px;
  margin-bottom:7px;
  font-weight: 500;
}
.tableTitle::before{
  position: absolute;
  top:3px;
  left: -5px;
  display: inline-block;
  content: "";
  width: 2px;
  height: 16px;
  background-color: #1e88e5;
}
</style>
