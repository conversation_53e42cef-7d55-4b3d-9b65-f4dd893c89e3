package com.enrising.ctsc.discharge.api.vo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放数据填报表（油）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeDataTotalVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 填报单位id
	 */
	private Long companyId;

	/**
	 * 数据填报单位名称
	 */
	private String companyName;

	/**
	 * 填报时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date reportTime;

	/**
	 * 汽油
	 */
	private BigDecimal gasoline = new BigDecimal("0");

	/**
	 * 柴油
	 */
	private BigDecimal diesel = new BigDecimal("0");

	/**
	 * 原油
	 */
	private BigDecimal crude = new BigDecimal("0");

	/**
	 * 燃料油
	 */
	private BigDecimal fuel = new BigDecimal("0");

	/**
	 * 煤油
	 */
	private BigDecimal kerosene = new BigDecimal("0");

	/**
	 * 电力数据
	 */
	private BigDecimal power = new BigDecimal("0");

	/**
	 * 外购绿电
	 */
	private BigDecimal outsourcingGreenPower = new BigDecimal("0");

	/**
	 * 外购火电
	 */
	private BigDecimal outsourcingThermalPower = new BigDecimal("0");

	/**
	 * 自有新能源发电
	 */
	private BigDecimal ownGreenPower = new BigDecimal("0");

	/**
	 * 天然气
	 */
	private BigDecimal ng = new BigDecimal("0");

	/**
	 * 液化石油气
	 */
	private BigDecimal lpg = new BigDecimal("0");

	/**
	 * 热力总量
	 */
	private BigDecimal thermal = new BigDecimal("0");

	/**
	 * 煤碳用量
	 */
	private BigDecimal coal = new BigDecimal("0");

	/**
	 * 用水总量
	 */
	private BigDecimal water = new BigDecimal("0");

	/**
	 * 汽油
	 */
	private BigDecimal carbonGasoline = new BigDecimal("0");

	/**
	 * 柴油
	 */
	private BigDecimal carbonDiesel = new BigDecimal("0");

	/**
	 * 原油
	 */
	private BigDecimal carbonCrude = new BigDecimal("0");

	/**
	 * 燃料油
	 */
	private BigDecimal carbonFuel = new BigDecimal("0");

	/**
	 * 煤油
	 */
	private BigDecimal carbonKerosene = new BigDecimal("0");

	/**
	 * 电力数据
	 */
	private BigDecimal carbonPower = new BigDecimal("0");

	/**
	 * 天然气
	 */
	private BigDecimal carbonNg = new BigDecimal("0");

	/**
	 * 液化石油气
	 */
	private BigDecimal carbonLpg = new BigDecimal("0");

	/**
	 * 热力总量
	 */
	private BigDecimal carbonThermal = new BigDecimal("0");

	/**
	 * 用水总量
	 */
	private BigDecimal carbonWater = new BigDecimal("0");

	/**
	 * 煤碳排放量
	 */
	private BigDecimal carbonCoal = new BigDecimal("0");

	/**
	 * 汽油
	 */
	private BigDecimal consumptionGasoline = new BigDecimal("0");

	/**
	 * 柴油
	 */
	private BigDecimal consumptionDiesel = new BigDecimal("0");

	/**
	 * 原油
	 */
	private BigDecimal consumptionCrude = new BigDecimal("0");

	/**
	 * 燃料油
	 */
	private BigDecimal consumptionFuel = new BigDecimal("0");

	/**
	 * 煤油
	 */
	private BigDecimal consumptionKerosene = new BigDecimal("0");

	/**
	 * 电力数据
	 */
	private BigDecimal consumptionPower = new BigDecimal("0");

	/**
	 * 天然气
	 */
	private BigDecimal consumptionNg = new BigDecimal("0");

	/**
	 * 液化石油气
	 */
	private BigDecimal consumptionLpg = new BigDecimal("0");

	/**
	 * 热力总量
	 */
	private BigDecimal consumptionThermal = new BigDecimal("0");

	/**
	 * 用水总量
	 */
	private BigDecimal consumptionWater = new BigDecimal("0");

	/**
	 * 煤碳排放量
	 */
	private BigDecimal consumptionCoal = new BigDecimal("0");

	/**
	 * 数据月份
	 */
	private String dataMonth;

	/**
	 * 数据年份
	 */
	private String dataYear;

	/**
	 * 碳排放量
	 */
	private BigDecimal carbonEmissions = new BigDecimal("0");

	/**
	 * 能源消耗总量
	 */
	private BigDecimal energyConsumption = new BigDecimal("0");

	/**
	 * 气  能源消耗总量
	 */
	private BigDecimal gasEnergyConsumption = new BigDecimal("0");

	/**
	 * 油  能源消耗总量
	 */
	private BigDecimal oilEnergyConsumption = new BigDecimal("0");

	/**
	 * 碳排放量同比
	 */
	private BigDecimal carbonYoY;

	/**
	 * 碳排放量环比
	 */
	private BigDecimal carbonMoM;

	/**
	 * 月份下标
	 */
	private Integer month;

	/**
	 * 区划编码
	 */
	private String cityCode;

	/**
	 * 业务数据总量
	 */
	private BigDecimal telecomBusinessTotal = new BigDecimal("0");

	public DischargeDataTotalVo() {
	}

	public DischargeDataTotalVo(Long companyId, String companyName) {
		this.companyId = companyId;
		this.companyName = companyName;

	}

	public BigDecimal getPower() {
		return outsourcingThermalPower.subtract(ownGreenPower);
	}

	public BigDecimal getCarbonEmissions() {
		return carbonPower.add(carbonCoal).add(carbonWater).add(carbonThermal).add(carbonNg).add(carbonLpg)
				.add(carbonGasoline).add(carbonDiesel).add(carbonCrude).add(carbonFuel).add(carbonKerosene);
	}

	public BigDecimal getEnergyConsumption() {
		return consumptionPower.add(consumptionCoal).add(consumptionWater).add(consumptionThermal).add(consumptionNg).add(consumptionLpg)
				.add(consumptionGasoline).add(consumptionDiesel).add(consumptionCrude).add(consumptionFuel).add(consumptionKerosene);
	}

	public BigDecimal getGasEnergyConsumption() {
		return consumptionNg.add(consumptionLpg);
	}

	public BigDecimal getOilEnergyConsumption() {
		return consumptionGasoline.add(consumptionDiesel).add(consumptionCrude).add(consumptionFuel).add(consumptionKerosene);
	}

}
