package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.bo.GreenEnergyManagementBo;
import com.enrising.ctsc.discharge.api.entity.GreenEnergyManagement;
import com.enrising.ctsc.discharge.api.query.GreenEnergyManagementQuery;
import com.enrising.ctsc.discharge.api.vo.GreenEnergyManagementVo;

import java.math.BigDecimal;

/**
 * 绿电管理
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2024-09-15
 */
public interface GreenEnergyManagementService extends IService<GreenEnergyManagement> {

    /**
    * 分页查询
    * @param page  分页
    * @param query	参数
    * @return	列表
    */
    TableDataInfo<GreenEnergyManagementVo> findList(GreenEnergyManagementQuery query);

    /**
    * 详情
    *
    * @param query 参数
    * @return 详情
    */
    GreenEnergyManagementVo detail(GreenEnergyManagementQuery query);

    BigDecimal getDeduction(BigDecimal monthlyContractPower);
    /**
    * 新增
    * @param bo 参数
    */
    void add(GreenEnergyManagementBo bo);

    /**
    * 修改
    * @param bo 参数
    */
    void edit(GreenEnergyManagementBo bo);

    /**
    * 删除
    * @param id 主键id
    */
    void del(Long id);

    /**
    * 导出
    * @param query 参数
    */
    void export(GreenEnergyManagementQuery query);
}
