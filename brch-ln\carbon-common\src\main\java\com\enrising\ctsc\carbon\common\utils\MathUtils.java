package com.enrising.ctsc.carbon.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * 数学辅助工具
 *
 * <AUTHOR>
 * @date 2022/10/5 10:09
 */

public class MathUtils {
	/**
	 * 求环比,计算公式=(本期数据-上期数据)/上期数据 *100%
	 *
	 * @param bCount 本期数据
	 * @param sCount 上期数据
	 * @return
	 */
	public static String d2dRatio(Long bCount, Long sCount) {
		//差
		Long differ = bCount - sCount;
		//除数
		BigDecimal denominator = BigDecimal.valueOf(differ);
		//被除数
		BigDecimal numerator = BigDecimal.valueOf(sCount);
		//百分比
		String ratio;
		//被除数为零，无意义
		if (bCount == 0 && sCount == 0) {
			ratio = "0%";
		} else if (sCount == 0) {
			ratio = "+100%";
		} else {
			//商
			BigDecimal quotient = denominator.divide(numerator, 6, BigDecimal.ROUND_HALF_DOWN);
			ratio = new DecimalFormat("#########0%").format(quotient);
			if (!ratio.startsWith("-")) {
				ratio = "+" + ratio;
			}
		}
		return ratio;
	}

	/**
	 * @param bCount 本期数据
	 * @param sCount 上期数据
	 * @return
	 */
	public static String d2dRatio(BigDecimal bCount, BigDecimal sCount) {
		//除数
		BigDecimal denominator = bCount;
		//被除数
		BigDecimal numerator = sCount;
		//百分比
		String ratio;
		if (sCount.compareTo(new BigDecimal(0)) == 0) {//被除数为零，无意义
			ratio = new DecimalFormat("#########0.00%").format(denominator);
		} else {
			//商
			BigDecimal quotient = denominator.divide(numerator, 6, BigDecimal.ROUND_HALF_DOWN);
			ratio = new DecimalFormat("#########0.00%").format(quotient);
			if (ratio.startsWith("-")) {
				ratio = ratio.replaceAll("-", "");
			}
		}
		return ratio;
	}


	/**
	 * @param bCount 本期数据
	 * @param sCount 上期数据
	 * @return
	 */
	public static String d2dRate(BigDecimal bCount, BigDecimal sCount) {
		//除数
		BigDecimal denominator = bCount.subtract(sCount);
		//百分比
		String ratio;
		if (sCount.compareTo(new BigDecimal(0)) == 0) {//被除数为零，无意义
			ratio = new DecimalFormat("#########0.00%").format(bCount);
		} else {
			//商
			BigDecimal quotient = denominator.divide(sCount, 2, BigDecimal.ROUND_HALF_DOWN);
			ratio = new DecimalFormat("#########0.00%").format(quotient);
		}
		return ratio;
	}

	/**
	 * @param bCount (本期数据-上期数据)/上期数据 获取增长率 * 100
	 * @param sCount 上期数据
	 * @return
	 */
	public static String getUpRate(BigDecimal bCount, BigDecimal sCount) {
		DecimalFormat df = new DecimalFormat("#.00");
		//除数
		BigDecimal denominator = bCount.subtract(sCount);
		double ratio;
		if (sCount.compareTo(new BigDecimal(0)) == 0) {//被除数为零，无意义
//			ratio = bCount.doubleValue();
			return "0";
		} else {
			//商 取小数点后4位计算，百分号保留2位
			ratio = denominator.divide(sCount, 4, RoundingMode.HALF_DOWN).multiply(new BigDecimal(100))
					.setScale(2, RoundingMode.HALF_UP).doubleValue();
		}
		return df.format(ratio);
	}

	/**
	 * 求环比,计算公式=(本期数据-上期数据)/上期数据 *100%
	 *
	 * @param bCount 本期数据
	 * @param sCount 上期数据
	 * @return
	 */
	public static String d2dPercent(BigDecimal bCount, BigDecimal sCount) {
		//百分比
		String ratio;
		if (sCount.compareTo(new BigDecimal(0)) == 0) {//被除数为零，无意义
			ratio = new DecimalFormat("#########0.0%").format(bCount);
		} else {
			//商
			BigDecimal quotient = bCount.divide(sCount, 3, RoundingMode.HALF_UP);
			ratio = new DecimalFormat("#########0.0%").format(quotient);
		}
		return ratio;
	}

	/**
	 * @param bCount 除数
	 * @param sCount 被除数
	 * @return
	 */
	public static BigDecimal d2d(BigDecimal bCount, BigDecimal sCount) {
		//除数
		//被除数
		//百分比
		BigDecimal ratio;
		//被除数为零，无意义
		if (sCount.compareTo(new BigDecimal(0)) == 0) {
			return bCount;
		} else {
			//商
			ratio = bCount.divide(sCount, 2, RoundingMode.HALF_UP);
		}
		return ratio;
	}

	/**
	 * @param bCount 除数
	 * @param sCount 被除数
	 * @return
	 */
	public static BigDecimal division(BigDecimal bCount, BigDecimal sCount) {
		//被除数
		//百分比
		BigDecimal ratio;
		//被除数为零，无意义
		if (sCount.compareTo(new BigDecimal(0)) == 0) {
			return new BigDecimal(0);
		} else {
			//商
			ratio = bCount.divide(sCount, 2, RoundingMode.HALF_UP);
		}
		return ratio;
	}

	/**
	 * 计算吨数
	 *
	 * @param bCount 除数
	 * @return
	 */
	public static BigDecimal formatToTon(BigDecimal bCount) {
		//被除数为零，无意义
		if (bCount.compareTo(new BigDecimal(0)) == 0) {
			return bCount;
		} else {
			//商
			return bCount.divide(new BigDecimal("1000"), 4, RoundingMode.HALF_UP);
		}

	}

	/**
	 * 计算吨数
	 *
	 * @param bCount 除数
	 * @return
	 */
	public static BigDecimal formatScale(BigDecimal bCount) {
		//被除数为零，无意义
		if (bCount.compareTo(new BigDecimal(0)) == 0) {
			return bCount;
		} else {
			//商
			return bCount.setScale(2, RoundingMode.HALF_UP);
		}

	}

	/**
	 * 转换次数
	 *
	 * <AUTHOR>
	 * @date 2022/10/5 10:00
	 */
	public static String formatTimes(Integer times) {
		if (times > 10000) {
			return "1w+";
		} else if (times > 100000) {
			return "10w+";
		} else if (times > 1000000) {
			return "100w+";
		} else if (times > 10000000) {
			return "1000w+";
		} else {
			return times.toString();
		}
	}

	/**
	 * 获取100%的百分占比
	 *
	 * @param arr       数组
	 * @param sum       总数
	 * @param idx       索引
	 * @param precision 精度
	 * @return
	 */
	public static double getPercentValue(int[] arr, double sum, int idx, int precision) {
		if ((arr.length - 1) < idx) {
			return 0;
		}
		//求和
		if (sum <= 0) {
			for (int i = 0; i < arr.length; i++) {
				sum += arr[i];
			}
		}
		//10的2次幂是100，用于计算精度。
		double digits = Math.pow(10, precision);
		//扩大比例100
		double[] votesPerQuota = new double[arr.length];
		for (int i = 0; i < arr.length; i++) {
			double val = arr[i] / sum * digits * 100;
			votesPerQuota[i] = val;
		}
		//总数,扩大比例意味的总数要扩大
		double targetSeats = digits * 100;
		//再向下取值，组成数组
		double[] seats = new double[arr.length];
		for (int i = 0; i < votesPerQuota.length; i++) {
			seats[i] = Math.floor(votesPerQuota[i]);
		}
		//再新计算合计，用于判断与总数量是否相同,相同则占比会100%
		double currentSum = 0;
		for (int i = 0; i < seats.length; i++) {
			currentSum += seats[i];
		}
		//余数部分的数组:原先数组减去向下取值的数组,得到余数部分的数组
		double[] remainder = new double[arr.length];
		for (int i = 0; i < seats.length; i++) {
			remainder[i] = votesPerQuota[i] - seats[i];
		}
		while (currentSum < targetSeats) {
			double max = 0;
			int maxId = 0;
			int len = 0;
			for (int i = 0; i < remainder.length; ++i) {
				if (remainder[i] > max) {
					max = remainder[i];
					maxId = i;
				}
			}
			//对最大项余额加1
			++seats[maxId];
			//已经增加最大余数加1,则下次判断就可以不需要再判断这个余额数。
			remainder[maxId] = 0;
			//总的也要加1,为了判断是否总数是否相同,跳出循环。
			++currentSum;
		}
		// 这时候的seats就会总数占比会100%
		return seats[idx] / digits;
	}


	/**
	 * 求百分比
	 *
	 * @param bCount 本期数据
	 * @param sCount 上期数据
	 * @return
	 */
	public static BigDecimal getPercent(int bCount, int sCount) {
		//百分比
		if (sCount == 0) {//被除数为零，无意义
			return BigDecimal.valueOf(0);
		} else {
			//商
			BigDecimal quotient = new BigDecimal(bCount).multiply(BigDecimal.valueOf(100))
					.divide(new BigDecimal(sCount), 2, RoundingMode.HALF_UP);
			return quotient;
		}
	}
}
