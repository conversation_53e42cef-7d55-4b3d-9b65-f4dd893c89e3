package com.enrising.ctsc.discharge.service;

import com.enrising.ctsc.discharge.api.bo.DataReportBo;
import com.enrising.ctsc.discharge.api.bo.DischargeDataOpenBo;
import com.enrising.ctsc.discharge.api.bo.DischargeDataTotalBo;
import com.enrising.ctsc.discharge.api.bo.DischargeEmissionBo;
import com.enrising.ctsc.discharge.api.vo.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/17
 * @note
 */
public interface DischargeDataTotalService {

	List<DischargeDataTotalVo> getDataList(DischargeDataTotalBo dischargeDataTotalBo);

	DischargeDataOpenVo getCarbonData(DischargeDataOpenBo bo);

	List<DischargeDataTotalCompareVo> getDataCompareList(DischargeDataTotalBo dischargeDataTotalBo);

	List<DischargeDataTotalQueryVo> getDataQueryList(Integer dataYear, String dataType, String timeType);

	List<DischargeEmissionTrendVo> emissionTrendList(DischargeEmissionBo bo);

	DischargeElectricityAnalysisVo electricityAnalysis(DischargeEmissionBo bo);

	DischargePortraitVo  carbonDataCompareList(DischargeEmissionBo bo);

	List<DischargeEmissionTrendVo> mapCompanyData();

	HashMap<String, Object> getTwoYearsDataList(DischargeDataTotalBo dischargeDataTotalBo);

	List<DischargeDataTotalVo> getCarbonAssessDataList(DischargeDataTotalBo dischargeDataTotalBo);

	HashMap<String, Object> getCarbonOverview();

	HashMap<String, Object> getCarbonCompare(String timeType);

	HashMap<String, Object> getEnergyConsumptionOverview();

	HashMap<String, Object> getEnergyConsumptionCompare();

	HashMap<String, Object> getEnergyTrend(String energyType);

	HashMap<String, Object> getCompanyConsumption(String timeType);

	List<DataReportVo> getDataReportList(DataReportBo dataReportBo);

	void exportDataReport(HttpServletRequest request, HttpServletResponse response, DataReportBo dataReportBo);

	List<SysDeptVO>  getCompanyList();
}
