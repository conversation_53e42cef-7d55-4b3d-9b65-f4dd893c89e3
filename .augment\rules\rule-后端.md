---
type: "manual"
---

你的角色是高级开发工程师，写代码前你会查看当前项目框架与组件，会优先使用框架现在的组件与风格，以简洁的代码完成功能，不需要创建测试类与方法，不需要重新编译或重启，我可以自己验证

## 代码风格
    1. Controller 没必要每个接口都写 try-catch，项目已有全局异常处理
    2. 不要使用魔法值，多个固定类型需要创建枚举类
    3. 代码要易于维护，逻辑层次分明
    4. 关键地方，如if判断处要写注释
    5. 增删改操作优先mybatis-plus的方法
    6. 查询数据写mybatis mapper xml方式，mapper 继承于 com.baomidou.mybatisplus.core.mapper.BaseMapper;


## 增删改查
接收前端参数使用DTO对象
返回前端数据使用VO对象

### 新增
    1. 新建DTO对象用于接收前端参数

### 查询
    主要有两个接口：一个是查询列表，使用startPage()进行分页;一个是查询详情。
    1. 返回对象新建VO，不要和数据表对应的实体类混用
    
### 删除
    1. 支持单个删除和批量删除
### 修改
    1. 使用DTO对象接受参数

## 工具包
    优先使用已有的工具包：lombok、hutool、spring框架
    1.对象拷贝
        优先使用spring框架的BeanUtils.copyProperties();
        import org.springframework.beans.BeanUtils;
        对象拷贝时，优先检查对象属性是否一致，如果不一致，使用BeanUtils.copyProperties()后，再单独赋值
    2.非空判断
        优先使用Hutool工具包
        StrUtil.isNotBlank()
        StrUtil.isBlank()
        ObjUtil.isEmpty()
        ObjUtil.isNotEmpty()
        CollUtil.isEmpty()
        CollUtil.isNotEmpty()