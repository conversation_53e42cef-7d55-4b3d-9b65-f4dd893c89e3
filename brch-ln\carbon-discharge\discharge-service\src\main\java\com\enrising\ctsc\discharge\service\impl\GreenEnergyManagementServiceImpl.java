package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.zhxu.bs.BeanSearcher;
import cn.zhxu.bs.FieldOps;
import cn.zhxu.bs.SearchResult;
import cn.zhxu.bs.util.MapBuilder;
import cn.zhxu.bs.util.MapUtils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.carbon.common.entity.Attachments;
import com.enrising.ctsc.carbon.common.entity.Organization;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.Bs;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.bo.GreenEnergyManagementBo;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyFactor;
import com.enrising.ctsc.discharge.api.entity.GreenEnergyManagement;
import com.enrising.ctsc.discharge.api.entity.GreenEnergyManagementAudit;
import com.enrising.ctsc.discharge.api.enums.GreenEnergyAudit;
import com.enrising.ctsc.discharge.api.export.GreenEnergyManagementExport;
import com.enrising.ctsc.discharge.api.query.GreenEnergyManagementQuery;
import com.enrising.ctsc.discharge.api.vo.GreenEnergyManagementVo;
import com.enrising.ctsc.discharge.mapper.GreenEnergyManagementMapper;
import com.enrising.ctsc.discharge.service.GreenEnergyManagementService;
import com.github.liaochong.myexcel.core.DefaultExcelBuilder;
import com.github.liaochong.myexcel.utils.AttachmentExportUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sccl.common.utils.ServletUtils;
import lombok.AllArgsConstructor;
import org.apache.commons.codec.CharEncoding;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 绿电管理
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2024-09-15
 */
@Service
@AllArgsConstructor
public class GreenEnergyManagementServiceImpl extends ServiceImpl<GreenEnergyManagementMapper, GreenEnergyManagement> implements GreenEnergyManagementService {
    @Autowired
    private BeanSearcher beanSearcher;

    @Override
    public TableDataInfo<GreenEnergyManagementVo> findList(GreenEnergyManagementQuery query) {
        MapBuilder params = getParams(query);
        SearchResult<GreenEnergyManagementVo> result = Bs.getBean().search(GreenEnergyManagementVo.class, params.build());
        result.getDataList().stream().peek(this::getOrgName).collect(Collectors.toList());
        return TableDataInfo.build(result);
    }

    private MapBuilder getParams(GreenEnergyManagementQuery query) {
        MapBuilder params = Bs.params();
        params.field(GreenEnergyManagementVo::getTransactionMonth, query.getTransactionMonthStart(), query.getTransactionMonthEnd()).op(FieldOps.Between);
        params.field(GreenEnergyManagementVo::getEnergyType, query.getEnergyType());
        // 关键字查询
        if (StrUtil.isNotBlank(query.getKeyword())) {
            params.field(GreenEnergyManagementVo::getSubjectEntity, GreenEnergyManagementVo::getPowerSourceLocation).sql("$1 like ? or $2 like ?", query.getKeyword(), query.getKeyword());
        }
        if (query.getCompanies() == null) {
            throw new BusinessException("所属分公司不能为空");
        }
        params.orderBy(GreenEnergyManagementVo::getCreateTime).desc();
        return params;
    }

    @Override
    public GreenEnergyManagementVo detail(GreenEnergyManagementQuery query) {
        if (ObjectUtil.allFieldIsNull(query)) {
            throw new BusinessException("查询参数不能为空");
        }
        GreenEnergyManagementVo detail = Bs.getBean().searchFirst(GreenEnergyManagementVo.class, Bs.params().build());
        getOrgName(detail);
        return detail;
    }

    /**
     * 获取机构名称
     *
     * @param detail 详情
     */
    private void getOrgName(GreenEnergyManagementVo detail) {
        Map<String, Object> companyBranch = Maps.newHashMap();
        companyBranch.put("id", detail.getCompanyBranch());
        Organization organization = beanSearcher.searchFirst(Organization.class, companyBranch);
        if (organization != null) {
            detail.setCompanyBranchName(organization.getOrgName());
        }
        Map<String, Object> companies = Maps.newHashMap();
        companies.put("id", detail.getCompanies());
        Organization companiesOrg = beanSearcher.searchFirst(Organization.class, companies);
        if (companiesOrg != null) {
            detail.setCompaniesName(companiesOrg.getOrgName());
        }
        // 获取附件
        if (detail.getSupportingDocument() != null) {
            Attachments attachments = beanSearcher.searchFirst(Attachments.class, MapUtils.of("id", detail.getSupportingDocument()));
            detail.setAttachments(attachments);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(GreenEnergyManagementBo bo) {
        GreenEnergyManagement entity = new GreenEnergyManagement();
        BeanUtils.copyProperties(bo, entity);
        entity.setAuditResult(GreenEnergyAudit.DRAFT.getCode());
        // 获取抵扣碳排放
        BigDecimal carbonCredits = getDeduction(entity.getMonthlyContractPower());
        entity.setDeduction(carbonCredits);
        // 计算抵扣碳排放
        baseMapper.insert(entity);
    }

    /**
     * 获取抵扣碳排放
     * @param monthlyContractPower 月合同电量
     * @return 抵扣碳排放
     */
    public BigDecimal getDeduction(BigDecimal monthlyContractPower) {
        if (monthlyContractPower == null) {
            return new BigDecimal(0);
        }
        // 获取今年及以前的碳排放因子-外购火电
        String energyType = "3";
        // 取当年范围的
        DischargeEnergyFactor factor = new DischargeEnergyFactor().selectOne(Wrappers.<DischargeEnergyFactor>lambdaQuery()
                .eq(DischargeEnergyFactor::getEnergyTypeId, energyType)
                .gt(DischargeEnergyFactor::getValidityStart, DateUtil.year(new Date()))
                .lt(DischargeEnergyFactor::getValidityEnd, DateUtil.year(new Date()))
                .orderByDesc(DischargeEnergyFactor::getValidityEnd)
                .last("limit 1")
        );
        // 如果找不到，则取最近的一个
        if (factor == null) {
            factor = new DischargeEnergyFactor().selectOne(Wrappers.<DischargeEnergyFactor>lambdaQuery()
                    .eq(DischargeEnergyFactor::getEnergyTypeId, energyType)
                    .orderByDesc(DischargeEnergyFactor::getValidityEnd)
                    .last("limit 1")
            );
        }

        if (factor != null) {
            // 可抵扣碳排放自动计算=分月合同电量*对应年份电碳排放因子
            return factor.getFactor().multiply(monthlyContractPower);
        }

        return new BigDecimal(0);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(GreenEnergyManagementBo bo) {
        if (bo.getId() == null) {
            throw new BusinessException("id不能为空");
        }
        GreenEnergyManagement entity = new GreenEnergyManagement();
        BeanUtils.copyProperties(bo, entity);
        LambdaUpdateWrapper<GreenEnergyManagement> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(GreenEnergyManagement::getId, bo.getId());

        if (bo.getSupportingDocument() == null) {
            lambdaUpdateWrapper.set(GreenEnergyManagement::getSupportingDocument, null);
        }
        // 获取抵扣碳排放
        BigDecimal carbonCredits = getDeduction(entity.getMonthlyContractPower());
        entity.setDeduction(carbonCredits);

        baseMapper.update(entity, lambdaUpdateWrapper);

        // 如果有附件，并且已提交审核，说明是补传附件，将附件更新到审核表，更新最新一条的待审核记录
        if (bo.getSupportingDocument() != null) {
            GreenEnergyManagement energyManagement = getById(bo.getId());
            // 待审核
            if (energyManagement.getAuditResult().equals(GreenEnergyAudit.PENDING.getCode())) {
                // 获取最新一条待审核记录, 并更新附件
                GreenEnergyManagementAudit audit = beanSearcher.searchFirst(GreenEnergyManagementAudit.class,
                        MapUtils.builder().field(GreenEnergyManagementAudit::getGreenId, bo.getId()).orderBy(GreenEnergyManagementAudit::getSubmitTime).desc().build());
                audit.setSupportingDocument(bo.getSupportingDocument());
                audit.updateById();
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(Long id) {
        // 删除审核记录
        new GreenEnergyManagementAudit().delete(Wrappers.<GreenEnergyManagementAudit>lambdaQuery()
                .eq(GreenEnergyManagementAudit::getGreenId, id)
        );
        baseMapper.deleteById(id);
    }

    @Override
    public void export(GreenEnergyManagementQuery query) {
        List<GreenEnergyManagementExport> exportList = Lists.newArrayList();
        // 分页查询到list，并封装到vo，调用导出方法
        MapBuilder params = getParams(query);
        // 设置分页参数
        int pageNum = 1;
        int pageSize = 100;
        while (true) {
            params.page(pageNum, pageSize);
            List<GreenEnergyManagementVo> result = Bs.getBean().searchList(GreenEnergyManagementVo.class, params.build());
            result.stream().peek(this::getOrgName).collect(Collectors.toList());
            for (GreenEnergyManagementVo vo : result) {
                GreenEnergyManagementExport export = new GreenEnergyManagementExport();
                BeanUtils.copyProperties(vo, export);
                exportList.add(export);
            }
            if (result.size() < pageSize) {
                break;
            }
            pageNum++;
        }

        Workbook workbook = DefaultExcelBuilder.of(GreenEnergyManagementExport.class).build(exportList);
        HttpServletResponse response = ServletUtils.getResponse();
        response.setCharacterEncoding(CharEncoding.UTF_8);

        AttachmentExportUtil.export(workbook, "绿电管理导出.xlsx", response);

    }

}
