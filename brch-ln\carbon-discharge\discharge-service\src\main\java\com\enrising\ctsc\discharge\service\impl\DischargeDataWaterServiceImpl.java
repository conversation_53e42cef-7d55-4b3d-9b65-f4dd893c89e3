package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.carbon.common.entity.User;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.JwtUtils;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.discharge.api.bo.DischargeDataWaterBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataWater;
import com.enrising.ctsc.discharge.api.enums.EnergyType;
import com.enrising.ctsc.discharge.api.vo.DischargeDataWaterVo;
import com.enrising.ctsc.discharge.mapper.DischargeDataWaterMapper;
import com.enrising.ctsc.discharge.service.DischargeDataWaterService;
import com.enrising.ctsc.discharge.service.DischargeEnergyCoefficientService;
import com.enrising.ctsc.discharge.service.DischargeEnergyFactorService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 碳排放数据填报表（水）
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Service
@AllArgsConstructor
public class DischargeDataWaterServiceImpl extends ServiceImpl<DischargeDataWaterMapper, DischargeDataWater> implements DischargeDataWaterService {

	private final DischargeEnergyCoefficientService dischargeEnergyCoefficientService;

	private final DischargeEnergyFactorService dischargeEnergyFactorService;

//	private final RemoteUserService remoteUserService;

	@Override
	public Page<DischargeDataWaterVo> getWaterListPage(QueryPage<DischargeDataWaterBo> queryPage) {
		LambdaQueryWrapper<DischargeDataWater> qw = Wrappers.lambdaQuery();
		DischargeDataWaterBo dischargeDataWaterBo = queryPage.getModel();
		dischargeDataWaterBo.setSize(queryPage.getSize());
		dischargeDataWaterBo.setOffset((queryPage.getCurrent() - 1 ) * queryPage.getSize());
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			Page<DischargeDataWater> dischargeDataWaterPage = new Page<>(queryPage.getCurrent(),
					queryPage.getSize(), true);
			Page<DischargeDataWaterVo> dischargeDataWaterVoPage = new Page<>();
			BeanUtils.copyProperties(dischargeDataWaterPage, dischargeDataWaterVoPage);
			if (ObjectUtil.isNotEmpty(dischargeDataWaterBo)) {
				if (ObjectUtil.isEmpty(dischargeDataWaterBo.getCompanyId())) {
					dischargeDataWaterBo.setCompanyId(0L);
				}
				if (ObjectUtil.isNotEmpty(dischargeDataWaterBo.getReportTime())) {
					//查询条件 填报时间
					qw.eq(DischargeDataWater::getReportTime, dischargeDataWaterBo.getReportTime());
					dischargeDataWaterBo.setQueryStartTime(DateUtil.beginOfMonth(dischargeDataWaterBo.getReportTime()));
					dischargeDataWaterBo.setQueryEndTime(DateUtil.endOfMonth(dischargeDataWaterBo.getReportTime()));
				} else {
					//查询条件 数据年份
					String startYear = dischargeDataWaterBo.getDataYear() + "-01-01 0:00:00";
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
					dateStart = sdf.parse(startYear);
					dateEnd = DateUtil.endOfYear(dateStart);
					dischargeDataWaterBo.setQueryStartTime(dateStart);
					dischargeDataWaterBo.setQueryEndTime(dateEnd);
					qw.ge(DischargeDataWater::getReportTime, dateStart)
							.le(DischargeDataWater::getReportTime, dateEnd);
				}
				//查询条件，公司
				qw.eq(dischargeDataWaterBo.getCompanyId() != 0, DischargeDataWater::getCompanyId,
						dischargeDataWaterBo.getCompanyId());
			}
			dischargeDataWaterVoPage.setTotal(this.count(qw));
			if (ObjectUtil.isEmpty(dischargeDataWaterBo) || ObjectUtil.isEmpty(dischargeDataWaterBo.getCompanyId()) ||
					dischargeDataWaterBo.getCompanyId().equals(0L)) {
				dischargeDataWaterVoPage.setRecords(baseMapper.countCompanyData(dischargeDataWaterBo));
			} else {
				dischargeDataWaterVoPage.setRecords(baseMapper.getCompanyDataList(dischargeDataWaterBo));
			}
			return dischargeDataWaterVoPage;
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public List<DischargeDataWaterVo> getWaterListToExcel(DischargeDataWaterBo dischargeDataWaterBo) {
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			if (ObjectUtil.isNotEmpty(dischargeDataWaterBo)) {
				if (ObjectUtil.isEmpty(dischargeDataWaterBo.getCompanyId())) {
					dischargeDataWaterBo.setCompanyId(0L);
				}
				if (ObjectUtil.isNotEmpty(dischargeDataWaterBo.getReportTime())) {
					dischargeDataWaterBo.setQueryStartTime(DateUtil.beginOfMonth(dischargeDataWaterBo.getReportTime()));
					dischargeDataWaterBo.setQueryEndTime(DateUtil.endOfMonth(dischargeDataWaterBo.getReportTime()));
				} else {
					//查询条件 数据年份
					String startYear = dischargeDataWaterBo.getDataYear() + "-01-01 0:00:00";
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
					dateStart = sdf.parse(startYear);
					dateEnd = DateUtil.endOfYear(dateStart);
					dischargeDataWaterBo.setQueryStartTime(dateStart);
					dischargeDataWaterBo.setQueryEndTime(dateEnd);
				}
			}
			return baseMapper.countCompanyData(dischargeDataWaterBo);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public List<DischargeDataWaterVo> getDataList(Integer dataYear, Long companyId) {
		if (ObjectUtil.isEmpty(dataYear)) {
			throw new BusinessException("数据年份不能为空！");
		}
		if (ObjectUtil.isEmpty(companyId)) {
//			companyId = remoteUserService.getCityDeptId();
			companyId = JwtUtils.getCurrentUserCompanyId();
		}
		String startYear = dataYear + "-01-01 0:00:00";
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			dateStart = sdf.parse(startYear);
			dateEnd = DateUtil.endOfYear(dateStart);
			return this.countCompanyData(dateStart, dateEnd, companyId);

		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}


	@Override
	public List<DischargeDataWaterVo> getDataListByDate(Date dateStart, Date dateEnd, Long companyId) {
		if (ObjectUtil.isEmpty(companyId)) {
//			companyId = remoteUserService.getCityDeptId();
			companyId = JwtUtils.getCurrentUserCompanyId();
		}
		return this.countCompanyData(dateStart, dateEnd, companyId);
	}

	@Override
	public DischargeDataWaterVo detail(Long id) {
		if (ObjectUtil.isEmpty(id)) {
			throw new BusinessException("查询参数不能为空");
		}
		DischargeDataWater dischargeDataWater = baseMapper.selectById(id);
		if (ObjectUtil.isEmpty(dischargeDataWater)) {
			return null;
		}
		DischargeDataWaterVo dischargeDataWaterVo = new DischargeDataWaterVo();
		BeanUtils.copyProperties(dischargeDataWater ,dischargeDataWaterVo);
		Calendar cal = Calendar.getInstance();
		cal.setTime(dischargeDataWaterVo.getReportTime());
		Integer year=cal.get(Calendar.YEAR);//获取年
		Integer month = cal.get(Calendar.MONTH) + 1;//获取月（月份从0开始，需要加一）
		dischargeDataWaterVo.setDataMonth(month + "月");
		dischargeDataWaterVo.setDataYear(year + "年");
		dischargeDataWaterVo.setCarbonEmissions(dischargeDataWaterVo.getWater().
				multiply(dischargeEnergyFactorService.getFactorByTime(EnergyType.WATER.getId(),
						dischargeDataWaterVo.getReportTime())).divide(new BigDecimal(1000)).
				setScale(4, RoundingMode.HALF_UP));
		dischargeDataWaterVo.setEnergyConsumption(dischargeDataWaterVo.getWater().
				multiply(dischargeEnergyCoefficientService.getCoefficientByTime(EnergyType.WATER.getId(),
						dischargeDataWaterVo.getReportTime())).divide(new BigDecimal(1000)).setScale(4,
						RoundingMode.HALF_UP));
		return  dischargeDataWaterVo;
	}

	@Override
	public String add(DischargeDataWaterBo bo) {
		DischargeDataWater entity = new DischargeDataWater();
		BeanUtils.copyProperties(bo, entity);
//		entity.setCompanyId(remoteUserService.getCityDeptId());
		entity.setCompanyId(JwtUtils.getCurrentUserCompanyId());
		//查询已有数据是否重复
		List<DischargeDataWater> dischargeDataWaterList = list(
				new LambdaQueryWrapper<DischargeDataWater>()
						.eq(DischargeDataWater::getCompanyId, entity.getCompanyId())
						.eq(DischargeDataWater::getReportTime, entity.getReportTime())
						.select(DischargeDataWater::getId));
		if (CollectionUtil.isNotEmpty(dischargeDataWaterList) && dischargeDataWaterList.size() > 0) {
			return "所选月份数据已填报";
		}
		if (baseMapper.insert(entity) == 1) {
			return "";
		} else {
			return "保存失败";
		}
	}

	@Override
	public void edit(DischargeDataWaterBo bo) {
		DischargeDataWater entity = new DischargeDataWater();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.updateById(entity);
	}

	@Override
	public void del(Long id) {
		baseMapper.deleteById(id);
	}

	@Override
	public List<DischargeDataWaterVo> countCompanyData(Date dateStart, Date dateEnd, Long companyId) {
		DischargeDataWaterBo queryBo = new DischargeDataWaterBo();
		queryBo.setCompanyId(companyId);
		queryBo.setQueryStartTime(dateStart);
		queryBo.setQueryEndTime(dateEnd);
		if (ObjectUtil.isEmpty(companyId) || companyId.equals(0L)) {
			return baseMapper.countCompanyData(queryBo);
		} else {
			return baseMapper.getCompanyDataList(queryBo);
		}
	}
}
