<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeEnergyFactorMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.energy_type_id,
            t.validity_start,
            t.validity_end,
            t.factor,
            t.source,
            t.net_calorific_power,
            t.carbon_per_unit_calorific_value,
            t.carbon_oxidation_rate,
			energy_type.second_name as energySecondName,
			energy_type.energy_type as energyType,
			energy_type.unit as energyTypeUnit
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeEnergyFactorVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_energy_factor t
        LEFT JOIN discharge_energy_type energy_type ON energy_type.id = t.energy_type_id
        ${ew.customSqlSegment}
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.discharge.api.vo.DischargeEnergyFactorVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_energy_factor t
		LEFT JOIN discharge_energy_type energy_type ON energy_type.id = t.energy_type_id
        ${ew.customSqlSegment}
        limit 1
    </select>
	<select id="getGainFactor" resultType="com.enrising.ctsc.discharge.api.vo.DischargeEnergyFactorVo">
		SELECT
		<include refid="baseColumns" />
		FROM discharge_energy_factor t
		LEFT JOIN discharge_energy_type energy_type ON energy_type.id = t.energy_type_id
		where t.del_flag = '0'
		<if test="bo.typeName!=null and bo.typeName!=''">
			and energy_type.second_name = #{bo.typeName}
		</if>
		<if test="bo.year!=null and bo.year!=''">
			and DATE_FORMAT(t.validity_start,'%Y') &lt;= #{bo.year}
			and DATE_FORMAT(t.validity_end,'%Y') >= #{bo.year}
		</if>
	</select>
</mapper>