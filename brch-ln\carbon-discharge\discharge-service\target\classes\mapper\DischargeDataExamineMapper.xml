<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeDataExamineMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.indicator_name,
            t.unit,
            t.status,
            t.del_flag
    </sql>
	<select id="getList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataExamineVo">
		SELECT
	    m.*,
		n.stock_data as useTotal,
		n.indicator_name
	FROM
		(
		SELECT
		    A.ID,
			A.examine_name,
			A.unit,
			A.sort,
			A.energy_indicator_id,
			d.energy_type,
			C.factor,
			b.description AS unit_description,
			b.label AS unit_name
		FROM
			(
				discharge_energy_examine
				A LEFT JOIN sys_dict_item b ON (
						b.TYPE = 'energy_type_unit'
						AND b.VALUE = A.unit )
			)
			LEFT JOIN discharge_energy_factor C ON C.id = A.factor_id
			LEFT JOIN discharge_energy_type d ON C.energy_type_id = d.id
		WHERE
			A.del_flag = '0'
		ORDER BY
			A.sort ASC
		)
		AS M LEFT JOIN (
		SELECT T
			.energy_indicator_id,
			T.stock_data,
			b.indicator_name
		FROM
			(
			SELECT A
				.energy_indicator_id,
				sum( A.stock_data ) AS stock_data
			FROM
				discharge_data_energy A
			WHERE
				A.del_flag = '0'
				AND report_flag = '2'
			<if test="bo.companyId != null">
				and a.company_id = #{ bo.companyId }
			</if>
			<if test="bo.reportTime!=null and bo.reportTime!='' ">
				and to_char(report_time,'yyyy') = #{ bo.reportTime }
			</if>
			GROUP BY
				A.energy_indicator_id
			)
			AS T LEFT JOIN discharge_energy_indicator b ON ( ( T.energy_indicator_id = b.ID ) )
		ORDER BY
		T.energy_indicator_id ASC
		) n ON M.energy_indicator_id = n.energy_indicator_id
	</select>
	<select id="getUseDataByExamineIds"
			resultType="com.enrising.ctsc.discharge.api.vo.DischargeEnergyExamineVo">
		SELECT
			sum(a.use) as use,
			a.energy_examine_id as id
		FROM  discharge_data_examine a
		WHERE a.energy_examine_id in
         <foreach collection="bo.energyExamineIds" item="id"  open="("  separator="," close=")">
			       #{id}
		 </foreach>
         <if test="bo.companyId!=null">
			 and a.company_id = #{bo.companyId}
		 </if>
		<if test="bo.reportTime!=null and bo.reportTime!=''">
			and a.report_time = #{bo.reportTime}
		</if>
		and a.del_flag= '0'
		GROUP BY a.energy_examine_id
	</select>
	<select id="getCompanyNameById" resultType="java.lang.String">
		SELECT
			org_name
		FROM  rmp.sys_organizations
		WHERE
		    id = #{companyId}
	</select>


</mapper>