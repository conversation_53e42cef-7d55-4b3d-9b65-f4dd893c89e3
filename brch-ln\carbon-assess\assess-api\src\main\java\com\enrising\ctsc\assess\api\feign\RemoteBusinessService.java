package com.enrising.ctsc.assess.api.feign;

import com.enrising.ctsc.assess.api.bo.BusinessProductionDataBo;
import com.enrising.ctsc.assess.api.utils.FeignConfigure;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.assess.api.vo.BusinessProductionDataCompareVo;
import com.enrising.ctsc.assess.api.vo.CarbonRankingVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.List;

/**
 * 碳排放接口
 * <AUTHOR>
 * @date 2024-10-16
 */
@FeignClient(contextId = "remoteBusinessService",
		value = "business-api",
		url = "${feign.webUrl:http://127.0.0.1:8080/energy-cost}",
		configuration = FeignConfigure.class)
public interface RemoteBusinessService {

	/**
	 * 获取各个公司的业务总量、业务流量总量
	 */
	@PostMapping("/business-api/business/getCompanyBusList")
	R<List<BusinessProductionDataCompareVo>> getCompanyBusList(@RequestBody BusinessProductionDataBo bo);

	/**
	 * 查询碳排强度排名
	 * @param year 年份
	 * @return
	 */
	@GetMapping("/business-api/business/getCarbonRanking")
	R<List<CarbonRankingVo>> getCarbonRanking(@RequestParam(value = "year") String year);

	/**
	 * 获取公司列表
	 * @return 公司列表
	 */
	@GetMapping("/business-api/business/getCompanyList")
	R<List<CarbonRankingVo>> getCompanyList();

	/**
	 * 获取公司年度各月业务总量
	 * @param companyId 公司id
	 * @param dataYear 年份
	 * @return
	 */
	@GetMapping("/business-api/business/getCompanyBusinessTotalList")
	R<List<HashMap<String, Object>>> getCompanyBusinessTotalList(@RequestParam(value = "companyId") Long companyId,
																 @RequestParam(value = "dataYear") String dataYear);


	/**
	 * 双碳云脑--双碳全景图--生产业务数据情况
	 * @return
	 */
	@GetMapping("/business-api/business/getBusinessOverview")
	R<HashMap<String, Object>> getBusinessOverview();
}
