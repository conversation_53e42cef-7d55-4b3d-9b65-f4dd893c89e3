<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.EnergyCalculatePriceMapper">
	<resultMap id="BaseResultMap" type="com.enrising.ctsc.discharge.api.entity.EnergyCalculatePrice">
		<result column="id" property="id" />
		<result column="create_by" property="createBy" />
		<result column="create_time" property="createTime" />
		<result column="update_by" property="updateBy" />
		<result column="update_time" property="updateTime" />
		<result column="report_time" property="reportTime" />
		<result column="electricity_price" property="electricityPrice" />
		<result column="gasoline_price_two" property="gasolinePriceTwo" />
		<result column="gasoline_price_five" property="gasolinePriceFive" />
		<result column="gasoline_price_eight" property="gasolinePriceEight" />
		<result column="diesel_price" property="dieselPrice" />
		<result column="thermal_priceTwo" property="thermalPrice" />
		<result column="coal_price" property="coalPrice" />
		<result column="water_price" property="waterPrice" />
		<result column="del_flag" property="delFlag" />
	</resultMap>
	<select id="getLastOne"  resultType="com.enrising.ctsc.discharge.api.entity.EnergyCalculatePrice">
		SELECT
			*
		FROM energy_calculate_price
		<where>
			del_flag = '0'
			<if test="query.reportTime != null">
				AND report_time <![CDATA[<=]]> #{query.reportTime}
			</if>
		</where>
		ORDER BY report_time DESC
		limit 1
	</select>
</mapper>