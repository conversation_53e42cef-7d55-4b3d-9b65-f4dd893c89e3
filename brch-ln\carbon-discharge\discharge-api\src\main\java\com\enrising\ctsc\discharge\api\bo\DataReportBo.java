package com.enrising.ctsc.discharge.api.bo;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 数据通报查询
 *
 * <AUTHOR>
 * @since 1.0.0 2024-06-20
 */

@Data
public class DataReportBo extends Model<DataReportBo> {

    /**
     * 填报单位id
     */
    private Long companyId;

    /**
     * 填报单位名称
     */
    private String companyName;

    /**
     * 填报单位城市编码
     */
    private String cityCode;

    /**
     * 查询开始日期
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD)
    private Date startDate;

    /**
     * 查询结束日期
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD)
    private Date endDate;
}
