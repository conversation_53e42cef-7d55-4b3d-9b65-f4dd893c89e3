package com.enrising.ctsc.discharge.api.export;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.liaochong.myexcel.core.annotation.ExcelColumn;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* 绿电管理
*
* <AUTHOR> <EMAIL>
* @since 1.0.0 2024-09-15
*/
@Data
public class GreenEnergyManagementExport  {

	/**
	 * 所属分公司名称
	 */
	@ExcelColumn(title = "所属分公司")
	private String companiesName;

	/**
	 * 所属部门名称
	 */
	@ExcelColumn(title = "所属部门")
	private String companyBranchName;

	/**
	 * 所属主体
	 */
	@ExcelColumn(title = "所属主体")
	private String subjectEntity;

	/**
	* 交易年月
	*/
	@ExcelColumn(title = "交易年月",format = "yyyy-MM")
	@JsonFormat(pattern = DateUtils.YYYY_MM)
	private Date transactionMonth;

	/**
	* 分月合同绿电量(兆瓦时)
	*/
	@ExcelColumn(title = "分月合同绿电量(兆瓦时)", format = "0.00")
	private BigDecimal monthlyContractPower;

	/**
	 * 能源类型
	 */
	@ExcelColumn(title = "能源类型", mapping = "1:风电,2:光伏")
	private String energyType;

	/**
	* 电源所属地
	*/
	@ExcelColumn(title = "电源所属地")
	private String powerSourceLocation;

	/**
	* 价格
	*/
	@ExcelColumn(title = "价格", format = "0.00")
	private BigDecimal price;

	/**
	 * 抵扣碳排放（tCO2)
	 */
	@ExcelColumn(title = "抵扣碳排放（tCO2)")
	private BigDecimal deduction;

	/**
	 * 审核结果
	 */
	@ExcelColumn(title = "状态", mapping = "0:草稿,1:审核中,2:已通过,3:已驳回")
	private String auditResult;

	/**
	 * 备注
	 */
	@ExcelColumn(title = "备注")
	private String remarks;

	/**
	 * 创建时间
	 */
	@ExcelColumn(title = "创建时间", format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date createTime;

}
