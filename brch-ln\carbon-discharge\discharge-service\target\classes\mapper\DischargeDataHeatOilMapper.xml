<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeDataHeatOilMapper">
    <!-- 表字段 -->
    <sql id="baseColumns">
		t.id,
		t.create_by,
		t.create_time,
		t.update_by,
		t.update_time,
		t.company_id,
		t.`year`,
		t.`month`,
		t.gasoline_stock,
		t.gasoline_group,
		t.diesel_stock,
		t.diesel_group,
		t.heat_stock,
		t.heat_group,
		t.water_stock,
		t.water_group,
		t.del_flag
    </sql>
    <!-- 查询列表 -->
    <select id="findList" parameterType="com.enrising.ctsc.discharge.api.entity.DischargeDataHeatOil" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataHeatOilVo">
        SELECT
			t.id,
			t.company_id,
			t.`year`,
			t.`month`,
			t.gasoline_stock,
			t.gasoline_group,
			t.diesel_stock,
			t.diesel_group,
			t.heat_stock,
			t.heat_group,
			t.water_stock,
			t.water_group,
			rso.org_name as company_name
        FROM discharge_data_heat_oil t
        LEFT JOIN rmp.sys_organizations rso ON t.company_id = rso.id
		<where>
			t.del_flag='0'
			<if test="id != null">
				AND t.id = #{id}
			</if>
			<if test="companyId != null and companyId != -1">
				AND t.company_id = #{companyId}
			</if>
			<if test="year != null and year != ''">
				AND t.`year` = #{year}
			</if>
			<if test="month != null and month != ''">
				AND t.`month` = #{month}
			</if>
		</where>
    </select>
    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataHeatOilVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_data_heat_oil t
		<where>
			t.del_flag='0'
			<if test="id != null">
				AND t.id = #{id}
			</if>
			<if test="companyId != null">
				AND t.company_id = #{companyId}
			</if>
			<if test="year != null and year != ''">
				AND t.`year` = #{year}
			</if>
			<if test="month != null and month != ''">
				AND t.`month` = #{month}
			</if>
		</where>
        limit 1
    </select>
	<select id="getCompanyIdByName" resultType="java.lang.Long">
		SELECT
			id
		FROM rmp.sys_organizations
		WHERE
			del_flag = '0'
		  AND status = '1'
		  AND parent_company_no = '2600000000'
		  AND org_name LIKE CONCAT('%', #{companyName}, '%')
			LIMIT 1
	</select>
</mapper>