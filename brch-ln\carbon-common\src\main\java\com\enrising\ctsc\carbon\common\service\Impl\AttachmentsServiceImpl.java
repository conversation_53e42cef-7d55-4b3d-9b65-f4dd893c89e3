package com.enrising.ctsc.carbon.common.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.carbon.common.entity.Attachments;
import com.enrising.ctsc.carbon.common.mapper.AttachmentsMapper;
import com.enrising.ctsc.carbon.common.service.AttachmentsService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 附件服务实现类
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Service
public class AttachmentsServiceImpl extends ServiceImpl<AttachmentsMapper, Attachments> implements AttachmentsService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAttachment(Attachments attachments) {
        try {
            return save(attachments);
        } catch (Exception e) {
            log.error("保存附件信息失败：{}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<Attachments> getByBusiIdAndAlias(Long busiId, String busiAlias) {
        LambdaQueryWrapper<Attachments> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Attachments::getBusiId, busiId)
                .eq(Attachments::getBusiAlias, busiAlias)
                .eq(Attachments::getDelFlag, "0")
                .orderByDesc(Attachments::getCreateTime);
        return list(queryWrapper);
    }

    @Override
    public List<Attachments> getByBusiIds(List<Long> busiIds) {
        if (busiIds == null || busiIds.isEmpty()) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<Attachments> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Attachments::getBusiId, busiIds)
                .eq(Attachments::getDelFlag, "0")
                .orderByDesc(Attachments::getCreateTime);
        return list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAttachment(Long id) {
        try {
            return removeById(id);
        } catch (Exception e) {
            log.error("删除附件失败，ID：{}，错误：{}", id, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteAttachments(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return true;
        }
        try {
            return removeByIds(ids);
        } catch (Exception e) {
            log.error("批量删除附件失败，IDs：{}，错误：{}", ids, e.getMessage(), e);
            return false;
        }
    }
}
