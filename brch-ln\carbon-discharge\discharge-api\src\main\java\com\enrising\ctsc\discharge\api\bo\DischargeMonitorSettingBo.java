package com.enrising.ctsc.discharge.api.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 碳排放监测设置表
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeMonitorSettingBo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
		private Long id;


	/**
	 * 单位id
	 */
		private Long companyId;

	/**
	 * 年份
	 */
		private String year;


	/**
	 * 上年份
	 */
		private String preYear;

	/**
	 * 上年份
	 */
		private String toYear;

	/**
	 * 定额值
	 */
		private String quota;

	/**
	 * 监测选项：1-去年全省碳排放量均值；2-去年碳排放总量%；3-自定义
	 */
		private String quotaSelection = "3";

	/**
	 * 监测选择值
	 */
		private String selectionValue;

	/**
	 * 关键字
	 */
	private String keyword;


	private List<Long> companyIds;


}
