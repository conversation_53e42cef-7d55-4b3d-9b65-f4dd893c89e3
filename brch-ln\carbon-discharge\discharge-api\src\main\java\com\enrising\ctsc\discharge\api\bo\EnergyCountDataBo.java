package com.enrising.ctsc.discharge.api.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 能耗系统统计双碳数据
 * @Auther qinxinmin
 * @Date 2024/09/16
 */
@Data
public class EnergyCountDataBo {
    /**
     * 分公司ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long companyId;
    /**
     * 数据时间
     */
    private Date reportTime;
    /**
     * 数据类型，A-股份，B-集团
     */
    private String dataType;
    /**
     * 统计方式：0-原始数据；1-按规则计算
     */
    private String countType;
    /**
     * 1.3、耗电量（总）(千瓦时)
     */
    private BigDecimal totalPowerData;
    /**
     * 1.3.1、生产用房耗电量(千瓦时)
     */
    private BigDecimal productionRoomData;

    /**
     * 1.3.1.1、其中：通信机房耗电量(千瓦时)
     */
    private BigDecimal communicationRoomData = BigDecimal.ZERO;

    /**
     * 1.3.1.2、其中：基站耗电量(千瓦时)
     */
    private BigDecimal baseStationData;

    /**
     * 1.3.1.2.1、其中：铁塔公司基站耗电量（包括室内分布、室外站等） (千瓦时)
     */
    private BigDecimal towerBaseStationData = BigDecimal.ZERO;

    /**
     * 1.3.1.2.2、其中：第三方租赁基站耗电量（包括室内分布、室外站等） (千瓦时)
     */
    private BigDecimal leasingBaseStationData = BigDecimal.ZERO;

    /**
     * 1.3.1.2.3、其中：自有产权基站耗电量（包括室内分布、室外站等） (千瓦时)
     */
    private BigDecimal ownBaseStationData = BigDecimal.ZERO;

    /**
     * 1.3.1.3、其中：数据中心耗电量(千瓦时)
     */
    private BigDecimal dataCenterData;
    /**
     * 1.3.1.3.2、其中：对外数据中心耗电量(千瓦时)
     */
    private BigDecimal externalDataCenterData = BigDecimal.ZERO;
    /**
     * 1.3.1.3.3、其中：自有数据中心耗电量(千瓦时)
     */
    private BigDecimal selfDataCenterData = BigDecimal.ZERO;
    /**
     * 1.3.1.4、其中：接入局所及室外机柜耗电量(千瓦时)
     */
    private BigDecimal accessRoomData = BigDecimal.ZERO;
    /**
     * 1.3.2、非生产用房耗电量(千瓦时)
     */
    private BigDecimal nonProductionBuildingsData;
    /**
     * 1.3.2.1、其中：管理用房耗电量(千瓦时)
     */
    private BigDecimal managerRoomData = BigDecimal.ZERO;
    /**
     * 1.3.2.2、其中：渠道用房耗电量(千瓦时)
     */
    private BigDecimal canalRoomData = BigDecimal.ZERO;
    /**
     * 1.1、 煤炭(吨)
     */
    private BigDecimal coalData = BigDecimal.ZERO;
    /**
     * 1.1.1、其中发电用煤(吨)
     */
    private BigDecimal powerCoalData = BigDecimal.ZERO;
    /**
     * 1.2、 焦炭(吨)
     */
    private BigDecimal cokeData = BigDecimal.ZERO;
    /**
     * 1.5、汽油消耗量(升)
     */
    private BigDecimal gasolineData = BigDecimal.ZERO;
    /**
     * 1.5.1、其中：移动源（升）
     */
    private BigDecimal gasolineMovableData = BigDecimal.ZERO;
    /**
     * 1.5.2、其中：固定源（升）
     */
    private BigDecimal gasolineStationaryData = BigDecimal.ZERO;
    /**
     * 1.7、柴油消耗量(升)
     */
    private BigDecimal dieselData = BigDecimal.ZERO;
    /**
     * 1.7.1、其中：移动源（升）
     */
    private BigDecimal dieselMovableData = BigDecimal.ZERO;
    /**
     * 1.7.2、其中：固定源（升）
     */
    private BigDecimal dieselStationaryData = BigDecimal.ZERO;
    /**
     * 2.1、热力(十亿焦)
     */
    private BigDecimal thermalData = BigDecimal.ZERO;
    /**
     * 金额
     */
    private BigDecimal amount = BigDecimal.ZERO;
    /**
     * 唯一关键字，companyId+reportTime
     */
    private String uniqueKey;

    public BigDecimal getNonProductionBuildingsData() {
        //1.3.2、非生产用房耗电量(千瓦时)
        nonProductionBuildingsData = managerRoomData.add(canalRoomData);
        return nonProductionBuildingsData;
    }

    public BigDecimal getBaseStationData() {
        return towerBaseStationData.add(leasingBaseStationData).add(ownBaseStationData);
    }

    public BigDecimal getProductionRoomData() {
        productionRoomData = communicationRoomData.add(getBaseStationData()).add(getDataCenterData()).add(accessRoomData);
        return productionRoomData;
    }
    public BigDecimal getDataCenterData() {
        return externalDataCenterData.add(selfDataCenterData);
    }

    public BigDecimal getTotalPowerData() {
        totalPowerData = getProductionRoomData().add(getNonProductionBuildingsData());
        return totalPowerData;
    }

    public String getUniqueKey() {
        uniqueKey = getKey();
        return uniqueKey;
    }


    private String getKey() {
        return this.companyId + "_" + this.dataType + "_" +this.countType;
    }
}
