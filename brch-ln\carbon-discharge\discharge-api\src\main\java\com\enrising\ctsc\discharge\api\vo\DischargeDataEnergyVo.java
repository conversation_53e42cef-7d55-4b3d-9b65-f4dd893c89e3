package com.enrising.ctsc.discharge.api.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放数据填报表（能源）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeDataEnergyVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
	private Long id;

	/**
	 * 创建者id
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date createTime;

	/**
	 * 更新者id
	 */
	private Long updateBy;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date updateTime;

	/**
	 * 填报单位id
	 */
	private Long companyId;

	/**
	 * 填报单位id
	 */
	private String companyName;

	/**
	 * 填报时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date reportTime;

	/**
	 * 能源指标id
	 */
	private Long energyIndicatorId;

	/**
	 * 集团数据
	 */
	@Excel(name = "集团", width = 20,orderNum = "2")
	private String groupData = "/";

	/**
	 * 股份数据
	 */
	@Excel(name = "股份", width = 20,orderNum = "3")
	private String stockData = "/";

	/**
	 * 大型数据中心数据
	 */
	@Excel(groupName = "数据中心",name = "大型", width = 20,orderNum = "4")
	private String largeData = "/";

	/**
	 * 中小型数据中心数据
	 */
	@Excel(groupName = "数据中心",name = "中小型", width = 20,orderNum = "5")
	private String mediumData = "/";

	/**
	 * 移动业务数据
	 */
	@Excel(name = "移动业务", width = 20,orderNum = "6")
	private String mobileData = "/";

	/**
	 * 上报标志：2-已上报；3-未上报, 4-申请退回
	 */
	private String reportFlag;

	/**
	 * 申请退回理由
	 */
	private String applyReason;
	/**
	 * 退回理由
	 */
	private String returnReason;

	/**
	 * 上报状态：已上报；未上报
	 */
	private String reportStatus;

	/**
	 * 能源指标编码
	 */
	private String indicatorCode;

	/**
	 * 能源指标类型名称
	 */
	@Excel(name = "指标", width = 40,orderNum = "1")
	private String indicatorName;

	/**
	 * 单位id
	 */
	private String unit;

	/**
	 * 单位名称
	 */
	private String unitName;

	/**
	 * 能源类型id
	 */
	private Long energyTypeId;

	/**
	 * 能源类型名称
	 */
	private String energyTypeName;

	/**
	 * group 是否修改过
	 */
	private boolean groupEdit = false;

	/**
	 * stock 是否修改过
	 */
	private boolean stockEdit = false;

	/**
	 * large 是否修改过
	 */
	private boolean largeEdit = false;

	/**
	 * medium 是否修改过
	 */
	private boolean mediumEdit = false;

	/**
	 * mobile 是否修改过
	 */
	private boolean mobileEdit = false;

	/**
	 * 集团数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String groupInputType;

	/**
	 * 股份数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String stockInputType;

	/**
	 * 大型数据中心数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String largeInputType;

	/**
	 * 中小型数据中心数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String mediumInputType;

	/**
	 * 移动业务数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String mobileInputType;

	/**
	 * 单位描述
	 */
	private String unitDescription;
	/**
	 * 碳排放因子
	 */
	private BigDecimal factor;
	/**
	 * 能源转换系数
	 */
	private BigDecimal coefficient;

	public boolean equals(Object obj) {
		DischargeDataEnergyVo u = (DischargeDataEnergyVo) obj;
		return companyName.equals(u.companyName);
	}

	public int hashCode() {
		String in = companyName;
		return in.hashCode();
	}


	/**
	 * 修改记录条数
	 */
	private Long recordCount;
}
