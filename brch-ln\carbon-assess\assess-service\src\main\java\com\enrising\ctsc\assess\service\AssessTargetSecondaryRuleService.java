package com.enrising.ctsc.assess.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.assess.api.bo.AssessTargetSecondaryRuleBo;
import com.enrising.ctsc.assess.api.entity.AssessTargetSecondaryRule;
import com.enrising.ctsc.assess.api.query.AssessTargetSecondaryRuleQuery;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.assess.api.vo.AssessTargetSecondaryRuleVo;

import java.util.List;

/**
 * 考核二级指标规则
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
public interface AssessTargetSecondaryRuleService extends IService<AssessTargetSecondaryRule> {

	/**
	 * 分页查询
	 *
	 * @param page  分页
	 * @param query 参数
	 * @return 列表
	 */
	TableDataInfo<AssessTargetSecondaryRuleVo> findList(Page<AssessTargetSecondaryRuleVo> page, AssessTargetSecondaryRuleQuery query);

	/**
	 * 列表查询
	 *
	 * @param secondaryId 二级指标id
	 * @return 列表
	 */
	List<AssessTargetSecondaryRule> getRuleListBySecondaryId(Long secondaryId);

	/**
	 * 详情
	 *
	 * @param query 参数
	 * @return 详情
	 */
	AssessTargetSecondaryRuleVo detail(AssessTargetSecondaryRuleQuery query);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	void add(AssessTargetSecondaryRuleBo bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	void edit(AssessTargetSecondaryRuleBo bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	void del(Long id);
}
