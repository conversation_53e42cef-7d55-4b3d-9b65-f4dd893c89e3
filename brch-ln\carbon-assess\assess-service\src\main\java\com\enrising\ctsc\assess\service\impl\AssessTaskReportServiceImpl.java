package com.enrising.ctsc.assess.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.assess.api.bo.AssessReportScoreBo;
import com.enrising.ctsc.assess.api.bo.AssessTaskReportBo;
import com.enrising.ctsc.assess.api.bo.AssessTemplateBo;
import com.enrising.ctsc.assess.api.entity.*;
import com.enrising.ctsc.carbon.common.entity.Organization;
import com.enrising.ctsc.carbon.common.enums.DelFlagEnum;
import com.enrising.ctsc.assess.api.enums.ReportStatus;
import com.enrising.ctsc.assess.api.enums.ReportTimeType;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.assess.api.query.AssessTaskReportQuery;
import com.enrising.ctsc.carbon.common.utils.JwtUtils;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.assess.api.utils.constant.AssessConstant;
import com.enrising.ctsc.assess.api.vo.AssessTaskReportScoreDetailVo;
import com.enrising.ctsc.assess.api.vo.AssessTaskReportScoreVo;
import com.enrising.ctsc.assess.api.vo.AssessTaskReportVo;
import com.enrising.ctsc.assess.mapper.AssessTaskReportMapper;
import com.enrising.ctsc.assess.service.AssessTaskReportAttachmentService;
import com.enrising.ctsc.assess.service.AssessTaskReportService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 考核任务上报表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Slf4j
@Service
@AllArgsConstructor
public class AssessTaskReportServiceImpl extends ServiceImpl<AssessTaskReportMapper, AssessTaskReport> implements AssessTaskReportService {

	private final AssessTaskReportAttachmentService assessTaskReportAttachmentService;
	// todo 1
//	private final RemoteAdminService remoteAdminService;

	private final AssessTaskReportMapper assessTaskReportMapper;

	@Override
	public TableDataInfo<AssessTaskReportScoreVo> scoreList(Page<AssessTaskReportVo> page, AssessTaskReportQuery query) {
		QueryWrapper<AssessTaskReportQuery> wrapper = this.getWrapper(query);
		//省级管理员查看的是全部评分任务（数据来源：考核评分—未打分），地市管理员查看的是考核任务（数据来源：考核任务管理（地市公司）—未完成）

		IPage<AssessTaskReportScoreVo> resultPage = baseMapper.scoreList(page, wrapper);
		return TableDataInfo.build(resultPage);
	}

	@Override
	public AssessTaskReportVo detail(AssessTaskReportQuery query) {
		if (ObjectUtil.allFieldIsNull(query)) {
			throw new BusinessException("查询参数不能为空");
		}
		QueryWrapper<AssessTaskReportQuery> wrapper = this.getWrapper(query);
		return baseMapper.detail(wrapper);
	}

	private QueryWrapper<AssessTaskReportQuery> getWrapper(AssessTaskReportQuery query) {
		QueryWrapper<AssessTaskReportQuery> wrapper = new QueryWrapper<>();
		wrapper.eq(query.getId() != null, "t.id", query.getId());
		wrapper.eq("t.del_flag", DelFlagEnum.NOT_DELETED.getValue());
		// 默认查询已上报状态
		wrapper.eq("t.report_status", ReportStatus.HAVE_REPORTED.getValue());
		// 是否打分
		if (null != query.getNotRated()) {
			// 未打分
			wrapper.isNull(query.getNotRated(), "t.assess_score");
			wrapper.orderByDesc(query.getNotRated(), "t.create_time");
			// 已打分
			wrapper.isNotNull(!query.getNotRated(), "t.assess_score");
			wrapper.orderByDesc(!query.getNotRated(), "t.assess_time");
		}
		// 关键字
		if (StrUtil.isNotBlank(query.getKeys())) {
			wrapper.and(w -> w.like("assess_template.template_name", query.getKeys())
					.or().like("target_secondary.target_name", query.getKeys())
			);
		}
		//上报部门
		wrapper.eq(null != query.getCompanyId(), "target_object.company_id", query.getCompanyId());
		return wrapper;
	}

	@Override
	public void add(AssessTaskReportBo bo) {
		AssessTaskReport entity = new AssessTaskReport();
		BeanUtils.copyProperties(bo, entity);
		entity.setReportDate(new Date());
		baseMapper.insert(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void edit(AssessTaskReportBo bo) {
		AssessTaskReport assessTaskReport = new AssessTaskReport();
		BeanUtils.copyProperties(bo, assessTaskReport);
		// 先删除附件
		assessTaskReportAttachmentService.remove(Wrappers.<AssessTaskReportAttachment>lambdaQuery()
				.eq(AssessTaskReportAttachment::getTaskReportId, bo.getId()));
		// 处理附件
		List<AssessTaskReportAttachment> fileList = new ArrayList<>();

		bo.getFileIds().forEach(node -> {
			AssessTaskReportAttachment assessTaskReportAttachment = new AssessTaskReportAttachment();
			assessTaskReportAttachment.setAttachmentId(node);
			assessTaskReportAttachment.setTaskReportId(bo.getId());
			fileList.add(assessTaskReportAttachment);
		});
		if (ReportStatus.HAVE_REPORTED.getValue().equals(bo.getReportStatus())) {
			assessTaskReport.setReportDate(new Date());
		}
		baseMapper.updateById(assessTaskReport);
		assessTaskReportAttachmentService.saveBatch(fileList);
		baseMapper.updateAttachment(bo.getId(), bo.getFileIds());
	}

	@Override
	public void del(Long id) {
		baseMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void reporte(AssessTaskReportBo bo) {
		AssessTaskReport assessTaskReport = new AssessTaskReport();
		BeanUtils.copyProperties(bo, assessTaskReport);
		long taskReportId = IdWorker.getId();
		assessTaskReport.setReportDate(new Date());
		baseMapper.insert(assessTaskReport);
		// 处理附件
		List<AssessTaskReportAttachment> fileList = new ArrayList<>();

		bo.getFileIds().forEach(node -> {
			AssessTaskReportAttachment assessTaskReportAttachment = new AssessTaskReportAttachment();
			assessTaskReportAttachment.setAttachmentId(node);
			assessTaskReportAttachment.setTaskReportId(assessTaskReport.getId());
			fileList.add(assessTaskReportAttachment);
		});
		assessTaskReport.setId(taskReportId);
		assessTaskReportAttachmentService.saveBatch(fileList);
		//更新附件表busi_id
		baseMapper.updateAttachment(taskReportId, bo.getFileIds());
	}

	@Override
	public AssessTaskReportScoreDetailVo scoreDetail(Long reportId) {
		AssessTaskReportScoreDetailVo detail = new AssessTaskReportScoreDetailVo();
		// 任务详情
		AssessTaskReportVo taskReport = baseMapper.getScoreDetail(reportId);
		Assert.notNull(taskReport, "考核任务不存在");
		detail.setTaskReport(taskReport);

		// 考核对象
		AssessTemplateTargetObject assessTemplateTargetObject = new AssessTemplateTargetObject().selectById(taskReport.getTemplateObjectId());
		detail.setTemplateTargetObject(assessTemplateTargetObject);

		Organization company = baseMapper.getOrganizationById(assessTemplateTargetObject.getCompanyId());
		detail.setCompanyName(company.getOrgName());
		Organization sysDept = baseMapper.getOrganizationById(assessTemplateTargetObject.getDeptId());
		detail.setDeptName(sysDept.getOrgName());


		// 模板指标
		AssessTemplateTarget templateTarget = new AssessTemplateTarget().selectById(assessTemplateTargetObject.getTemplateTargetId());
		detail.setTemplateTarget(templateTarget);

		// 考核模板
		AssessTemplate assessTemplate = new AssessTemplate().selectById(templateTarget.getTemplateId());
		detail.setAssessTemplate(assessTemplate);

		// 考核指标详情
		AssessTarget target = new AssessTarget().selectById(templateTarget.getTargetId());
		detail.setAssessTarget(target);
		if (target == null) {
			throw new BusinessException("指标已被删除");
		}

		// 考核二级指标
		AssessTargetSecondary targetSecondary = new AssessTargetSecondary().selectById(templateTarget.getSecondaryTargetId());
		detail.setAssessTargetSecondary(targetSecondary);

		List<AssessTargetSecondaryRule> targetSecondaryRule = new AssessTargetSecondaryRule().selectList(
				Wrappers.<AssessTargetSecondaryRule>lambdaQuery().eq(AssessTargetSecondaryRule::getSecondaryTargetId, targetSecondary.getId())
		);
		detail.setSecondaryRuleList(targetSecondaryRule);
		return detail;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AssessTaskReport score(AssessReportScoreBo bo) {
		AssessTaskReport report = new AssessTaskReport();
		report.setId(bo.getReportId());
		report.setAssessScore(bo.getAssessScore());
		report.setRuleScore(bo.getRuleScore());
		report.setAssessEvaluate(bo.getAssessEvaluate());
		report.setAssesser(JwtUtils.getUser().getId());
		report.setAssessTime(new Date());
		this.updateById(report);
		// 获取下一条打分数据
		return new AssessTaskReport().selectOne(Wrappers.<AssessTaskReport>lambdaQuery()
				// 已上报
				.eq(AssessTaskReport::getReportStatus, ReportStatus.HAVE_REPORTED.getValue())
				// 未打分
				.isNull(AssessTaskReport::getRuleScore)
				// 上报时间倒序
				.orderByDesc(AssessTaskReport::getCreateTime)
				.last("LIMIT 1")
		);
	}

	@Override
	public List<AssessTaskReportVo> listByTemplateObjectId(Long templateObjectId) {
		List<AssessTaskReportVo> resultList = Lists.newArrayList();
		MPJLambdaWrapper<AssessTaskReport> wrapper = new MPJLambdaWrapper<AssessTaskReport>()
				.selectAll(AssessTaskReport.class)
				.eq(AssessTaskReport::getTemplateObjectId, templateObjectId)
				.eq(AssessTaskReport::getReportStatus, ReportStatus.HAVE_REPORTED.getValue())
				.orderByAsc(AssessTaskReport::getCreateTime);
		List<AssessTaskReportVo> list = baseMapper.selectJoinList(AssessTaskReportVo.class, wrapper);

		// 任务附件
		for (AssessTaskReportVo reportVo : list) {
			List<Long> reportAttachmentIdList = new AssessTaskReportAttachment().selectList(Wrappers.<AssessTaskReportAttachment>lambdaQuery()
					.eq(AssessTaskReportAttachment::getTaskReportId, reportVo.getId())
			).stream().map(AssessTaskReportAttachment::getAttachmentId).collect(Collectors.toList());

			List<Attachment> attachmentList = baseMapper.getAttachmentListByIds(reportAttachmentIdList);
			reportVo.setAttachmentList(attachmentList);

		}
		// 构建前端占位
		if (list.size() > 0) {
			// 报告类型 1-1份报告 2-3份报告 3-4份报告  4-12份报告

			//模板周期 <= 指标周期 一份报告。
			//模板周期 >  指标周期 ：
			//模板为年，指标为季度，四份报告。
			//模板为年，指标为月，12份报告。
			//模板为季度，指标为月，三份报告。
			String reportTimeType = list.get(0).getReportType();
			// 年度报告
			if (Objects.equals(reportTimeType, ReportTimeType.YEAR.getType())) {
				extractedReportList(resultList, list, ReportTimeType.YEAR);
			}
			// 模板为季度，指标为月
			else if (Objects.equals(reportTimeType, ReportTimeType.YEAR_MONTH.getType())) {
				extractedReportList(resultList, list, ReportTimeType.YEAR_MONTH);
			}
			// 模板为季度
			else if (Objects.equals(reportTimeType, ReportTimeType.QUARTER.getType())) {
				extractedReportList(resultList, list, ReportTimeType.QUARTER);
			}
			// 模板为月
			else if (Objects.equals(reportTimeType, ReportTimeType.MONTH.getType())) {
				extractedReportList(resultList, list, ReportTimeType.MONTH);
			}
		}
		return resultList;
	}

	/**
	 * 提取上报月份
	 *
	 * @param resultList     结果集
	 * @param list           上报列表
	 * @param reportTimeType 上报类型
	 */
	private void extractedReportList(List<AssessTaskReportVo> resultList, List<AssessTaskReportVo> list, ReportTimeType reportTimeType) {
		log.info("报告类型:{}", reportTimeType.getName());
		for (int i = 0; i < reportTimeType.getReportNum(); i++) {
			AssessTaskReportVo vo = new AssessTaskReportVo();
			vo.setIndex(StrUtil.format("第{}{}", AssessConstant.MONTH_NUM_TO_TEXT.get(i + 1), reportTimeType.getUnitOfTime()));
			// 默认未上报
			vo.setReportStatus(ReportStatus.FAIL_TO_REPORT.getValue());
			resultList.add(vo);
		}
		for (AssessTaskReportVo vo : list) {
			if (vo.getReportTime() > reportTimeType.getReportNum()) {
				log.warn("警告-业务出错：提交的报告分数超出；{}", vo);
				continue;
			}
			vo.setNum(vo.getReportTime() - 1);
			// 等于0 是年份报告
			if (vo.getReportTime() == 0) {
				resultList.set(vo.getReportTime(), vo);
				vo.setIndex(StrUtil.format("第{}{}", AssessConstant.MONTH_NUM_TO_TEXT.get(1), reportTimeType.getUnitOfTime()));
			} else {
				resultList.set(vo.getReportTime() - 1, vo);
				vo.setIndex(StrUtil.format("第{}{}", AssessConstant.MONTH_NUM_TO_TEXT.get(vo.getReportTime()), reportTimeType.getUnitOfTime()));
			}
		}
	}

	@Override
	public List<AssessTaskReportVo> getAllAssessTemplateByYear(List<Long> templateIds, AssessTemplateBo bo) {
		return assessTaskReportMapper.getAllAssessTemplateByYear(templateIds, bo);
	}

}
