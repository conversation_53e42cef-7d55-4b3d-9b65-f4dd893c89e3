package com.enrising.ctsc.assess.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.assess.api.bo.AssessTaskReportAttachmentBo;
import com.enrising.ctsc.assess.api.entity.AssessTaskReportAttachment;
import com.enrising.ctsc.assess.api.query.AssessTaskReportAttachmentQuery;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.assess.api.vo.AssessTaskReportAttachmentVo;

/**
 * 考核任务上报附件
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
public interface AssessTaskReportAttachmentService extends IService<AssessTaskReportAttachment> {

	/**
	 * 分页查询
	 *
	 * @param page  分页
	 * @param query 参数
	 * @return 列表
	 */
	TableDataInfo<AssessTaskReportAttachmentVo> findList(Page<AssessTaskReportAttachmentVo> page, AssessTaskReportAttachmentQuery query);

	/**
	 * 详情
	 *
	 * @param query 参数
	 * @return 详情
	 */
	AssessTaskReportAttachmentVo detail(AssessTaskReportAttachmentQuery query);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	void add(AssessTaskReportAttachmentBo bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	void edit(AssessTaskReportAttachmentBo bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	void del(Long id);
}
