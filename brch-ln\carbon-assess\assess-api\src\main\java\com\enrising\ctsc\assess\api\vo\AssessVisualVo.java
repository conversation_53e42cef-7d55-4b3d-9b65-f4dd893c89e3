package com.enrising.ctsc.assess.api.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/28
 * @note 考核成效管理--可视化展示
 */
@Data
public class AssessVisualVo {

    /*  任务基本信息 */

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务时间
     */
    private String taskTime;

    /**
     * 考核指标
     */
    private String targetNum;

    /**
     * 考核指标 总分
     */
    private double targetScore;

    /**
     * 被考核对象
     */
    private String companyNum;

    /**
     * 考核预警值
     */
    private String warnValue;

    /**
     * 平均分
     */
    private String avgScore;

    /**
     * 最高分
     */
    private double maxScore;

    /**
     * 最低分
     */
    private double minScore;

    /*  考核预警分析 */

    /**
     * 考核成绩高于预警值
     */
    private int thanWarnNum;

    /**
     * 考核成绩低于预警值
     */
    private int lowWarnNum;


    /*  考核成效分析 */

    /**
     * 95-100
     */
    private Integer value1;

    /**
     * 90-95
     */
    private Integer value2;

    /**
     * 80-90
     */
    private Integer value3;

    /**
     * 80以下
     */
    private Integer value4;

    /**
     * 考核排名
     */
    private List<String> companyRank;

    /**
     * 指标分数列表
     */
    private List<TargetScoreVo> targetScoreVoList;

    @Data
    public static class TargetScoreVo {

        private Long id;

        private String name;

        private List<BigDecimal> data;

    }

}
