package com.enrising.ctsc.carbon.common.utils;

/**
 * <AUTHOR>
 * @date 2023/5/22
 * @note
 */

import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

public class InMemoryMultipartFile implements MultipartFile {

	private final String name;
	private final String originalFileName;
	private final String contentType;
	private final byte[] content;

	public InMemoryMultipartFile(String name, String originalFileName, String contentType, byte[] content) {
		this.name = Objects.requireNonNull(name);
		this.originalFileName = Objects.requireNonNull(originalFileName);
		this.contentType = Objects.requireNonNull(contentType);
		this.content = Objects.requireNonNull(content);
	}

	@Override
	public String getName() {
		return name;
	}

	@Override
	public String getOriginalFilename() {
		return originalFileName;
	}

	@Override
	public String getContentType() {
		return contentType;
	}

	@Override
	public boolean isEmpty() {
		return content.length == 0;
	}

	@Override
	public long getSize() {
		return content.length;
	}

	@Override
	public byte[] getBytes() throws IOException {
		return content;
	}

	@Override
	public InputStream getInputStream() throws IOException {
		return new ByteArrayInputStream(content);
	}

	@Override
	public void transferTo(java.io.File file) throws IOException, IllegalStateException {
		throw new UnsupportedOperationException("Method not supported");
	}

}

