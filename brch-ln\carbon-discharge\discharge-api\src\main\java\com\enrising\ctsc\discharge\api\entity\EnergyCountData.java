package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 能耗系统统计双碳数据
 * @Auther qinxinmin
 * @Date 2024/09/16
 */
@Data
@TableName("energy_count_data")
public class EnergyCountData {
    /**
     * 主键id,采用雪花id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 填报单位id
     */
    private Long companyId;

    /**
     * 填报时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date reportTime;

    /**
     * 能源指标id
     */
    private Long energyIndicatorId;

    /**
     * 统计方式：0-原始数据；1-按规则计算
     */
    private String countType;

    /**
     * 集团数据
     */
    private BigDecimal groupData;

    /**
     * 股份数据
     */
    private BigDecimal stockData;

    /**
     * 报账金额
     */
    private BigDecimal amount;

    /**
     * 删除标志：0-正常；1-删除
     */
    @TableLogic
    private String delFlag;
}
