package com.enrising.ctsc.discharge.api.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 绿电审核状态
 *
 * <AUTHOR>
 * @date 2024/9/19 14:18
 */

@Getter
@RequiredArgsConstructor
public enum GreenEnergyAudit {
    DRAFT("0", "草稿"),
    PENDING("1", "待审核"),
    APPROVED("2", "已通过"),
    REJECTED("3", "已驳回");

    private final String code;
    private final String description;

}
