package com.enrising.ctsc.assess.api.utils;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

public class FeignConfigure implements RequestInterceptor {

    /**
     * <p> 在此可以做request参数处理，例如添加头部参数等 </p>
     * @author: qinxinmin
     * @date: 2024-10-17
     * @param requestTemplate :
     **/
    @Override
    public void apply(RequestTemplate requestTemplate) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        Enumeration<String> headerNames = request.getHeaderNames();
        if (headerNames != null) {
            while (headerNames.hasMoreElements()) {
                String name = headerNames.nextElement();
                if (!"content-length".equals(name.toLowerCase())) {
                    String value = request.getHeader(name);
                    requestTemplate.header(name, value);
                }
            }
        }
    }
}