package com.enrising.ctsc.carbon.common.utils;

import cn.afterturn.easypoi.handler.inter.IExcelDictHandler;
import org.apache.commons.lang3.StringUtils;

public class ExcelCommonDictHandlerImpl implements IExcelDictHandler {

	@Override
	public String toName(String dict, Object obj, String name, Object value) {
		if (StringUtils.isEmpty(dict) || ObjectUtil.isEmpty(value)) {
			return "";
		}
		String res = "";
		switch (dict) {
			case "BigDecimal.percentage":
				res = value.toString() + "%";
				break;
		}
		return res;
	}

	@Override
	public String toValue(String dict, Object obj, String name, Object value) {
		return null;
	}
}
