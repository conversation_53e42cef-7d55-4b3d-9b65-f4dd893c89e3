package com.enrising.ctsc.assess.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.assess.api.bo.AssessTemplateTargetObjectBo;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.assess.api.vo.*;
import com.enrising.ctsc.assess.service.AssessTemplateTargetObjectService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 考核模板对象
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-08
 */
@RestController
@RequestMapping("/assess/templateObject")
@AllArgsConstructor
public class AssessTemplateTargetObjectController {
	private final AssessTemplateTargetObjectService assessTemplateTargetObjectService;

	@PostMapping("/getTaskInfoByOrgId")
	public R<List<AssessTemplateTargetObjectVo>> getTaskInfoByOrgId(@RequestBody @Validated({AssessTemplateTargetObjectBo.query.class}) AssessTemplateTargetObjectBo bo) {
		return R.success(assessTemplateTargetObjectService.getTaskInfoByOrgId(bo));
	}

	@PostMapping("/getCompanyInfoList")
	public R<List<AssessProvinceComVo>> getCompanyInfoList(@RequestBody AssessTemplateTargetObjectBo bo) {
		return R.success(assessTemplateTargetObjectService.getCompanyInfoList(bo));
	}

	@PostMapping("/getTaskInfoByTemplateId")
	public R<List<AssessTemplateTargetObjectVo>> getTaskInfoByTemplateId(@RequestBody AssessTemplateTargetObjectBo bo) {
//		return R.success(assessTemplateTargetObjectService.getTaskInfoByTemplateId(bo));
		return R.success(assessTemplateTargetObjectService.getTaskInfoByOrgId(bo));
	}

	@PostMapping("/getScoreByTemplateId")
	public R<AssessProvinceComVo> getScoreByTemplateId(@RequestBody AssessTemplateTargetObjectBo bo) {
		return R.success(assessTemplateTargetObjectService.getScoreByTemplateId(bo));
	}

	@PostMapping("/getTaskInfoById")
	public R<AssessTaskInfoVo> getTaskInfoById(@RequestBody AssessTemplateTargetObjectBo bo) {
		AssessTaskInfoVo detail = assessTemplateTargetObjectService.getTaskInfoById(bo);
		return R.success(detail, "查询成功");
	}

	@PostMapping("/getTargetListByTemplateId")
	public R<Page<AssessRankVo>> getTargetListByTemplateId(@RequestBody QueryPage<AssessTemplateTargetObjectBo> queryPage) {
		return R.success(assessTemplateTargetObjectService.getTargetListByTemplateId(queryPage));
	}

	@PostMapping("/getScoreListByTemplateId")
	public R<Page<AssessRankVo>> getScoreListByTemplateId(@RequestBody QueryPage<AssessTemplateTargetObjectBo> queryPage) {
		return R.success(assessTemplateTargetObjectService.getScoreListByTemplateId(queryPage));
	}

	@PostMapping("/getHeaderScoreList")
	public R<List<AssessScoreVo>> getHeaderScoreList(@RequestBody AssessTemplateTargetObjectBo bo) {
		return R.success(assessTemplateTargetObjectService.getHeaderScoreList(bo));
	}

	@PostMapping("/getRankRateInfoById")
	public R<AssessRankRateVo> getRankRateInfoById(@RequestBody AssessTemplateTargetObjectBo bo) {
		AssessRankRateVo detail = assessTemplateTargetObjectService.getRankRateInfoById(bo);
		return R.success(detail, "查询成功");
	}

	@PostMapping("/getAssessRanking")
	public R getAssessRanking(@RequestBody AssessTemplateTargetObjectBo bo) {
		return R.success(assessTemplateTargetObjectService.getAssessRanking(bo));
	}



}
