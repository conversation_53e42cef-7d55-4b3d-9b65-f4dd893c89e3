package com.enrising.ctsc.discharge.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.discharge.api.bo.DischargeDataCoalBo;
import com.enrising.ctsc.discharge.api.vo.DischargeDataCoalVo;
import com.enrising.ctsc.discharge.service.DischargeDataCoalService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 碳排放数据（碳）控制接口
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@RestController
@RequestMapping("/discharge/coal")
@AllArgsConstructor
public class DischargeDataCoalController {
	private final DischargeDataCoalService DischargeDataCoalService;

	@PostMapping("/getCoalListPage")
		public R<Page<DischargeDataCoalVo>> getCoalListPage(@RequestBody QueryPage<DischargeDataCoalBo> queryPage) {
		return R.success(DischargeDataCoalService.getCoalListPage(queryPage));
	}

	@PostMapping("/getCoalListToExcel")
		public R<List<DischargeDataCoalVo>> getCoalListToExcel(@RequestBody DischargeDataCoalBo DischargeDataCoalBo) {
		return R.success(DischargeDataCoalService.getCoalListToExcel(DischargeDataCoalBo));
	}

	@GetMapping("/getDataList")
		public R<List<DischargeDataCoalVo>> getDataList(Integer dataYear, Long companyId) {
		return R.success(DischargeDataCoalService.getDataList(dataYear, companyId));
	}

	@GetMapping("/detail")
		public R<DischargeDataCoalVo> get(Long id) {
		DischargeDataCoalVo detail = DischargeDataCoalService.detail(id);
		return R.success(detail, "查询成功");
	}

		@PostMapping(value = "/save")
		public R<String> save(@RequestBody DischargeDataCoalBo bo) {
		String sRet = DischargeDataCoalService.add(bo);
		if (StrUtil.isBlank(sRet)){
			return R.success("保存成功");
		}
		return R.failed(sRet);
	}

		@PostMapping(value = "/update")
		public R<String> update(@RequestBody DischargeDataCoalBo bo) {
		DischargeDataCoalService.edit(bo);
		return R.success("修改成功");
	}

		@PostMapping(value = "/delete/{id}")
		public R<String> delete(@PathVariable Long id) {
		DischargeDataCoalService.del(id);
		return R.success("删除成功");
	}
}
