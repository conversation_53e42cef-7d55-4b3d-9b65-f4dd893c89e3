package com.enrising.ctsc.assess.api.query;

import com.enrising.ctsc.assess.api.entity.AssessTaskReport;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 考核任务上报表查询
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AssessTaskReportQuery extends AssessTaskReport {

    /**
     * 未打分
     */
    private Boolean notRated;

    /**
     * 关键字
     */
    private String keys;

    /**
     * 上报部门
     */
    private Long companyId;
}
