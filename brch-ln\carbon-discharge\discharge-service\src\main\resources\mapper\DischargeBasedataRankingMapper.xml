<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeBasedataRankingMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.time_period_year,
            t.time_period_quarter,
            t.carbon_quantity_amplify,
            t.carbon_quantity_amplify_ranking,
            t.carbon_strength_reduction,
            t.carbon_strength_reduction_ranking,
            t.carbon_business_growth,
            t.carbon_business_growth_ranking,
            t.display_state
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeBasedataRankingVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_basedata_ranking t
        ${ew.customSqlSegment}
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.discharge.api.vo.DischargeBasedataRankingVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_basedata_ranking t
        ${ew.customSqlSegment}
        limit 1
    </select>
</mapper>