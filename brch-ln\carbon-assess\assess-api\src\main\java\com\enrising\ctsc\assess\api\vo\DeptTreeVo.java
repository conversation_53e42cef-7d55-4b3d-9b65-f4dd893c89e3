/*
 * Copyright (c) 2021 ctsc-cloudx Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.enrising.ctsc.assess.api.vo;

import com.enrising.ctsc.carbon.common.utils.TreeNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * <AUTHOR>
 * @date 2021/3/1 部门树
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeptTreeVo extends TreeNode {

	/**
	 * 部门名称
	 */
	private String name;

	/**
	 * 主体编码(集团同步使用)
	 */
	private String bodyCode;

	/**
	 * 级别，1-公司，2-部门
	 */
	private String objectType;

	@Override
	public String toString() {
	    return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
	}
}
