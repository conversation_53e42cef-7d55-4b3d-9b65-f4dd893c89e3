package com.enrising.ctsc.carbon.common.utils;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/5/20 17:00
 */
@Data
public class QueryPage<T> implements Serializable {
		protected long current;
		protected long size;
		protected T model;

		@Override
		public String toString() {
			return ToStringBuilder.reflectionToString(this, ToStringStyle.SIMPLE_STYLE);
		}
}
