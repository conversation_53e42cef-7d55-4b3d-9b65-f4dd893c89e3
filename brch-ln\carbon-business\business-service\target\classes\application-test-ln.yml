#日志配置
logging:
  level:
    com.enrising: DEBUG
    org.springframework: INFO
    cn.zhxu.bs: DEBUG

spring:
  datasource:
    ecm:
      url: **********************************************************************************************************************************************************************************
      username: ecm
      password: 3e4r5t#$%
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    rmp:
      url: ****************************************************************************************************************************************************************************
      username: ecm
      password: 3e4r5t#$%
      driver-class-name: com.mysql.cj.jdbc.Driver
    dynamic:
      primary: ecm
      strict: false
      datasource:
        ecm:
          url: ${spring.datasource.ecm.url}
          username: ${spring.datasource.ecm.username}
          password: ${spring.datasource.ecm.password}
          driver-class-name: ${spring.datasource.ecm.driver-class-name}
        rmp:
          url: ${spring.datasource.rmp.url}
          username: ${spring.datasource.rmp.username}
          password: ${spring.datasource.rmp.password}
          driver-class-name: ${spring.datasource.rmp.driver-class-name}
    hikari:
      # HikariCP连接池配置 - 基于HikariConfig类的实际属性
      # 连接池基础配置
      minimum-idle: 3                             # 最小空闲连接数
      maximum-pool-size: 30                       # 最大连接池大小
      auto-commit: true                           # 自动提交
      idle-timeout: 300000                        # 空闲连接超时时间（5分钟）
      pool-name: HikariCP-Assess                  # 连接池名称
      max-lifetime: 420000                        # 连接最大生命周期（7分钟）
      connection-timeout: 30000                   # 连接超时时间（30秒）
      validation-timeout: 5000                    # 验证连接超时时间（5秒）
      # 连接测试配置
      connection-test-query: SELECT 1             # 连接测试查询
      # 性能优化配置
      leak-detection-threshold: 60000             # 连接泄漏检测阈值（60秒）
      # 连接初始化配置
      connection-init-sql: SELECT 1               # 连接初始化SQL
      # 其他配置
      initialization-fail-timeout: 1              # 初始化失败超时时间（1毫秒，快速失败）
      register-mbeans: false                      # 是否注册MBeans
  #redis配置
  #redis配置
  redis:
    #单机环境
    host: ***********
    port: 30101
    username: nhpt
    password: 2bL*mnqVdxp+
    database: 0
    #客户端超时时间单位是毫秒 默认是2000
    timeout: 2000
    jedis:
      pool:
        #连接池的最大数据库连接数。设为0表示无限制,如果是jedis 2.4以后用redis.maxTotal
        max-active: 600
        max-wait: 1
        #最大空闲数
        max-idle: 8
        min-idle: 0


permsCache:
  enable: true
  #  为了保证在token过期刷新token能同时刷新权限过期时间，权限过期时间应当比token过期时间更大
  expireTime: 150
  cacheLength: 1000
  strategy: only_redis

towerManager:
  enable: true
  url: http://localhost:8081/energy-interface
  username: Ad6M7JGFearcoJAM0DV29g==
  password: OLQVEBahTFT8Hxe3YBc3Yw==

#kafka (单独放在spring以外通过 @bean的方式注入)
kafka:
  bootstrap.servers: *************:9092,*************:9093,*************:9094
  #生产者
  producer:
    acks: all #server端将等待所有的副本都被接收后才发送确认。
    retries: 1 #生产者发送失败后，重试的次数
    batch.size: 16384 #当多条消息发送到同一个partition时，该值控制生产者批量发送消息的大小,提高客户端和服务端的性能
    linger.ms: 1 #设置为大于0的值，这样发送者将等待一段时间后，再向服务端发送请求，以实现每次请求可以尽可能多的发送批量消息。
    buffer.memory: 33554432 #生产者缓冲区的大小
    key.serializer: org.apache.kafka.common.serialization.StringSerializer
    value.serializer: org.apache.kafka.common.serialization.StringSerializera
  #消费者
  consumer:
    group.id: scnh-workflow
    enable.auto.commit: false
    auto.offset.reset: earliest
    auto.commit.interval.ms: 1000
    session.timeout.ms: 30000
    key.serializer: org.apache.kafka.common.serialization.StringDeserializer
    value.serializer: org.apache.kafka.common.serialization.StringDeserializer
  # Kerberos基础配合配置
  properties:
    security.protocol: SASL_PLAINTEXT
    java.security.krb5.realm: SC.COM
    java.security.krb5.kdc: scdcam1



#流程引擎PI地址
pi:
  url: http://*************:8090/act-pi-webapp

#流程引擎PI地址
proc:
  inst:
    #url: http://10.206.20.188:8080/act-pi-webapp/procInstRestApi
    url: ${pi.url}/procInstRestApi

#是否同步请求流程引擎
is:
  sync:
    gen:
      task: false
    complete:
      task: false
#候选人角色
DO_CASE_TYPE_1_ROLECODE: ROLE_491
DO_CASE_TYPE_2_ROLECODE: ROLE_575
#辽宁推送OA待办webservice地址
oa:
  taskUrl: http://************:9081/UnifiedWorkbench/ProcessTaskService
  projectName: lnnh
  key: lnnh20190402




# 四川 报账接口信息
MssInterface:
  # 四川 电信报账接口新增报账流程回调参数
  Account:
    URL: http://subsys.sctower.cn/whptcs/InterfaceApi/zgdx/monitor?OPERATION=REIMBURSE
    USER: ***********
    PASSWORD: sctt123123
  MssClient:
    # 报账发送请求 taken
    PASSWORD: ZPIAPPL_SC:Zpiappl!1234
    # 报账发送请求 地址
    SOAPURL: http://************:10001/PIproxy #http://************:10001/PIproxy http://***********:10001/PIproxy

    PASSWORDTESTLN: zpiuser1:Zpiappl@1234
    SOAPURLTESTFORLN: http://************:9001/mssproxy
  BaseInfo:
    # 报账发送xml 头部信息
    RETRY: 1
    S_PROVINCE: 13
    S_SYSTEM: NH
    T_PROVINCE: 13
    T_SYSTEM: CW-CFBZ-CSQN
  MssJsonClient:
    # 报账电表信息、报账单信息 同步集团 open API 信息
    URL: http://************:8001
    PROVINCECODE: 006
    APPKey: 32e9c4952904219c6d532086b3905b50
    AppSecret: 6d403ace70e313ccd10f18663d9e14f7
    msgF: SCNH_
    NHURL: http://************:8000/serviceAgent/rest/ems
  HrId:
    # 查询 财辅 hrloginid 后缀 已存入数据库 无需再加
    orgEndStr: ""#@SC
  #能耗接口编码
  serverName:
    #能耗集成报账接口模式一
    sn_bill: "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_AutoCreateWriteoff"
    #能耗获取报账单状态
    sn_status: "OP_GainWriteoffInstStatus"
    #能耗获取sap标准凭证信息
    sn_sap: "SI_CF_Integrated_IN_Syn_OP_GetSapNumber"
    #能耗获取报账组织信息
    sn_org: "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_AutoCreateWriteoff"
  processCode: "FSJJSX_13"
# 四川单点登录
OaClient:
  GET_ACCESS_TOKEN_URL: "http://************:9001/eam-apps/oauth/token"
  CLIEND_ID: "CTSCNHF20170410"
  RANGT_TYPE: "authorization_code"
  PRIVATE_KEY: "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKjt+a+evZNANB6DfipCSEBSP+ISnF5R8NYXc/c89hKoYKVaoZWwjVQFPGb8HWJgj5fJ1lLwWudFqYH2Odnz2KlMd/4L9M/yqIKIWENB3wOrSS2dq2TGUULQq9NvawL3byK2aApyIKR12/aEB9aqXIvY8HEla5xt6xZbjV3eoN4pAgMBAAECgYEAjEeB7jptxpesMCKVZFZCVVd3wS4fN5vn2IEFRawT046LIwTN5dYEpl7qt8JoTEdPNobfOWRquF560nc7tyftI9qWQ+9iojq5l3G05UuLIJ6UdD31nomZQTlv9mhbVIbQmvoiHlCkb92d2AqJttfW2hqNNem2RTEjINRyVaapnjkCQQD4MYkvmoe+7/PzirfzkcPJ5qEOM9PF6pPX4znjuSyuvWNmJqHmG2LkpyuRgnqhPKnHWDdSxGzi4rnhCrQnGDHHAkEArj40CDqNAWYJ+E8uXzmFMLLLi3W9Koy8HImkd4GlM7Mo1GdqGH8krNRQ1In2gc1IDcc50YrE+PaSKZF4evpwjwJAa4TSIGw6WE4NCblTfVHtVn7x0HxFRt0bVRAb2Po0UDK9Z9helJfSnKmK50t2InlsN26Q8OslHJkJ22LOroz9qwJADSDutrgCq9pOYgFFUzN4T6UJJSbn6+7StavBUqfYFL8pEmgAXHwn/UW6GWmTw0/+X9tlBd7Y3JFnje5CD7ITDQJANMIfNgSYaSldvIWxBzzaKNTJMQv/OqxHa7CNUEtaJ4cMS/cfpwPCffaayPMbhnjDyGWqInMNMhRe18xqLY2PiA=="
  REDIRECT_URL: "http://*************:80/login"

# 四川 接口转发 doMss doWeb
scclTnterface:
  #  http://localhost:8080/energy-interface/httpforward/
  #  http://*************:8080/energy-interface/httpforward/
  httpforwardUrl: "http://localhost:8081/energy-interface/httpforward/"

towerMapUrl: http://*************:8081/ # http://*************:8081/ #铁塔地址subsys.sctower.cn 映射内网地址的URL
file:
  attachments:
    sc:
      endpoint: http://objs.paas.sc.ctc.com
      ak: BWDi1Kv0CwoxrwEfYfVV
      sk: aksJunzfPlXTXLCrxXlnPnGri69ofvUECYXdJBBA
      bucketName: scnh-file

Ai:
  ammeterCode:
    aiUrl: http://*************:8899/ai/aiFactoryServer/v1/apis/1/app-sqx-ammeteocr/ammeter_ocr
    accessKey: 484ee4a25c2c697cbda8eadb0e47083c
    accessSecret: 4a81751e87f37e63efc7705613631757
    percent: 0.7 # 认为识别成功时的概率阈值
    rightLen: 6 # 电表读数的正确长度
    minRightLen: 4 # 认为识别成功的读数最小长度
    openFormat: true # 是否开启格式化，开启后将会自动去除非数字字符并裁剪过长结果或补位缺失位数
    fillString: "*" # openforamt打开时，如果位数不够rightLen位将会填充该字符
autojob:
  server:
    url: http://localhost:8080/energy-interface

#局站日均电量统计 离当前月份 偏离的最大月份
powerStationInfoAveDayElec:
  maxmonth: 12


#后面加的
# 项目相关配置
sccl:
  # 附件存储路径
  profile: G:/work-project-chuangli/file-upload/brch/
  #部署环境
  deployTo: sc
  #单点登录时验证用
  appName: scnh
  #名称
  name: sccl-basic-frame
  #版本
  version: 1.0
  #版权年份
  copyrightYear: 2018
  # 获取ip地址开关
  addressEnabled: true
  #jwt 过期时间 单位 分钟
  jwtExpiredTime: 60
  #操作过期时间 单位分钟 超过这个时间没有操作 不再token续期
  opExpiredTime: 60
  #jwt 加密密钥
  jwtkey: sccl-web-jwt-encrypt-key


#用户配置
user:
  password:
    #密码错误{maxRetryCount}次锁定10分钟
    maxRetryCount: 5
#Spring配置

# MyBatis
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.sccl.modules
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mybatis/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper 单数据源
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql


# Shiro
shiro:
  user:
    # 登录地址
    loginUrl: /login/login
    # 权限认证失败地址
    unauthorizedUrl: /unauth
    # 首页地址
    indexUrl: /index
    #后台授权
    authz: false,
    # 验证码开关
    captchaEnabled: false
    # 验证码类型 math 数组计算 char 字符
    captchaType: math
  cookie:
    # 设置Cookie的域名 默认空，即当前访问的域名
    domain:
    # 设置cookie的有效访问路径
    path: /
    # 设置HttpOnly属性
    httpOnly: true
    # 设置Cookie的过期时间，天为单位
    maxAge: 30
  session:
    # Session超时时间（默认30分钟）
    expireTime: 30
    # 同步session到数据库的周期（默认1分钟）
    dbSyncPeriod: 1
    # 相隔多久检查一次session的有效性，默认就是10分钟
    validationInterval: 10
# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/*,/uniflow/*,/auth/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

#雪花ID生成器版本
snowflakeID: 1.6

#辽宁双碳同步地址、凭证
Twoc:
  test:
    X-APP-ID: 3e97c481fc9225c133533ba709caa156
    X-APP-KEY: a40fa3e3b0c317e550d5f04a74cab68c
    MeterURL: http://136.127.50.149:8888/api/openapi/ln.dbjc.sjtb/syncMeterInfos
    PowerURL: http://136.127.50.149:8888/api/openapi/ln.yd.sjtb/syncMeterDatas
    OtherURL: http://136.127.50.149:8888/api/openapi/ln.qtnh.sjtb/syncOtherEnergyDatas
    PollutionURL: http://136.127.50.149:8888/api/openapi/ln.fsfq.clsj/syncPolluteGovernDatas
  prod:
    X-APP-ID: e8a900c8ad020ff2e2f1f3fee0878191
    X-APP-KEY: 5a633348de89ce6d5e33953b88e437cb
    MeterURL: http://136.96.61.114:8888/api/openapi/ln.stgl.dbsj/syncMeterInfos
    PowerURL: http://136.127.50.149:8888/api/openapi/ln.yd.sjtb/syncMeterDatas
    OtherURL: http://136.96.61.114:8888/api/openapi/ln.stgl.qtnh/syncOtherEnergyDatas
    PollutionURL: http://136.96.61.114:8888/api/openapi/ln.stgl.tbfsfq/syncPolluteGovernDatas

dcoss:
  # 集团
  jituan:
    # 同步能耗
    syncPowerDataInfos: http://10.142.244.169:12500/serviceAgent/rest/api/ctcarbon/report/syncPowerDataInfos
platform:
  mq:
    #dcoos注册的X-APP-ID
    x-app-id: 3e97c481fc9225c133533ba709caa156
    #dcoos注册的X-APP-KEY
    x-app-key: a40fa3e3b0c317e550d5f04a74cab68c
    #dcoos注册时的版本，默认V1.0.00
    x-ctg-version: V1.0.00
    #各个系统申请的clientId
    client-id: CTSCSTPT20230607
minio:
  access-key: Mjz0nPtegx7eUPE12snX
  secret-key: fa39wwL3e0H13TlOKUsu47UwWNpZemugT4cxls95
  end-point: http://33.34.48.82:9000
  bucket-name: ln-nh-oss

