package com.enrising.ctsc.discharge.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.discharge.api.bo.DischargeDataHeatOilBo;
import com.enrising.ctsc.discharge.api.bo.DischargeDataHeatOilExcel;
import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergyCalculate;
import com.enrising.ctsc.discharge.api.entity.DischargeDataHeatOil;
import com.enrising.ctsc.discharge.api.entity.EnergyCalculatePrice;
import com.enrising.ctsc.discharge.api.entity.EnergyCountData;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.enrising.ctsc.carbon.common.utils.JwtUtils;
import com.enrising.ctsc.discharge.api.vo.DischargeDataHeatOilVo;
import com.enrising.ctsc.discharge.mapper.DischargeDataHeatOilMapper;
import com.enrising.ctsc.discharge.mapper.EnergyCountDataMapper;
import com.enrising.ctsc.discharge.service.DischargeDataEnergyCalculateService;
import com.enrising.ctsc.discharge.service.DischargeDataHeatOilService;
import com.enrising.ctsc.discharge.service.EnergyCalculatePriceService;
import com.sccl.common.lang.StringUtils;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 热力燃油数据表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2024-12-20
 */
@Service
@AllArgsConstructor
public class DischargeDataHeatOilServiceImpl extends ServiceImpl<DischargeDataHeatOilMapper, DischargeDataHeatOil> implements DischargeDataHeatOilService {

	private final DischargeDataEnergyCalculateService dischargeDataEnergyCalculateService;
	private final EnergyCalculatePriceService energyCalculatePriceService;
	private final EnergyCountDataMapper energyCountDataMapper;
	private static Long gasolineIndicatorId;
	private static Long dieselIndicatorId;
	private static Long gasolineMovableId;
	private static Long dieselMovableId;
	private static Long thermalIndicatorId;
	private static Long waterIndicatorId;

	@Override
	public List<DischargeDataHeatOilVo> getDataList(DischargeDataHeatOilBo dischargeDataHeatOilBo) {
		if (StrUtil.isNotBlank(dischargeDataHeatOilBo.getYearMonth())) {
			dischargeDataHeatOilBo.setYear(dischargeDataHeatOilBo.getYearMonth().substring(0, 4));
			dischargeDataHeatOilBo.setMonth(dischargeDataHeatOilBo.getYearMonth().substring(5, 7));
		}
		return baseMapper.findList(dischargeDataHeatOilBo);
	}

	@Override
	public DischargeDataHeatOilVo detail(Long id) {
		if (ObjectUtil.isEmpty(id)) {
			throw new BusinessException("查询参数不能为空");
		}
		DischargeDataHeatOil query = new DischargeDataHeatOil();
		query.setId(id);
		return  baseMapper.detail(query);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public String add(DischargeDataHeatOilBo bo) {
		DischargeDataHeatOil entity = new DischargeDataHeatOil();
		BeanUtils.copyProperties(bo, entity);
		if (ObjectUtil.isEmpty(entity.getCompanyId())) {
			if (StrUtil.isNotBlank(bo.getCompanyName())) {
				entity.setCompanyId(baseMapper.getCompanyIdByName(bo.getCompanyName()));
			} else {
				entity.setCompanyId(JwtUtils.getCurrentUserCompanyId());
			}
		}
		if (ObjectUtil.isEmpty(entity.getCompanyId())) {
			return "获取分公司信息失败。";
		}
		if (StrUtil.isNotBlank(bo.getYearMonth())) {
			String yearMonth = bo.getYearMonth();
			if (yearMonth.contains("/")) {
				String[] strings = yearMonth.split("/");
				if (strings.length > 2) {
					String sYear = "20" + strings[2];
					entity.setYear(StringUtils.right(sYear, 4));
					String sMonth = "0" + strings[0];
					entity.setMonth(StringUtils.right(sMonth, 2));
				}
			} else if (yearMonth.contains("年")) {
				int yearIndex = yearMonth.indexOf("年");
				String sYear = "20" + StringUtils.left(yearMonth, yearIndex);
				int monthIndex = yearMonth.indexOf("月");
				String sMonth = "0" + yearMonth.substring(yearIndex + 1, monthIndex);
				entity.setYear(StringUtils.right(sYear, 4));
				entity.setMonth(StringUtils.right(sMonth, 2));
			}
		}
		if (StrUtil.isBlank(entity.getYear()) || StrUtil.isBlank(entity.getMonth())) {
			return "获取分数据年月信息失败。";
		}
		//删除已有数据
		remove(new LambdaQueryWrapper<DischargeDataHeatOil>()
				.eq(DischargeDataHeatOil::getCompanyId, entity.getCompanyId())
				.eq(DischargeDataHeatOil::getYear, entity.getYear())
				.eq(DischargeDataHeatOil::getMonth, entity.getMonth()));
		//查询已有数据是否重复
//		List<DischargeDataHeatOil> dischargeDataHeatOilList = list(
//				new LambdaQueryWrapper<DischargeDataHeatOil>()
//						.eq(DischargeDataHeatOil::getCompanyId, entity.getCompanyId())
//						.eq(DischargeDataHeatOil::getYear, entity.getYear())
//						.eq(DischargeDataHeatOil::getMonth, entity.getMonth())
//						.select(DischargeDataHeatOil::getId));
//		if (CollectionUtil.isNotEmpty(dischargeDataHeatOilList) && dischargeDataHeatOilList.size() > 0) {
//			if (StrUtil.isNotBlank(bo.getCompanyName())) {
//				return String.format("%s %s月数据已存在。\n", bo.getCompanyName(), entity.getYear() + "-" + entity.getMonth());
//			} else {
//				return String.format("%d %s月数据已存在。\n", entity.getCompanyId(), entity.getYear() + "-" + entity.getMonth());
//			}
//		}
		if (baseMapper.insert(entity) == 1) {
			saveEnergyCountData(entity);
			return "";
		} else {
			return "保存失败";
		}
	}

	@Override
	public void edit(DischargeDataHeatOilBo bo) {
		DischargeDataHeatOil entity = new DischargeDataHeatOil();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.updateById(entity);
	}

	@Override
	public void del(Long id) {
		baseMapper.deleteById(id);
	}

	@Override
	public List<DischargeDataHeatOilExcel> importExcel(MultipartFile file) throws Exception {
		ImportParams params = new ImportParams();
		params.setTitleRows(0);
		params.setHeadRows(2);

		List<DischargeDataHeatOilExcel> list = ExcelImportUtil.importExcel(file.getInputStream(), DischargeDataHeatOilExcel.class, params);
		return list;
	}

	@Override
	public String addBatch(List<DischargeDataHeatOilBo> boList) {
		String result = "";
		for (DischargeDataHeatOilBo bo : boList) {
			String ret = add(bo);
			if (StrUtil.isNotBlank(ret)) {
				result = result + ret;
			}
		}
		return result;
	}

	@Override
	@SneakyThrows
	public void downloadTemplate(HttpServletRequest request, HttpServletResponse response) {
		ClassPathResource resource = new ClassPathResource("/template/热力燃油数据导入模板.xlsx");
		InputStream inputStream = resource.getInputStream();
		byte[] fileBytes = IOUtils.toByteArray(inputStream);
		response.setCharacterEncoding("utf-8");
		response.setContentType("application/octet-stream");
		response.getOutputStream().write(fileBytes);
	}

	private void saveEnergyCountData(DischargeDataHeatOil entity) {
		Date reportTime = DateUtils.parseDate(entity.getYear() + "-" + entity.getMonth() + "-01 00:00:00");
		if (ObjectUtil.isEmpty(gasolineIndicatorId)) {
			gasolineIndicatorId = energyCountDataMapper.getIndicatorIdByCode("1.5");//汽油消耗量(升)
			gasolineMovableId = energyCountDataMapper.getIndicatorIdByCode("1.5.1");//其中：移动源（升）
			dieselIndicatorId = energyCountDataMapper.getIndicatorIdByCode("1.7");  //柴油消耗量(升)
			dieselMovableId = energyCountDataMapper.getIndicatorIdByCode("1.7.1");  //其中：移动源（升）
			thermalIndicatorId = energyCountDataMapper.getIndicatorIdByCode("2.1"); //热力(十亿焦)
			waterIndicatorId = energyCountDataMapper.getIndicatorIdByCode("2.3");   //新水用量(吨)
		}
		//查询能源价格
		EnergyCalculatePrice query = new EnergyCalculatePrice();
		query.setReportTime(reportTime);
		EnergyCalculatePrice energyCalculatePrice = energyCalculatePriceService.getLastOne(query);

		if (ObjectUtil.isNotEmpty(energyCalculatePrice)) {
			//根据价格与金额计算用量
			if (energyCalculatePrice.getGasolinePriceFive().compareTo(BigDecimal.ZERO) > 0) {
				entity.setGasolineStock(entity.getGasolineStock().divide(energyCalculatePrice.getGasolinePriceTwo(), 6, BigDecimal.ROUND_HALF_UP));
				entity.setGasolineGroup(entity.getGasolineGroup().divide(energyCalculatePrice.getGasolinePriceTwo(), 6, BigDecimal.ROUND_HALF_UP));
			}
			if (energyCalculatePrice.getDieselPrice().compareTo(BigDecimal.ZERO) > 0) {
				entity.setDieselStock(entity.getDieselStock().divide(energyCalculatePrice.getDieselPrice(), 6, BigDecimal.ROUND_HALF_UP));
				entity.setDieselGroup(entity.getDieselGroup().divide(energyCalculatePrice.getDieselPrice(), 6, BigDecimal.ROUND_HALF_UP));
			}
			if (energyCalculatePrice.getThermalPrice().compareTo(BigDecimal.ZERO) > 0) {
				//热力值=价格/单价*30*24*60*0.7*3.6/1000000
				//30*24*60*0.7*3.6 = 108864
				entity.setHeatStock(entity.getHeatStock().multiply(new BigDecimal(108864)).multiply(new BigDecimal(1.17)).
						divide(energyCalculatePrice.getThermalPrice().multiply(new BigDecimal(1000000)), 6, BigDecimal.ROUND_HALF_UP));
				entity.setHeatGroup(entity.getHeatGroup().multiply(new BigDecimal(108864)).multiply(new BigDecimal(1.17)).
						divide(energyCalculatePrice.getThermalPrice().multiply(new BigDecimal(1000000)), 6, BigDecimal.ROUND_HALF_UP));
			}
			if (energyCalculatePrice.getWaterPrice().compareTo(BigDecimal.ZERO) > 0) {
				//水
				entity.setWaterStock(entity.getWaterStock().divide(energyCalculatePrice.getWaterPrice(), 6, BigDecimal.ROUND_HALF_UP));
				entity.setWaterGroup(entity.getWaterGroup().divide(energyCalculatePrice.getWaterPrice(), 6, BigDecimal.ROUND_HALF_UP));
			}
		}
		//保存统计原始数据
		insertEnergyCountData(entity.getCompanyId(), reportTime, gasolineIndicatorId, entity.getGasolineStock(),
				entity.getGasolineGroup());
		insertEnergyCountData(entity.getCompanyId(), reportTime, dieselIndicatorId, entity.getDieselStock(),
				entity.getDieselGroup());
		insertEnergyCountData(entity.getCompanyId(), reportTime, gasolineMovableId, entity.getGasolineStock(),
				entity.getGasolineGroup());
		insertEnergyCountData(entity.getCompanyId(), reportTime, dieselMovableId, entity.getDieselStock(),
				entity.getDieselGroup());
		insertEnergyCountData(entity.getCompanyId(), reportTime, thermalIndicatorId, entity.getHeatStock(),
				entity.getHeatGroup());
		insertEnergyCountData(entity.getCompanyId(), reportTime, waterIndicatorId, entity.getWaterStock(),
				entity.getWaterGroup());
		//保存按规则计算数据
		saveEnergyCalculateData(entity.getCompanyId(), reportTime, gasolineIndicatorId, entity.getGasolineStock(),
				entity.getGasolineGroup());
		saveEnergyCalculateData(entity.getCompanyId(), reportTime, dieselIndicatorId, entity.getDieselStock(),
				entity.getDieselGroup());
		saveEnergyCalculateData(entity.getCompanyId(), reportTime, gasolineMovableId, entity.getGasolineStock(),
				entity.getGasolineGroup());
		saveEnergyCalculateData(entity.getCompanyId(), reportTime, dieselMovableId, entity.getDieselStock(),
				entity.getDieselGroup());
		saveEnergyCalculateData(entity.getCompanyId(), reportTime, thermalIndicatorId, entity.getHeatStock(),
				entity.getHeatGroup());
		saveEnergyCalculateData(entity.getCompanyId(), reportTime, waterIndicatorId, entity.getWaterStock(),
				entity.getWaterGroup());
	}
	/**
	 * 保存统计原始数据
	 *
	 * @<NAME_EMAIL>
	 * @since 1.0.0 2024-12-25
	 */
	private int insertEnergyCountData(Long companyId, Date reportTime, Long indicatorId, BigDecimal stockData,
									   BigDecimal groupData) {
		energyCountDataMapper.delete(new LambdaQueryWrapper<EnergyCountData>()
				.eq(EnergyCountData::getCompanyId, companyId)
				.eq(EnergyCountData::getReportTime, reportTime)
				.eq(EnergyCountData::getEnergyIndicatorId, indicatorId));
		EnergyCountData energyCountData = new EnergyCountData();
		energyCountData.setCompanyId(companyId);
		energyCountData.setReportTime(reportTime);
		energyCountData.setEnergyIndicatorId(indicatorId);
		energyCountData.setStockData(stockData);
		energyCountData.setGroupData(groupData);
		return energyCountDataMapper.insert(energyCountData);
	}
	/**
	 * 保存按规则计算数据
	 *
	 * @<NAME_EMAIL>
	 * @since 1.0.0 2024-12-25
	 */
	private int saveEnergyCalculateData(Long companyId, Date reportTime, Long indicatorId, BigDecimal stockData,
									  BigDecimal groupData) {
		DischargeDataEnergyCalculate dischargeDataEnergyCalculate = new DischargeDataEnergyCalculate();
		dischargeDataEnergyCalculate.setCompanyId(companyId);
		dischargeDataEnergyCalculate.setReportTime(reportTime);
		dischargeDataEnergyCalculate.setEnergyIndicatorId(indicatorId);
		dischargeDataEnergyCalculate.setStockData(stockData);
		dischargeDataEnergyCalculate.setGroupData(groupData);
		return dischargeDataEnergyCalculateService.saveData(dischargeDataEnergyCalculate);
	}
}
