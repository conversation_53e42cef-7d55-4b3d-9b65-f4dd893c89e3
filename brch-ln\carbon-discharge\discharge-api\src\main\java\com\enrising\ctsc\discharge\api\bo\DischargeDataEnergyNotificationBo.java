package com.enrising.ctsc.discharge.api.bo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 碳排放数据填报（能源）提醒
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-12
 */
@Data
public class DischargeDataEnergyNotificationBo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
		private Long id;


	/**
	 * 填报单位id
	 */
		private Long companyId;

	/**
	 * 填报单位id列表
	 */
		private List<Long> companyIdList;

	/**
	 * 填报时间
	 */
		@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date reportTime;

	/**
	 * 状态
	 */
		private String status;


}
