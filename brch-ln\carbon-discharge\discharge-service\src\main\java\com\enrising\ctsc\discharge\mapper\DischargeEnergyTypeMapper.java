package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyType;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyTypeQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyTypeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 碳排放能源类型表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Mapper
public interface DischargeEnergyTypeMapper extends BaseMapper<DischargeEnergyType> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
	IPage<DischargeEnergyTypeVo> findList(Page<DischargeEnergyTypeVo> page, @Param(Constants.WRAPPER) Wrapper<DischargeEnergyTypeQuery> wrapper);

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
	List<DischargeEnergyTypeVo> findList(@Param(Constants.WRAPPER) Wrapper<DischargeEnergyTypeQuery> wrapper);

	/**
	 * 详情查询
	 *
	 * @param wrapper 条件
	 * @return 结果
	 */
	DischargeEnergyTypeVo detail(@Param(Constants.WRAPPER) Wrapper<DischargeEnergyTypeQuery> wrapper);
}