package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 碳排放数据填报（能源）提醒表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-12
 */

@Data
@TableName("discharge_data_energy_notification")
public class DischargeDataEnergyNotification extends Model<DischargeDataEnergyNotification> {

	/**
	 * 主键id,采用雪花id
	 */
		@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 创建者id
	 */
		private Long createBy;

	/**
	 * 创建时间
	 */
		private Date createTime;

	/**
	 * 更新者id
	 */
		private Long updateBy;

	/**
	 * 更新时间
	 */
		private Date updateTime;

	/**
	 * 填报单位id
	 */
		private Long companyId;

	/**
	 * 填报时间
	 */
		private Date reportTime;

	/**
	 * 提醒时间
	 */
		private Date remindTime;

	/**
	 * 状态：1-未读；2-已读
	 */
		private String status;

	/**
	 * 删除标志：0-正常；1-删除
	 */
	@TableLogic
		private String delFlag;

}
