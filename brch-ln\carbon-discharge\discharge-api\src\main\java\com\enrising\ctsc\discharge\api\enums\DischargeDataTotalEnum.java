package com.enrising.ctsc.discharge.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
public interface DischargeDataTotalEnum {

	@AllArgsConstructor
	@Getter
	enum CarbonCompareTimeType implements DischargeDataTotalEnum {
		//双碳云脑--双碳全景图--碳排放量数据对比
		按月("1", "按月"),
		按年("2", "按年");
		private String value;
		private String name;
	}

	@AllArgsConstructor
	@Getter
	enum EnergyCountType implements DischargeDataTotalEnum {
		//能耗数据统计类型
		原始数据("0", "原始数据"),
		按规则计算("1", "按规则计算");
		private String value;
		private String name;
	}

	@AllArgsConstructor
	@Getter
	enum EnergyDataType implements DischargeDataTotalEnum {
		//能耗数据类型
		股份数据("A", "股份数据"),
		集团数据("B", "集团数据");
		private String value;
		private String name;
	}

	@AllArgsConstructor
	@Getter
	enum ReportGroupFlagType implements DischargeDataTotalEnum {
		//上报集团标志类型
		未上报集团("0", "未上报集团"),
		已上报集团("1", "已上报集团");
		private String value;
		private String name;
	}
}
