package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.carbon.common.constant.CommonConstants;
import com.enrising.ctsc.carbon.common.utils.*;
import com.enrising.ctsc.discharge.api.bo.*;
import com.enrising.ctsc.discharge.api.entity.DischargeMonitorSetting;
import com.enrising.ctsc.discharge.api.enums.DelFlagEnum;
import com.enrising.ctsc.discharge.api.enums.TargetType;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.discharge.api.query.AssessTargetSecondaryQuery;
import com.enrising.ctsc.discharge.api.query.DischargeMonitorSettingQuery;
import com.enrising.ctsc.discharge.api.utils.*;
import com.enrising.ctsc.discharge.api.vo.*;
import com.enrising.ctsc.discharge.mapper.*;
import com.enrising.ctsc.discharge.service.DischargeMonitorSettingService;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 碳排放监测设置表
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Service
@AllArgsConstructor
public class DischargeMonitorSettingServiceImpl extends ServiceImpl<DischargeMonitorSettingMapper, DischargeMonitorSetting> implements DischargeMonitorSettingService {

	private final DischargeMonitorSettingMapper dischargeMonitorSettingMapper;

	private final DischargeDataElectricMapper dischargeDataElectricMapper;

	private final DischargeDataOilMapper dischargeDataOilMapper;

	private final DischargeDataGasMapper dischargeDataGasMapper;

	private final DischargeDataThermalMapper dischargeDataThermalMapper;

	private final DischargeDataWaterMapper dischargeDataWaterMapper;

	private final DischargeDataCoalMapper dischargeDataCoalMapper;

	private DischargeDataExamineMapper dischargeDataExamineMapper;
//	private final RemoteAdminService remoteAdminService;

//	private final RemoteDictService remoteDictService;

	@Override
	public TableDataInfo<DischargeMonitorSettingVo> findList(Page<DischargeMonitorSettingVo> page, DischargeMonitorSettingQuery query) {
		QueryWrapper<DischargeMonitorSettingQuery> wrapper = this.getWrapper(query);
		IPage<DischargeMonitorSettingVo> resultPage = baseMapper.findList(page, wrapper);
		return TableDataInfo.build(resultPage);
	}

	@Override
	public TableDataInfo<DischargeMonitorSettingVo> getSettingList(Page<DischargeMonitorSettingVo> page, DischargeMonitorSettingBo bo) {
		if(ObjectUtil.isNotNull(bo.getYear())){
			BigDecimal lastYear = new BigDecimal(bo.getYear()).subtract(new BigDecimal("1"));
			bo.setPreYear(lastYear.toString());
		} else {
			bo.setYear(String.valueOf(DateUtil.year(new Date())));
			BigDecimal lastYear = new BigDecimal(bo.getYear()).subtract(new BigDecimal("1"));
			bo.setPreYear(lastYear.toString());
		}
		IPage<DischargeMonitorSettingVo> resultPage = dischargeMonitorSettingMapper.getSettingList(page,bo);
		if(CollectionUtil.isNotEmpty(resultPage.getRecords())){
			List<DischargeMonitorSettingVo> records = resultPage.getRecords();
			records.forEach(node->{
				node.setPreCarbonTotal(node.getPreCarbonTotal().setScale(2,RoundingMode.HALF_UP));
				// 设置使用率
				node.setUserRate(MathUtils.d2dPercent(node.getPreCarbonTotal(),new BigDecimal(node.getPreQuota())));

				node.setPreCarbonTotalStr(node.getPreCarbonTotal().compareTo(new BigDecimal(0))> CommonConstants.ZERO_NUMBER?
						node.getPreCarbonTotal().toString():"暂无相关数据");
				node.setQuota(CommonConstants.ZERO_STRING.equals(node.getQuota())?"——":node.getQuota());
				node.setPreQuota(CommonConstants.ZERO_STRING.equals(node.getPreQuota())?"暂无相关数据":node.getPreQuota());
			});
		}
		return TableDataInfo.build(resultPage);
	}

	@Override
	public TableDataInfo<DischargeMonitorSettingVo> getRecordsList(Page<DischargeMonitorSettingVo> page, DischargeMonitorSettingBo bo) {
		if(ObjectUtil.isNull(bo.getYear())){
			bo.setYear(String.valueOf(DateUtil.year(new Date())));
		}
		IPage<DischargeMonitorSettingVo> resultPage = new Page<>();
//		if(SecurityUtils.isAdmin()||SecurityUtils.getRoleCodes().contains(SysRoleEnums.ROLE_PROVINCE_ADMIN.getValue())){
//			 resultPage = dischargeMonitorSettingMapper.getRecordsList(page,bo);
//		}else {
//			AiUser user = SecurityUtils.getUser();
//			// 获取部门id  只看自己
//			assert user != null;
//			Long deptId = user.getDeptId();
//			bo.setCompanyId(deptId);
//		}
		bo.setCompanyId(JwtUtils.getCurrentUserCompanyId());
		resultPage = dischargeMonitorSettingMapper.getRecordsList(page,bo);
		if(CollectionUtil.isEmpty(resultPage.getRecords())){
			return TableDataInfo.build(resultPage);
		}
		List<DischargeMonitorSettingVo> records = resultPage.getRecords();
		records.forEach(node->{
			node.setCarbonTotal(NumberFormatUtils.formatValue(node.getCarbonTotal()));
			// 设置使用率
			node.setUserRate(MathUtils.d2dPercent(node.getCarbonTotal(),new BigDecimal(node.getQuota())));
			// 计算颜色
			BigDecimal useRate = new BigDecimal(node.getUserRate().replace("%",""));
			node.setColorType(setColorType(useRate));
			int num = node.getCarbonTotal().compareTo(new BigDecimal(node.getQuota()));
				// 查看是否超过全年定额值
			if( num > CommonConstants.ZERO_NUMBER){
				// 查找是哪个月超量的
				DischargeMonitorSettingBo settingBo = new DischargeMonitorSettingBo();
				settingBo.setYear(bo.getYear());
				settingBo.setCompanyId(node.getCompanyId());
				List<DischargeSettingCompanyVo> list = getMonthCompanyList(settingBo);
				if(CollectionUtil.isNotEmpty(list)){
					for (DischargeSettingCompanyVo dischargeSettingCompanyVo : list) {
						int i1 =NumberFormatUtils.formatValue(dischargeSettingCompanyVo.getMonthCarbon())
								.compareTo(new BigDecimal(node.getQuota()));
						if (i1 > 0) {
							node.setOverTime(bo.getYear() + "-" + dischargeSettingCompanyVo.getMonth());
							break;
						}
					}
				}
			}
			node.setCarbonTotalStr(node.getCarbonTotal().compareTo(new BigDecimal(0))>CommonConstants.ZERO_NUMBER?
					node.getCarbonTotal().toString():"暂无相关数据");
			node.setQuota(CommonConstants.ZERO_STRING.equals(node.getQuota())?"暂无相关数据":node.getQuota());
			});
		return TableDataInfo.build(resultPage);
	}

	@Override
	public TableDataInfo<DischargeMonitorSettingVo> getEnergyCompanyList(Page<DischargeMonitorSettingVo> page,
																		 DischargeMonitorSettingBo bo) {
		IPage<DischargeMonitorSettingVo> resultPage = dischargeMonitorSettingMapper.getEnergyCompanyList(page,bo);
		return TableDataInfo.build(resultPage);
	}

	@Override
	public HashMap<String, Object> getMonitorSituationList() {
		HashMap<String, Object> resultMap = new HashMap<>();
		DischargeMonitorSettingBo bo = new DischargeMonitorSettingBo();
		bo.setYear(String.valueOf(DateUtil.year(new Date())));

		List<DischargeMonitorSettingVo> dataList = dischargeMonitorSettingMapper.getRecordsList(bo);
		if(CollectionUtil.isEmpty(dataList)){
			return null;
		}
		dataList.forEach(node->{
			node.setCarbonTotal(NumberFormatUtils.formatValue(node.getCarbonTotal()));
			// 设置使用率
			node.setUserRate(MathUtils.d2dPercent(node.getCarbonTotal(),new BigDecimal(node.getQuota())));
			// 计算颜色
			BigDecimal useRate = new BigDecimal(node.getUserRate().replace("%",""));
			node.setColorType(setColorType(useRate));
			int num = node.getCarbonTotal().compareTo(new BigDecimal(node.getQuota()));
			// 查看是否超过全年定额值
			if( num > CommonConstants.ZERO_NUMBER){
				// 查找是哪个月超量的
				DischargeMonitorSettingBo settingBo = new DischargeMonitorSettingBo();
				settingBo.setYear(bo.getYear());
				settingBo.setCompanyId(node.getCompanyId());
				List<DischargeSettingCompanyVo> list = getMonthCompanyList(settingBo);
				if(CollectionUtil.isNotEmpty(list)){
					for (DischargeSettingCompanyVo dischargeSettingCompanyVo : list) {
						int i1 =NumberFormatUtils.formatValue(dischargeSettingCompanyVo.getMonthCarbon())
								.compareTo(new BigDecimal(node.getQuota()));
						if (i1 > 0) {
							node.setOverTime(bo.getYear() + "-" + dischargeSettingCompanyVo.getMonth());
							break;
						}
					}
				}
			}
		});
		Long underSettingNumber = dataList.stream().filter(data -> data.getCarbonTotal().compareTo(new BigDecimal(data.getQuota())) <= 0).count();
		Long aboveSettingNumber = dataList.stream().filter(data -> data.getCarbonTotal().compareTo(new BigDecimal(data.getQuota())) > 0).count();
		resultMap.put("underSettingNumber", underSettingNumber);
		resultMap.put("aboveSettingNumber", aboveSettingNumber);
		resultMap.put("dataList", dataList);
		return resultMap;
	}

	@Override
	public List<DischargeEmissionTrendVo> getTelecomBusinessTotal(String nowYear) {
		return dischargeMonitorSettingMapper.getTelecomBusinessTotal(nowYear);
	}

	private Integer setColorType(BigDecimal useRate){
		int i = 0;
		if(useRate.compareTo(new BigDecimal(80)) < 0){
			i = 1;
		} else if(useRate.compareTo(new BigDecimal(100)) <= 0 ){
			i = 2;
		} else {
			i = 3;
		}
		return i;
	}

	@Override
	public List<DischargeMonitorSettingVo> getCarbonDownByTemplateId(List<Long> companyIds) {
		String nowYear = String.valueOf(DateUtil.year(new Date()));
		String preYear = new BigDecimal(nowYear).subtract(new BigDecimal("1")).toString();
		DischargeMonitorSettingBo bo = new DischargeMonitorSettingBo();
		bo.setCompanyIds(companyIds);
		bo.setYear(preYear);
		// 获取 上年单位电信业务总量碳排放
		List<DischargeMonitorSettingVo> lastYearList = dischargeMonitorSettingMapper.getYearTotalCarbon(bo);
		bo.setYear(nowYear);
		// 获取 今年单位电信业务总量碳排放
		List<DischargeMonitorSettingVo> nowYearList = dischargeMonitorSettingMapper.getYearTotalCarbon(bo);
		List<DischargeMonitorSettingVo> dataList = new ArrayList<>();
		companyIds.forEach(node ->{
			DischargeMonitorSettingVo vo = new DischargeMonitorSettingVo();
			DischargeMonitorSettingVo lastYear = lastYearList.stream().filter(node1 -> {
				return node1.getCompanyId().equals(node);
			}).findFirst().orElse(new DischargeMonitorSettingVo());
			DischargeMonitorSettingVo thisYear = nowYearList.stream().filter(node1 -> {
				return node1.getCompanyId().equals(node);
			}).findFirst().orElse(new DischargeMonitorSettingVo());
			vo.setCompanyId(node);
			vo.setCarbonTotal(thisYear.getCarbonTotal().setScale(2,RoundingMode.HALF_UP));
			vo.setPreCarbonTotal(lastYear.getCarbonTotal().setScale(2,RoundingMode.HALF_UP));
			dataList.add(vo);
		});
		return dataList;
	}

	@Override
	@SneakyThrows
	public List<CompanyCarbonVo> getCompanyCarbonByTarget(AssessTargetSecondaryQuery query) {
		String nowYear = "";
		String preYear = "";
		List<CompanyCarbonVo> dataList= new ArrayList<>();
		AssessTargetSecondaryQuery queryLast = new AssessTargetSecondaryQuery();
		if(CollectionUtil.isNotEmpty(query.getSearchTime())){
			DateTime beginTime = DateUtil.beginOfMonth(DateUtil.parse(query.getSearchTime().get(CommonConstants.ZERO_NUMBER), "yyyy-MM"));
			DateTime endTime = DateUtil.endOfMonth(DateUtil.parse(query.getSearchTime().get(1), "yyyy-MM"));
			query.setBeginTime(beginTime);
			query.setEndTime(endTime);


			queryLast.setSearchTime(query.getSearchTime());
			queryLast.setBeginTime(DateUtil.offset(query.getBeginTime(),DateField.YEAR,-1));
			queryLast.setEndTime(DateUtil.offset(query.getEndTime(),DateField.YEAR,-1));

			preYear= String.valueOf(DateUtil.year(DateUtil.parse(query.getSearchTime().get(CommonConstants.ZERO_NUMBER), "yyyy")));
			nowYear = String.valueOf(DateUtil.year(DateUtil.parse(query.getSearchTime().get(1), "yyyy")));
			if(preYear.equals(nowYear)){
				preYear = new BigDecimal(nowYear).subtract(new BigDecimal("1")).toString();
			}
		} else {
			nowYear = String.valueOf(DateUtil.year(new Date()));
			preYear = new BigDecimal(nowYear).subtract(new BigDecimal("1")).toString();
		}
		query.setYear(nowYear);
		queryLast.setYear(preYear);
		List<Long> companyIds = query.getCompanyIds();

		// 查询 业务总量（万元） 今年
		List<DischargeEmissionTrendVo> nowYearBusinessTotal = dischargeMonitorSettingMapper.getTelecomBusinessTotal(nowYear);
		List<DischargeEmissionTrendVo> lastYearBusinessTotal = dischargeMonitorSettingMapper.getTelecomBusinessTotal(preYear);

			// 碳排放强度=碳排放量（吨）/业务总量（万元）
		companyIds.forEach(node->{
				CompanyCarbonVo companyCarbonVo = new CompanyCarbonVo();
				query.setCompanyId(node);
				queryLast.setCompanyId(node);
				DischargeMonitorSettingVo thisYear = dischargeMonitorSettingMapper.getCarbonTotal(query);
				DischargeMonitorSettingVo lastYear = dischargeMonitorSettingMapper.getCarbonTotal(queryLast);
				companyCarbonVo.setCompanyId(node);

				if(TargetType.FOSSIL_ENERGY.getValue().equals(query.getType())){
					companyCarbonVo.setNowCarbonTotal(MathUtils.formatToTon(thisYear.getCarbonTotal()));
					companyCarbonVo.setPreCarbonTotal(MathUtils.formatToTon(lastYear.getCarbonTotal()));
					companyCarbonVo.setDownRate(MathUtils.d2dRate(companyCarbonVo.getNowCarbonTotal(),companyCarbonVo.getPreCarbonTotal()));
				} else if(TargetType.CARBON_TOTAL.getValue().equals(query.getType())) {
					Map<Long, BigDecimal> nowYearBTMp = nowYearBusinessTotal.stream().filter(node1 -> node1.getCompanyId().equals(node))
							.collect(Collectors.toMap(DischargeEmissionTrendVo::getCompanyId, DischargeEmissionTrendVo::getCarbonIntensity));

					Map<Long, BigDecimal> lastYearBTMp = lastYearBusinessTotal.stream().filter(node1 -> node1.getCompanyId().equals(node))
							.collect(Collectors.toMap(DischargeEmissionTrendVo::getCompanyId, DischargeEmissionTrendVo::getCarbonIntensity));

					companyCarbonVo.setNowCarbonTotal(
							nowYearBTMp.get(node) == null? new BigDecimal(0) :MathUtils.formatToTon(thisYear.getCarbonTotal())
									.divide(nowYearBTMp.get(node), 4, RoundingMode.HALF_UP)
					);
					companyCarbonVo.setPreCarbonTotal(
							lastYearBTMp.get(node) == null? new BigDecimal(0) :MathUtils.formatToTon(lastYear.getCarbonTotal())
									.divide(lastYearBTMp.get(node), 4, RoundingMode.HALF_UP)
					);
					companyCarbonVo.setDownRate(MathUtils.d2dRate(companyCarbonVo.getNowCarbonTotal(),companyCarbonVo.getPreCarbonTotal()));
				}

				dataList.add(companyCarbonVo);
		});

		return dataList;
	}

	@Override
	public List<CompanyCarbonVo> getMonthCarbonByTarget(AssessTargetSecondaryQuery query) {
		String nowYear = "";
		String preYear = "";
		List<CompanyCarbonVo> dataList = new ArrayList<>();
		if(CollectionUtil.isNotEmpty(query.getSearchTime())){
			preYear= String.valueOf(DateUtil.year(DateUtil.parse(query.getSearchTime().get(CommonConstants.ZERO_NUMBER), "yyyy")));
			nowYear = String.valueOf(DateUtil.year(DateUtil.parse(query.getSearchTime().get(1), "yyyy")));
			if(preYear.equals(nowYear)){
				preYear = new BigDecimal(nowYear).subtract(new BigDecimal("1")).toString();
			}
		} else {
			nowYear = String.valueOf(DateUtil.year(new Date()));
			preYear = new BigDecimal(nowYear).subtract(new BigDecimal("1")).toString();
		}
		DischargeMonitorSettingBo bo =new DischargeMonitorSettingBo();
		bo.setCompanyId(query.getCompanyId());
		bo.setYear(nowYear);
		List<DischargeSettingCompanyVo> nowYearCompanyList = getMonthCompanyList(bo);
		bo.setYear(preYear);
		List<DischargeSettingCompanyVo> preYearCompanyList = getMonthCompanyList(bo);



		if(nowYearCompanyList.size() >= preYearCompanyList.size()){
			nowYearCompanyList.forEach(item->{
				CompanyCarbonVo companyCarbonVo = makeUpData(item, 1,query.getType(), preYearCompanyList);
				dataList.add(companyCarbonVo);
			});
		} else {
			preYearCompanyList.forEach(item->{
				CompanyCarbonVo companyCarbonVo = makeUpData(item, 2,query.getType(), nowYearCompanyList);
				dataList.add(companyCarbonVo);
			});
		}

		if(TargetType.CARBON_TOTAL.getValue().equals(query.getType())){
			// 查询 业务总量（万元） 今年
			List<DischargeEmissionTrendVo> nowYearBusinessTotal = dischargeMonitorSettingMapper.getTelecomBusinessTotal(nowYear);
			List<DischargeEmissionTrendVo> lastYearBusinessTotal = dischargeMonitorSettingMapper.getTelecomBusinessTotal(preYear);

			Map<Long, BigDecimal> nowYearBTMp = nowYearBusinessTotal.stream().filter(node1 -> node1.getCompanyId().equals(query.getCompanyId()))
					.collect(Collectors.toMap(DischargeEmissionTrendVo::getCompanyId, DischargeEmissionTrendVo::getCarbonIntensity));

			Map<Long, BigDecimal> lastYearBTMp = lastYearBusinessTotal.stream().filter(node1 -> node1.getCompanyId().equals(query.getCompanyId()))
					.collect(Collectors.toMap(DischargeEmissionTrendVo::getCompanyId, DischargeEmissionTrendVo::getCarbonIntensity));

			dataList.forEach(node->{
               node.setNowCarbonTotal( nowYearBTMp.get(query.getCompanyId()) == null? new BigDecimal(0) :node.getNowCarbonTotal()
					   .divide(nowYearBTMp.get(query.getCompanyId()), 4, RoundingMode.HALF_UP));
				node.setNowAllCompanyTotal( nowYearBTMp.get(query.getCompanyId()) == null? new BigDecimal(0) :node.getNowAllCompanyTotal()
						.divide(nowYearBTMp.get(query.getCompanyId()), 4, RoundingMode.HALF_UP));

				node.setPreCarbonTotal( lastYearBTMp.get(query.getCompanyId()) == null? new BigDecimal(0) :node.getPreCarbonTotal()
						.divide(lastYearBTMp.get(query.getCompanyId()), 4, RoundingMode.HALF_UP));
				node.setPreAllCompanyTotal( lastYearBTMp.get(query.getCompanyId()) == null? new BigDecimal(0) :node.getPreAllCompanyTotal()
						.divide(lastYearBTMp.get(query.getCompanyId()), 4, RoundingMode.HALF_UP));

				node.setDownRate(MathUtils.d2dRate(node.getNowCarbonTotal(),node.getPreCarbonTotal()));
				node.setAllCompanyDownRate(MathUtils.d2dRate(node.getNowAllCompanyTotal(),node.getPreAllCompanyTotal()));
			});

		}
		return dataList;
	}

	@Override
	public List<DischargeSettingCompanyVo> getCompanyCarbonList(DischargeMonitorSettingBo bo) {
		DischargeMonitorSetting dischargeMonitorSetting = this.getById(bo.getId());
		if(StrUtil.isBlank(bo.getYear())){
			bo.setYear(String.valueOf(DateUtil.year(new Date())));
		}
		//监测详情应根据当前时间显示
		// （例：目前是2023年2月，则监测详情应显示1月、2月的数据，所有部门都一样，即使监测结果是0也要显示监测详情）
		int month = DateUtil.month(new Date()) + 1;
		List<DischargeSettingCompanyVo> list = getMonthCompanyList(bo);
		BigDecimal monthTotal = new BigDecimal(0);
		if(CollectionUtil.isNotEmpty(list)){
			List<DischargeSettingCompanyVo> dataList = new ArrayList<>();

			for (int i = 0; i < month; i++) {
				DischargeSettingCompanyVo node = null;
				int finalI = i;
				List<DischargeSettingCompanyVo> collect = list.stream().filter(node1 -> {
					return node1.getMonth().replace(CommonConstants.ZERO_STRING, "")
							.equals(String.valueOf(finalI + 1));
				}).collect(Collectors.toList());

				if(CollectionUtil.isNotEmpty(collect)){
					node = collect.get(CommonConstants.ZERO_NUMBER);
					node.setMonthCarbon(node.getMonthCarbon());
					if(node.getMonth().startsWith(CommonConstants.ZERO_STRING)){
						node.setMonth(node.getMonth().replace(CommonConstants.ZERO_STRING,""));
					}
					if(i == 0){
						node.setMonthCarbonTotal(node.getMonthCarbon());
					} else {
						node.setMonthCarbonTotal(node.getMonthCarbon().add(dataList.get(i-1).getMonthCarbonTotal()));
					}
					monthTotal = node.getMonthCarbonTotal();
					node.setMonth(node.getMonth()+"月");
				} else {
					node = new DischargeSettingCompanyVo();
					node.setMonthCarbon(new BigDecimal(CommonConstants.ZERO_NUMBER));
					node.setMonthCarbonTotal(monthTotal);
					node.setUseRate("0.0%");
					node.setMonth(i+1+"月");
				}
				dataList.add(node);
			   }
				// 最后在进行四舍五入，为了避免有偏差
				dataList.forEach(node -> {
					node.setMonthCarbon(NumberFormatUtils.formatValue(node.getMonthCarbon()));
					node.setMonthCarbonTotal(NumberFormatUtils.formatValue(node.getMonthCarbonTotal()));
					// 设置使用率
					node.setUseRate(MathUtils.d2dPercent(node.getMonthCarbonTotal(),new BigDecimal(ObjectUtil.isNull(dischargeMonitorSetting)? "0" :dischargeMonitorSetting.getQuota())));
					// 计算颜色
					BigDecimal useRate = new BigDecimal(node.getUseRate().replace("%",""));
					node.setColorType(setColorType(useRate));
				});
				return dataList;
			} else {
				for (int i = 0; i < month; i++) {
					DischargeSettingCompanyVo node = new DischargeSettingCompanyVo();
					node.setMonthCarbon(new BigDecimal(CommonConstants.ZERO_NUMBER));
					node.setMonthCarbonTotal(new BigDecimal(CommonConstants.ZERO_NUMBER));
					node.setUseRate("0.0%");
					node.setMonth(i+1+"月");
					node.setColorType(1);
					list.add(node);
				}
			}
		return list;

	}



	private BigDecimal getMonthCarbonTotal(List<DischargeSettingCompanyVo> collect) {
		return CollectionUtil.isNotEmpty(collect) ? MathUtils.formatToTon(collect.get(0).getMonthCarbon()) :
				new BigDecimal(CommonConstants.ZERO_NUMBER);
	}

	private CompanyCarbonVo makeUpData(DischargeSettingCompanyVo item, Integer type,String queryType, List<DischargeSettingCompanyVo> companyList){
		CompanyCarbonVo node = new CompanyCarbonVo();
		if(type == 1){
			node.setNowCarbonTotal(MathUtils.formatToTon(item.getMonthCarbon()));
			node.setNowAllCompanyTotal(MathUtils.formatToTon(item.getMonthCarbonTotal()));

			List<DischargeSettingCompanyVo> collect = companyList.stream().filter(item1 ->
					item.getMonth().equals(item1.getMonth())).collect(Collectors.toList());

			node.setPreCarbonTotal(getMonthCarbonTotal(collect));
			node.setPreAllCompanyTotal(getMonthCarbonTotal(collect));
		}  else {
			node.setPreCarbonTotal(MathUtils.formatToTon(item.getMonthCarbon()));
			node.setPreAllCompanyTotal(MathUtils.formatToTon(item.getMonthCarbonTotal()));

			List<DischargeSettingCompanyVo> collect = companyList.stream().filter(item1 ->
					item.getMonth().equals(item1.getMonth())).collect(Collectors.toList());

			node.setNowCarbonTotal(getMonthCarbonTotal(collect));
			node.setNowAllCompanyTotal(getMonthCarbonTotal(collect));
		}
		node.setDownRate(MathUtils.d2dRate(node.getNowCarbonTotal(),node.getPreCarbonTotal()));
		node.setAllCompanyDownRate(MathUtils.d2dRate(node.getNowAllCompanyTotal(),node.getPreAllCompanyTotal()));
		if(item.getMonth().startsWith(CommonConstants.ZERO_STRING)){
			item.setMonth(item.getMonth().replace(CommonConstants.ZERO_STRING,""));
		}
		node.setMonth(item.getMonth()+"月");
		return node;
	}

	private List<DischargeSettingCompanyVo>  getMonthCompanyList(DischargeMonitorSettingBo bo){

		// 获取当前年份各个类型的碳排使用量数据
		// 电
		List<DischargeDataElectricVo> electricVoList = dischargeDataElectricMapper.getCompanyCarbonList(bo);
		// 汽油
		List<DischargeDataOilVo> oilVoList = dischargeDataOilMapper.getCompanyCarbonList(bo);
		// 天然气
		List<DischargeDataGasVo> gasVoList = dischargeDataGasMapper.getCompanyCarbonList(bo);
		// 热量
		List<DischargeDataThermalVo> thermalVoList = dischargeDataThermalMapper.getCompanyCarbonList(bo);
		// 水
		List<DischargeDataWaterVo> waterVoList = dischargeDataWaterMapper.getCompanyCarbonList(bo);
        // 煤炭
		List<DischargeDataCoalVo> coalList = dischargeDataCoalMapper.getCompanyCarbonList(bo);

		DischargeSettingCompanyVo[] totalVos = initArray();

		if (CollectionUtil.isNotEmpty(electricVoList)) {
			electricVoList.forEach(electricVo -> {
				int iIndex = getMonthIndex(electricVo.getDataMonth());
				if (iIndex > 0 ) {
					totalVos[iIndex].setMonth(electricVo.getDataMonth());
					totalVos[iIndex].setMonthCarbon(totalVos[iIndex].getMonthCarbon()
							.add(electricVo.getCarbonEmissions()));
				}
			});
		}

		if (CollectionUtil.isNotEmpty(oilVoList)) {
			oilVoList.forEach(oilVo -> {
				int iIndex = getMonthIndex(oilVo.getDataMonth());
				if (iIndex > 0) {
					totalVos[iIndex].setMonth(oilVo.getDataMonth());
					totalVos[iIndex].setMonthCarbon(totalVos[iIndex].getMonthCarbon()
							.add(oilVo.getCarbonFuel())
							.add(oilVo.getCarbonDiesel())
							.add(oilVo.getCarbonKerosene())
							.add(oilVo.getCarbonGasoline())
					        .add(oilVo.getCarbonCrude()));
				}
			});
		}

		setGasData(gasVoList, totalVos);

		if (CollectionUtil.isNotEmpty(thermalVoList)) {
			thermalVoList.forEach(thermalVo -> {
				int iIndex = getMonthIndex(thermalVo.getDataMonth());
				if (iIndex > 0) {
					totalVos[iIndex].setMonth(thermalVo.getDataMonth());
					totalVos[iIndex].setMonthCarbon(totalVos[iIndex].getMonthCarbon()
							.add(thermalVo.getCarbonThermal()));
				}
			});
		}

		if (CollectionUtil.isNotEmpty(waterVoList)) {
			waterVoList.forEach(waterVo -> {
				int iIndex = Integer.parseInt(waterVo.getDataMonth());
				if (iIndex > 0) {
					totalVos[iIndex].setMonth(waterVo.getDataMonth());
					totalVos[iIndex].setMonthCarbon(totalVos[iIndex].getMonthCarbon().
							add(waterVo.getCarbonWater()));
				}
			});
		}
        // 煤炭的单位为吨
		if (CollectionUtil.isNotEmpty(coalList)) {
			coalList.forEach(coalVo -> {
				int iIndex = Integer.parseInt(coalVo.getDataMonth());
				if (iIndex > 0) {
					totalVos[iIndex].setMonth(coalVo.getDataMonth());
					totalVos[iIndex].setMonthCarbon(totalVos[iIndex].getMonthCarbon().
							add(coalVo.getCarbonCoal().multiply(new BigDecimal(1000))));
				}
			});
		}

		return Arrays.stream(totalVos)
				.filter(vo -> StrUtil.isNotBlank(vo.getMonth()))
				.collect(Collectors.toList());
	}

	private void setGasData(List<DischargeDataGasVo> gasVoList, DischargeSettingCompanyVo[] totalVos) {
		if (CollectionUtil.isNotEmpty(gasVoList)) {
			gasVoList.forEach(gasVo -> {
				int iIndex = getMonthIndex(gasVo.getDataMonth());
				if (iIndex > 0) {
					totalVos[iIndex].setMonth(gasVo.getDataMonth());
					totalVos[iIndex].setMonthCarbon(totalVos[iIndex].getMonthCarbon()
							.add(gasVo.getCarbonLpg())
							.add(gasVo.getCarbonNg()));
				}
			});
		}
	}

	private List<DischargeSettingCompanyVo>  getFossilMonthCompanyList(DischargeMonitorSettingBo bo){
		// 获取当前年份各个类型的碳排使用量数据
		// 汽油
		List<DischargeDataOilVo> oilVoList = dischargeDataOilMapper.getCompanyCarbonList(bo);
		// 天然气
		List<DischargeDataGasVo> gasVoList = dischargeDataGasMapper.getCompanyCarbonList(bo);
		DischargeSettingCompanyVo[] totalVos = initArray();
		if (CollectionUtil.isNotEmpty(oilVoList)) {
			oilVoList.forEach(oilVo -> {
				int iIndex = getMonthIndex(oilVo.getDataMonth());
				if (iIndex > 0) {
					totalVos[iIndex].setMonth(oilVo.getDataMonth());
					totalVos[iIndex].setMonthCarbon(totalVos[iIndex].getMonthCarbon()
							.add(oilVo.getCarbonCrude())
							.add(oilVo.getCarbonDiesel())
							.add(oilVo.getCarbonFuel())
							.add(oilVo.getCarbonGasoline()));
				}
			});
		}
		setGasData(gasVoList, totalVos);
		return Arrays.stream(totalVos)
				.filter(vo -> StrUtil.isNotBlank(vo.getMonth()))
				.collect(Collectors.toList());
	}

	private DischargeSettingCompanyVo[] initArray() {
		DischargeSettingCompanyVo[] totalVos = new DischargeSettingCompanyVo[13];
		for (int i = 0; i < totalVos.length; i++) {
			totalVos[i] = new DischargeSettingCompanyVo();
			totalVos[i].setMonthCarbon(new BigDecimal(CommonConstants.ZERO_NUMBER));
			totalVos[i].setMonthCarbonTotal(new BigDecimal(CommonConstants.ZERO_NUMBER));
		}
		return totalVos;
	}

	@Override
	public DischargeMonitorSettingVo detail(DischargeMonitorSettingQuery query) {
		if (ObjectUtil.allFieldIsNull(query)) {
			throw new BusinessException("查询参数不能为空");
		}
		QueryWrapper<DischargeMonitorSettingQuery> wrapper = this.getWrapper(query);
		return baseMapper.detail(wrapper);
	}

	@Override
	public DischargeMonitorSettingVo getRecordInfo(DischargeMonitorSettingQuery query) {
		DischargeMonitorSettingVo recordInfo = dischargeMonitorSettingMapper.getRecordInfo(query);
		if(ObjectUtil.isNotNull(recordInfo)){
			return recordInfo;
		}
		DischargeMonitorSettingVo dischargeMonitorSettingVo = new DischargeMonitorSettingVo();
		String companyName = ObjectUtil.isNull(query.getCompanyId()) ? "全省" :
				dischargeDataExamineMapper.getCompanyNameById(query.getCompanyId());
		dischargeMonitorSettingVo.setCompanyName(companyName);
		return dischargeMonitorSettingVo;
	}

	private QueryWrapper<DischargeMonitorSettingQuery> getWrapper(DischargeMonitorSettingQuery query) {
		QueryWrapper<DischargeMonitorSettingQuery> wrapper = new QueryWrapper<>();
		wrapper.eq(query.getId() != null, "t.id", query.getId());
		wrapper.orderByDesc("t.create_time");
		wrapper.eq("t.del_flag", DelFlagEnum.NOT_DELETED.getValue());
		return wrapper;
	}

	@Override
	public void add(DischargeMonitorSettingBo bo) {
		DischargeMonitorSetting entity = new DischargeMonitorSetting();
		BeanUtils.copyProperties(bo, entity);
		entity.setYear(String.valueOf(DateUtil.year(new Date())));
		calculateQuota(entity);
		this.saveOrUpdate(entity);
	}

	@Override
	public void edit(DischargeMonitorSettingBo bo) {
		DischargeMonitorSetting entity = new DischargeMonitorSetting();
		BeanUtils.copyProperties(bo, entity);
		calculateQuota(entity);
		baseMapper.updateById(entity);
	}

	@Override
	public void del(Long id) {
		baseMapper.deleteById(id);
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchSetting(List<DischargeMonitorSettingBo> list) {
//		List<DischargeMonitorSetting> addOrUpdateList = new ArrayList<>();
//		list.forEach(node ->{
//			DischargeMonitorSetting dischargeMonitorSetting = new DischargeMonitorSetting();
//			BeanUtils.copyProperties(node,dischargeMonitorSetting);
//			dischargeMonitorSetting.setYear(String.valueOf(DateUtil.year(new Date())));
//			calculateQuota(dischargeMonitorSetting);
//			addOrUpdateList.add(dischargeMonitorSetting);
//		});
//		this.saveOrUpdateBatch(addOrUpdateList);
		list.forEach(node ->{
			DischargeMonitorSetting dischargeMonitorSetting = new DischargeMonitorSetting();
			BeanUtils.copyProperties(node,dischargeMonitorSetting);
			dischargeMonitorSetting.setYear(String.valueOf(DateUtil.year(new Date())));
			calculateQuota(dischargeMonitorSetting);
			saveOrUpdateSetting(dischargeMonitorSetting);
		});
	}

	private int getMonthIndex(String dataMonth) {
		if (StrUtil.isBlank(dataMonth)) {
			return -1;
		}
		if(dataMonth.startsWith(CommonConstants.ZERO_STRING)){
			dataMonth = dataMonth.replace(CommonConstants.ZERO_STRING,"");
		}
		return Integer.parseInt(dataMonth);
	}


	/**
	 * 计算碳排放数据
	 *
	 * @param companyId 部门ID，为0时表示全省
	 * @param year 数据年度
	 * @return 碳排放数据
	 */
	private BigDecimal getAllCarbonEmissions(Long companyId, String year) {
		Date dateStart = DateUtils.dateTime("yyyy-MM-dd HH:mm:ss", year + "-01-01 0:00:00");
		Date dateEnd = DateUtils.dateTime("yyyy-MM-dd HH:mm:ss", year + "-12-31 23:59:59");
		final BigDecimal result = new BigDecimal(0);
		//获取电力数据
		DischargeDataElectricBo queryBo = new DischargeDataElectricBo();
		queryBo.setCompanyId(companyId);
		queryBo.setQueryStartTime(dateStart);
		queryBo.setQueryEndTime(dateEnd);
		List<DischargeDataElectricVo> electricVoList = dischargeDataElectricMapper.countCompanyData(queryBo);

		BigDecimal electricData = new BigDecimal(0);
		if (CollectionUtil.isNotEmpty(electricVoList)) {
			electricData = electricVoList.stream().map(electricVo -> electricVo.getCarbonEmissions())
					.reduce(BigDecimal.ZERO, BigDecimal::add);
		}
		//获取油类数据
		DischargeDataOilBo dischargeDataOilBo = new DischargeDataOilBo();
		dischargeDataOilBo.setCompanyId(companyId);
		dischargeDataOilBo.setQueryStartTime(dateStart);
		dischargeDataOilBo.setQueryEndTime(dateEnd);
		List<DischargeDataOilVo> dischargeDataOilVoList = dischargeDataOilMapper.countCompanyData(dischargeDataOilBo);
		BigDecimal oilData = new BigDecimal(0);
		if (CollectionUtil.isNotEmpty(dischargeDataOilVoList)) {
			oilData = dischargeDataOilVoList.stream().map(oilVo -> oilVo.getCarbonGasoline().add(oilVo.getCarbonDiesel())
					.add(oilVo.getCarbonCrude()).add(oilVo.getCarbonFuel()).add(oilVo.getCarbonKerosene()))
					.reduce(BigDecimal.ZERO, BigDecimal::add);
		}
		//获取汽类数据
		DischargeDataGasBo dischargeDataGasBo = new DischargeDataGasBo();
		dischargeDataGasBo.setCompanyId(companyId);
		dischargeDataGasBo.setQueryStartTime(dateStart);
		dischargeDataGasBo.setQueryEndTime(dateEnd);
		List<DischargeDataGasVo> dischargeDataGasVoList = dischargeDataGasMapper.countCompanyData(dischargeDataGasBo);
		BigDecimal gasData = new BigDecimal(0);
		if (CollectionUtil.isNotEmpty(dischargeDataGasVoList)) {
			gasData = dischargeDataGasVoList.stream().map(gasVo -> gasVo.getCarbonNg().add(gasVo.getCarbonLpg()))
					.reduce(BigDecimal.ZERO, BigDecimal::add);
		}
		//获取热力数据
		DischargeDataThermalBo dischargeDataThermalBo = new DischargeDataThermalBo();
		dischargeDataThermalBo.setCompanyId(companyId);
		dischargeDataThermalBo.setQueryStartTime(dateStart);
		dischargeDataThermalBo.setQueryEndTime(dateEnd);
		List<DischargeDataThermalVo> dischargeDataThermalVoList = dischargeDataThermalMapper.countCompanyData(dischargeDataThermalBo);
		BigDecimal thermalData = new BigDecimal(0);
		if (CollectionUtil.isNotEmpty(dischargeDataThermalVoList)) {
			thermalData = dischargeDataThermalVoList.stream().map(thermalVo -> thermalVo.getCarbonEmissions())
					.reduce(BigDecimal.ZERO, BigDecimal::add);
		}
		//获取水量数据
		DischargeDataWaterBo dischargeDataWaterBo = new DischargeDataWaterBo();
		dischargeDataWaterBo.setCompanyId(companyId);
		dischargeDataWaterBo.setQueryStartTime(dateStart);
		dischargeDataWaterBo.setQueryEndTime(dateEnd);
		List<DischargeDataWaterVo> dischargeDataWaterVoList = dischargeDataWaterMapper.countCompanyData(dischargeDataWaterBo);
		BigDecimal waterData = new BigDecimal(0);
		if (CollectionUtil.isNotEmpty(dischargeDataWaterVoList)) {
			waterData = dischargeDataWaterVoList.stream().map(waterVo -> waterVo.getCarbonEmissions())
					.reduce(BigDecimal.ZERO, BigDecimal::add);
		}
		//获取煤碳数据
		DischargeDataCoalBo dischargeDataCoalBo = new DischargeDataCoalBo();
		dischargeDataCoalBo.setCompanyId(companyId);
		dischargeDataCoalBo.setQueryStartTime(dateStart);
		dischargeDataCoalBo.setQueryEndTime(dateEnd);
		List<DischargeDataCoalVo> dischargeDataCoalVoList = dischargeDataCoalMapper.countCompanyData(dischargeDataCoalBo);
		BigDecimal coalData = new BigDecimal(0);
		if (CollectionUtil.isNotEmpty(dischargeDataCoalVoList)) {
			coalData = dischargeDataCoalVoList.stream().map(coalVo -> coalVo.getCarbonEmissions())
					.reduce(BigDecimal.ZERO, BigDecimal::add);
		}
		return result.add(electricData).add(oilData).add(gasData).add(thermalData).add(waterData).add(coalData)
				.setScale(2, BigDecimal.ROUND_HALF_UP);
	}

	/**
	 * 计算碳排放监测值
	 *
	 * @param entity 监测数据
	 */
	private void calculateQuota(DischargeMonitorSetting entity) {
		String lastYear = String.valueOf(Integer.parseInt(entity.getYear()) - 1);
		//1-去年全省碳排放量均值；2-去年碳排放总量%；3-自定义
		if ("1".equals(entity.getQuotaSelection())) {
			entity.setSelectionValue("");
			int companyCount = baseMapper.getCompanyCount();
			if (companyCount > 0) {
				BigDecimal carbonEmissions = getAllCarbonEmissions(0L, lastYear);
				entity.setQuota(carbonEmissions.divide(BigDecimal.valueOf(companyCount), 0, BigDecimal.ROUND_HALF_UP).toString());
			} else {
				entity.setQuota("0");
			}
		} else if ("2".equals(entity.getQuotaSelection())) {
			BigDecimal carbonEmissions = getAllCarbonEmissions(entity.getCompanyId(), lastYear);
			entity.setQuota(carbonEmissions.multiply(new BigDecimal(entity.getSelectionValue()))
					.divide(BigDecimal.valueOf(100), 0, BigDecimal.ROUND_HALF_UP).toString());
		} else {
			entity.setSelectionValue("");
		}
	}

	private boolean saveOrUpdateSetting(DischargeMonitorSetting entity) {
		long count = this.count(Wrappers.<DischargeMonitorSetting>lambdaQuery()
				.eq(DischargeMonitorSetting::getCompanyId, entity.getCompanyId())
				.eq(DischargeMonitorSetting::getYear, entity.getYear())
				.eq(DischargeMonitorSetting::getDelFlag, DelFlagEnum.NOT_DELETED.getValue()));
		if (count > 0) {
			return this.update(new LambdaUpdateWrapper<DischargeMonitorSetting>()
					.set(DischargeMonitorSetting::getQuota, entity.getQuota())
					.set(DischargeMonitorSetting::getQuotaSelection, entity.getQuotaSelection())
					.set(DischargeMonitorSetting::getSelectionValue, entity.getSelectionValue())
					.eq(DischargeMonitorSetting::getCompanyId, entity.getCompanyId())
					.eq(DischargeMonitorSetting::getYear, entity.getYear()));
		} else {
			return this.save(entity);
		}
	}
}
