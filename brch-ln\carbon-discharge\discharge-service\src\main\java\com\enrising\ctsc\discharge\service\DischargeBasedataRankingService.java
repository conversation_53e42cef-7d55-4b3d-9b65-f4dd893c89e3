package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.discharge.api.bo.DischargeBasedataRankingBo;
import com.enrising.ctsc.discharge.api.entity.DischargeBasedataRanking;
import com.enrising.ctsc.discharge.api.query.DischargeBasedataRankingQuery;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.vo.DischargeBasedataRankingVo;

/**
 * 集团排名情况
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-10-13
 */
public interface DischargeBasedataRankingService extends IService<DischargeBasedataRanking> {

	/**
	 * 分页查询
	 *
	 * @param page  分页
	 * @param query 参数
	 * @return 列表
	 */
	TableDataInfo<DischargeBasedataRankingVo> findList(Page<DischargeBasedataRankingVo> page, DischargeBasedataRankingQuery query);

	/**
	 * 详情
	 *
	 * @param query 参数
	 * @return 详情
	 */
	DischargeBasedataRankingVo detail(DischargeBasedataRankingQuery query);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	void add(DischargeBasedataRankingBo bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	void edit(DischargeBasedataRankingBo bo);


	/**
	 * 设置展示状态
	 * @param bo 参数
	 */
	void setDisplayState(DischargeBasedataRankingBo bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	void del(Long id);

	/**
	 * 获取展示的对象
	 */
	DischargeBasedataRanking getShowObj();
}
