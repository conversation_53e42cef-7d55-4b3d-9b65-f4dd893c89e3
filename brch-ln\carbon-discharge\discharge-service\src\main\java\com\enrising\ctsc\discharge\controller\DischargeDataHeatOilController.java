package com.enrising.ctsc.discharge.controller;

import cn.hutool.core.util.StrUtil;
import com.enrising.ctsc.discharge.api.bo.DischargeDataHeatOilBo;
import com.enrising.ctsc.discharge.api.bo.DischargeDataHeatOilExcel;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.discharge.api.vo.DischargeDataHeatOilVo;
import com.enrising.ctsc.discharge.service.DischargeDataHeatOilService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 热力燃油数据接口
 *
 ** @<NAME_EMAIL>
 *  * @since 1.0.0 2024-12-23
 *  */
@RestController
@RequestMapping("/discharge/heatoil")
@AllArgsConstructor
public class DischargeDataHeatOilController {
	private final DischargeDataHeatOilService dischargeDataHeatOilService;

	@PostMapping("/getDataList")
		public R<List<DischargeDataHeatOilVo>> getDataList(@RequestBody DischargeDataHeatOilBo bo) {
		return R.success(dischargeDataHeatOilService.getDataList(bo));
	}


	@GetMapping("/detail")
		public R<DischargeDataHeatOilVo> get(Long id) {
		DischargeDataHeatOilVo detail = dischargeDataHeatOilService.detail(id);
		return R.success(detail, "查询成功");
	}

	@PostMapping(value = "/save")
	public R<String> save(@RequestBody DischargeDataHeatOilBo bo) {
		String sRet = dischargeDataHeatOilService.add(bo);
		if (StrUtil.isBlank(sRet)){
			return R.success("保存成功");
		}
		return R.failed(sRet);
	}

	@PostMapping(value = "/saveBatch")
	public R<String> saveBatch(@RequestBody List<DischargeDataHeatOilBo> boList) {
		String sRet = dischargeDataHeatOilService.addBatch(boList);
		return R.success(sRet);
	}

	@PostMapping(value = "/update")
	public R<String> update(@RequestBody DischargeDataHeatOilBo bo) {
		dischargeDataHeatOilService.edit(bo);
		return R.success("修改成功");
	}

	@PostMapping(value = "/delete/{id}")
	public R<String> delete(@PathVariable Long id) {
		dischargeDataHeatOilService.del(id);
		return R.success("删除成功");
	}

	@PostMapping("/importExcel")
	public List<DischargeDataHeatOilExcel> importExcel(@RequestPart("file") MultipartFile file) throws Exception{
		List<DischargeDataHeatOilExcel> list = dischargeDataHeatOilService.importExcel(file);
		return list;
	}

	@GetMapping("/downloadTemplate")
	public void downloadTemplate(HttpServletRequest request, HttpServletResponse response) {
		dischargeDataHeatOilService.downloadTemplate(request,response);
	}
}
