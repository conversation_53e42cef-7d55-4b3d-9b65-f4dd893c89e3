package com.enrising.ctsc.assess.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.assess.api.bo.AssessReportBo;
import com.enrising.ctsc.assess.api.query.AssessReportQuery;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.assess.api.vo.AssessReportVo;
import com.enrising.ctsc.assess.api.vo.AssessVisualVo;
import com.enrising.ctsc.assess.service.AssessReportService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 考核报告
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@RestController
@RequestMapping("/assess/assessReport")
@AllArgsConstructor
public class AssessReportController {
	private final AssessReportService assessReportService;

	@GetMapping("/list")
	public TableDataInfo<AssessReportVo> page(Page<AssessReportVo> page, AssessReportQuery query) {
		return assessReportService.findList(page, query);
	}

	@GetMapping("/detail")
	public R<AssessReportVo> get(AssessReportQuery query) {
		AssessReportVo detail = assessReportService.detail(query);
		return R.success(detail, "查询成功");
	}

	@PostMapping(value = "/save")
	public R<String> save(@RequestBody AssessReportBo bo) {
		assessReportService.add(bo);
		return R.success("保存成功");
	}

	@PostMapping(value = "/update")
	public R<String> update(@RequestBody AssessReportBo bo) {
		assessReportService.edit(bo);
		return R.success("修改成功");
	}

	@PostMapping(value = "/delete/{id}")
	public R<String> delete(@PathVariable Long id) {
		assessReportService.del(id);
		return R.success("删除成功");
	}

	@PostMapping(value = "/download")
	public void download(HttpServletResponse response, @RequestBody AssessReportBo bo) {
		assessReportService.download(response,bo);
	}

	@PostMapping("/preview")
	public R preview(HttpServletResponse response, @RequestBody AssessReportBo bo) {
		return R.success(assessReportService.preview(response,bo));
	}

	@PostMapping("/visual")
	public R<AssessVisualVo> visual(@RequestBody AssessReportBo bo) {
		return R.success(assessReportService.visual(bo));
	}
}
