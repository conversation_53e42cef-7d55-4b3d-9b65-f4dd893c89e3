package com.enrising.ctsc.discharge.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.discharge.api.bo.DischargeDataThermalBo;
import com.enrising.ctsc.discharge.api.vo.DischargeDataThermalVo;
import com.enrising.ctsc.discharge.service.DischargeDataThermalService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 碳排放数据填报表（热）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@RestController
@RequestMapping("/discharge/thermal")
@AllArgsConstructor
public class DischargeDataThermalController {
	private final DischargeDataThermalService dischargeDataThermalService;

	@PostMapping("/getThermalListPage")
		public R<Page<DischargeDataThermalVo>> getThermalListPage(@RequestBody QueryPage<DischargeDataThermalBo> queryPage) {
		return R.success(dischargeDataThermalService.getThermalListPage(queryPage));
	}

	@PostMapping("/getThermalListToExcel")
		public R<List<DischargeDataThermalVo>> getThermalListToExcel(@RequestBody DischargeDataThermalBo dischargeDataThermalBo) {
		return R.success(dischargeDataThermalService.getThermalListToExcel(dischargeDataThermalBo));
	}

	@GetMapping("/getDataList")
		public R<List<DischargeDataThermalVo>> getDataList(Integer dataYear, Long companyId) {
		return R.success(dischargeDataThermalService.getDataList(dataYear, companyId));
	}

	@GetMapping("/detail")
		public R<DischargeDataThermalVo> get(Long id) {
		DischargeDataThermalVo detail = dischargeDataThermalService.detail(id);
		return R.success(detail, "查询成功");
	}

		@PostMapping(value = "/save")
		public R<String> save(@RequestBody DischargeDataThermalBo bo) {
		String sRet = dischargeDataThermalService.add(bo);
		if (StrUtil.isBlank(sRet)){
			return R.success("保存成功");
		}
		return R.failed(sRet);
	}

		@PostMapping(value = "/update")
		public R<String> update(@RequestBody DischargeDataThermalBo bo) {
		dischargeDataThermalService.edit(bo);
		return R.success("修改成功");
	}

		@PostMapping(value = "/delete/{id}")
		public R<String> delete(@PathVariable Long id) {
		dischargeDataThermalService.del(id);
		return R.success("删除成功");
	}
}
