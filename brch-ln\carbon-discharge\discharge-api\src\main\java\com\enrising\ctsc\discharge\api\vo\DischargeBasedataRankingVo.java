package com.enrising.ctsc.discharge.api.vo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 集团排名情况
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-10-13
 */
@Data
public class DischargeBasedataRankingVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
	private Long id;

	/**
	 * 创建者id
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date createTime;

	/**
	 * 更新者id
	 */
	private Long updateBy;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date updateTime;

	/**
	 * 时间周期-年
	 */
	private String timePeriodYear;

	/**
	 * 季度
	 */
	private String timePeriodQuarter;

	/**
	 * 碳排放量同比增幅
	 */
	private Double carbonQuantityAmplify;

	/**
	 * 排名
	 */
	private String carbonQuantityAmplifyRanking;

	/**
	 * 碳排放强度同比降幅
	 */
	private Double carbonStrengthReduction;

	/**
	 * 排名
	 */
	private String carbonStrengthReductionRanking;

	/**
	 * 电信业务总量同比增幅
	 */
	private Double carbonBusinessGrowth;

	/**
	 * 排名
	 */
	private String carbonBusinessGrowthRanking;

	/**
	 * 展示状态
	 */
	private String displayState;


}
