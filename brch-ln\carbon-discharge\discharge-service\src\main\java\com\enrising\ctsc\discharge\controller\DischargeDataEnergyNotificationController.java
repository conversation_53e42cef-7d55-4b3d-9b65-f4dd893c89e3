package com.enrising.ctsc.discharge.controller;

import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.discharge.api.bo.DischargeDataEnergyNotificationBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergyNotification;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyNotificationVo;
import com.enrising.ctsc.discharge.service.DischargeDataEnergyNotificationService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 碳排放数据填报（能源）提醒接口
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-12
 */
@RestController
@RequestMapping("/discharge/notification")
@AllArgsConstructor
public class DischargeDataEnergyNotificationController {
	private final DischargeDataEnergyNotificationService dischargeDataEnergyNotificationService;

	@PostMapping("/getRemindCount")
	public R<List<DischargeDataEnergyNotificationVo>> getRemindCount(@RequestBody DischargeDataEnergyNotificationBo dischargeDataEnergyNotificationBo) {
		return R.success(dischargeDataEnergyNotificationService.getRemindCount(dischargeDataEnergyNotificationBo));
	}

	@PostMapping("/getCompanyRemind")
	public R<DischargeDataEnergyNotification> getCompanyRemind(@RequestBody DischargeDataEnergyNotificationBo dischargeDataEnergyNotificationBo) {
		return R.success(dischargeDataEnergyNotificationService.getCompanyRemind(dischargeDataEnergyNotificationBo));
	}

		@PostMapping(value = "/save")
	public R<String> save(@RequestBody DischargeDataEnergyNotification dischargeDataEnergyNotification) {
		boolean isSuc = dischargeDataEnergyNotificationService.save(dischargeDataEnergyNotification);
		if (isSuc) {
			return R.success("保存成功");
		}
		return R.failed("保存失败");
	}

		@PostMapping(value = "/saveList")
	public R<String> saveList(@RequestBody List<DischargeDataEnergyNotificationVo> dischargeDataEnergyNotificationVoList) {
		Date remindTime = new Date();
		List<DischargeDataEnergyNotification> dischargeDataEnergyNotificationList = new ArrayList<>();
		dischargeDataEnergyNotificationVoList.forEach(dischargeDataEnergyNotificationVo -> {
			DischargeDataEnergyNotification dischargeDataEnergyNotification = new DischargeDataEnergyNotification();
			dischargeDataEnergyNotification.setRemindTime(remindTime);
			dischargeDataEnergyNotification.setCompanyId(dischargeDataEnergyNotificationVo.getCompanyId());
			dischargeDataEnergyNotification.setReportTime(dischargeDataEnergyNotificationVo.getReportTime());
			dischargeDataEnergyNotificationList.add(dischargeDataEnergyNotification);
		});
		boolean iSuc = dischargeDataEnergyNotificationService.saveBatch(dischargeDataEnergyNotificationList);
		if (iSuc) {
			return R.success("保存成功");
		} else {
			return R.success("保存失败");
		}
	}

		@PostMapping(value = "/clearCompanyRemind")
	public R<Boolean> clearCompanyRemind(@RequestBody DischargeDataEnergyNotificationBo dischargeDataEnergyNotificationBo) {
		return R.success(dischargeDataEnergyNotificationService.clearCompanyRemind(dischargeDataEnergyNotificationBo));
	}
}
