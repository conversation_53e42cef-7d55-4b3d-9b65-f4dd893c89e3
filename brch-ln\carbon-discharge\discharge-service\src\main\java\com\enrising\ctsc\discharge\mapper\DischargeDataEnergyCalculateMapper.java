package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergyCalculate;
import com.enrising.ctsc.discharge.api.query.DischargeDataEnergyQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 按规则计算能源数据表
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Mapper
public interface DischargeDataEnergyCalculateMapper extends BaseMapper<DischargeDataEnergyCalculate> {

    List<DischargeDataEnergyVo> getAllDataList(@Param("companyId") Long companyId, @Param("reportTime") Date reportTime);

    int removeCalculateData(@Param("companyId") Long companyId,
                            @Param("reportTime") Date reportTime,
                            @Param("indicatorCodeList") List<String> indicatorCodeList);
    BigDecimal getEnergyCoefficient(@Param("energyId") Long energyId,
                                    @Param("reportTime") Date reportTime);

    List<DischargeDataEnergyVo> getCalcReportList(@Param("query") DischargeDataEnergyQuery query);
}