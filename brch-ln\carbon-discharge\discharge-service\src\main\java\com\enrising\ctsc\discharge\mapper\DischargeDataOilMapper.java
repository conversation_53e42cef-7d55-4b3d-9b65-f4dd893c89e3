package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.bo.DischargeDataElectricBo;
import com.enrising.ctsc.discharge.api.bo.DischargeDataOilBo;
import com.enrising.ctsc.discharge.api.bo.DischargeMonitorSettingBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataOil;
import com.enrising.ctsc.discharge.api.query.DischargeDataOilQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeDataOilVo;
import com.enrising.ctsc.discharge.api.vo.DischargeDataTotalVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 碳排放数据填报表（油）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Mapper
public interface DischargeDataOilMapper extends BaseMapper<DischargeDataOil> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
	IPage<DischargeDataOilVo> findList(Page<DischargeDataOilVo> page, @Param(Constants.WRAPPER) Wrapper<DischargeDataOilQuery> wrapper);

	/**
	 * 列表查询
	 *
	 * @param wrapper 条件
	 * @return 列表
	 */
	List<DischargeDataOilVo> findList(@Param(Constants.WRAPPER) Wrapper<DischargeDataOilQuery> wrapper);

	/**
	 * 详情查询
	 *
	 * @param wrapper 条件
	 * @return 结果
	 */
	DischargeDataOilVo detail(@Param(Constants.WRAPPER) Wrapper<DischargeDataOilQuery> wrapper);


	List<DischargeDataOilVo> getCompanyCarbonList(@Param("bo") DischargeMonitorSettingBo bo);

	/**
	 * 查询统计数据列表
	 *
	 * @param queryBo 查询条件
	 * @return 结果
	 */
	List<DischargeDataOilVo> countCompanyData(@Param("queryBo") DischargeDataOilBo queryBo);

	/**
	 * 查询公司数据列表
	 *
	 * @param queryBo 查询条件
	 * @return 结果
	 */
	List<DischargeDataOilVo> getCompanyDataList(@Param("queryBo") DischargeDataOilBo queryBo);

	/**
	 * 查询统计月数据列表
	 *
	 * @param queryBo 查询条件
	 * @return 统计月数据列表
	 */
	List<DischargeDataTotalVo> countCarbonCompare(@Param("queryBo") DischargeDataElectricBo queryBo);
}