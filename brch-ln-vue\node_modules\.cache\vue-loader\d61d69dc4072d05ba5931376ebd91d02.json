{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basic\\login\\login.vue?vue&type=style&index=0&id=72e0c311&lang=less&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basic\\login\\login.vue", "mtime": 1753853977878}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749178874080}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICJsb2dpbi5sZXNzIjsNCkBpbXBvcnQgImxvZ2luLmNzcyI7DQo="}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";AACA;AACA", "file": "login.vue", "sourceRoot": "src/view/basic/login", "sourcesContent": ["<style lang=\"less\">\r\n@import \"login.less\";\r\n@import \"login.css\";\r\n</style>\r\n<template>\r\n  <div class=\"login satic-area\">\r\n    <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n    <!--<div class=\"dynamic-area1\"></div>-->\r\n    <!--<div class=\"dynamic-area2\"></div>-->\r\n    <img src=\"../../../assets/images/login_logo_new.png\" class=\"login_logo\" />\r\n    <img src=\"../../../assets/images/login_bottom_bg.png\" class=\"login_bottom_bg\" />\r\n    <img src=\"../../../assets/images/login_bottom_img1.png\" class=\"login_bottom_img1\" />\r\n    <img src=\"../../../assets/images/login_bottom_img2.png\" class=\"login_bottom_img2\" />\r\n    <p class=\"login_copyright\">中通服创立信息科技有限责任公司</p>\r\n    <div v-if=\"showLogin\" class=\"login-con\" id=\"login-con\" :style=\"loginStyle\">\r\n      <!--<img src=\"../../../assets/images/login_fly.png\" class=\"login_fly\"/>\r\n            <img src=\"../../../assets/images/login_fly_light.png\" class=\"login_fly_light\"/>-->\r\n      <!--<Card dis-hover class=\"login-card\" icon=\"log-in\" title=\"欢迎登录\" :bordered=\"false\">-->\r\n      <div class=\"form-con\">\r\n        <login-form ref=\"loginForm\" @on-success-valid=\"handleSubmit\"></login-form>\r\n        <!--<p class=\"login-tip\">中通服创立信息科技有限责任公司</p>-->\r\n      </div>\r\n      <!-- </Card> -->\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport config from \"@/config/index\";\r\nimport LoginForm from \"_c/login-form\";\r\nimport { mapActions, mapMutations } from \"vuex\";\r\n\r\nexport default {\r\n  components: {\r\n    LoginForm,\r\n  },\r\n  mounted() {\r\n\r\n    this.login_sso();\r\n    if (\"development\" == process.env.NODE_ENV || \"dev\" === config.deploy_env)\r\n      this.showLogin = true;\r\n\r\n    this.reStyle();\r\n    window.addEventListener(\"resize\", () => {\r\n      this.reStyle();\r\n    });\r\n  },\r\n  data() {\r\n    return {\r\n      loginStyle: \"\",\r\n      loading: false,\r\n      showLogin: false,\r\n    };\r\n  },\r\n  methods: {\r\n    ...mapMutations([\"setTagNavList\", \"addTag\"]),\r\n    ...mapActions([\"handleLogin\", \"getUserInfo\", \"getPublicKey\", \"dictInit\"]),\r\n    handleSubmit({ userName, password }, button) {\r\n      this.getPublicKey(userName).then((res) => {\r\n        localStorage.setItem(\"exponent\", res.data.exponent);\r\n        localStorage.setItem(\"modulus\", res.data.modulus);\r\n        const pwdKey = new RSAUtils.getKeyPair(res.data.exponent, \"\", res.data.modulus);\r\n        const reversedPwd = password.split(\"\").reverse().join(\"\");\r\n        const encrypedPwd = RSAUtils.encryptedString(pwdKey, reversedPwd);\r\n\r\n        let page = this.$route.query.page;\r\n\r\n        this.handleLogin({\r\n          userName: userName,\r\n          encrypedPwd: encrypedPwd,\r\n          loginType: \"0\",\r\n        }).then((res) => {\r\n          if (res.code == 0) {\r\n            this.dictInit();\r\n            this.getUserInfo().then((res) => {\r\n              if (page) {\r\n                this.$router.push({\r\n                  name: this.$config.homeName,\r\n                  params: {\r\n                    page: \"wfProcInst\",\r\n                    fromLogin: true,\r\n                  },\r\n                });\r\n              } else {\r\n                this.$router.push({\r\n                  name: this.$config.homeName,\r\n                  params: {\r\n                    fromLogin: true,\r\n                  },\r\n                });\r\n              }\r\n\r\n              this.$nextTick(() => {\r\n                this.setTagNavList([]);\r\n                this.addTag({\r\n                  route: this.$store.state.app.homeRoute,\r\n                });\r\n              });\r\n            });\r\n          } else {\r\n            this.$refs.loginForm.buttonLoad(false);\r\n          }\r\n        });\r\n      });\r\n    },\r\n    reStyle() {\r\n      this.$nextTick(() => {\r\n        var window_width = document.documentElement.clientWidth;\r\n        var window_height = document.documentElement.clientHeight;\r\n        var login_content_left = (window_width - $(\".login-con\").width()) / 2;\r\n        var login_content_top = (window_height - $(\".login-con\").height()) / 2;\r\n\r\n        this.loginStyle = \"left:\" + login_content_left + \"px\";\r\n      });\r\n    },\r\n    login_sso() {\r\n      // 辽宁 单点登录\r\n      let code = this.$route.query.code;\r\n      let userName = this.$route.query.UserName;\r\n      let appKey = this.$route.query.AppKey;\r\n      let page = this.$route.query.page;\r\n      if (code != null && code != \"\") {\r\n        console.log(\"省认证中心统一认证登录\")\r\n        //统一认证单点登录\r\n        this.showLogin = false;\r\n        this.loading = true;\r\n        this.handleLogin({ userName: code, encrypedPwd: code, loginType: \"ln\" }).then(\r\n          async (res) => {\r\n            this.loading = false;\r\n            if (res.code == 0) {\r\n              this.login_success(null);\r\n            } else {\r\n              this.$refs.loginForm.buttonLoad(false);\r\n            }\r\n          }\r\n        );\r\n      } else if (userName != null && userName != \"\" && appKey != null && appKey != \"\") {\r\n        console.log(\"OA单点登录\")\r\n        //原有OA单点登录\r\n        this.showLogin = false;\r\n        this.loading = true;\r\n        this.getPublicKey(userName).then((res) => {\r\n          const pwdKey = new RSAUtils.getKeyPair(res.data.exponent, \"\", res.data.modulus);\r\n          const reversedPwd = appKey.split(\"\").reverse().join(\"\");\r\n          const encrypedPwd = RSAUtils.encryptedString(pwdKey, reversedPwd);\r\n\r\n          this.handleLogin({\r\n            userName: userName,\r\n            encrypedPwd: encrypedPwd,\r\n            loginType: \"1\",\r\n          }).then(async (res) => {\r\n            this.loading = false;\r\n            if (res.code == 0) {\r\n              this.login_success(page);\r\n            } else {\r\n              this.$refs.loginForm.buttonLoad(false);\r\n            }\r\n          });\r\n        });\r\n      } else {\r\n        this.showLogin = true;\r\n        // if (\"production\" == process.env.NODE_ENV && \"dev\" !== config.deploy_env)\r\n        //   window.location.href =\r\n        //     \"http://eam.sc.ctc.com:8002/eam-apps/oauth/authorize?client_id=CTSCNHXT20210819&response_type=code&redirect_uri=http://172.16.47.127:80/login\";\r\n        //alert(\"pc href:\"+window.location.href);\r\n        /*if(\"production\"==process.env.NODE_ENV&&\"dev\"!==config.deploy_env)\r\n                        window.location.href=\"http://uamportal.paas.sc.ctc.com:22002？redirect_uri=http://172.16.47.127:80/login\"\r\n*/\r\n      }\r\n    },\r\n    login_success(page) {\r\n      this.dictInit();\r\n      this.getUserInfo().then((res) => {\r\n        if (page) {\r\n          this.$router.push({\r\n            name: this.$config.homeName,\r\n            params: {\r\n              page: page,\r\n              fromLogin: true,\r\n            },\r\n          });\r\n        } else {\r\n          this.$router.push({\r\n            name: this.$config.homeName,\r\n            params: {\r\n              fromLogin: true,\r\n            },\r\n          });\r\n        }\r\n        this.$nextTick(() => {\r\n          this.setTagNavList([]);\r\n          this.addTag({\r\n            route: this.$store.state.app.homeRoute,\r\n          });\r\n        });\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style></style>\r\n"]}]}