package com.enrising.ctsc.discharge.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.bo.DischargeReportBo;
import com.enrising.ctsc.discharge.api.query.DischargeReportQuery;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.vo.DischargeReportVo;
import com.enrising.ctsc.discharge.service.DischargeReportService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 碳排放报告
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@RestController
@RequestMapping("/discharge/report")
@AllArgsConstructor
public class DischargeReportController {
	private final DischargeReportService dischargeReportService;

	@GetMapping("/list")

	public TableDataInfo<DischargeReportVo> page(Page<DischargeReportVo> page, DischargeReportQuery query) {
		return dischargeReportService.findList(page, query);
	}

	@GetMapping("/detail")
	public R<DischargeReportVo> get(DischargeReportQuery query) {
		DischargeReportVo detail = dischargeReportService.detail(query);
		return R.success(detail, "查询成功");
	}

    @PostMapping(value = "/save")
    public R<String> save(@RequestBody DischargeReportBo bo) {
		dischargeReportService.add(bo);
		return R.success("保存成功");
	}

    @PostMapping(value = "/update")
    public R<String> update(@RequestBody DischargeReportBo bo) {
		dischargeReportService.edit(bo);
		return R.success("修改成功");
	}

    @PostMapping(value = "/delete/{id}")
    public R<String> delete(@PathVariable Long id) {
		dischargeReportService.del(id);
		return R.success("删除成功");
	}

    @PostMapping(value = "/checkExist")
	public R checkExist(@RequestBody DischargeReportBo bo) {
		return R.success(dischargeReportService.checkExist(bo));
	}

	@PostMapping("/preview")
    public R preview(HttpServletResponse response, @RequestBody DischargeReportBo bo) {
		return R.success(dischargeReportService.preview(response,bo));
	}

    @PostMapping("/download")
    public void download(HttpServletResponse response, @RequestBody DischargeReportBo bo) {
		dischargeReportService.download(response,bo);
	}
}
