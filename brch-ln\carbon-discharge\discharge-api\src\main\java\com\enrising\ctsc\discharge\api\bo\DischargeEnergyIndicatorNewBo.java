package com.enrising.ctsc.discharge.api.bo;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 碳排放能源指标表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeEnergyIndicatorNewBo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
		private Long id;

	/**
	 * 能源指标类型编号
	 */
	@Length(min = 1, max = 15, message = "类型最多输入15字符")
	private String indicatorCode;

	/**
	 * 能源指标类型名称
	 */
	@Length(min = 1, max = 100, message = "类型最多输入100字符")
		private String indicatorName;

	/**
	 * 能源类型id
	 */
	private Long energyTypeId;

	/**
	 * 集团数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String groupInputType;

	/**
	 * 股份数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String stockInputType;

	/**
	 * 大型数据中心数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String largeInputType;

	/**
	 * 中小型数据中心数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String mediumInputType;

	/**
	 * 移动业务数据输入类型，0-必填，1-非必填，2-无需填写
	 */
	private String mobileInputType;

	/**
	 * 状态，1-启用，2-禁用
	 */
	@NotBlank(message = "状态不能为空")
		private String status;

	/**
	 * 父指标id
	 */
		private Long parentId;

	/**
	 * 排序值
	 */
		private Integer sort;
}
