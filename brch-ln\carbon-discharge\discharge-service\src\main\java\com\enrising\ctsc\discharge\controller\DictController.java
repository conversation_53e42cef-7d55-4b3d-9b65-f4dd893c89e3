/*
 * Copyright (c) 2021 ctsc-cloudx Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.enrising.ctsc.discharge.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.dto.SysDictDTO;
import com.enrising.ctsc.discharge.api.entity.SysDict;
import com.enrising.ctsc.discharge.api.entity.SysDictItem;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.discharge.api.vo.DictTypeVO;
import com.enrising.ctsc.discharge.service.SysDictItemService;
import com.enrising.ctsc.discharge.service.SysDictService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 字典表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-19
 */
@RestController
@RequestMapping("/dict")
@AllArgsConstructor
public class DictController {

	private final SysDictItemService sysDictItemService;

	private final SysDictService sysDictService;

	/**
	 * 通过ID查询字典信息
	 *
	 * @param id ID
	 * @return 字典信息
	 */
	@GetMapping("/{id}")
	public R getById(@PathVariable Integer id) {
		return R.success(sysDictService.getById(id));
	}

	/**
	 * 分页查询字典信息
	 *
	 * @param page 分页对象
	 * @return 分页对象
	 */
	@GetMapping("/page")
	public R<IPage<SysDict>> getDictPage(Page<SysDict> page, SysDictDTO dto) {
		return R.success(sysDictService.page(page, Wrappers.<SysDict>lambdaQuery()
				.and(StrUtil.isNotBlank(dto.getKeys()), w -> w.like(StrUtil.isNotBlank(dto.getKeys()), SysDict::getDescription, dto.getKeys())
						.or().like(StrUtil.isNotBlank(dto.getKeys()), SysDict::getRemarks, dto.getKeys())
				)
				.eq(StrUtil.isNotBlank(dto.getType()), SysDict::getType, dto.getType())
				.eq(StrUtil.isNotBlank(dto.getSystem()), SysDict::getSystem, dto.getSystem())
		));
	}

	/**
	 * 通过字典类型查找字典
	 *
	 * @param type 类型
	 * @return 同类型字典
	 */
	@GetMapping("/type/{type}")
	public R<List<SysDictItem>> getDictByType(@PathVariable("type") String type) {
		List<SysDictItem> items = sysDictItemService.listItems(type);
		return R.success(items);
	}

	@PostMapping("/types")
	public R<Map<String, List<SysDictItem>>> getDictByType(@RequestBody DictTypeVO type) {
		Map<String, List<SysDictItem>> map = this.sysDictItemService.listDictItemMap(type.getTypes());
		return R.success(map);
	}

	@PostMapping("/type/all")
	public R<Map<String, List<SysDictItem>>> getAllDictByTypeGroup() {
		Map<String, List<SysDictItem>> map = this.sysDictItemService.listDictItemMap();
		return R.success(map);
	}

	/**
	 * 添加字典
	 *
	 * @param sysDict 字典信息
	 * @return success、false
	 */
	@PostMapping
	public R save(@Valid @RequestBody SysDict sysDict) {
		return R.success(sysDictService.save(sysDict));
	}

	/**
	 * 删除字典，并且清除字典缓存
	 *
	 * @param id ID
	 * @return R
	 */
	@DeleteMapping("/{id}")
	public R removeById(@PathVariable Long id) {
		sysDictService.removeDict(id);
		return R.success();
	}

	/**
	 * 修改字典
	 *
	 * @param sysDict 字典信息
	 * @return success/false
	 */
	@PutMapping
	public R updateById(@Valid @RequestBody SysDict sysDict) {
		sysDictService.updateDict(sysDict);
		return R.success();
	}

	/**
	 * ------------------------------字典项---------------------------
	 * 分页查询
	 *
	 * @param page        分页对象
	 * @param sysDictItem 字典项
	 * @return
	 */
	@GetMapping("/item/page")
	public R getSysDictItemPage(Page page, SysDictItem sysDictItem) {
		return R.success(sysDictItemService.page(page, Wrappers.query(sysDictItem).lambda().orderByAsc(SysDictItem::getSort)));
	}

	/**
	 * 通过id查询字典项
	 *
	 * @param id id
	 * @return R
	 */
	@GetMapping("/item/{id}")
	public R getDictItemById(@PathVariable("id") Integer id) {
		return R.success(sysDictItemService.getById(id));
	}

	/**
	 * 新增字典项
	 *
	 * @param sysDictItem 字典项
	 * @return R
	 */
	@PostMapping("/item")
	public R save(@RequestBody SysDictItem sysDictItem) {
		return R.success(sysDictItemService.save(sysDictItem));
	}

	/**
	 * 修改字典项
	 *
	 * @param sysDictItem 字典项
	 * @return R
	 */
	@PutMapping("/item")
	public R updateById(@RequestBody SysDictItem sysDictItem) {
		return sysDictItemService.updateDictItem(sysDictItem);
	}

	/**
	 * 通过id删除字典项
	 *
	 * @param id id
	 * @return R
	 */
	@DeleteMapping("/item/{id}")
	public R removeDictItemById(@PathVariable Long id) {
		return sysDictItemService.removeDictItem(id);
	}

}
