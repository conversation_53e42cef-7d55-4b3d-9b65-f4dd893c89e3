<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.assess.mapper.AssessTargetSecondaryMapper">

	<!-- 表字段 -->
	<sql id="baseColumns">
		SELECT t.id,
			   t.primary_target_id,
			   t.target_name,
			   t.score,
			   t.target_description,
			   t.algorithm,
			   t.rules_description,
			   t.formula,
		       t.assess_period
	</sql>

	<!-- 查询列表 -->
	<select id="findList" resultType="com.enrising.ctsc.assess.api.vo.AssessTargetSecondaryVo">
		<include refid="baseColumns"/>
		FROM assess_target_secondary t
		${ew.customSqlSegment}
	</select>

	<!-- 查询详情 -->
	<select id="detail" resultType="com.enrising.ctsc.assess.api.vo.AssessTargetSecondaryVo">
		<include refid="baseColumns"/>
		FROM assess_target_secondary t
		${ew.customSqlSegment}
		limit 1
	</select>
	<select id="getTotalScoreByTargetIds" resultType="java.lang.Double">
		SELECT
		sum(score)
		FROM assess_target_secondary
		WHERE id in
		<foreach collection="targetIds" open="(" item="id" separator="," close=")">
			#{id}
		</foreach>
	</select>
	<select id="getInfoByTargetId" resultType="com.enrising.ctsc.assess.api.vo.AssessTaskInfoVo">
		SELECT
		    c.id as template_target_id,
			d.template_name,
			d.period,
			A.target_name,
			A.assess_period,
			A.score,
			b.target_category,
			C.data_source,
			c.assess_method,
			A.algorithm,
			A.rules_description,
			d.period as templatePeriod,
			d.template_start_time,
	        A.assess_period as targetPeriod
		FROM
			assess_target_secondary A
			    LEFT JOIN assess_target b ON A.primary_target_id = b.ID
			    LEFT JOIN assess_template_target C ON A.id = C.secondary_target_id
			LEFT JOIN assess_template d ON C.template_id = d.id
		WHERE
			A.id = #{ bo.secondaryTargetId}
		and c.template_id = #{bo.templateId}
	</select>

	<!-- 查询完全详情 -->
	<select id="getFullDetail" resultType="com.enrising.ctsc.assess.api.vo.AssessTargetSecondaryVo">
		SELECT A.id AS secondary_target_id,
			   A.primary_target_id AS target_id,
			   A.target_name AS secondary_target_name,
			   A.score,
			   A.formula,
			   B.target_type,
			   B.target_year,
			   B.target_category,
			   A.algorithm,
			   A.rules_description,
			   A.target_description,
			   A.assess_period
		FROM (assess_target_secondary A
			LEFT JOIN assess_target B ON ((A.primary_target_id = B.id)))
		WHERE A.id = #{id}
		limit 1
	</select>

	<select id="targetSecondaryList" resultType="com.enrising.ctsc.assess.api.vo.AssessTargetSecondaryVo">
		select
		  a.id,
		  a.primary_target_id,
		  a.target_name,
		  a.formula
		from assess_target_secondary a
		LEFT JOIN assess_target b on a.primary_target_id = b.id
		WHERE b.target_year = #{query.year}
		<!-- 新增指标公式不为空的条件 -->
		and a.formula is not null
	</select>
	<select id="getCompanyCarbonByPage" resultType="com.enrising.ctsc.assess.api.vo.CompanyCarbonVo">
		SELECT
			id  as companyId,
			org_name as companyName
		FROM rmp.sys_organizations
		WHERE del_flag='0'
		  AND `status`='1'
		  AND org_type='1'
		  AND parent_company_no = '2600000000'
	</select>
</mapper>