package com.enrising.ctsc.discharge.api.entity;

import cn.zhxu.bs.bean.DbField;
import cn.zhxu.bs.bean.SearchBean;
import lombok.Data;

@Data
@SearchBean(
        dataSource = "rmpDs",
        tables = "sys_r_users_roles ur " +
                "left join sys_role sr on ur.role_id = sr.id " +
                "where sr.code = 'GREEN_CARBON_AUDIT'"
)
public class GreenAuditUserId {

    /**
     * 主键ID
     */
    @DbField(value = "ur.id")
    private Long id;

    /**
     * 用户ID
     */
    @DbField(value = "ur.user_id")
    private Long userId;

    /**
     * 角色ID
     */
    @DbField(value = "ur.role_id")
    private Long roleId;

}
