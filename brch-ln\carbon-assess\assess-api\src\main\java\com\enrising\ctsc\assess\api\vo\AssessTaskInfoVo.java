package com.enrising.ctsc.assess.api.vo;

import com.enrising.ctsc.assess.api.entity.AssessTargetSecondaryRule;
import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 考核任务管理--》详情按钮
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
public class AssessTaskInfoVo {

    /*
     *考核模板对象表 id
     * */
    private Long templateObjectId;

    private Long templateTargetId;
    /*
     * 任务名称
     * */
    private String templateName;

    /*
     * 任务时间
     * */
    private String period;

    /*
     * 考核指标
     * */
    private String targetName;

    /*
     * 考核周期
     * */
    private String assessPeriod;

    /*
     * 指标分值
     * */
    private String score;

    /*
     * 指标类型
     * */
    private String targetCategory;

    /*
     * 数据来源
     * */
    private String dataSource;

    /*
     * 数据来源
     * */
    private String assessMethod;

    /*
     * 指标算法
     * */
    private String algorithm;

    /*
     * 规则说明
     * */
    private String rulesDescription;

    /**
     * 规则列表
     */
    private List<AssessTargetSecondaryRule> ruleList;

    public List<AssessTargetSecondaryRule> getRuleList() {
        if (ruleList == null) {
            return Lists.newArrayList();
        }
        return ruleList;
    }

    /**
     * 模板周期
     */
    private String templatePeriod;

    /*
     * 指标周期
     * */
    private String targetPeriod;

    /*
     * 报告类型 1-1份报告 2-3份报告 3-4份报告  4-12份报告
     * */
    private String reportType;

    /**
     * 任务开始时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date templateStartTime;

    /**
     * 任务时间
     */
    private String templateTaskTime;

    /*
     * 上报内容  单个
     * */
    private AssessTaskReportVo assessTaskReportVo = new AssessTaskReportVo();

    /*
     * 上报内容  多份
     * */
    private List<AssessTaskReportVo> assessTaskReportVoList;

    /**
     * 上报时间
     */
    private Date reportDate;

    /**
     * 上报人
     */
    private String createBy;
}
