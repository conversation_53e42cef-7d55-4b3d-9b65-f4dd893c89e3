package com.enrising.ctsc.assess.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.assess.api.entity.AssessTargetSecondaryRule;
import com.enrising.ctsc.assess.api.query.AssessTargetSecondaryRuleQuery;
import com.enrising.ctsc.assess.api.vo.AssessTargetSecondaryRuleVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 考核二级指标规则
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Mapper
public interface AssessTargetSecondaryRuleMapper extends BaseMapper<AssessTargetSecondaryRule> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
	IPage<AssessTargetSecondaryRuleVo> findList(Page<AssessTargetSecondaryRuleVo> page, @Param(Constants.WRAPPER) Wrapper<AssessTargetSecondaryRuleQuery> wrapper);

	/**
	 * 详情查询
	 *
	 * @param wrapper 条件
	 * @return 结果
	 */
	AssessTargetSecondaryRuleVo detail(@Param(Constants.WRAPPER) Wrapper<AssessTargetSecondaryRuleQuery> wrapper);

	/**
	 * 根据一级指标删除考核规则
	 *
	 * @param id 一级指标id
	 * @return 结果
	 */
	@Select("delete from assess_target_secondary_rule where primary_target_id = #{id}")
	Integer delByPrimaryTargetId(@Param("id") Long id);
}