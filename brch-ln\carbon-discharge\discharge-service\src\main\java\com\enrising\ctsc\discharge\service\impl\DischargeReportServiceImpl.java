package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.InMemoryMultipartFile;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.bo.DischargeDataTotalBo;
import com.enrising.ctsc.discharge.api.bo.DischargeReportBo;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyFactor;
import com.enrising.ctsc.discharge.api.entity.DischargeReport;
import com.enrising.ctsc.discharge.api.enums.DelFlagEnum;
import com.enrising.ctsc.discharge.api.enums.ViewType;
import com.enrising.ctsc.discharge.api.query.DischargeReportQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeDataTotalVo;
import com.enrising.ctsc.discharge.api.vo.DischargeReportVo;
import com.enrising.ctsc.discharge.mapper.DischargeReportMapper;
import com.enrising.ctsc.discharge.service.DischargeDataTotalService;
import com.enrising.ctsc.discharge.service.DischargeEnergyFactorService;
import com.enrising.ctsc.discharge.service.DischargeReportService;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 碳排放报告
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Service
@AllArgsConstructor
@Slf4j
public class DischargeReportServiceImpl extends ServiceImpl<DischargeReportMapper, DischargeReport> implements DischargeReportService {

	private final DischargeDataTotalService dischargeDataTotalService;

	private final DischargeEnergyFactorService dischargeEnergyFactorService;

//	private final UploadService uploadService;
//
//	private final CommonService commonService;

	@Override
	public TableDataInfo<DischargeReportVo> findList(Page<DischargeReportVo> page, DischargeReportQuery query) {
		QueryWrapper<DischargeReportQuery> wrapper = this.getWrapper(query);
		IPage<DischargeReportVo> resultPage = baseMapper.findList(page, wrapper);
		if(CollectionUtil.isNotEmpty(resultPage.getRecords())){
			List<DischargeReportVo> records = resultPage.getRecords();
			records.forEach(node-> {
				node.setReportName(node.getReportName());
			});
		}
		return TableDataInfo.build(resultPage);
	}

	@Override
	public DischargeReportVo detail(DischargeReportQuery query) {
		if (ObjectUtil.allFieldIsNull(query)) {
			throw new BusinessException("查询参数不能为空");
		}
		QueryWrapper<DischargeReportQuery> wrapper = this.getWrapper(query);
		return baseMapper.detail(wrapper);
	}

	private QueryWrapper<DischargeReportQuery> getWrapper(DischargeReportQuery query) {
		QueryWrapper<DischargeReportQuery> wrapper = new QueryWrapper<>();
		wrapper.eq(query.getId() != null, "t.id", query.getId());
		wrapper.eq(StrUtil.isNotEmpty(query.getYear()),"t.year",query.getYear());
		wrapper.like(StrUtil.isNotEmpty(query.getKeyword()),"t.report_name",query.getKeyword());
		wrapper.orderByDesc("t.create_time");
		wrapper.eq("t.del_flag", DelFlagEnum.NOT_DELETED.getValue());
		return wrapper;
	}

	@Override
	public void add(DischargeReportBo bo) {
		// 判断是否存在
		DischargeReport dischargeReport = baseMapper.selectOne(new LambdaQueryWrapper<DischargeReport>()
				.eq(DischargeReport::getReporter, bo.getReporter())
				.eq(DischargeReport::getYear, bo.getYear()));
		if(ObjectUtil.isNotNull(dischargeReport)){
			dischargeReport.setContent(bo.getContent());
			dischargeReport.setIsNotification(bo.getIsNotification());
			dischargeReport.setReportName(bo.getReportName());
			dischargeReport.setReportTime(bo.getReportTime());
			this.updateById(dischargeReport);
		} else {
			DischargeReport entity = new DischargeReport();
			BeanUtils.copyProperties(bo, entity);
			baseMapper.insert(entity);
		}
	}

	@Override
	public void edit(DischargeReportBo bo) {
		DischargeReport entity = new DischargeReport();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.updateById(entity);
	}

	@Override
	public void del(Long id) {
		baseMapper.deleteById(id);
	}

	@Override
	@SneakyThrows
	public String preview(HttpServletResponse response, DischargeReportBo bo) {
		ByteArrayOutputStream byteArrayOutputStream = formatData(bo);
		// 填充 ByteArrayOutputStream 对象
		MultipartFile multipartFile = new InMemoryMultipartFile(
				"file",
				"考核报告.docx",
				"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
				byteArrayOutputStream.toByteArray()
		);
//		FileVo fileVo = uploadService.uploadFile(multipartFile);
//		return commonService.previewAttachmentFile(Long.parseLong(fileVo.getAttachmentId()));
		return null;
	}

	@SneakyThrows
	private ByteArrayOutputStream formatData(DischargeReportBo bo){
//		Map<String, String> params = new HashMap<String, String>();
//		int nowYear = DateUtil.year(new Date());
//		int month = DateUtil.month(new Date())+1;
//		int day = DateUtil.dayOfMonth(new Date());
//		String content,reportName,year;
//		int cYear,cMonth,cDay;
//		if(ViewType.ADD_VIEW.getValue().equals(bo.getViewType())){
//			content = bo.getContent();
//			reportName = bo.getReportName();
//			year = bo.getYear();
//			cYear  = nowYear;
//			cMonth = month;
//			cDay = day;
//		}else {
//			DischargeReport dischargeReport = this.getById(bo.getId());
//			content = dischargeReport.getContent();
//			reportName = dischargeReport.getReportName();
//			year = dischargeReport.getYear();
//			Date createTime = dischargeReport.getCreateTime();
//			cYear = DateUtil.year(createTime);
//			cMonth = DateUtil.month(createTime)+1;
//			cDay = DateUtil.dayOfMonth(createTime);
//		}
//		// 标题
//		params.put("title",reportName);
//		params.put("year", year);
//		params.put("Y", String.valueOf(nowYear));
//		params.put("M", String.valueOf(month));
//		params.put("D", String.valueOf(day));
//
//		params.put("cY", String.valueOf(cYear));
//		params.put("cM",  String.valueOf(cMonth));
//		params.put("cD", String.valueOf(cDay));
//		// 其它希望情况说明
//		params.put("content",content);
//		getCarbonData(params);
//
//		ClassPathResource resource = new ClassPathResource("/template/企业温室气体排放报告.docx");
//		return PoiWordUtil.replaceWord(resource.getInputStream(), params);
		return null;
	}

	private void getCarbonData(Map<String, String> params){
		LambdaQueryWrapper<DischargeEnergyFactor> lqw = new LambdaQueryWrapper<>();
		ArrayList<Long> longs = CollectionUtil.newArrayList(5L, 6L, 7L, 8L, 3L, 1L, 13L);
		lqw.in(DischargeEnergyFactor::getEnergyTypeId,longs);
		List<DischargeEnergyFactor> list = dischargeEnergyFactorService.list(lqw);
		// 天然气
		DischargeEnergyFactor ngFactor = getFactor(5L, list);
		// 报告主体某年活动水平数据及来源
		// 天然气
		params.put("ngPower",checkContent(ngFactor.getNetCalorificPower()));
		params.put("ngValue",checkContent(ngFactor.getCarbonPerUnitCalorificValue()));
		params.put("ngOxRate",checkContent(ngFactor.getCarbonOxidationRate()));
		params.put("ngSource",checkContent(ngFactor.getSource()));
		// 液化石油气
		DischargeEnergyFactor lpgFactor = getFactor(6L, list);
		params.put("lpgPower",checkContent(lpgFactor.getNetCalorificPower()));
		params.put("lpgValue",checkContent(lpgFactor.getCarbonPerUnitCalorificValue()));
		params.put("lpgOxRate",checkContent(lpgFactor.getCarbonOxidationRate()));
		params.put("lpgSource",checkContent(lpgFactor.getSource()));
		// 汽油
		DischargeEnergyFactor gasFactor = getFactor(7L, list);
		params.put("gasPower",checkContent(gasFactor.getNetCalorificPower()));
		params.put("gasValue",checkContent(gasFactor.getCarbonPerUnitCalorificValue()));
		params.put("gasOxRate",checkContent(gasFactor.getCarbonOxidationRate()));
		params.put("gasSource",checkContent(gasFactor.getSource()));
		// 柴油
		DischargeEnergyFactor diFactor = getFactor(8L, list);
		params.put("diPower",checkContent(diFactor.getNetCalorificPower()));
		params.put("diValue",checkContent(diFactor.getCarbonPerUnitCalorificValue()));
		params.put("diOxRate",checkContent(diFactor.getCarbonOxidationRate()));
		params.put("diSource",checkContent(diFactor.getSource()));
		// 电
		DischargeEnergyFactor powerFactor = getFactor(3L, list);
		params.put("powerFactor",checkContent(powerFactor.getFactor()));
		params.put("powerSource",checkContent(powerFactor.getSource()));
		// 原煤
		DischargeEnergyFactor coalFactor = getFactor(13L, list);
		params.put("coalFactor",checkContent(coalFactor.getFactor()));
		params.put("coalSource",checkContent(coalFactor.getSource()));
		// 水
		DischargeEnergyFactor waterFactor = getFactor(1L, list);
		params.put("waterFactor",checkContent(waterFactor.getFactor()));
		params.put("waterSource",checkContent(waterFactor.getSource()));

		// 温室气体排放量报告
		DischargeDataTotalBo dataTotalBo = new DischargeDataTotalBo();
		dataTotalBo.setCompanyId(0L);
		int nowYear = DateUtil.year(new Date());

		dataTotalBo.setDataYear(nowYear);
		List<DischargeDataTotalVo> nowYearCarbonData = dischargeDataTotalService.getDataList(dataTotalBo);
		BigDecimal carbonElectric = new BigDecimal(0);
		BigDecimal electric = new BigDecimal(0);
		BigDecimal carbonGas = new BigDecimal(0);
		BigDecimal gas = new BigDecimal(0);
		BigDecimal carbonOil = new BigDecimal(0);
		BigDecimal oil = new BigDecimal(0);
		BigDecimal carbonWater = new BigDecimal(0);
		BigDecimal water = new BigDecimal(0);
		BigDecimal carbonCoal = new BigDecimal(0);
		BigDecimal coal = new BigDecimal(0);
		BigDecimal carbonTotal = new BigDecimal(0);
		BigDecimal useTotal = new BigDecimal(0);
		for (int i = 0; i < 12; i++) {
			if(nowYearCarbonData.size() <= i){
			} else {
				DischargeDataTotalVo dischargeDataTotalVo = nowYearCarbonData.get(i);

				carbonElectric = carbonElectric.add(dischargeDataTotalVo.getCarbonPower());
				electric = electric.add(dischargeDataTotalVo.getPower());

				carbonGas = carbonGas.add(dischargeDataTotalVo.getCarbonNg()).add(dischargeDataTotalVo.getCarbonLpg());
				gas = gas.add(dischargeDataTotalVo.getNg()).add(dischargeDataTotalVo.getLpg());

				carbonOil = carbonOil.add(dischargeDataTotalVo.getCarbonGasoline()).add(dischargeDataTotalVo.getCarbonDiesel())
						.add(dischargeDataTotalVo.getCarbonCrude()).add(dischargeDataTotalVo.getCarbonFuel())
						.add(dischargeDataTotalVo.getCarbonKerosene());
				oil = oil.add(dischargeDataTotalVo.getGasoline()).add(dischargeDataTotalVo.getDiesel()).add(dischargeDataTotalVo.getCrude())
						.add(dischargeDataTotalVo.getFuel()).add(dischargeDataTotalVo.getKerosene());
				carbonWater = carbonWater.add(dischargeDataTotalVo.getCarbonWater());
				water = water.add(dischargeDataTotalVo.getWater());
//				carbonThermal = carbonThermal.add(dischargeDataTotalVo.getCarbonThermal());
				carbonCoal = carbonCoal.add(dischargeDataTotalVo.getCarbonCoal());
				coal = coal.add(dischargeDataTotalVo.getCoal());
			}
		}
		// 碳排总量
		String carbonAll = formatValue(carbonTotal.add(carbonElectric
				.add(carbonGas)
				.add(carbonOil).add(carbonWater)
				.add(carbonCoal)));
		// 碳排总量
		String useAll = formatValue(useTotal.add(electric
				.add(gas)
				.add(oil).add(water)
				.add(coal)));
		// 碳排总量
		params.put("useTotal", useAll+" tCO₂");
		params.put("carbonTotal", carbonAll+" tCO₂");
		// 电
		params.put("powerUse", formatValue(electric)+" kW·h");
		params.put("powerCarbon", formatValue(carbonElectric)+" tCO₂");
		// 气
		params.put("gasUse", formatValue(gas)+" L");
		params.put("gasCarbon", formatValue(carbonGas)+" tCO₂");
		// 油
		params.put("oilUse", formatValue(oil)+" L");
		params.put("oilCarbon", formatValue(carbonOil)+" tCO₂");
		// 水
		params.put("waterUse", formatValue(water)+" m³");
		params.put("waterCarbon",formatValue(carbonWater)+" tCO₂");
		// 原煤
		params.put("coalUse", formatValue(coal)+" t");
		params.put("coalCarbon",formatValue(carbonCoal)+" tCO₂");

	}


	private DischargeEnergyFactor getFactor(Long typeId,List<DischargeEnergyFactor> list){
		return list.stream().filter(node->{
			return node.getEnergyTypeId().equals(typeId);
		}).collect(Collectors.toList()).get(0);
	}

	private String checkContent(Object o){
		return ObjectUtil.isNull(o)?"":o.toString();
	}

	@Override
	@SneakyThrows
	public void download(HttpServletResponse response, DischargeReportBo bo) {
		bo.setViewType(ViewType.ADD_AFTER_VIEW.getValue());
		ByteArrayOutputStream byteArrayOutputStream = formatData(bo);
		response.setCharacterEncoding("utf-8");
		response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
		response.getOutputStream().write(byteArrayOutputStream.toByteArray());
	}

	@Override
	public Boolean checkExist(DischargeReportBo bo) {
		// 判断是否存在
		DischargeReport dischargeReport = baseMapper.selectOne(new LambdaQueryWrapper<DischargeReport>()
				.eq(DischargeReport::getReporter, bo.getReporter())
				.eq(DischargeReport::getYear, bo.getYear()));
		return ObjectUtil.isNotNull(dischargeReport);
	}

	private String formatValue(BigDecimal value){
		return value.setScale(2,RoundingMode.HALF_UP).toString();
	}

}
