package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.discharge.api.bo.DischargeDataGasBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataGas;
import com.enrising.ctsc.discharge.api.enums.EnergyType;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.JwtUtils;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.discharge.api.vo.DischargeDataGasVo;
import com.enrising.ctsc.discharge.mapper.DischargeDataGasMapper;
import com.enrising.ctsc.discharge.service.DischargeDataGasService;
import com.enrising.ctsc.discharge.service.DischargeEnergyCoefficientService;
import com.enrising.ctsc.discharge.service.DischargeEnergyFactorService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 碳排放数据填报表（气）
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Service
@AllArgsConstructor
public class DischargeDataGasServiceImpl extends ServiceImpl<DischargeDataGasMapper, DischargeDataGas> implements DischargeDataGasService {


	private final DischargeEnergyCoefficientService dischargeEnergyCoefficientService;

	private final DischargeEnergyFactorService dischargeEnergyFactorService;

//	private final RemoteUserService userService;

	@Override
	public Page<DischargeDataGasVo> getGasListPage(QueryPage<DischargeDataGasBo> queryPage) {
		LambdaQueryWrapper<DischargeDataGas> qw = Wrappers.lambdaQuery();
		DischargeDataGasBo dischargeDataGasBo = queryPage.getModel();
		dischargeDataGasBo.setSize(queryPage.getSize());
		dischargeDataGasBo.setOffset((queryPage.getCurrent() - 1 ) * queryPage.getSize());
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			Page<DischargeDataGas> dischargeDataGasPage = new Page<>(queryPage.getCurrent(),
					queryPage.getSize(), true);
			Page<DischargeDataGasVo> dischargeDataGasVoPage = new Page<>();
			BeanUtils.copyProperties(dischargeDataGasPage, dischargeDataGasVoPage);
			if (ObjectUtil.isNotEmpty(dischargeDataGasBo)) {
				if (ObjectUtil.isEmpty(dischargeDataGasBo.getCompanyId())) {
					dischargeDataGasBo.setCompanyId(0L);
				}
				if (ObjectUtil.isNotEmpty(dischargeDataGasBo.getReportTime())) {
					//查询条件 填报时间
					qw.eq(DischargeDataGas::getReportTime, dischargeDataGasBo.getReportTime());
					dischargeDataGasBo.setQueryStartTime(DateUtil.beginOfMonth(dischargeDataGasBo.getReportTime()));
					dischargeDataGasBo.setQueryEndTime(DateUtil.endOfMonth(dischargeDataGasBo.getReportTime()));
				} else {
					//查询条件 数据年份
					String startYear = dischargeDataGasBo.getDataYear() + "-01-01 0:00:00";
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
					dateStart = sdf.parse(startYear);
					dateEnd = DateUtil.endOfYear(dateStart);
					dischargeDataGasBo.setQueryStartTime(dateStart);
					dischargeDataGasBo.setQueryEndTime(dateEnd);
					qw.ge(DischargeDataGas::getReportTime, dateStart)
							.le(DischargeDataGas::getReportTime, dateEnd);
				}
				//查询条件，公司
				qw.eq(dischargeDataGasBo.getCompanyId() != 0, DischargeDataGas::getCompanyId,
						dischargeDataGasBo.getCompanyId());
			}
			dischargeDataGasVoPage.setTotal(this.count(qw));
			dischargeDataGasVoPage.setRecords(getCompanyDataList(dischargeDataGasBo));
			return dischargeDataGasVoPage;
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public List<DischargeDataGasVo> getGasListToExcel(DischargeDataGasBo dischargeDataGasBo) {
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			if (ObjectUtil.isNotEmpty(dischargeDataGasBo)) {
				if (ObjectUtil.isEmpty(dischargeDataGasBo.getCompanyId())) {
					dischargeDataGasBo.setCompanyId(0L);
				}
				if (ObjectUtil.isNotEmpty(dischargeDataGasBo.getReportTime())) {
					dischargeDataGasBo.setQueryStartTime(DateUtil.beginOfMonth(dischargeDataGasBo.getReportTime()));
					dischargeDataGasBo.setQueryEndTime(DateUtil.endOfMonth(dischargeDataGasBo.getReportTime()));
				} else {
					//查询条件 数据年份
					String startYear = dischargeDataGasBo.getDataYear() + "-01-01 0:00:00";
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
					dateStart = sdf.parse(startYear);
					dateEnd = DateUtil.endOfYear(dateStart);
					dischargeDataGasBo.setQueryStartTime(dateStart);
					dischargeDataGasBo.setQueryEndTime(dateEnd);
				}
			}
			return getCompanyDataList(dischargeDataGasBo);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public List<DischargeDataGasVo> getDataList(Integer dataYear, Long companyId) {
		if (ObjectUtil.isEmpty(dataYear)) {
			throw new BusinessException("数据年份不能为空！");
		}
		if (ObjectUtil.isEmpty(companyId)) {
//			companyId = userService.getCityDeptId();
			companyId = JwtUtils.getCurrentUserCompanyId();
		}
		String startYear = dataYear + "-01-01 0:00:00";
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			dateStart = sdf.parse(startYear);
			dateEnd = DateUtil.endOfYear(dateStart);
			return this.countCompanyData(dateStart, dateEnd, companyId);

		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}


	@Override
	public List<DischargeDataGasVo> getDataListByDate(Date dateStart, Date dateEnd, Long companyId) {
		if (ObjectUtil.isEmpty(companyId)) {
//			companyId = userService.getCityDeptId();
			companyId = JwtUtils.getCurrentUserCompanyId();
		}
		return this.countCompanyData(dateStart, dateEnd, companyId);
	}

	@Override
	public DischargeDataGasVo detail(Long id) {
		if (ObjectUtil.isEmpty(id)) {
			throw new BusinessException("查询参数不能为空");
		}
		DischargeDataGas dischargeDataGas = baseMapper.selectById(id);
		if (ObjectUtil.isEmpty(dischargeDataGas)) {
			return null;
		}
		DischargeDataGasVo dischargeDataGasVo = new DischargeDataGasVo();
		BeanUtils.copyProperties(dischargeDataGas ,dischargeDataGasVo);
		Calendar cal = Calendar.getInstance();
		cal.setTime(dischargeDataGasVo.getReportTime());
		Integer year=cal.get(Calendar.YEAR);//获取年
		Integer month = cal.get(Calendar.MONTH) + 1;//获取月（月份从0开始，需要加一）
		dischargeDataGasVo.setDataMonth(month + "月");
		dischargeDataGasVo.setDataYear(year + "年");
		BigDecimal carbonNg = dischargeDataGasVo.getNg().multiply(dischargeEnergyFactorService.
				getFactorByTime(EnergyType.NG.getId(), dischargeDataGasVo.getReportTime())).divide(new BigDecimal(1000));
		BigDecimal carbonLpg = dischargeDataGasVo.getLpg().multiply(dischargeEnergyFactorService.
				getFactorByTime(EnergyType.LPG.getId(), dischargeDataGasVo.getReportTime())).divide(new BigDecimal(1000));
		dischargeDataGasVo.setCarbonEmissions(carbonNg.add(carbonLpg).setScale(4, RoundingMode.HALF_UP));
		BigDecimal consumptionNg = dischargeDataGasVo.getNg().multiply(dischargeEnergyCoefficientService.
				getCoefficientByTime(EnergyType.NG.getId(), dischargeDataGasVo.getReportTime())).divide(new BigDecimal(1000));
		BigDecimal consumptionLpg = dischargeDataGasVo.getLpg().multiply(dischargeEnergyCoefficientService.
				getCoefficientByTime(EnergyType.LPG.getId(), dischargeDataGasVo.getReportTime())).divide(new BigDecimal(1000));
		dischargeDataGasVo.setEnergyConsumption(consumptionNg.add(consumptionLpg).setScale(4, RoundingMode.HALF_UP));
		return  dischargeDataGasVo;
	}

	@Override
	public String add(DischargeDataGasBo bo) {
		DischargeDataGas entity = new DischargeDataGas();
		BeanUtils.copyProperties(bo, entity);
		if (ObjectUtil.isEmpty(entity.getCompanyId())) {
//		entity.setCompanyId(userService.getCityDeptId());
			entity.setCompanyId(JwtUtils.getCurrentUserCompanyId());
		}
		//查询已有数据是否重复
		List<DischargeDataGas> dischargeDataGasList = list(
				new LambdaQueryWrapper<DischargeDataGas>()
						.eq(DischargeDataGas::getCompanyId, entity.getCompanyId())
						.eq(DischargeDataGas::getReportTime, entity.getReportTime())
						.select(DischargeDataGas::getId));
		if (CollectionUtil.isNotEmpty(dischargeDataGasList) && dischargeDataGasList.size() > 0) {
			return "所选月份数据已填报";
		}
		if (baseMapper.insert(entity) == 1) {
			return "";
		} else {
			return "保存失败";
		}
	}

	@Override
	public void edit(DischargeDataGasBo bo) {
		DischargeDataGas entity = new DischargeDataGas();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.updateById(entity);
	}

	@Override
	public void del(Long id) {
		baseMapper.deleteById(id);
	}

	@Override
	public List<DischargeDataGasVo> countCompanyData(Date dateStart, Date dateEnd, Long companyId) {
		DischargeDataGasBo queryBo = new DischargeDataGasBo();
		queryBo.setCompanyId(companyId);
		queryBo.setQueryStartTime(dateStart);
		queryBo.setQueryEndTime(dateEnd);
		return getCompanyDataList(queryBo);
	}

	private List<DischargeDataGasVo> getCompanyDataList(DischargeDataGasBo dischargeDataGasBo) {
		List<DischargeDataGasVo> dischargeDataGasVoList;
		if (ObjectUtil.isEmpty(dischargeDataGasBo) || ObjectUtil.isEmpty(dischargeDataGasBo.getCompanyId()) ||
				dischargeDataGasBo.getCompanyId().equals(0L)) {
			dischargeDataGasVoList = baseMapper.countCompanyData(dischargeDataGasBo);
		} else {
			dischargeDataGasVoList = baseMapper.getCompanyDataList(dischargeDataGasBo);
		}
		return dischargeDataGasVoList;
	}
}
