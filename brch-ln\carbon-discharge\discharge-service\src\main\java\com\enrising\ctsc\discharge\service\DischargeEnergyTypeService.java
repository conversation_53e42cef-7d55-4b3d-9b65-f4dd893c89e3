package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.discharge.api.bo.DischargeEnergyTypeSave;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyType;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyTypeQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyTypeVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 碳排放能源类型表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
public interface DischargeEnergyTypeService extends IService<DischargeEnergyType> {

	/**
	 * 分页查询
	 *
	 * @param page  分页
	 * @param query 参数
	 * @return 列表
	 */
	TableDataInfo<DischargeEnergyTypeVo> findList(Page<DischargeEnergyTypeVo> page, DischargeEnergyTypeQuery query);

	/**
	 * 详情
	 *
	 * @param query 参数
	 * @return 详情
	 */
	DischargeEnergyTypeVo detail(DischargeEnergyTypeQuery query);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	void add(DischargeEnergyTypeSave bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	void edit(DischargeEnergyTypeSave bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	void del(Long id);

	/**
	 * 能源类型列表查询
	 *
	 * @param energyType 字典能源类型
	 * @return 列表
	 */
	List<DischargeEnergyType> getEnergyTypeList(String energyType);

	/**
	 * 能源类型密度查询
	 *
	 * @param energyTypeId 能源类型id
	 * @return 能源类型密度
	 */
	BigDecimal getEnergyDensity(Long energyTypeId);
}
