<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeEnergyCoefficientMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.energy_type_id,
            t.validity_start,
            t.validity_end,
            t.coefficient,
            t.source,
			t.energy_type_unit_name,
			energy_type.second_name as energySecondName,
			energy_type.energy_type as energyType,
			energy_type.unit as energyTypeUnit
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeEnergyCoefficientVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_energy_coefficient t
		LEFT JOIN discharge_energy_type energy_type ON energy_type.id = t.energy_type_id
        ${ew.customSqlSegment}
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.discharge.api.vo.DischargeEnergyCoefficientVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_energy_coefficient t
		LEFT JOIN discharge_energy_type energy_type ON energy_type.id = t.energy_type_id
        ${ew.customSqlSegment}
        limit 1
    </select>
	<!-- 根据时间查询系数列表 -->
	<select id="findListByTime" resultType="com.enrising.ctsc.discharge.api.entity.DischargeEnergyCoefficient">
		SELECT
		    *
		from discharge_energy_coefficient as a
		    INNER JOIN (SELECT
		                    energy_type_id,max(validity_start) as validity_start
		                FROM discharge_energy_coefficient
		                where validity_start <![CDATA[<=]]> #{reportTime}
		                GROUP BY energy_type_id) as b
		        on (a.energy_type_id = b.energy_type_id and a.validity_start = b.validity_start)
	</select>
	<select id="getGainFactor" resultType="com.enrising.ctsc.discharge.api.vo.DischargeEnergyCoefficientVo">
		SELECT
		<include refid="baseColumns" />
		FROM discharge_energy_coefficient t
		LEFT JOIN discharge_energy_type energy_type ON energy_type.id = t.energy_type_id
		where t.del_flag = '0'
		<if test="bo.typeName!=null and bo.typeName!=''">
			and energy_type.second_name = #{bo.typeName}
		</if>
		<if test="bo.year!=null and bo.year!=''">
			and DATE_FORMAT(t.validity_start,'%Y') &lt;= #{bo.year}
			and DATE_FORMAT(t.validity_end,'%Y') >= #{bo.year}
		</if>
	</select>
</mapper>