package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.carbon.common.entity.User;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.JwtUtils;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.discharge.api.bo.DischargeDataElectricBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataElectric;
import com.enrising.ctsc.discharge.api.enums.EnergyType;
import com.enrising.ctsc.discharge.api.vo.DischargeDataElectricVo;
import com.enrising.ctsc.discharge.mapper.DischargeDataElectricMapper;
import com.enrising.ctsc.discharge.service.DischargeDataElectricService;
import com.enrising.ctsc.discharge.service.DischargeEnergyCoefficientService;
import com.enrising.ctsc.discharge.service.DischargeEnergyFactorService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 碳排放数据填报表（电）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Service
@AllArgsConstructor
public class DischargeDataElectricServiceImpl extends ServiceImpl<DischargeDataElectricMapper,
		DischargeDataElectric> implements DischargeDataElectricService {

	private final DischargeEnergyCoefficientService dischargeEnergyCoefficientService;

	private final DischargeDataElectricMapper dischargeDataElectricMapper;

	private final DischargeEnergyFactorService dischargeEnergyFactorService;

	@Override
	public
	Page<DischargeDataElectricVo> getElectricListPage(QueryPage<DischargeDataElectricBo> queryPage) {
		LambdaQueryWrapper<DischargeDataElectric> qw = Wrappers.lambdaQuery();
		DischargeDataElectricBo dischargeDataElectricBo = queryPage.getModel();
		dischargeDataElectricBo.setSize(queryPage.getSize());
		dischargeDataElectricBo.setOffset((queryPage.getCurrent() - 1 ) * queryPage.getSize());
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			Page<DischargeDataElectric> dischargeDataElectricPage = new Page<>(queryPage.getCurrent(),
					queryPage.getSize(), true);
			Page<DischargeDataElectricVo> dischargeDataElectricVoPage = new Page<>();
			BeanUtils.copyProperties(dischargeDataElectricPage, dischargeDataElectricVoPage);
			if (ObjectUtil.isNotEmpty(dischargeDataElectricBo)) {
				if (ObjectUtil.isEmpty(dischargeDataElectricBo.getCompanyId())) {
					dischargeDataElectricBo.setCompanyId(0L);
				}
				if (ObjectUtil.isNotEmpty(dischargeDataElectricBo.getReportTime())) {
					//查询条件 填报时间
					qw.eq(DischargeDataElectric::getReportTime, dischargeDataElectricBo.getReportTime());
					dischargeDataElectricBo.setQueryStartTime(DateUtil.beginOfMonth(dischargeDataElectricBo.getReportTime()));
					dischargeDataElectricBo.setQueryEndTime(DateUtil.endOfMonth(dischargeDataElectricBo.getReportTime()));
				} else {
					//查询条件 数据年份
					String startYear = dischargeDataElectricBo.getDataYear() + "-01-01 0:00:00";
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
					dateStart = sdf.parse(startYear);
					dateEnd = DateUtil.endOfYear(dateStart);
					dischargeDataElectricBo.setQueryStartTime(dateStart);
					dischargeDataElectricBo.setQueryEndTime(dateEnd);
					qw.ge(DischargeDataElectric::getReportTime, dateStart)
						.le(DischargeDataElectric::getReportTime, dateEnd);

				}
				//查询条件，公司
				qw.eq(dischargeDataElectricBo.getCompanyId() != 0, DischargeDataElectric::getCompanyId,
						dischargeDataElectricBo.getCompanyId());
			}
			dischargeDataElectricVoPage.setTotal(this.count(qw));
			if (ObjectUtil.isEmpty(dischargeDataElectricBo) || ObjectUtil.isEmpty(dischargeDataElectricBo.getCompanyId()) ||
					dischargeDataElectricBo.getCompanyId().equals(0L)) {
				dischargeDataElectricVoPage.setRecords(baseMapper.countCompanyData(dischargeDataElectricBo));
			} else {
				dischargeDataElectricVoPage.setRecords(baseMapper.getCompanyDataList(dischargeDataElectricBo));
			}
			return dischargeDataElectricVoPage;
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public
	List<DischargeDataElectricVo> getElectricListToExcel(DischargeDataElectricBo dischargeDataElectricBo) {
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			if (ObjectUtil.isNotEmpty(dischargeDataElectricBo)) {
				if (ObjectUtil.isEmpty(dischargeDataElectricBo.getCompanyId())) {
					dischargeDataElectricBo.setCompanyId(0L);
				}
				if (ObjectUtil.isNotEmpty(dischargeDataElectricBo.getReportTime())) {
					dischargeDataElectricBo.setQueryStartTime(DateUtil.beginOfMonth(dischargeDataElectricBo.getReportTime()));
					dischargeDataElectricBo.setQueryEndTime(DateUtil.endOfMonth(dischargeDataElectricBo.getReportTime()));
				} else {
					//查询条件 数据年份
					String startYear = dischargeDataElectricBo.getDataYear() + "-01-01 0:00:00";
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
						dateStart = sdf.parse(startYear);
						dateEnd = DateUtil.endOfYear(dateStart);
						dischargeDataElectricBo.setQueryStartTime(dateStart);
						dischargeDataElectricBo.setQueryEndTime(dateEnd);
				}
			}
			return baseMapper.countCompanyData(dischargeDataElectricBo);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public List<DischargeDataElectricVo> getDataList(Integer dataYear, Long companyId) {
		if (ObjectUtil.isEmpty(dataYear)) {
			throw new BusinessException("数据年份不能为空！");
		}
		if (ObjectUtil.isEmpty(companyId)) {
//			companyId = userService.getCityDeptId();
		}
		String startYear = dataYear + "-01-01 0:00:00";
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			dateStart = sdf.parse(startYear);
			dateEnd = DateUtil.endOfYear(dateStart);
			return this.countCompanyData(dateStart, dateEnd, companyId);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}


	@Override
	public List<DischargeDataElectricVo> getDataListByDate(Date dateStart, Date dateEnd, Long companyId) {
		if (ObjectUtil.isEmpty(companyId)) {
			companyId = JwtUtils.getCurrentUserCompanyId();
//			companyId = userService.getCityDeptId();
		}
		return this.countCompanyData(dateStart, dateEnd, companyId);
	}

	@Override
	public DischargeDataElectricVo detail(Long id) {
		if (ObjectUtil.isEmpty(id)) {
			throw new BusinessException("查询参数不能为空");
		}
		DischargeDataElectric dischargeDataElectric = baseMapper.selectById(id);
		if (ObjectUtil.isEmpty(dischargeDataElectric)) {
			return null;
		}
		DischargeDataElectricVo dischargeDataElectricVo = new DischargeDataElectricVo();
		BeanUtils.copyProperties(dischargeDataElectric ,dischargeDataElectricVo);
		BigDecimal coefficient = dischargeEnergyCoefficientService.
				getCoefficientByTime(EnergyType.THERMAL_POWER.getId(), dischargeDataElectric.getReportTime());
		BigDecimal factor = dischargeEnergyFactorService.
				getFactorByTime(EnergyType.THERMAL_POWER.getId(), dischargeDataElectric.getReportTime());
		Calendar cal = Calendar.getInstance();
		cal.setTime(dischargeDataElectricVo.getReportTime());
		Integer year=cal.get(Calendar.YEAR);//获取年
		Integer month = cal.get(Calendar.MONTH) + 1;//获取月（月份从0开始，需要加一）
		dischargeDataElectricVo.setDataMonth(month + "月");
		dischargeDataElectricVo.setDataYear(year + "年");
		BigDecimal addPower = dischargeDataElectricVo.getOutsourcingThermalPower().
				subtract(dischargeDataElectricVo.getOwnGreenPower());
		dischargeDataElectricVo.setCarbonEmissions(factor.multiply(addPower).divide(new BigDecimal(1000)).
				setScale(4, RoundingMode.HALF_UP));
		dischargeDataElectricVo.setEnergyConsumption(coefficient.multiply(addPower).divide(new BigDecimal(1000)).
				setScale(4, RoundingMode.HALF_UP));
		return  dischargeDataElectricVo;
	}

	@Override
	public String add(DischargeDataElectricBo bo) {
//		Long cityDeptId = userService.getCityDeptId();
		Long cityDeptId = JwtUtils.getCurrentUserCompanyId();
		DischargeDataElectric entity = new DischargeDataElectric();
		BeanUtils.copyProperties(bo, entity);
		entity.setCompanyId(cityDeptId);
		//查询已有数据是否重复
		List<DischargeDataElectric> dischargeDataElectricList = list(
				new LambdaQueryWrapper<DischargeDataElectric>()
						.eq(DischargeDataElectric::getCompanyId, entity.getCompanyId())
						.eq(DischargeDataElectric::getReportTime, entity.getReportTime())
						.select(DischargeDataElectric::getId));
		if (CollectionUtil.isNotEmpty(dischargeDataElectricList) && dischargeDataElectricList.size() > 0) {
			return "所选月份数据已填报";
		}
		if (baseMapper.insert(entity) == 1) {
			return "";
		} else {
			return "保存失败";
		}
	}

	@Override
	public void edit(DischargeDataElectricBo bo) {
		DischargeDataElectric entity = new DischargeDataElectric();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.updateById(entity);
	}

	@Override
	public void del(Long id) {
		baseMapper.deleteById(id);
	}

	@Override
	public DischargeDataElectricVo getAllElectricity(DischargeDataElectricBo queryBo) {
		return dischargeDataElectricMapper.allElectricity(queryBo);
	}

	@Override
	public List<DischargeDataElectricVo> countCompanyData(Date dateStart, Date dateEnd, Long companyId) {
		DischargeDataElectricBo queryBo = new DischargeDataElectricBo();
		queryBo.setCompanyId(companyId);
		queryBo.setQueryStartTime(dateStart);
		queryBo.setQueryEndTime(dateEnd);
		if (ObjectUtil.isEmpty(companyId) || companyId.compareTo(0L) == 0) {
			return dischargeDataElectricMapper.countCompanyData(queryBo);
		} else {
			return dischargeDataElectricMapper.getCompanyDataList(queryBo);
		}
	}
}
