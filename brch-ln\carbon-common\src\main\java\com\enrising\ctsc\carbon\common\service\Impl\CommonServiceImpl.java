package com.enrising.ctsc.carbon.common.service.Impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.zhxu.bs.BeanSearcher;
import cn.zhxu.bs.FieldOps;
import cn.zhxu.bs.util.MapUtils;
import com.enrising.ctsc.carbon.common.config.MinioConfig;
import com.enrising.ctsc.carbon.common.entity.*;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.service.CommonService;
import com.enrising.ctsc.carbon.common.utils.JwtUtils;
import com.enrising.ctsc.carbon.common.utils.MinioUtil;
import io.minio.StatObjectResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 通用服务接口实现
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CommonServiceImpl implements CommonService {

    //	private final MinioPropertiesConfiguration minioProperties;
    private final MinioConfig minioConfig;

	private final MinioUtil minioUtil;

	private final BeanSearcher beanSearcher;


	@Override
	public User getCurrentUser() {
//		UserInfo userInfo = SecurityUtils.getUserInfo();
//		List roleCodeList = Arrays.asList(userInfo.getRoleCodes());
//		if (CollectionUtil.isNotEmpty(roleCodeList) &&
//				(roleCodeList.contains(SysRoleEnums.ROLE_ADMIN.getValue())
//						|| roleCodeList.contains(SysRoleEnums.ROLE_PROVINCE_ADMIN.getValue()))) {
//			//系统管理员或省管理员
//			userInfo.setMaxRoleLevel(SysRoleEnums.ROLE_ADMIN.getLevel());
//		} else if (CollectionUtil.isNotEmpty(roleCodeList) &&
//				roleCodeList.contains(SysRoleEnums.ROLE_CITY_ADMIN.getValue())) {
//			//地市管理员
//			userInfo.setMaxRoleLevel(SysRoleEnums.ROLE_CITY_ADMIN.getLevel());
//		} else {
//			//普通用户
//			userInfo.setMaxRoleLevel(SysRoleEnums.ROLE_ORDINARY.getLevel());
//		}
		return JwtUtils.getUser();
	}

	@Override
	@SneakyThrows
	public void downloadAttachmentFile(HttpServletResponse response, List<CarbonAttachment> attachmentList) {
		if (CollectionUtil.isEmpty(attachmentList)) {
			throw new BusinessException("附件列表不能为空！");
		}
		if (attachmentList.size() == 1) {
			//下载单个附件
            String bucketName = minioConfig.getBucketName();
			StatObjectResponse statObjectResponse = minioUtil.statObject(bucketName, attachmentList.get(0).getSavedName());
			response.setContentType(statObjectResponse.contentType());
			// 设置响应头
			response.setHeader("content-type", statObjectResponse.contentType());
			String name = new String(attachmentList.get(0).getFileName().getBytes("utf-8"), "iso-8859-1");
			response.setHeader("Content-Disposition", "attachment; filename = " + name);
			InputStream is = getAttachmentStream(attachmentList.get(0));
			IOUtils.copy(is, response.getOutputStream());
			is.close();
		} else {
			//多个附件压缩为zip下载
			List<String> fileNameList = new ArrayList<>();
			List<InputStream> fileStreamList = new ArrayList<>();
			for (CarbonAttachment attachment : attachmentList) {
				fileNameList.add(attachment.getFileName());
				fileStreamList.add(getAttachmentStream(attachment));
			}
			if (CollectionUtil.isNotEmpty(fileStreamList)) {
				ZipUtil.zip(response.getOutputStream(), fileNameList.stream().toArray(String[]::new),
						fileStreamList.stream().toArray(InputStream[]::new));
			} else {
				throw new BusinessException("附件列表不能为空");
			}
		}
	}

	@Override
	@SneakyThrows
	public void downloadSingleAttachmentFile(HttpServletResponse response, CarbonAttachment attachment) {
		if (ObjectUtil.isEmpty(attachment)) {
			throw new BusinessException("附件不能为空！");
		}
		//下载单个附件
        String bucketName = minioConfig.getBucketName();
		StatObjectResponse statObjectResponse = minioUtil.statObject(bucketName, attachment.getSavedName());
		response.setContentType(statObjectResponse.contentType());
		// 设置响应头
		response.setHeader("content-type", statObjectResponse.contentType());
		String name = new String(attachment.getFileName().getBytes("utf-8"), "iso-8859-1");
		response.setHeader("Content-Disposition", "attachment; filename = " + name);
		InputStream is = getAttachmentStream(attachment);
		IOUtils.copy(is, response.getOutputStream());
		is.close();
	}

	@Override
	public String previewAttachmentFile(CarbonAttachment attachment) {
		String previewUrl = "";
		if (ObjectUtil.isNotEmpty(attachment)) {
            String bucketName = minioConfig.getBucketName();
			String objectName = attachment.getSavedName();
			previewUrl = minioUtil.getObjectUrl(bucketName, objectName);
		}
		if (StrUtil.isBlank(previewUrl)) {
			throw new BusinessException("获取预览地址失败！");
		}
		return previewUrl;
	}

	@Override
	public String getPreviewUrlByUrl(String fileUrl) {
		return minioUtil.getViewUrl(fileUrl);
	}

	private InputStream getAttachmentStream(CarbonAttachment attachment) {
        String bucketName = minioConfig.getBucketName();
		return minioUtil.getObject(bucketName, attachment.getSavedName());
	}

	@Override
	public User getUserById(Long id) {
		Map<String, Object> params = MapUtils.builder()
				.field(User::getId, id).op(FieldOps.Equal)
				.build();
		User user = beanSearcher.searchFirst(User.class, params);
		if (ObjectUtil.isEmpty(user)) {
			log.error("用户不存在！id:{}", id);
			return new User();
		}
		// 设置用户机构
		Organization organization = beanSearcher.searchFirst(Organization.class, MapUtils.builder()
				.field(Organization::getId, user.getDepartmentNo()).op(FieldOps.Equal)
				.build());

		user.setOrganization(organization);

		// 设置角色列表
		user.setRoleIds(beanSearcher.searchList(UserRoles.class, MapUtils.builder()
								.field(UserRoles::getUserId, user.getId()).op(FieldOps.Equal)
								.build()
						).stream().map(UserRoles::getRoleId)
						.collect(Collectors.toList())
		);
		if (CollectionUtil.isNotEmpty(user.getRoleIds())) {
			user.setRoleList(beanSearcher.searchList(Role.class, MapUtils.builder()
					.field(Role::getId, user.getRoleIds()).op(FieldOps.InList)
					.build()));
		}
		return user;
	}
}
