package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.bo.DischargeDataElectricBo;
import com.enrising.ctsc.discharge.api.bo.DischargeDataGasBo;
import com.enrising.ctsc.discharge.api.bo.DischargeMonitorSettingBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataGas;
import com.enrising.ctsc.discharge.api.query.DischargeDataGasQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeDataGasVo;
import com.enrising.ctsc.discharge.api.vo.DischargeDataTotalVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 碳排放数据填报表（气）
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Mapper
public interface DischargeDataGasMapper extends BaseMapper<DischargeDataGas> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
	IPage<DischargeDataGasVo> findList(Page<DischargeDataGasVo> page, @Param(Constants.WRAPPER) Wrapper<DischargeDataGasQuery> wrapper);

	/**
	 * 列表查询
	 *
	 * @param wrapper 条件
	 * @return 列表
	 */
	List<DischargeDataGasVo> findList(@Param(Constants.WRAPPER) Wrapper<DischargeDataGasQuery> wrapper);

	/**
	 * 详情查询
	 *
	 * @param wrapper 条件
	 * @return 结果
	 */
	DischargeDataGasVo detail(@Param(Constants.WRAPPER) Wrapper<DischargeDataGasQuery> wrapper);

	List<DischargeDataGasVo> getCompanyCarbonList(@Param("bo") DischargeMonitorSettingBo bo);


	/**
	 * 查询统计数据列表
	 *
	 * @param queryBo 查询条件
	 * @return 结果
	 */
	List<DischargeDataGasVo> countCompanyData(@Param("queryBo") DischargeDataGasBo queryBo);

	/**
	 * 查询公司数据列表
	 *
	 * @param queryBo 查询条件
	 * @return 结果
	 */
	List<DischargeDataGasVo> getCompanyDataList(@Param("queryBo") DischargeDataGasBo queryBo);

	/**
	 * 查询统计月数据列表
	 *
	 * @param queryBo 查询条件
	 * @return 统计月数据列表
	 */
	List<DischargeDataTotalVo> countCarbonCompare(@Param("queryBo") DischargeDataElectricBo queryBo);
}