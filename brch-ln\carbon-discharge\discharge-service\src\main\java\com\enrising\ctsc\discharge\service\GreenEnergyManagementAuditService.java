package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.carbon.common.entity.User;
import com.enrising.ctsc.discharge.api.bo.GreenEnergyManagementAuditBo;
import com.enrising.ctsc.discharge.api.entity.GreenEnergyManagementAudit;
import com.enrising.ctsc.discharge.api.query.GreenEnergyManagementAuditQuery;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.vo.GreenEnergyManagementAuditVo;

import java.util.List;

/**
 * 绿电管理审核
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2024-09-18
 */
public interface GreenEnergyManagementAuditService extends IService<GreenEnergyManagementAudit> {

    /**
     * 分页查询
     *
     * @param query 参数
     * @return 列表
     */
    TableDataInfo<GreenEnergyManagementAuditVo> findList(GreenEnergyManagementAuditQuery query);

    /**
     * 详情
     *
     * @param query 参数
     * @return 详情
     */
    GreenEnergyManagementAuditVo detail(GreenEnergyManagementAuditQuery query);

    /**
     * 新增
     *
     * @param bo 参数
     */
    void add(GreenEnergyManagementAuditBo bo);

    /**
     * 修改
     *
     * @param bo 参数
     */
    void edit(GreenEnergyManagementAuditBo bo);

    /**
     * 删除
     *
     * @param id 主键id
     */
    void del(Long id);

    /**
     * 查询审核管理员列表
     */
    List<User> auditUserList();
}
