package com.enrising.ctsc.assess.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.assess.api.bo.AssessReportBo;
import com.enrising.ctsc.assess.api.bo.AssessTemplateTargetObjectBo;
import com.enrising.ctsc.assess.api.entity.AssessReport;
import com.enrising.ctsc.assess.api.entity.AssessTargetSecondaryRule;
import com.enrising.ctsc.assess.api.enums.AssessType;
import com.enrising.ctsc.assess.api.enums.ReportType;
import com.enrising.ctsc.assess.api.enums.ViewType;
import com.enrising.ctsc.assess.api.feign.RemoteDischargeService;
import com.enrising.ctsc.assess.api.query.AssessReportQuery;
import com.enrising.ctsc.assess.api.vo.*;
import com.enrising.ctsc.assess.mapper.AssessReportMapper;
import com.enrising.ctsc.assess.service.AssessReportService;
import com.enrising.ctsc.assess.service.AssessTemplateService;
import com.enrising.ctsc.assess.service.AssessTemplateTargetObjectService;
import com.enrising.ctsc.carbon.common.constant.CommonConstants;
import com.enrising.ctsc.carbon.common.entity.CarbonAttachment;
import com.enrising.ctsc.carbon.common.enums.DelFlagEnum;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.*;
import com.enrising.ctsc.carbon.common.utils.pdf.PieChartToWord;
import com.enrising.ctsc.carbon.common.utils.pdf.PoiWordUtil;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 考核报告
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Service
@AllArgsConstructor
@Slf4j
public class AssessReportServiceImpl extends ServiceImpl<AssessReportMapper, AssessReport> implements AssessReportService {

	private final AssessTemplateService assessTemplateService;

	private final AssessTemplateTargetObjectService assessTemplateTargetObjectService;

	private final RemoteDischargeService remoteDischargeService;

//	todo 1
//	private final UploadService uploadService;
//	todo 1
//	private final CommonService commonService;

	@Override
	public TableDataInfo<AssessReportVo> findList(Page<AssessReportVo> page, AssessReportQuery query) {
		IPage<AssessReportVo> resultPage = baseMapper.findList(page, query);
		return TableDataInfo.build(resultPage);
	}

	@Override
	public AssessReportVo detail(AssessReportQuery query) {
		if (ObjectUtil.allFieldIsNull(query)) {
			throw new BusinessException("查询参数不能为空");
		}
		QueryWrapper<AssessReportQuery> wrapper = this.getWrapper(query);

		return baseMapper.detail(wrapper);
	}

	private QueryWrapper<AssessReportQuery> getWrapper(AssessReportQuery query) {
		QueryWrapper<AssessReportQuery> wrapper = new QueryWrapper<>();
		wrapper.eq(query.getId() != null, "t.id", query.getId());
		wrapper.orderByDesc("t.create_time");
		wrapper.eq("t.del_flag", DelFlagEnum.NOT_DELETED.getValue());
		return wrapper;
	}

	@Override
	public void add(AssessReportBo bo) {
		AssessReport entity = new AssessReport();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.insert(entity);
	}

	@Override
	public void edit(AssessReportBo bo) {
		AssessReport entity = new AssessReport();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.updateById(entity);
	}

	@Override
	public void del(Long id) {
		baseMapper.deleteById(id);
	}

	@Override
	@SneakyThrows
	public void download(HttpServletResponse response, AssessReportBo bo) {
		ByteArrayOutputStream byteArrayOutputStream = formatData(response, bo);
		response.setCharacterEncoding("utf-8");
		response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
		response.getOutputStream().write(byteArrayOutputStream.toByteArray());
	}

	@Override
	@SneakyThrows
	public String preview(HttpServletResponse response, AssessReportBo bo) {
		ByteArrayOutputStream byteArrayOutputStream = formatData(response, bo);

		// 填充 ByteArrayOutputStream 对象
		MultipartFile multipartFile = new InMemoryMultipartFile(
				"file",
				"考核报告.docx",
				"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
				byteArrayOutputStream.toByteArray()
		);
		// todo 1
//		FileVo fileVo = uploadService.uploadFile(multipartFile);
		// todo 1
//		return commonService.previewAttachmentFile(Long.parseLong(fileVo.getAttachmentId()));
		return null;
	}
	@SneakyThrows
	private ByteArrayOutputStream formatData(HttpServletResponse response, AssessReportBo bo){
		// 需要替换的模板值集合
		HashMap<String, String> map = new HashMap<>();
		String suggestion = "";
		if(ViewType.ADD_VIEW.getValue().equals(bo.getViewType())){
			// 标题
			map.put("title",bo.getReportName());
			if(StrUtil.isNotBlank(bo.getSuggestion())){
				suggestion = bo.getSuggestion();
			}
		} else {
			AssessReport assessReport = this.getById(bo.getId());
			// 标题
			map.put("title",assessReport.getReportName());
			if(StrUtil.isNotBlank(assessReport.getSuggestion())){
				suggestion = assessReport.getSuggestion();
			}
		}
		ClassPathResource resource = new ClassPathResource("/template/任务考核报告.docx");
		XWPFDocument doc = new XWPFDocument(resource.getInputStream());
		List<XWPFTable> tables = doc.getTables();

		// 一、考核任务信息
		// 根据模板id 查询模板信息
		getTaskInfo(map,bo);

		// 二、考核情况说明
		List<AssessScoreVo> headerScoreList = getHeadScoreList(bo);

		List<AssessRankVo> scoreList = getAssessScoreList(bo);

		int targetNum = headerScoreList.size();
		//考核指标
		map.put("targetNum",String.valueOf(targetNum));
		// 加分指标
		List<AssessScoreVo> addNum = headerScoreList.stream().filter(node -> {
			return node.getTargetCategory() == 2;
		}).collect(Collectors.toList());
		map.put("addNum",String.valueOf(addNum.size()));
		//减分指标
		map.put("subNum",String.valueOf(targetNum - addNum.size()));

		// 第一步：添加第一个表格行
		XWPFTable xwpfTable = tables.get(0);
		// 需要占位的模板值集合
		HashMap<String, String> holdMap = new HashMap<>();
		holdMap.put("target","target");
		holdMap.put("period","period");
		holdMap.put("category","category");
		holdMap.put("score","score");

		// 表末尾添加行(表，要复制样式的行，添加行数，插入的行下标索引)
		PoiWordUtil.addRows(xwpfTable,1,targetNum-1,2,holdMap);
		// 填充表格数据
		for (int i = 0; i < targetNum; i++) {
			String targetName = headerScoreList.get(i).getTargetName();
			String period = formatPeriod(headerScoreList.get(i).getAssessPeriod());
			String category = formatPeriod(headerScoreList.get(i).getTargetCategory());
			String score = headerScoreList.get(i).getScore().toString();
			if(i == 0){
				map.put("target",targetName);
				map.put("period",period);
				map.put("category",category);
				map.put("score",score);
			} else {
				map.put("target"+i,targetName);
				map.put("period"+i,period);
				map.put("category"+i,category);
				map.put("score"+i,score);
			}
		}
		// 考核对象
		int companyNum = scoreList.size();
		// 第二步：处理部门
		holdMap.put("companyName","companyName");
		PoiWordUtil.fillText(doc,holdMap,companyNum,"companyName","考核对象${companyNum}个：");
		map.put("companyNum",String.valueOf(companyNum));
		for (int i = 0; i < scoreList.size(); i++) {
			map.put("companyName"+i,scoreList.get(i).getCompanyName());
		}
		// 三、考核成效说明
		// 分数最高的公司
		AssessRankVo maxAssess = scoreList.get(0);
		map.put("maxScore",String.valueOf(maxAssess.getAssessScore()));
		map.put("maxRate", MathUtils.d2dPercent(new BigDecimal(maxAssess.getAssessScore()),new BigDecimal(headerScoreList.get(0).getTotalScore())));
		map.put("maxComName",maxAssess.getCompanyName());
		// 分数最小的公司
		AssessRankVo minAssess = scoreList.get(scoreList.size() -1);
		map.put("minScore",String.valueOf(minAssess.getAssessScore()));
		map.put("minRate",MathUtils.d2dPercent(new BigDecimal(minAssess.getAssessScore()),new BigDecimal(headerScoreList.get(0).getTotalScore())));
		map.put("minComName",minAssess.getCompanyName());
		map.put("minNum", NumberFormatUtils.arabicNumToChineseNum(scoreList.size()));
		// 公司考核成绩低于预警值
		List<AssessRankVo> lowAssess = getLowWarnList(map,scoreList);
		map.put("lowNum",String.valueOf(lowAssess.size()));
		String holdChar = "";
		if(lowAssess.size() == 0){
			map.put("low"," 暂无公司");
		}else {
			holdChar = "个公司考核成绩低于预警值，分别是";
			HashMap<String, String>  map1 = new HashMap<>();
			map1.put("low","low");
			PoiWordUtil.fillText(doc,map1,lowAssess.size(),"low",holdChar);

			for (int i = 0; i < lowAssess.size(); i++) {
				map.put("low"+i,lowAssess.get(i).getCompanyName());
			}
		}
		// 表2 总体考核成效
		int comNum = scoreList.size();
		XWPFTable xwpfTable1 = tables.get(1);
		// 需要占位的模板值集合
		HashMap<String, String> holdMap1 = new HashMap<>();
		holdMap1.put("comName","comName");
		holdMap1.put("comScore","comScore");
		holdMap1.put("comRate","comRate");
		holdMap1.put("comRank","comRank");
		// 表末尾添加行(表，要复制样式的行，添加行数，插入的行下标索引)
		PoiWordUtil.addRows(xwpfTable1,1,comNum-1,2,holdMap1);
		for (int i = 0; i < comNum; i++) {
			String comName = scoreList.get(i).getCompanyName();
			String comScore = String.valueOf(scoreList.get(i).getAssessScore());
			String comRate = MathUtils.d2dPercent(new BigDecimal(scoreList.get(i).getAssessScore()),new BigDecimal(headerScoreList.get(0).getTotalScore()));
			String comRank = scoreList.get(i).getAssessRank();
			if(i == 0){
				map.put("comName",comName);
				map.put("comScore",comScore);
				map.put("comRate",comRate);
				map.put("comRank",comRank);
			} else {
				map.put("comName"+i,comName);
				map.put("comScore"+i,comScore);
				map.put("comRate"+i,comRate);
				map.put("comRank"+i,comRank);
			}
		}
		XWPFTable xwpfTable3 = tables.get(tables.size()-1);

		// 查询指标对应的公司得分详情
		AssessTemplateTargetObjectBo queryBo = new AssessTemplateTargetObjectBo();
		queryBo.setTemplateId(bo.getTemplateId());
		List<Long> targetIds = headerScoreList.stream().map(AssessScoreVo::getTargetId).collect(Collectors.toList());
		queryBo.setTargetIds(targetIds);
		List<AssessScoreVo> companyInfos = assessTemplateTargetObjectService.getComRankByTarAndTemId(queryBo);
		// 各指标考核情况
		for (int i = 0; i < headerScoreList.size(); i++) {
			AssessScoreVo assessScoreVo = headerScoreList.get(i);

			List<AssessScoreVo> companyInfo = companyInfos.stream().filter(item -> {
				return item.getTargetId().equals(assessScoreVo.getTargetId());
			}).collect(Collectors.toList());
			// 指标名称
			String tGet = assessScoreVo.getTargetName();
			// 分值
			String tNum = String.valueOf(assessScoreVo.getTotalScore());
			// 若是主动上报 则需要系统自己计算
			if(ReportType.AUTO_REPORTING.getValue().equals(assessScoreVo.getAssessMethod())) {
				List<Long> companyIds = scoreList.stream().map(AssessRankVo::getCompanyId).collect(Collectors.toList());
				makeUpAutoScore(assessScoreVo,companyIds,companyInfo,1);
			}
			// 平均得分
			String tAvgGet = companyInfo.stream().map(AssessScoreVo::getAssessScore).reduce(BigDecimal::add).get()
					.divide(new BigDecimal(companyInfo.size()), 2, RoundingMode.HALF_UP).toString();

			// 排序 根据考核得分排序
			companyInfo = companyInfo.stream().sorted(Comparator.comparing(AssessScoreVo::getAssessScore).reversed()).collect(Collectors.toList());

			if(i == 0){
				map.put("tGet",tGet);
				map.put("tNum",tNum);
				map.put("tAvgGet",tAvgGet);
				holdMap1.put("dept","dept");
				holdMap1.put("num","num");
				holdMap1.put("rank","rank");

				PoiWordUtil.addRows(xwpfTable3,1,companyInfo.size()-1,2,holdMap1);
				for (int j = 0; j < companyInfo.size(); j++) {
					// 部门
					String companyName1 = companyInfo.get(j).getCompanyName();
					// 得分
					String assessScore1 = companyInfo.get(j).getAssessScore().toString();
					if(j == 0){
						// 第一行
						map.put("dept",companyName1);
						map.put("num",assessScore1);
						map.put("rank",NumberFormatUtils.arabicNumToChineseNum(j+1));
					}else  {
						map.put("dept"+j,companyName1);
						map.put("num"+j,assessScore1);
						map.put("rank"+j,NumberFormatUtils.arabicNumToChineseNum(j+1));
					}

				}
			}else {
				XWPFParagraph lastParagraph1 = doc.createParagraph();
				XWPFRun run1 = lastParagraph1.createRun();
				// 追加文本字段
				run1.setText("${tGet"+i+"}"+"：分值"+"${tNum"+i+"}"+"分，考核平均得分"+"${tAvgGet"+i+"}"+"分。");
				//---首行缩进,指定额外的缩进，应适用于父段的第一行。
				lastParagraph1.setIndentationFirstLine(400);
				// 占位符
				String param1 = "p"+i;
				String param2 = "p"+i+10;
				String param3 = "p"+i+20;
				// 添加表格
				PoiWordUtil.addTables(xwpfTable3.getRow(0),doc,param1,param2,param3);
				// 在添加行数
				XWPFTable xwpfTable4 = tables.get(tables.size() - 1);
				holdMap1.put("p"+i,"p"+i);
				holdMap1.put("p"+i+10,"p"+i+10);
				holdMap1.put("p"+i+20,"p"+i+20);
				PoiWordUtil.addRows(xwpfTable4,1,companyInfo.size()-1,2,holdMap1);
				map.put("tGet"+i,tGet);
				map.put("tNum"+i,tNum);
				map.put("tAvgGet"+i,tAvgGet);
				for (int j = 0; j < companyInfo.size(); j++) {
					// 部门
					String companyName1 = companyInfo.get(j).getCompanyName();
					// 得分
					String assessScore1 = companyInfo.get(j).getAssessScore().toString();
					// 设置值
					if(j == 0){
						map.put("p"+i,companyName1);
						map.put("p"+i+10,assessScore1);
						map.put("p"+i+20, NumberFormatUtils.arabicNumToChineseNum(j+1));

					}  {
						map.put("p"+i+j,companyName1);
						map.put("p"+i+10+j,assessScore1);
						map.put("p"+i+20+j, NumberFormatUtils.arabicNumToChineseNum(j+1));
					}
				}

			}
		}
		// 四、考核成效
		XWPFParagraph para = doc.createParagraph();
		XWPFRun run2 = para.createRun();
		run2.setText("四、考核成效分析");
		run2.setFontFamily("宋体");

		int lowNum = lowAssess.size();
		int highNum = new BigDecimal(scoreList.size()).subtract(new BigDecimal(lowNum)).intValue();
		// 生成饼图
		double[] data = {lowNum, highNum};
		String[] keys = {"考核成绩低于预警值", "考核成绩高于预警值"};
		// 生成统计图
		PieChartToWord.makePieChart(data,keys);
		// 追加到word中
		PieChartToWord.appendPicture(doc);

		// 设置得分率
		HashMap<String, Integer> scoreMap = scoreAnalysis(scoreList);

		double[] data1 = {scoreMap.get("num1"),scoreMap.get("num2"),scoreMap.get("num3"),scoreMap.get("num4")};
		String[] keys1 = {"得分率0-60%", "得分率61-80%","得分率81-90%","得分率91-100%"};
		// 生成统计图
		PieChartToWord.makePieChart(data1,keys1);
		// 追加到word中
		PieChartToWord.appendPicture(doc);

		// 意见建议
		if(StrUtil.isNotBlank(suggestion)){
			XWPFParagraph para2 = doc.createParagraph();
			XWPFRun run3 = para2.createRun();
			run3.setText("五、意见建议");
			run3.setFontFamily("宋体");

			XWPFParagraph para3 = doc.createParagraph();
			XWPFRun run4 = para3.createRun();
			run4.setText(suggestion);
			//---首行缩进,指定额外的缩进，应适用于父段的第一行。
			para3.setIndentationFirstLine(400);
			run4.addBreak();
		}

		int month = new BigDecimal(DateUtil.month(new Date())).add(new BigDecimal(1)).intValue();
		XWPFParagraph para1 = doc.createParagraph();
		XWPFRun run3 = para1.createRun();
		run3.setText(DateUtil.year(new Date())+" 年 "+month+" 月 " +DateUtil.dayOfMonth(new Date())+" 日");
		run3.setFontFamily("宋体");
		para1.setAlignment(ParagraphAlignment.RIGHT);

		return PoiWordUtil.replaceWord1(doc, map);
	}

	@Override
	public AssessVisualVo visual(AssessReportBo bo) {
		AssessVisualVo vo = new AssessVisualVo();
		HashMap<String, String> map = new HashMap<>();
		//  考核指标
		List<AssessScoreVo> headerScoreList = getHeadScoreList(bo);
		//  公司排名
		List<AssessRankVo> scoreList = getAssessScoreList(bo);
		// 1、任务基本信息
		getTaskInfo(map,bo);
		// 任务名称
		vo.setTaskName(map.get("taskName"));
		// 任务时间
		vo.setTaskTime(map.get("taskTime"));
		// 考核预警
		vo.setWarnValue(map.get("warnValue"));
		// 考核指标
		vo.setTargetNum(String.valueOf(headerScoreList.size()));
		// 被考核对象
		vo.setCompanyNum(String.valueOf(scoreList.size()));
		vo.setTargetScore(headerScoreList.get(CommonConstants.ZERO_NUMBER).getTotalScore());

		// 分数最高的公司
		AssessRankVo maxAssess = scoreList.get(CommonConstants.ZERO_NUMBER);
		// 最高分
		vo.setMaxScore(maxAssess.getAssessScore());
		// 分数最小的公司
		AssessRankVo minAssess = scoreList.get(scoreList.size() -1);
		// 最低分
		vo.setMinScore(minAssess.getAssessScore());
		double sum = scoreList.stream().mapToDouble(AssessRankVo::getAssessScore).sum();
		// 平均分
		String tAvgGet = new BigDecimal(sum).divide(new BigDecimal(scoreList.size()), 2, RoundingMode.HALF_UP).toString();
		vo.setAvgScore(tAvgGet);

		// 考核成绩低于预警值
		List<AssessRankVo> lowAssess = getLowWarnList(map,scoreList);
		int lowNum = lowAssess.size();
		int highNum = new BigDecimal(scoreList.size()).subtract(new BigDecimal(lowNum)).intValue();
		vo.setLowWarnNum(lowAssess.size());
		vo.setThanWarnNum(highNum);

		HashMap<String, Integer> scoreMap = scoreAnalysis(scoreList);
		vo.setValue4(scoreMap.get("num1"));
		vo.setValue3(scoreMap.get("num2"));
		vo.setValue2(scoreMap.get("num3"));
		vo.setValue1(scoreMap.get("num4"));

		// 考核排名
        // 获取各个指标的得分情况
		AssessTemplateTargetObjectBo queryBo = new AssessTemplateTargetObjectBo();
		queryBo.setTemplateId(bo.getTemplateId());
		List<Long> companyIds = scoreList.stream().map(AssessRankVo::getCompanyId).collect(Collectors.toList());
		queryBo.setCompanyIds(companyIds);
		List<AssessScoreVo> targetScoreList = assessTemplateTargetObjectService.getTargetScoreByCompanyIds(queryBo);
		ArrayList<String> companyRank = new ArrayList<>();

		List<AssessVisualVo.TargetScoreVo> targetScoreVoList = new ArrayList<>();
		headerScoreList.forEach(item->{
			AssessVisualVo.TargetScoreVo  targetScoreVo = new AssessVisualVo.TargetScoreVo();
			ArrayList<BigDecimal> data = new ArrayList<>();
			targetScoreVo.setName(item.getTargetName());
			targetScoreVo.setId(item.getTargetId());
			targetScoreVo.setData(data);
			targetScoreVoList.add(targetScoreVo);
			// 若是主动上报 则需要系统自己计算
			if(ReportType.AUTO_REPORTING.getValue().equals(item.getAssessMethod())) {
				makeUpAutoScore(item,companyIds,targetScoreList,2);
			}
		});

		scoreList.forEach(node->{
			List<AssessScoreVo> collect = targetScoreList.stream().filter(item -> {
				return node.getCompanyId().equals(item.getCompanyId());
			}).collect(Collectors.toList());
			collect.forEach(score->{
				AssessVisualVo.TargetScoreVo targetScoreVo = targetScoreVoList.stream().filter(target -> {
					return score.getTargetId().equals(target.getId());
				}).collect(Collectors.toList()).get(CommonConstants.ZERO_NUMBER);
				List<BigDecimal> data = targetScoreVo.getData();
				data.add(score.getAssessScore());
			});

			String str = StrUtil.format("{} 第{}名 {}分",node.getCompanyName(),node.getAssessRank(),node.getAssessScore());
		    companyRank.add(str);
		});

		vo.setTargetScoreVoList(targetScoreVoList);
		vo.setCompanyRank(companyRank);
		return vo;
	}

	@Override
	public boolean saveCarbonAttachment(CarbonAttachment attachment) {
		if (ObjectUtil.isEmpty(attachment.getId())) {
			attachment.setId(IdWorker.getId());
		}
		attachment.setCreateTime(new Date());
		int i = baseMapper.saveCarbonAttachment(attachment);
		return i == 1;
	}

	@Override
	public CarbonAttachment getAttachmentById(Long id) {
		return baseMapper.getAttachmentById(id);
	}


	/**
	 * 自动评分
	 * @param item
	 * @param companyIds
	 * @param targetScoreList
	 * @param type
	 */
	private void makeUpAutoScore(AssessScoreVo item,List<Long> companyIds,
								 List<AssessScoreVo> targetScoreList,Integer type){
		if (StrUtil.isNotBlank(item.getFormula())
				&& AssessType.ENERGY_UP.getValue().equals(item.getFormula())) {
			// 找到指标对应的几个公司
			List<DischargeMonitorSettingVo> remoteList = remoteDischargeService.getCarbonDownByTemplateId(companyIds);
			// 拿到考核规则
			List<AssessTargetSecondaryRule> ruleList = new AssessTargetSecondaryRule().selectList(Wrappers.<AssessTargetSecondaryRule>lambdaQuery().eq(AssessTargetSecondaryRule::getSecondaryTargetId, item.getTargetId()));
			remoteList.forEach(node -> {
				AssessScoreVo assessScoreVo1;
				if (type == 2) {
					assessScoreVo1 = targetScoreList.stream().filter(node1 -> {
						return node1.getCompanyId().equals(node.getCompanyId())
								&& node1.getTargetId().equals(item.getTargetId());
					}).collect(Collectors.toList()).get(CommonConstants.ZERO_NUMBER);
				} else {
					assessScoreVo1 = targetScoreList.stream().filter(node1 -> {
						return node1.getCompanyId().equals(node.getCompanyId());
					}).collect(Collectors.toList()).get(CommonConstants.ZERO_NUMBER);
				}
				double autoCalScore = assessTemplateTargetObjectService.getAutoCalScoreByEnergyUp(node, ruleList);
				assessScoreVo1.setAssessScore(new BigDecimal(autoCalScore));
			});
		}
	}

	private void  getTaskInfo(HashMap<String, String> map,AssessReportBo bo){

		AssessTemplateVo assessTemplateVo = assessTemplateService.getDetailById(bo.getTemplateId());
		// 任务名称
		map.put("taskName",assessTemplateVo.getTemplateName());
		// 任务时间
		String taskTime = assessTemplateTargetObjectService.getTemplateTime(assessTemplateVo.getTemplateStartTime()
				, assessTemplateVo.getPeriod());
		map.put("taskTime",taskTime);
		// 考核预警值
		map.put("warnValue",assessTemplateVo.getWarningValue().toString());
		//任务说明
		map.put("content",assessTemplateVo.getContent());
	}
	/**
	 * 公司排名
	 */
	private List<AssessRankVo> getAssessScoreList(AssessReportBo bo){
		QueryPage<AssessTemplateTargetObjectBo> scoreBo = new QueryPage<AssessTemplateTargetObjectBo>();
		AssessTemplateTargetObjectBo assessTemplateTargetObjectBo = new AssessTemplateTargetObjectBo();
		assessTemplateTargetObjectBo.setTemplateId(bo.getTemplateId());
		scoreBo.setCurrent(1);
		scoreBo.setSize(50);
		scoreBo.setModel(assessTemplateTargetObjectBo);
		return assessTemplateTargetObjectService.getScoreListByTemplateId(scoreBo).getRecords();
	}

	/**
	 * 考核指标
	 */
	private List<AssessScoreVo> getHeadScoreList(AssessReportBo bo){
		AssessTemplateTargetObjectBo assessTemplateTargetObjectBo = new AssessTemplateTargetObjectBo();
		assessTemplateTargetObjectBo.setTemplateId(bo.getTemplateId());
		return assessTemplateTargetObjectService.getHeaderScoreList(assessTemplateTargetObjectBo);
	}

	/**
	 * 考核成绩低于预警值
	 */
	private List<AssessRankVo> getLowWarnList(HashMap<String, String> map,List<AssessRankVo> scoreList){
		return scoreList.stream().filter(node -> {
			return MathUtils.d2d(new BigDecimal(node.getAssessScore()), new BigDecimal(node.getTotalScore()))
					.compareTo(new BigDecimal(map.get("warnValue"))) < 0;
		}).collect(Collectors.toList());
	}

	/**
	 * 考核成效分析
	 */
	private HashMap<String, Integer>  scoreAnalysis(List<AssessRankVo> scoreList){

		HashMap<String, Integer> map = new HashMap<>();
		AtomicInteger num1 = new AtomicInteger(CommonConstants.ZERO_NUMBER);
		AtomicInteger num2 = new AtomicInteger(CommonConstants.ZERO_NUMBER);
		AtomicInteger num3 = new AtomicInteger(CommonConstants.ZERO_NUMBER);
		AtomicInteger num4 = new AtomicInteger(CommonConstants.ZERO_NUMBER);

		scoreList.forEach(node->{
			int socre = MathUtils.d2d(new BigDecimal(node.getAssessScore()), new BigDecimal(node.getTotalScore())).intValue();
			if(socre <= 60){
				num1.getAndIncrement();
			} else if(socre <= 80 ){
				num2.getAndIncrement();
			}else if(socre <= 90){
				num3.getAndIncrement();
			}else {
				num4.getAndIncrement();
			}
		});
		map.put("num1",num1.get());
		map.put("num2",num2.get());
		map.put("num3",num3.get());
		map.put("num4",num4.get());
		return map;
	}


	/**
	* 转换考核周期
	* */
	private String formatPeriod(Integer period){
		if(period == 1){
			return "年";
		}else if(period == 2){
			return "季度";
		} else {
			return "月";
		}
	}

	/**
	 * 转换指标类别
	 * */
	private String formatCategory(Integer category){
		if(category == 1){
			return "减分";
		}else {
			return "加分";
		}
	}

}
