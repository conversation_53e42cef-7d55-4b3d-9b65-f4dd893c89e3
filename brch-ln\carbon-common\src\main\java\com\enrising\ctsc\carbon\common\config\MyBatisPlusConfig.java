package com.enrising.ctsc.carbon.common.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * MyBatis-Plus 配置
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Configuration
public class MyBatisPlusConfig {

    /**
     * 分页插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        return interceptor;
    }

    /**
     * 自动填充处理器
     */
    @Component
    public static class MyMetaObjectHandler implements MetaObjectHandler {

        @Override
        public void insertFill(MetaObject metaObject) {
            LocalDateTime now = LocalDateTime.now();

            // 创建时间
            this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, now);
            // 更新时间
            this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, now);
            // 创建年份
            this.strictInsertFill(metaObject, "year", Integer.class, now.getYear());

            // TODO: 这里可以集成当前登录用户信息
            // 创建人ID
            this.strictInsertFill(metaObject, "creatorId", Long.class, 1L);
            // 创建人姓名
            this.strictInsertFill(metaObject, "creatorName", String.class, "system");
        }

        @Override
        public void updateFill(MetaObject metaObject) {
            LocalDateTime now = LocalDateTime.now();

            // 更新时间
            this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, now);
        }
    }
}
