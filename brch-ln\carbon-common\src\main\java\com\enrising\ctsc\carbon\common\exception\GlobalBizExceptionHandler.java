/*
 * Copyright (c) 2021 ctsc-cloudx Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.enrising.ctsc.carbon.common.exception;

import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.enrising.ctsc.carbon.common.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.sql.SQLException;
import java.sql.SQLSyntaxErrorException;
import java.util.List;

/**
 * <p>
 * 全局异常处理器不能作用在
 * </p>
 *
 * <AUTHOR>
 * @date 2021-04-03
 */
@Slf4j
@Component
@RestControllerAdvice
public class GlobalBizExceptionHandler {

    /**
     * 2、使用 @ExceptionHandler  说明处理具体的异常类型
     * 全局异常.
     *
     * @param e the e
     * @return R
     */
    @ExceptionHandler(Exception.class)
    public R handleGlobalException(Exception e) {
        log.error("全局异常信息 ex={}", e.getMessage(), e);
        String errMsg = e.getLocalizedMessage();
        if (errMsg != null && (errMsg.contains("SQLException") || errMsg.contains("SQLSyntaxErrorException"))) {
            errMsg = "sql错误，请核对";
        }
        return R.failed(errMsg);
    }

    /**
     * 处理业务校验过程中碰到的非法参数异常 该异常基本由{@link Assert}抛出
     *
     * @param exception 参数校验异常
     * @return API返回结果对象包装后的错误输出结果
     * @see Assert#hasLength(String, String)
     * @see Assert#hasText(String, String)
     * @see Assert#isTrue(boolean, String)
     * @see Assert#isNull(Object, String)
     * @see Assert#notNull(Object, String)
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public R<String> handleIllegalArgumentException(IllegalArgumentException exception) {
        log.error("非法参数,ex = {}", exception.getMessage(), exception);
        return R.failed(exception.getMessage());
    }

    /**
     * validation Exception
     *
     * @return R
     */
    @ExceptionHandler({MethodArgumentNotValidException.class})
    public R<String> handleBodyValidException(MethodArgumentNotValidException exception) {
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
        log.warn("参数绑定异常,ex = {}", fieldErrors.get(0).getDefaultMessage());
        return R.failed(fieldErrors.get(0).getDefaultMessage());
    }

    @ExceptionHandler({SQLException.class, SQLSyntaxErrorException.class})
    public R<String> dbExceptionHandler(BindException exception) {
        log.error("数据库操作执行异常", exception);
        return R.failed("数据库操作执行异常");
    }

    @ExceptionHandler({MybatisPlusException.class})
    public R<String> sqlParsingException(MybatisPlusException exception) {
        log.error("SQL解析异常", exception);
        return R.failed("SQL解析异常");
    }

    @ExceptionHandler({BusinessException.class})
    public R<String> businessException(BusinessException exception) {
        log.error("业务异常", exception);
        return R.failed(exception.getMessage());
    }

    @ExceptionHandler({NullPointerException.class})
    public R<String> businessException(NullPointerException exception) {
        log.error("空指针异常", exception);
        return R.failed("空指针异常");
    }
}
