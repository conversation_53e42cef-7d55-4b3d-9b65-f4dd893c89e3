///*
// * Copyright (c) 2021 ctsc-cloudx Authors. All Rights Reserved.
// *
// * Licensed under the Apache License, Version 2.0 (the "License");
// * you may not use this file except in compliance with the License.
// * You may obtain a copy of the License at
// *
// *     http://www.apache.org/licenses/LICENSE-2.0
// *
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS,
// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// * See the License for the specific language governing permissions and
// * limitations under the License.
// */
//
//package com.enrising.ctsc.discharge.api.bo;
//
//import com.enrising.ctsc.admin.api.dto.TreeNode;
//import com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorVo;
//import com.google.common.collect.Lists;
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.io.Serializable;
//import java.util.List;
//
///**
// * <AUTHOR>
// * @date 2025-5-25
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//public class IndicatorTree extends TreeNode implements Serializable {
//
//	/**
//	 * 能源指标类型名称
//	 */
//	private String indicatorName;
//
//	/**
//	 * 单位id
//	 */
//	private String unit;
//
//	/**
//	 * 状态，1-启用，2-禁用
//	 */
//	private String status;
//
//	/**
//	 * 排序值
//	 */
//	//	private Integer sort;
//
//	/**
//	 * 单位名称
//	 */
//	private String unitName;
//
//	/**
//	 * 单位描述
//	 */
//	private String unitDescription;
//
//	private boolean spread = true;
//
//	/**
//	 * 是否包含子节点
//	 *
//	 */
//	private Boolean hasChildren;
//
//	public IndicatorTree() {
//	}
//
//	public IndicatorTree(Long id, String name, Long parentId) {
//		this.id = id;
//		this.parentId = parentId;
//		this.indicatorName = name;
//	}
//
//	public IndicatorTree(Long id, String name, IndicatorTree parent) {
//		this.id = id;
//		this.parentId = parent.getId();
//		this.indicatorName = name;
//	}
//
//	public IndicatorTree(DischargeEnergyIndicatorVo dischargeEnergyIndicatorVo) {
//		this.id = dischargeEnergyIndicatorVo.getId();
//		this.parentId = dischargeEnergyIndicatorVo.getParentId();
//		this.indicatorName = dischargeEnergyIndicatorVo.getIndicatorName();
//		this.sort = dischargeEnergyIndicatorVo.getSort();
//		this.status = dischargeEnergyIndicatorVo.getStatus();
//		this.unit = dischargeEnergyIndicatorVo.getUnit();
//		this.unitName = dischargeEnergyIndicatorVo.getUnitName();
//		this.unitDescription = dischargeEnergyIndicatorVo.getUnitDescription();
//		this.hasChildren = false;
//	}
//
//}
