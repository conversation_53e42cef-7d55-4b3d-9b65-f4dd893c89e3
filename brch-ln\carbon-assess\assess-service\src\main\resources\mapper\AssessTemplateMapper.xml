<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.assess.mapper.AssessTemplateMapper">
	<resultMap id="BaseResultMap" type="com.enrising.ctsc.assess.api.entity.AssessTemplate">
		<result column="id" property="id" />
		<result column="create_by" property="createBy" />
		<result column="create_time" property="createTime" />
		<result column="update_by" property="updateBy" />
		<result column="update_time" property="updateTime" />
		<result column="template_name" property="templateName" />
		<result column="period" property="period" />
		<result column="template_start_time" property="templateStartTime" />
		<result column="template_end_time" property="templateEndTime" />
		<result column="warning_value" property="warningValue" />
		<result column="content" property="content" />
		<result column="send_status" property="sendStatus" />
		<result column="send_time" property="sendTime" />
		<result column="status" property="status" />
		<result column="del_flag" property="delFlag" />
	</resultMap>
    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.template_name,
            t.period,
            t.template_start_time,
            t.template_end_time,
            t.warning_value,
            t.content,
            t.send_status,
            t.send_time,
            t.status,
            t.del_flag
    </sql>
	<select id="getDeliveredTaskByPage" resultType="com.enrising.ctsc.assess.api.vo.AssessTemplateVo">
		SELECT
			id,
			template_name,
			period,
			send_time,
		    template_start_time,
		    template_end_time,
			CASE 			status
			WHEN '1' THEN '任务未开始'
			when '2' then '任务进行中'
			when '3' then '任务已结束'
			ELSE
			'未知状态'
			END as status
		FROM
		   assess_template
		WHERE
		   del_flag = '0'
		and send_status = '1'
		<if test="bo.templateName!='' and bo.templateName!=null ">
			and template_name like concat('%',#{bo.templateName},'%')
		</if>
		<if test="bo.status!='' and bo.status!=null ">
			and status = #{bo.status}
		</if>
		<choose>
			<when test="bo.templateStartTime!=null and bo.templateEndTime!=null">
				<!-- 场景1：开始时间≤结束时间 -->
				and (
				(template_start_time &gt;= #{bo.templateStartTime} and template_start_time &lt;= #{bo.templateEndTime})
				or (template_end_time &gt;= #{bo.templateStartTime} and template_end_time &lt;= #{bo.templateEndTime})
				or (template_start_time &lt;= #{bo.templateStartTime} and template_end_time &gt;= #{bo.templateEndTime})
				)
			</when>
			<when test="bo.templateStartTime!=null">
				<!-- 场景2：只选择开始时间：查询从所选时间到当前时间的所有数据 -->
				and (
				template_start_time &gt;= #{bo.templateStartTime}
				or template_end_time &gt;= #{bo.templateStartTime}
				)
			</when>
			<when test="bo.templateEndTime!=null">
				<!-- 场景3：只选择结束时间：查询到所选时间为止的所有数据 -->
				and (
				template_start_time &lt;= #{bo.templateEndTime}
				or template_end_time &lt;= #{bo.templateEndTime}
				)
			</when>
		</choose>
		order by send_time desc
	</select>
	<select id="getAllAssessTemplateByYear" resultType="com.enrising.ctsc.assess.api.vo.AssessTemplateVo">
		SELECT a.ID
		,
		a.template_name,
		a.period,
		a.status
		FROM
		assess_template  a
		LEFT JOIN assess_template_target b on a.id = b.template_id
		LEFT JOIN assess_template_target_object c on c.template_target_id = b.id

		WHERE
		a.del_flag = '0'
		AND a.send_status != '3'
		AND a.status IN ( '2', '3' )
		<if test="bo.companyId!=null">
			and c.company_id = #{bo.companyId}
		</if>
		<if test="bo.year!=null">
			and to_char(template_start_time, 'yyyy') = #{bo.year}
		</if>
		GROUP BY
		a.id
		order by a.template_end_time desc
	</select>
	<select id="getAllAssessTemplate" resultType="com.enrising.ctsc.assess.api.vo.AssessTemplateVo">
		SELECT
		t.*,
		n.org_name as companyName
		FROM  (
			SELECT
				a.ID,
				a.status,
				a.template_name,
				a.period,
				a.template_start_time,
				a.template_end_time,
				a.create_by,
				a.send_time,
				c.company_id
			FROM
				assess_template  a
			LEFT JOIN assess_template_target b on a.id = b.template_id
			LEFT JOIN assess_template_target_object c on c.template_target_id = b.id
			WHERE
			a.del_flag = '0'
			AND a.send_status = '1'
				<if test="bo.year!=null and bo.year!=''">
					and YEAR(template_start_time) = #{bo.year}
				</if>
				<if test="bo.companyId != null">
					and c.company_id = #{bo.companyId}
				</if>
				<if test="bo.deptId != null">
				and c.dept_id = #{bo.deptId}
				</if>
			GROUP BY a.id
			ORDER BY a.send_time DESC
		) t
		left JOIN  rmp.sys_user m on m.id = t.create_by
		LEFT JOIN rmp.sys_organizations n on n.id = t.company_id

	</select>
	<select id="getProvinceAssessTemplate" resultType="com.enrising.ctsc.assess.api.vo.AssessTemplateVo">
		SELECT
		  a.ID
		FROM
		assess_template  a
		WHERE
		a.del_flag = '0'
		AND a.send_status = '1'
		<if test="bo.year!=null and bo.year!=''">
			and to_char(a.template_start_time, 'yyyy') = #{bo.year}
		</if>
	</select>
	<select id="getSysDeptList" resultType="java.util.HashMap">
		SELECT
		    id,
			org_name,
			parent_company_no
		FROM rmp.sys_organizations
		<where>
		    del_flag = 0
		  AND (
				parent_company_no = '0000000000'
				OR parent_company_no = '2600000000'
				<if test="objectType == '2'">
					OR parent_company_no in (
						  SELECT id from rmp.sys_organizations WHERE parent_company_no = '2600000000')
					  )
				</if>
				<if test="name != null and name != ''">
					AND org_name like concat('%',#{name},'%')
				</if>
			)
			</where>
	</select>
	<select id="getSysDeptById" resultType="java.util.HashMap">
		SELECT
			id,
			org_name,
			parent_company_no
		FROM rmp.sys_organizations
		WHERE
			id = #{id}
	</select>
	<select id="getSysDeptChildrenList" resultType="java.util.HashMap">
		SELECT
			id,
			org_name,
			parent_company_no
		FROM rmp.sys_organizations
		WHERE
			del_flag = 0
			AND parent_company_no = #{parentId}
	</select>
</mapper>
