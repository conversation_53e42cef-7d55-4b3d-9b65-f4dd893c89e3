package com.enrising.ctsc.discharge.api.query;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 碳排放能源转换因子表查询
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DischargeEnergyFactorOpenBo  {

	/**
	 * 能源类型名称
	 */
	@NotBlank(message = "查询条件：能源类型名称不能为空！")
	private String typeName;

	/**
	 * 年份
	 */
	@NotBlank(message = "查询条件：年份不能为空！")
	private String year;
}
