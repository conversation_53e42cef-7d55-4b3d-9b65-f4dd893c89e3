package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.carbon.common.entity.User;
import com.enrising.ctsc.discharge.api.enums.EnergyType;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.JwtUtils;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.discharge.api.bo.DischargeDataOilBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataOil;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyType;
import com.enrising.ctsc.discharge.api.vo.DischargeDataOilVo;
import com.enrising.ctsc.discharge.mapper.DischargeDataOilMapper;
import com.enrising.ctsc.discharge.service.DischargeDataOilService;
import com.enrising.ctsc.discharge.service.DischargeEnergyCoefficientService;
import com.enrising.ctsc.discharge.service.DischargeEnergyFactorService;
import com.enrising.ctsc.discharge.service.DischargeEnergyTypeService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 碳排放数据填报表（油）
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Service
@AllArgsConstructor
public class DischargeDataOilServiceImpl extends ServiceImpl<DischargeDataOilMapper, DischargeDataOil> implements DischargeDataOilService {

	private final DischargeEnergyCoefficientService dischargeEnergyCoefficientService;

	private final DischargeEnergyFactorService dischargeEnergyFactorService;

	private final DischargeEnergyTypeService dischargeEnergyTypeService;

//	private final RemoteUserService remoteUserService;

	@Override
	public Page<DischargeDataOilVo> getOilListPage(QueryPage<DischargeDataOilBo> queryPage) {
		LambdaQueryWrapper<DischargeDataOil> qw = Wrappers.lambdaQuery();
		DischargeDataOilBo dischargeDataOilBo = queryPage.getModel();
		dischargeDataOilBo.setSize(queryPage.getSize());
		dischargeDataOilBo.setOffset((queryPage.getCurrent() - 1 ) * queryPage.getSize());
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			Page<DischargeDataOil> dischargeDataOilPage = new Page<>(queryPage.getCurrent(),
					queryPage.getSize(), true);
			Page<DischargeDataOilVo> dischargeDataOilVoPage = new Page<>();
			BeanUtils.copyProperties(dischargeDataOilPage, dischargeDataOilVoPage);
			if (ObjectUtil.isNotEmpty(dischargeDataOilBo)) {
				if (ObjectUtil.isEmpty(dischargeDataOilBo.getCompanyId())) {
					dischargeDataOilBo.setCompanyId(0L);
				}
				if (ObjectUtil.isNotEmpty(dischargeDataOilBo.getReportTime())) {
					//查询条件 填报时间
					qw.eq(DischargeDataOil::getReportTime, dischargeDataOilBo.getReportTime());
					dischargeDataOilBo.setQueryStartTime(DateUtil.beginOfMonth(dischargeDataOilBo.getReportTime()));
					dischargeDataOilBo.setQueryEndTime(DateUtil.endOfMonth(dischargeDataOilBo.getReportTime()));
				} else {
					//查询条件 数据年份
					String startYear = dischargeDataOilBo.getDataYear() + "-01-01 0:00:00";
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
					dateStart = sdf.parse(startYear);
					dateEnd = DateUtil.endOfYear(dateStart);
					dischargeDataOilBo.setQueryStartTime(dateStart);
					dischargeDataOilBo.setQueryEndTime(dateEnd);
					qw.ge(DischargeDataOil::getReportTime, dateStart)
							.le(DischargeDataOil::getReportTime, dateEnd);
				}
				//查询条件，公司
				qw.eq(dischargeDataOilBo.getCompanyId() != 0, DischargeDataOil::getCompanyId,
						dischargeDataOilBo.getCompanyId());
			}
			dischargeDataOilVoPage.setTotal(this.count(qw));
			dischargeDataOilVoPage.setRecords(getCompanyDataList(dischargeDataOilBo));
			return dischargeDataOilVoPage;
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public List<DischargeDataOilVo> getOilListToExcel(DischargeDataOilBo dischargeDataOilBo) {
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			if (ObjectUtil.isNotEmpty(dischargeDataOilBo)) {
				if (ObjectUtil.isEmpty(dischargeDataOilBo.getCompanyId())) {
					dischargeDataOilBo.setCompanyId(0L);
				}
				if (ObjectUtil.isNotEmpty(dischargeDataOilBo.getReportTime())) {
					dischargeDataOilBo.setQueryStartTime(DateUtil.beginOfMonth(dischargeDataOilBo.getReportTime()));
					dischargeDataOilBo.setQueryEndTime(DateUtil.endOfMonth(dischargeDataOilBo.getReportTime()));
				} else {
					//查询条件 数据年份
					String startYear = dischargeDataOilBo.getDataYear() + "-01-01 0:00:00";
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
					dateStart = sdf.parse(startYear);
					dateEnd = DateUtil.endOfYear(dateStart);
					dischargeDataOilBo.setQueryStartTime(dateStart);
					dischargeDataOilBo.setQueryEndTime(dateEnd);
				}
			}
			return getCompanyDataList(dischargeDataOilBo);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public List<DischargeDataOilVo> getDataList(Integer dataYear, Long companyId) {
		if (ObjectUtil.isEmpty(dataYear)) {
			throw new BusinessException("数据年份不能为空！");
		}
		if (ObjectUtil.isEmpty(companyId)) {
//			companyId = remoteUserService.getCityDeptId();
			companyId = JwtUtils.getCurrentUserCompanyId();
		}
		String startYear = dataYear + "-01-01 0:00:00";
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
		//sdf将字符串转化成java.util.Date
		Date dateStart = null;
		Date dateEnd = null;
		try {
			dateStart = sdf.parse(startYear);
			dateEnd = DateUtil.endOfYear(dateStart);
			return this.countCompanyData(dateStart, dateEnd, companyId);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}


	@Override
	public List<DischargeDataOilVo> getDataListByDate(Date dateStart, Date dateEnd, Long companyId) {
		if (ObjectUtil.isEmpty(companyId)) {
//			companyId = remoteUserService.getCityDeptId();
			companyId = JwtUtils.getCurrentUserCompanyId();
		}
		return this.countCompanyData(dateStart, dateEnd, companyId);
	}

	@Override
	public DischargeDataOilVo detail(Long id) {
		if (ObjectUtil.isEmpty(id)) {
			throw new BusinessException("查询参数不能为空");
		}
		DischargeDataOil dischargeDataOil = baseMapper.selectById(id);
		if (ObjectUtil.isEmpty(dischargeDataOil)) {
			return null;
		}
		DischargeDataOilVo dischargeDataOilVo = getEntityToVo(dischargeDataOil);
		return  dischargeDataOilVo;
	}

	@Override
	public String add(DischargeDataOilBo bo) {
		DischargeDataOil entity = new DischargeDataOil();
		BeanUtils.copyProperties(bo, entity);
		if (ObjectUtil.isEmpty(entity.getCompanyId())) {
//		entity.setCompanyId(remoteUserService.getCityDeptId());
			entity.setCompanyId(JwtUtils.getCurrentUserCompanyId());
		}
		//查询已有数据是否重复
		List<DischargeDataOil> dischargeDataOilList = list(
				new LambdaQueryWrapper<DischargeDataOil>()
						.eq(DischargeDataOil::getCompanyId, entity.getCompanyId())
						.eq(DischargeDataOil::getReportTime, entity.getReportTime())
						.select(DischargeDataOil::getId));
		if (CollectionUtil.isNotEmpty(dischargeDataOilList) && dischargeDataOilList.size() > 0) {
			return "所选月份数据已填报";
		}
		if (baseMapper.insert(entity) == 1) {
			return "";
		} else {
			return "保存失败";
		}
	}

	@Override
	public void edit(DischargeDataOilBo bo) {
		DischargeDataOil entity = new DischargeDataOil();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.updateById(entity);
	}

	@Override
	public void del(Long id) {
		baseMapper.deleteById(id);
	}

	@Override
	public List<DischargeDataOilVo> countCompanyData(Date dateStart, Date dateEnd, Long companyId) {
		DischargeDataOilBo queryBo = new DischargeDataOilBo();
		queryBo.setCompanyId(companyId);
		queryBo.setQueryStartTime(dateStart);
		queryBo.setQueryEndTime(dateEnd);
		return getCompanyDataList(queryBo);
	}

	private List<DischargeDataOilVo> getCompanyDataList(DischargeDataOilBo dischargeDataOilBo) {
		List<DischargeDataOilVo> dischargeDataOilVoList;
		if (ObjectUtil.isEmpty(dischargeDataOilBo) || ObjectUtil.isEmpty(dischargeDataOilBo.getCompanyId()) ||
				dischargeDataOilBo.getCompanyId().equals(0L)) {
			dischargeDataOilVoList = baseMapper.countCompanyData(dischargeDataOilBo);
		} else {
			dischargeDataOilVoList = baseMapper.getCompanyDataList(dischargeDataOilBo);
		}
		return dischargeDataOilVoList;
	}

	private DischargeDataOil[] iniDataOilArray(int count) {
		try {
			DischargeDataOil[] dischargeDataOils = new DischargeDataOil[count];
			for (int i = 0; i< dischargeDataOils.length; i++) {
				dischargeDataOils[i] = new DischargeDataOil();
				Field[] fields = dischargeDataOils[i].getClass().getDeclaredFields();
				for (Field field: fields) {
					if (field.getType().toString().contains("BigDecimal")) {
						field.setAccessible(true);
						field.set(dischargeDataOils[i], BigDecimal.valueOf(0));
					}
				}
			}
			return dischargeDataOils;
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		return null;
	}

	private DischargeEnergyType getTypeByName(List<DischargeEnergyType> dischargeEnergyTypeList, String typeName) {
		List<DischargeEnergyType> dischargeEnergyTypes = dischargeEnergyTypeList.stream().filter(item -> {
			return typeName.equals(item.getSecondName());
		}).collect(Collectors.toList());
		if (CollectionUtil.isNotEmpty(dischargeEnergyTypes)) {
			return dischargeEnergyTypes.get(0);
		}
		return null;
	}

	private DischargeDataOilVo getEntityToVo(DischargeDataOil dischargeDataOil) {
		DischargeDataOilVo dischargeDataOilVo = new DischargeDataOilVo();
		BeanUtils.copyProperties(dischargeDataOil, dischargeDataOilVo);
		Calendar cal = Calendar.getInstance();
		cal.setTime(dischargeDataOilVo.getReportTime());
		Integer year=cal.get(Calendar.YEAR);//获取年
		Integer month = cal.get(Calendar.MONTH) + 1;//获取月（月份从0开始，需要加一）
		dischargeDataOilVo.setDataMonth(month + "月");
		dischargeDataOilVo.setDataYear(year + "年");
		List<DischargeEnergyType> dischargeEnergyTypeList = dischargeEnergyTypeService.list(Wrappers.<DischargeEnergyType>lambdaQuery()
				.eq(DischargeEnergyType::getEnergyType, "4")
		);
		DischargeEnergyType dischargeEnergyTypeGasoline = getTypeByName(dischargeEnergyTypeList, "汽油");
		DischargeEnergyType dischargeEnergyTypeDiesel = getTypeByName(dischargeEnergyTypeList, "柴油");
		DischargeEnergyType dischargeEnergyTypeCrude = getTypeByName(dischargeEnergyTypeList, "原油");
		DischargeEnergyType dischargeEnergyTypeFuel = getTypeByName(dischargeEnergyTypeList, "燃料油");
		DischargeEnergyType dischargeEnergyTypeKerosene = getTypeByName(dischargeEnergyTypeList, "煤油");
		BigDecimal carbonGasoline = new BigDecimal(0);
		BigDecimal carbonDiesel = new BigDecimal(0);
		BigDecimal carbonCrude = new BigDecimal(0);
		BigDecimal carbonFuel = new BigDecimal(0);
		BigDecimal carbonKerosene = new BigDecimal(0);
		BigDecimal consumptionGasoline = new BigDecimal(0);
		BigDecimal consumptionDiesel = new BigDecimal(0);
		BigDecimal consumptionCrude = new BigDecimal(0);
		BigDecimal consumptionFuel = new BigDecimal(0);
		BigDecimal consumptionKerosene = new BigDecimal(0);
		if ( ObjectUtil.isEmpty(dischargeEnergyTypeGasoline) || !dischargeEnergyTypeGasoline.getStatus().equals("2")) {
			carbonGasoline = dischargeDataOilVo.getGasoline().multiply(dischargeEnergyFactorService.
							getFactorByTime(EnergyType.GASOLINE.getId(), dischargeDataOilVo.getReportTime())).
					divide(new BigDecimal(1000));
			consumptionGasoline = dischargeDataOilVo.getGasoline().multiply(dischargeEnergyCoefficientService.
							getCoefficientByTime(EnergyType.GASOLINE.getId(), dischargeDataOilVo.getReportTime())).
					multiply(dischargeEnergyTypeService.getEnergyDensity(EnergyType.GASOLINE.getId())).
					divide(new BigDecimal(1000));
		}
		dischargeDataOilVo.setCarbonGasoline(carbonGasoline.setScale(4, RoundingMode.HALF_UP));
		if ( ObjectUtil.isEmpty(dischargeEnergyTypeDiesel) || !dischargeEnergyTypeDiesel.getStatus().equals("2")) {
			carbonDiesel = dischargeDataOilVo.getDiesel().multiply(dischargeEnergyFactorService.
							getFactorByTime(EnergyType.DIESEL.getId(), dischargeDataOilVo.getReportTime())).
					divide(new BigDecimal(1000));
			consumptionDiesel = dischargeDataOilVo.getDiesel().multiply(dischargeEnergyCoefficientService.
							getCoefficientByTime(EnergyType.DIESEL.getId(), dischargeDataOilVo.getReportTime())).
					multiply(dischargeEnergyTypeService.getEnergyDensity(EnergyType.DIESEL.getId())).
					divide(new BigDecimal(1000));
		}
		dischargeDataOilVo.setCarbonDiesel(carbonDiesel.setScale(4, RoundingMode.HALF_UP));
		if ( ObjectUtil.isEmpty(dischargeEnergyTypeCrude) || !dischargeEnergyTypeCrude.getStatus().equals("2")) {
			carbonCrude = dischargeDataOilVo.getCrude().multiply(dischargeEnergyFactorService.
							getFactorByTime(EnergyType.CRUDE.getId(), dischargeDataOilVo.getReportTime())).
					divide(new BigDecimal(1000));
			consumptionCrude = dischargeDataOilVo.getCrude().multiply(dischargeEnergyCoefficientService.
							getCoefficientByTime(EnergyType.CRUDE.getId(), dischargeDataOilVo.getReportTime())).
					multiply(dischargeEnergyTypeService.getEnergyDensity(EnergyType.CRUDE.getId())).
					divide(new BigDecimal(1000));
		}
		dischargeDataOilVo.setCarbonCrude(carbonCrude.setScale(4, RoundingMode.HALF_UP));
		if ( ObjectUtil.isEmpty(dischargeEnergyTypeFuel) || !dischargeEnergyTypeFuel.getStatus().equals("2")) {
			carbonFuel = dischargeDataOilVo.getFuel().multiply(dischargeEnergyFactorService.
							getFactorByTime(EnergyType.FUEL.getId(), dischargeDataOilVo.getReportTime())).
					divide(new BigDecimal(1000));
			consumptionFuel = dischargeDataOilVo.getFuel().multiply(dischargeEnergyCoefficientService.
							getCoefficientByTime(EnergyType.FUEL.getId(), dischargeDataOilVo.getReportTime())).
					multiply(dischargeEnergyTypeService.getEnergyDensity(EnergyType.FUEL.getId())).
					divide(new BigDecimal(1000));
		}
		dischargeDataOilVo.setCarbonFuel(carbonFuel.setScale(4, RoundingMode.HALF_UP));
		if ( ObjectUtil.isEmpty(dischargeEnergyTypeKerosene) || !dischargeEnergyTypeKerosene.getStatus().equals("2")) {
			carbonKerosene = dischargeDataOilVo.getKerosene().multiply(dischargeEnergyFactorService.
							getFactorByTime(EnergyType.KEROSENE.getId(), dischargeDataOilVo.getReportTime())).
					divide(new BigDecimal(1000));
			consumptionKerosene = dischargeDataOilVo.getKerosene().multiply(dischargeEnergyCoefficientService.
							getCoefficientByTime(EnergyType.KEROSENE.getId(), dischargeDataOilVo.getReportTime())).
					multiply(dischargeEnergyTypeService.getEnergyDensity(EnergyType.KEROSENE.getId())).
					divide(new BigDecimal(1000));
		}
		dischargeDataOilVo.setCarbonKerosene(carbonKerosene.setScale(4, RoundingMode.HALF_UP));
		dischargeDataOilVo.setCarbonEmissions(carbonGasoline.add(carbonDiesel).add(carbonCrude).add(carbonFuel).
				add(carbonKerosene).setScale(4, RoundingMode.HALF_UP));
		dischargeDataOilVo.setEnergyConsumption(consumptionGasoline.add(consumptionDiesel).add(consumptionCrude).
				add(consumptionFuel).add(consumptionKerosene).setScale(4, RoundingMode.HALF_UP));
		return dischargeDataOilVo;
	}
}
