package com.enrising.ctsc.assess.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.assess.api.entity.AssessTaskReportAttachment;
import com.enrising.ctsc.assess.api.query.AssessTaskReportAttachmentQuery;
import com.enrising.ctsc.assess.api.vo.AssessTaskReportAttachmentVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 考核任务上报附件
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Mapper
public interface AssessTaskReportAttachmentMapper extends BaseMapper<AssessTaskReportAttachment> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
	IPage<AssessTaskReportAttachmentVo> findList(Page<AssessTaskReportAttachmentVo> page, @Param(Constants.WRAPPER) Wrapper<AssessTaskReportAttachmentQuery> wrapper);

	/**
	 * 详情查询
	 *
	 * @param wrapper 条件
	 * @return 结果
	 */
	AssessTaskReportAttachmentVo detail(@Param(Constants.WRAPPER) Wrapper<AssessTaskReportAttachmentQuery> wrapper);
}