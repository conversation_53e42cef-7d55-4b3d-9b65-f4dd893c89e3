package com.enrising.ctsc.discharge.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.carbon.common.entity.User;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.*;
import com.enrising.ctsc.discharge.api.bo.DischargeDataEnergyBo;
import com.enrising.ctsc.discharge.api.bo.EnergyConsumptionReportToTheGroupBo;
import com.enrising.ctsc.discharge.api.bo.ReportEnergyDataBo;
import com.enrising.ctsc.discharge.api.bo.ReportEnergyToGroupBo;
import com.enrising.ctsc.discharge.api.entity.*;
import com.enrising.ctsc.discharge.api.enums.*;
import com.enrising.ctsc.discharge.api.query.DischargeDataEnergyQuery;
import com.enrising.ctsc.discharge.api.query.DischargeJituanEnergySyncLogQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyUpdateLogVo;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorVo;
import com.enrising.ctsc.discharge.api.vo.SysDeptVO;
import com.enrising.ctsc.discharge.mapper.DischargeDataEnergyMapper;
import com.enrising.ctsc.discharge.mapper.DischargeDataTotalMapper;
import com.enrising.ctsc.discharge.service.*;
import com.google.common.collect.Lists;
import com.sccl.common.constant.HttpStatusConstants;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 碳排放数据填报表（能源）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DischargeDataEnergyServiceImpl extends ServiceImpl<DischargeDataEnergyMapper, DischargeDataEnergy> implements DischargeDataEnergyService {

    private final DischargeDataElectricService dischargeDataElectricService;
    private final DischargeDataGasService dischargeDataGasService;
    private final DischargeDataOilService dischargeDataOilService;
    private final DischargeDataWaterService dischargeDataWaterService;
    private final DischargeDataThermalService dischargeDataThermalService;
    private final DischargeDataCoalService dischargeDataCoalService;
    private final DischargeDataEnergyMapper dischargeDataEnergyMapper;
//    private final RemoteUserService userService;
//    private final SysDeptMapper sysDeptMapper;
    private final DischargeDataEnergyUpdateRecordService dischargeDataEnergyUpdateRecordService;
    private final DischargeDataEnergyUpdateLogService dischargeDataEnergyUpdateLogService;

    private final DischargeDataTotalMapper dischargeDataTotalMapper;

    private final EnergyCountDataService energyCountDataService;

    private final DischargeDataEnergyCalculateService dischargeDataEnergyCalculateService;
    /**
     * 集团同步能耗
     */
    @Value("${dcoss.jituan.syncPowerDataInfos:http://10.141.134.30:12500/serviceAgent/rest/api/ctcarbon/report/syncEnergyDataInfos}")
    public String syncPowerDataInfos;
    @Value("${platform.mq.x-app-id:4cac2ff7deec1ed596d9d20e1f9e9723}")
    private String xAppId;
    @Value("${platform.mq.x-app-key:d24eb905ca5f39c8d6db55c416063228}")
    private String xAppKey;
    @Value("${platform.mq.x-ctg-version:V1.0.00}")
    private String xCtgVersion;
    @Value("${platform.mq.client-id:CTSCSTPT20230607}")
    private String clientId;



    @Override
    public List<DischargeDataEnergyVo> getDataList(DischargeDataEnergyBo dischargeDataEnergyBo) {
        Date reportTime = dischargeDataEnergyBo.getReportTime();
        if (ObjectUtil.isEmpty(reportTime)) {
            throw new BusinessException("数据时间不能为空！");
        }
        Long companyId;
        if (ObjectUtil.isNotEmpty(dischargeDataEnergyBo.getCompanyId())) {
            companyId = dischargeDataEnergyBo.getCompanyId();
        } else {
//            SysUser sysUser = JwtUtils.getUser();
//            companyId = sysUser.getDeptId();
            companyId = JwtUtils.getCurrentUserCompanyId();
        }
        return baseMapper.getDataList(companyId, reportTime);
    }

    @Override
    public List<DischargeDataEnergyVo> getDataUpdateList(DischargeDataEnergyBo dischargeDataEnergyBo) {
        List<DischargeDataEnergyVo> dataList = getDataList(dischargeDataEnergyBo);
        // 查询当前公司当前上报时间的修改记录
        List<DischargeDataEnergyUpdateLogVo> dataUpdateList = dischargeDataEnergyUpdateRecordService.getDataUpdateList(dischargeDataEnergyBo);
        if(CollectionUtil.isEmpty(dataUpdateList)){
            return dataList;
        }
        dataUpdateList.forEach(node->{
            List<DischargeDataEnergyVo> collect = dataList.stream().filter(item -> {
                return item.getId().equals(node.getEnergyId());
            }).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(collect)){
                DischargeDataEnergyVo dischargeDataEnergyVo = collect.get(0);
                switch (node.getNum()) {
                    case 1:
                        dischargeDataEnergyVo.setGroupEdit(true);
                        break;
                    case 2:
                        dischargeDataEnergyVo.setStockEdit(true);
                        break;
                    case 3:
                        dischargeDataEnergyVo.setLargeEdit(true);
                        break;
                    case 4:
                        dischargeDataEnergyVo.setMediumEdit(true);
                        break;
                    case 5:
                        dischargeDataEnergyVo.setMobileEdit(true);
                        break;
                    default:
                        break;
                }
            }
        });
        return dataList;
    }

    @Override
    public List<DischargeDataEnergyVo> getAllDataList(DischargeDataEnergyQuery query) {
        if (ObjectUtil.isEmpty(query.getReportTime())) {
            throw new BusinessException("数据时间不能为空！");
        }
        return dischargeDataEnergyMapper.queryDataList(query);
    }

    @Override
    public List<DischargeDataEnergyVo> getReportList(DischargeDataEnergyQuery query) {
        if (ObjectUtil.isEmpty(query.getReportTime())) {
            throw new BusinessException("数据时间不能为空！");
        }
        return dischargeDataEnergyMapper.getReportList(query);
    }

    @Override
    public void energyConsumptionReportToTheGroup(EnergyConsumptionReportToTheGroupBo bo) {
        // 每月1号到10可以上报
//        DateTime now = DateUtil.date();
//        int dayOfMonth = now.dayOfMonth();
//        log.info("今天日期:{}号", dayOfMonth);
//        if (dayOfMonth < 1 || dayOfMonth > 10) {
//            throw new BusinessException("请于每月1-10号上报数据");
//        }

        // 1.获取省公司/各市公司列表
//        SysDeptVo sc = sysDeptMapper.selectOne(Wrappers.<SysDept>lambdaQuery().eq(SysDept::getParentId, 0));
//        if (sc == null) {
//            throw new BusinessException("未找到省公司信息");
//        }
//        List<SysDept> cityList = sysDeptMapper.selectList(Wrappers.<SysDept>lambdaQuery()
//                .select(SysDept::getId, SysDept::getName, SysDept::getAreaCode, SysDept::getBodyCode)
//                .eq(SysDept::getParentId, sc.getId())
//                .isNotNull(SysDept::getBodyCode)
//                .orderByAsc(SysDept::getAreaCode)
//        );
        List<SysDeptVO> cityList = dischargeDataTotalMapper.getSysDeptList();
        // 2.获取每个公司的编码/名称
        for (SysDeptVO sysDept : cityList) {
            // 封装提交的数据
            ReportEnergyToGroupBo groupBo = new ReportEnergyToGroupBo();
            // 外层封装   暂时用provinceCode替换bodyCode
            groupBo.setSubjectCode(sysDept.getProvinceCode());
            groupBo.setMsgId(sysDept.getProvinceCode() + getSerialNbr());
            /* 获取当前机构的能耗数据列表 */
            DischargeDataEnergyQuery query = new DischargeDataEnergyQuery();
            query.setReportTime(bo.getReportTime());
            query.setCompanyId(sysDept.getId());

            List<DischargeDataEnergyVo> allDataList;
            if (DischargeDataTotalEnum.EnergyCountType.原始数据.getValue().equals(bo.getCountType())) {
                //原始数据
                allDataList = getAllDataList(query);
            } else {
                //按规则计算数据
                DischargeDataEnergyCalculate dischargeDataEnergyCalculate = new DischargeDataEnergyCalculate();
                dischargeDataEnergyCalculate.setCompanyId(query.getCompanyId());
                dischargeDataEnergyCalculate.setReportTime(query.getReportTime());
                allDataList = dischargeDataEnergyCalculateService.getAllDataList(dischargeDataEnergyCalculate);
            }
            log.info("填报数据条数:{}", allDataList.size());
            List<ReportEnergyDataBo> dataInfos = Lists.newArrayList();
            for (DischargeDataEnergyVo energyVo : allDataList) {
//                String energyCode = energyVo.getIndicatorName().split("、")[0];
                String energyCode = energyVo.getIndicatorCode();
                // 1：集团存续 2：股份上市 3：大型数据中心 4：中小型数据中心 5：移动业务
                for (int i = 1; i <= 5; i++) {
                    ReportEnergyDataBo dataBo = new ReportEnergyDataBo();
                    dataBo.setProvinceCode(sysDept.getProvinceCode().substring(0, 2));
                    dataBo.setCityCode(sysDept.getProvinceCode());
                    dataBo.setCityName(BodyCodeInfo.getName(dataBo.getCityCode()));
                    dataBo.setStatisPeriod(DateUtils.parseDateToStr("YYYYMM", bo.getReportTime()));
                    dataBo.setGroupType(String.valueOf(i));
                    dataBo.setEnergyCode(energyCode);
                    if (i == 1) {
                        dataBo.setEnergyValue(energyVo.getGroupData());
                    } else if (i == 2) {
                        dataBo.setEnergyValue(energyVo.getStockData());
                    } else if (i == 3) {
                        dataBo.setEnergyValue(energyVo.getLargeData());
                    } else if (i == 4) {
                        dataBo.setEnergyValue(energyVo.getMediumData());
                    } else {
                        dataBo.setEnergyValue(energyVo.getMobileData());
                    }
                    if (StrUtil.isBlank(dataBo.getEnergyValue()) || "/".equals(dataBo.getEnergyValue())) {
                        dataBo.setEnergyValue("0");
                    }
                    dataInfos.add(dataBo);
                }
            }
            if (CollectionUtil.isNotEmpty(dataInfos)) {
                groupBo.setDataInfos(dataInfos);
                log.info("提交同步能耗数据:{}", JSONUtil.formatJsonStr(JSONUtil.toJsonStr(groupBo)));
                log.info("msgId:{},subjectCode:{}", groupBo.getMsgId(),groupBo.getSubjectCode());
                HttpRequest request = HttpUtil.createPost(syncPowerDataInfos)
                        .header("X-APP-ID", xAppId)
                        .header("X-APP-KEY", xAppKey)
                        .header("X-CTG-VERSION", xCtgVersion)
                        .body(JSONUtil.toJsonStr(groupBo));
                request.setConnectionTimeout(10000);
                log.info("request:{}", request);
                try {
                    HttpResponse response = request.execute();
                    JSONObject result = JSONUtil.parseObj(response.body());
                    log.info("提交地址地址:{}", syncPowerDataInfos);
                    log.info("提交结果:{}", result);
                    int code = result.getInt("code");
                    if (code == HttpStatusConstants.SUCCESS) {
                        log.info("通过成功");
                    } else if (code == HttpStatusConstants.INCOMPLETE) {
                        throw new BusinessException("请求参数不完整");
                    } else if (code == HttpStatusConstants.INCOMPLETE_INFORMATION) {
                        throw new BusinessException("请求头缺少信息");
                    } else if (code == HttpStatusConstants.ERROR) {
                        throw new BusinessException("集团接口错误");
                    } else {
                        throw new BusinessException("同步失败，请稍后再试");
                    }
                } catch (IORuntimeException e) {
                    throw new BusinessException("同步地址超时：" + syncPowerDataInfos);
                } catch (Exception e) {
                    throw new BusinessException("同步失败：" + e.getMessage());
                }
            }
        }
        // 生成日志
        User user = JwtUtils.getUser();
        Date nowDate = new Date();
        DischargeJituanEnergySyncLog syncLog = new DischargeJituanEnergySyncLogQuery();
        syncLog.setReportDate(bo.getReportTime());
        syncLog.setReportDept(JwtUtils.getCurrentUserCompanyId(user));
        syncLog.setReportUser(user.getId());
        syncLog.setCreateTime(nowDate);
        syncLog.insert();
        log.info("生成日志成功，本次同步的机构：{}", cityList.stream().map(SysDeptVO::getOrgName).collect(Collectors.toList()));
    }

    @Override
    public DischargeDataEnergyVo detail(DischargeDataEnergyQuery query) {
        if (ObjectUtil.isEmpty(query)) {
            throw new BusinessException("查询参数不能为空");
        }
        QueryWrapper<DischargeDataEnergyQuery> wrapper = this.getWrapper(query);
        return baseMapper.detail(wrapper);
    }

    private QueryWrapper<DischargeDataEnergyQuery> getWrapper(DischargeDataEnergyQuery query) {
        QueryWrapper<DischargeDataEnergyQuery> wrapper = new QueryWrapper<>();
        wrapper.eq(query.getId() != null, "t.id", query.getId());
        wrapper.orderByDesc("t.create_time");
        wrapper.eq("t.del_flag", DelFlagEnum.NOT_DELETED.getValue());
        return wrapper;
    }

    /**
     * 批量新增
     *
     * @param dischargeEnergyIndicatorVos 参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveList(List<DischargeEnergyIndicatorVo> dischargeEnergyIndicatorVos) {
        Long companyId;
        if (ObjectUtil.isNotEmpty(dischargeEnergyIndicatorVos.get(0).getCompanyId())) {
            companyId = dischargeEnergyIndicatorVos.get(0).getCompanyId();
        } else {
//            companyId = userService.getCityDeptId();
            companyId = JwtUtils.getCurrentUserCompanyId();
        }
        if (CollectionUtil.isNotEmpty(dischargeEnergyIndicatorVos) && dischargeEnergyIndicatorVos.size() > 0) {
            //删除已保存的数据
            remove(new LambdaQueryWrapper<DischargeDataEnergy>()
                    .eq(DischargeDataEnergy::getCompanyId, companyId)
                    .eq(DischargeDataEnergy::getReportTime, dischargeEnergyIndicatorVos.get(0).getReportTime()));
        }
        if (dischargeEnergyIndicatorVos.get(0).getReportFlag().equals(DischargeDataEnergyReportFlag.FLAG_REPORTED.getValue())) {
            //保存到分类数据表
            this.saveDischargeData(dischargeEnergyIndicatorVos, companyId);
            //保存上报记录
            this.saveReportRecord(companyId, dischargeEnergyIndicatorVos.get(0).getReportTime());
        }
        //保存到能源表
        dischargeEnergyIndicatorVos.forEach(dischargeEnergyIndicatorVo -> {
            DischargeDataEnergy dischargeDataEnergy = new DischargeDataEnergy();
            BeanUtils.copyProperties(dischargeEnergyIndicatorVo, dischargeDataEnergy);
            dischargeDataEnergy.setId(null);
            dischargeDataEnergy.setEnergyIndicatorId(dischargeEnergyIndicatorVo.getId());
            dischargeDataEnergy.setCompanyId(companyId);
            baseMapper.insert(dischargeDataEnergy);
        });
        return true;
    }

    /**
     * 批量新增
     *
     * @param bo 参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateList(DischargeDataEnergyBo bo) {
        bo.getDischargeEnergyIndicatorVos().forEach(dischargeEnergyIndicatorVo -> {
            DischargeDataEnergy dischargeDataEnergy = baseMapper.selectById(dischargeEnergyIndicatorVo.getId());
            dischargeDataEnergy.setGroupData(dischargeEnergyIndicatorVo.getGroupData());
            dischargeDataEnergy.setStockData(dischargeEnergyIndicatorVo.getStockData());
            dischargeDataEnergy.setLargeData(dischargeEnergyIndicatorVo.getLargeData());
            dischargeDataEnergy.setMediumData(dischargeEnergyIndicatorVo.getMediumData());
            dischargeDataEnergy.setMobileData(dischargeEnergyIndicatorVo.getMobileData());
//			DischargeDataEnergy dischargeDataEnergy = new DischargeDataEnergy();
//			BeanUtils.copyProperties(dischargeEnergyIndicatorVo, dischargeDataEnergy);
            baseMapper.updateById(dischargeDataEnergy);
        });
        //更新分类数据
        updateDischargeData(bo.getDischargeEnergyIndicatorVos(), bo.getCompanyId(), bo.getReportTime());

        if(CollectionUtil.isNotEmpty(bo.getDischargeDataEnergyUpdateLogList())){
            DischargeDataEnergyUpdateRecord record = new DischargeDataEnergyUpdateRecord();
            record.setCompanyId(bo.getCompanyId());
            record.setOperateType(DischargeDataEnergyReportFlag.FLAG_UPDATE.getValue());
            record.setReportTime(bo.getReportTime());
            dischargeDataEnergyUpdateRecordService.save(record);
            bo.getDischargeDataEnergyUpdateLogList().forEach(node->{
                node.setRecordId(record.getId());
            });
            dischargeDataEnergyUpdateLogService.saveBatch(bo.getDischargeDataEnergyUpdateLogList());
        }
        return true;
    }

    /**
     * 退回数据
     *
     * @param dischargeDataEnergyBo 参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rejectData(DischargeDataEnergyBo dischargeDataEnergyBo) {
        //删除电数据
        dischargeDataElectricService.remove(Wrappers.<DischargeDataElectric>lambdaQuery()
                .eq(DischargeDataElectric::getCompanyId, dischargeDataEnergyBo.getCompanyId())
                .eq(DischargeDataElectric::getReportTime, dischargeDataEnergyBo.getReportTime()));
        //删除气数据
        dischargeDataGasService.remove(Wrappers.<DischargeDataGas>lambdaQuery()
                .eq(DischargeDataGas::getCompanyId, dischargeDataEnergyBo.getCompanyId())
                .eq(DischargeDataGas::getReportTime, dischargeDataEnergyBo.getReportTime()));
        //删除油数据
        dischargeDataOilService.remove(Wrappers.<DischargeDataOil>lambdaQuery()
                .eq(DischargeDataOil::getCompanyId, dischargeDataEnergyBo.getCompanyId())
                .eq(DischargeDataOil::getReportTime, dischargeDataEnergyBo.getReportTime()));
        //删除水数据
        dischargeDataWaterService.remove(Wrappers.<DischargeDataWater>lambdaQuery()
                .eq(DischargeDataWater::getCompanyId, dischargeDataEnergyBo.getCompanyId())
                .eq(DischargeDataWater::getReportTime, dischargeDataEnergyBo.getReportTime()));
        //删除热力数据
        dischargeDataThermalService.remove(Wrappers.<DischargeDataThermal>lambdaQuery()
                .eq(DischargeDataThermal::getCompanyId, dischargeDataEnergyBo.getCompanyId())
                .eq(DischargeDataThermal::getReportTime, dischargeDataEnergyBo.getReportTime()));
        //删除煤碳数据
        dischargeDataCoalService.remove(Wrappers.<DischargeDataCoal>lambdaQuery()
                .eq(DischargeDataCoal::getCompanyId, dischargeDataEnergyBo.getCompanyId())
                .eq(DischargeDataCoal::getReportTime, dischargeDataEnergyBo.getReportTime()));

        // 删除修改历史数据表
        dischargeDataEnergyUpdateRecordService.remove(Wrappers.<DischargeDataEnergyUpdateRecord>lambdaQuery()
                .eq(DischargeDataEnergyUpdateRecord::getCompanyId, dischargeDataEnergyBo.getCompanyId())
                .eq(DischargeDataEnergyUpdateRecord::getReportTime, dischargeDataEnergyBo.getReportTime()));

        return this.update(new LambdaUpdateWrapper<DischargeDataEnergy>().set(DischargeDataEnergy::getReportFlag,
                        DischargeDataEnergyReportFlag.FLAG_UNREPORTED.getValue())
                .set(StrUtil.isNotBlank(dischargeDataEnergyBo.getReturnReason()), DischargeDataEnergy::getReturnReason,
                        dischargeDataEnergyBo.getReturnReason())
                .eq(DischargeDataEnergy::getCompanyId, dischargeDataEnergyBo.getCompanyId())
                .eq(DischargeDataEnergy::getReportTime, dischargeDataEnergyBo.getReportTime()));
    }

    /**
     * 申请退回数据
     *
     * @param dischargeDataEnergyBo 参数
     */
    @Override
    public boolean applyReturn(DischargeDataEnergyBo dischargeDataEnergyBo) {
        LocalDate currentDate = LocalDate.now();
        // 设置日期为上个月
        LocalDate lastMonthDate = currentDate.minusMonths(1);
        // 获取上个月的天数
        int lastDays = lastMonthDate.lengthOfMonth() + 10;
        long diffDays = DateUtil.betweenDay(dischargeDataEnergyBo.getReportTime(), new Date(), false);
        if (diffDays > lastDays) {
            throw new BusinessException("已超过时间限制，不允许退回！\n如需修改数据请联系省级管理员调整。");
        }
        if (ObjectUtil.isEmpty(dischargeDataEnergyBo.getCompanyId())) {
//            dischargeDataEnergyBo.setCompanyId(userService.getCityDeptId());
            dischargeDataEnergyBo.setCompanyId(JwtUtils.getCurrentUserCompanyId());
        }
        // 删除修改历史数据表
        dischargeDataEnergyUpdateRecordService.remove(Wrappers.<DischargeDataEnergyUpdateRecord>lambdaQuery()
                .eq(DischargeDataEnergyUpdateRecord::getCompanyId, dischargeDataEnergyBo.getCompanyId())
                .eq(DischargeDataEnergyUpdateRecord::getReportTime, dischargeDataEnergyBo.getReportTime()));
        return this.update(new LambdaUpdateWrapper<DischargeDataEnergy>().set(DischargeDataEnergy::getReportFlag,
                        DischargeDataEnergyReportFlag.FLAG_APPLY_RETURN.getValue())
                .set(StrUtil.isNotBlank(dischargeDataEnergyBo.getApplyReason()), DischargeDataEnergy::getApplyReason,
                        dischargeDataEnergyBo.getApplyReason())
                .eq(DischargeDataEnergy::getCompanyId, dischargeDataEnergyBo.getCompanyId())
                .eq(DischargeDataEnergy::getReportTime, dischargeDataEnergyBo.getReportTime()));
    }

    private void saveDischargeData(List<DischargeEnergyIndicatorVo> dischargeEnergyIndicatorVos, Long companyId) {
        //保存电数据
        DischargeDataElectric dischargeDataElectric = new DischargeDataElectric();
        dischargeDataElectric.setCompanyId(companyId);
        dischargeDataElectric.setReportTime(dischargeEnergyIndicatorVos.get(0).getReportTime());
        dischargeDataElectric.setOutsourcingGreenPower(BigDecimal.valueOf(0));
        //自有新能源发电数据，已经删除
//		dischargeDataElectric.setOwnGreenPower(dischargeEnergyIndicatorVos.get(21).getStockData());
        dischargeDataElectric.setOwnGreenPower(BigDecimal.valueOf(0));
        DischargeEnergyIndicatorVo electricIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "耗电量（总）");
        if (ObjectUtil.isNotEmpty(electricIndicator)) {
            dischargeDataElectric.setOutsourcingThermalPower(getSum(electricIndicator.getGroupData(), electricIndicator.getStockData()));
        } else {
            dischargeDataElectric.setOutsourcingThermalPower(BigDecimal.valueOf(0));
        }
        dischargeDataElectricService.remove(Wrappers.<DischargeDataElectric>lambdaQuery()
                .eq(DischargeDataElectric::getCompanyId, companyId)
                .eq(DischargeDataElectric::getReportTime, dischargeDataElectric.getReportTime()));
        dischargeDataElectricService.save(dischargeDataElectric);
        //保存气数据
        DischargeDataGas dischargeDataGas = new DischargeDataGas();
        dischargeDataGas.setCompanyId(companyId);
        dischargeDataGas.setReportTime(dischargeEnergyIndicatorVos.get(0).getReportTime());
//		dischargeDataGas.setNg(dischargeEnergyIndicatorVos.get(32).getStockData());
        DischargeEnergyIndicatorVo ngIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "天然气消耗量");
        if (ObjectUtil.isNotEmpty(ngIndicator)) {
            dischargeDataGas.setNg(getSum(ngIndicator.getGroupData(), ngIndicator.getStockData()));
        } else {
            dischargeDataGas.setNg(BigDecimal.valueOf(0));
        }
//		dischargeDataGas.setLpg(dischargeEnergyIndicatorVos.get(31).getStockData().multiply(BigDecimal.valueOf(1000)));
        DischargeEnergyIndicatorVo lpgIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "液化石油气");
        if (ObjectUtil.isNotEmpty(lpgIndicator)) {
            dischargeDataGas.setLpg(getSum(lpgIndicator.getGroupData(), lpgIndicator.getStockData()).multiply(BigDecimal.valueOf(1000)));
        } else {
            dischargeDataGas.setLpg(BigDecimal.valueOf(0));
        }
        dischargeDataGasService.remove(Wrappers.<DischargeDataGas>lambdaQuery()
                .eq(DischargeDataGas::getCompanyId, companyId)
                .eq(DischargeDataGas::getReportTime, dischargeDataGas.getReportTime()));
        if (ObjectUtil.isNotEmpty(dischargeDataGas.getLpg()) || ObjectUtil.isNotEmpty(dischargeDataGas.getNg())) {
            dischargeDataGasService.save(dischargeDataGas);
        }
        //保存油数据
        DischargeDataOil dischargeDataOil = new DischargeDataOil();
        dischargeDataOil.setCompanyId(companyId);
        dischargeDataOil.setReportTime(dischargeEnergyIndicatorVos.get(0).getReportTime());
//		dischargeDataOil.setGasoline(dischargeEnergyIndicatorVos.get(23).getStockData());
        DischargeEnergyIndicatorVo gasolineIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "汽油消耗量");
        if (ObjectUtil.isNotEmpty(gasolineIndicator)) {
            dischargeDataOil.setGasoline(getSum(gasolineIndicator.getGroupData(), gasolineIndicator.getStockData()));
        } else {
            dischargeDataOil.setGasoline(BigDecimal.valueOf(0));
        }
//		dischargeDataOil.setDiesel(dischargeEnergyIndicatorVos.get(27).getStockData());
        DischargeEnergyIndicatorVo dieselIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "柴油消耗量");
        if (ObjectUtil.isNotEmpty(dieselIndicator)) {
            dischargeDataOil.setDiesel(getSum(dieselIndicator.getGroupData(), dieselIndicator.getStockData()));
        } else {
            dischargeDataOil.setDiesel(BigDecimal.valueOf(0));
        }
//		dischargeDataOil.setCrude(dischargeEnergyIndicatorVos.get(22).getStockData());
        DischargeEnergyIndicatorVo crudeIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "原油");
        if (ObjectUtil.isNotEmpty(crudeIndicator)) {
            dischargeDataOil.setCrude(getSum(crudeIndicator.getGroupData(), crudeIndicator.getStockData()));
        } else {
            dischargeDataOil.setCrude(BigDecimal.valueOf(0));
        }
//		dischargeDataOil.setKerosene(dischargeEnergyIndicatorVos.get(26).getStockData());
        DischargeEnergyIndicatorVo keroseneIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "煤油");
        if (ObjectUtil.isNotEmpty(keroseneIndicator)) {
            dischargeDataOil.setKerosene(getSum(keroseneIndicator.getGroupData(), keroseneIndicator.getStockData()));
        } else {
            dischargeDataOil.setKerosene(BigDecimal.valueOf(0));
        }
//		dischargeDataOil.setFuel(dischargeEnergyIndicatorVos.get(30).getStockData());
        DischargeEnergyIndicatorVo fuelIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "燃料油");
        if (ObjectUtil.isNotEmpty(fuelIndicator)) {
            dischargeDataOil.setFuel(getSum(fuelIndicator.getGroupData(), fuelIndicator.getStockData()));
        } else {
            dischargeDataOil.setFuel(BigDecimal.valueOf(0));
        }
        dischargeDataOilService.remove(Wrappers.<DischargeDataOil>lambdaQuery()
                .eq(DischargeDataOil::getCompanyId, companyId)
                .eq(DischargeDataOil::getReportTime, dischargeDataOil.getReportTime()));
        dischargeDataOilService.save(dischargeDataOil);
        //保存水数据
        DischargeDataWater dischargeDataWater = new DischargeDataWater();
        dischargeDataWater.setCompanyId(companyId);
        dischargeDataWater.setReportTime(dischargeEnergyIndicatorVos.get(0).getReportTime());
//		dischargeDataWater.setWater(dischargeEnergyIndicatorVos.get(35).getStockData());
        DischargeEnergyIndicatorVo waterIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "新水用量");
        if (ObjectUtil.isNotEmpty(waterIndicator)) {
            dischargeDataWater.setWater(getSum(waterIndicator.getGroupData(), waterIndicator.getStockData()));
        } else {
            dischargeDataWater.setWater(BigDecimal.valueOf(0));
        }
        dischargeDataWaterService.remove(Wrappers.<DischargeDataWater>lambdaQuery()
                .eq(DischargeDataWater::getCompanyId, companyId)
                .eq(DischargeDataWater::getReportTime, dischargeDataGas.getReportTime()));
        dischargeDataWaterService.save(dischargeDataWater);
        //保存热力数据
        DischargeDataThermal dischargeDataThermal = new DischargeDataThermal();
        dischargeDataThermal.setCompanyId(companyId);
        dischargeDataThermal.setReportTime(dischargeEnergyIndicatorVos.get(0).getReportTime());
//		dischargeDataThermal.setThermal(dischargeEnergyIndicatorVos.get(33).getStockData().multiply(BigDecimal.valueOf(1000)));
        DischargeEnergyIndicatorVo thermalIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "热力");
        if (ObjectUtil.isNotEmpty(thermalIndicator)) {
            dischargeDataThermal.setThermal(getSum(thermalIndicator.getGroupData(),
                    thermalIndicator.getStockData()).multiply(BigDecimal.valueOf(1000)));
        } else {
            dischargeDataThermal.setThermal(BigDecimal.valueOf(0));
        }
        dischargeDataThermalService.remove(Wrappers.<DischargeDataThermal>lambdaQuery()
                .eq(DischargeDataThermal::getCompanyId, companyId)
                .eq(DischargeDataThermal::getReportTime, dischargeDataGas.getReportTime()));
        dischargeDataThermalService.save(dischargeDataThermal);
        //保存煤碳数据
        DischargeDataCoal dischargeDataCoal = new DischargeDataCoal();
        dischargeDataCoal.setCompanyId(companyId);
        dischargeDataCoal.setReportTime(dischargeEnergyIndicatorVos.get(0).getReportTime());
//		dischargeDataCoal.setCoal(dischargeEnergyIndicatorVos.get(1).getStockData());
        DischargeEnergyIndicatorVo coalIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "煤炭");
        if (ObjectUtil.isNotEmpty(coalIndicator)) {
            dischargeDataCoal.setCoal(getSum(coalIndicator.getGroupData(), coalIndicator.getStockData()));
        } else {
            dischargeDataCoal.setCoal(BigDecimal.valueOf(0));
        }
        dischargeDataCoalService.remove(Wrappers.<DischargeDataCoal>lambdaQuery()
                .eq(DischargeDataCoal::getCompanyId, companyId)
                .eq(DischargeDataCoal::getReportTime, dischargeDataGas.getReportTime()));
        dischargeDataCoalService.save(dischargeDataCoal);
    }

    private void updateDischargeData(List<DischargeEnergyIndicatorVo> dischargeEnergyIndicatorVos,
                                     Long companyId, Date reportTime) {
        //更新电数据
        DischargeDataElectric dischargeDataElectric = new DischargeDataElectric();
        //自有新能源发电数据，已经删除
//		dischargeDataElectric.setOwnGreenPower(dischargeEnergyIndicatorVos.get(21).getStockData());
        DischargeEnergyIndicatorVo electricIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "耗电量（总）");
        if (ObjectUtil.isNotEmpty(electricIndicator)) {
            dischargeDataElectric.setOutsourcingThermalPower(getSum(electricIndicator.getGroupData(), electricIndicator.getStockData()));
        } else {
            dischargeDataElectric.setOutsourcingThermalPower(BigDecimal.valueOf(0));
        }
        dischargeDataElectricService.update(new LambdaUpdateWrapper<DischargeDataElectric>().
                set(DischargeDataElectric::getOutsourcingThermalPower, dischargeDataElectric.getOutsourcingThermalPower()).
                eq(DischargeDataElectric::getCompanyId, companyId).
                eq(DischargeDataElectric::getReportTime, reportTime));

        //更新气数据
        DischargeDataGas dischargeDataGas = new DischargeDataGas();
        DischargeEnergyIndicatorVo ngIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "天然气消耗量");
        if (ObjectUtil.isNotEmpty(ngIndicator)) {
            dischargeDataGas.setNg(getSum(ngIndicator.getGroupData(), ngIndicator.getStockData()));
        } else {
            dischargeDataGas.setNg(BigDecimal.valueOf(0));
        }
        DischargeEnergyIndicatorVo lpgIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "液化石油气");
        if (ObjectUtil.isNotEmpty(lpgIndicator)) {
            dischargeDataGas.setLpg(getSum(lpgIndicator.getGroupData(), lpgIndicator.getStockData()).multiply(BigDecimal.valueOf(1000)));
        } else {
            dischargeDataGas.setLpg(BigDecimal.valueOf(0));
        }
        dischargeDataGasService.update(new LambdaUpdateWrapper<DischargeDataGas>().
                set(DischargeDataGas::getNg, dischargeDataGas.getNg()).
                set(DischargeDataGas::getLpg, dischargeDataGas.getLpg()).
                eq(DischargeDataGas::getCompanyId, companyId).
                eq(DischargeDataGas::getReportTime, reportTime));
        //更新油数据
        DischargeDataOil dischargeDataOil = new DischargeDataOil();
        DischargeEnergyIndicatorVo gasolineIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "汽油消耗量");
        if (ObjectUtil.isNotEmpty(gasolineIndicator)) {
            dischargeDataOil.setGasoline(getSum(gasolineIndicator.getGroupData(), gasolineIndicator.getStockData()));
        } else {
            dischargeDataOil.setGasoline(BigDecimal.valueOf(0));
        }
        DischargeEnergyIndicatorVo dieselIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "柴油消耗量");
        if (ObjectUtil.isNotEmpty(dieselIndicator)) {
            dischargeDataOil.setDiesel(getSum(dieselIndicator.getGroupData(), dieselIndicator.getStockData()));
        } else {
            dischargeDataOil.setDiesel(BigDecimal.valueOf(0));
        }
        DischargeEnergyIndicatorVo crudeIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "原油");
        if (ObjectUtil.isNotEmpty(crudeIndicator)) {
            dischargeDataOil.setCrude(getSum(crudeIndicator.getGroupData(), crudeIndicator.getStockData()));
        } else {
            dischargeDataOil.setCrude(BigDecimal.valueOf(0));
        }
        DischargeEnergyIndicatorVo keroseneIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "煤油");
        if (ObjectUtil.isNotEmpty(keroseneIndicator)) {
            dischargeDataOil.setKerosene(getSum(keroseneIndicator.getGroupData(), keroseneIndicator.getStockData()));
        } else {
            dischargeDataOil.setKerosene(BigDecimal.valueOf(0));
        }
        DischargeEnergyIndicatorVo fuelIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "燃料油");
        if (ObjectUtil.isNotEmpty(fuelIndicator)) {
            dischargeDataOil.setFuel(getSum(fuelIndicator.getGroupData(), fuelIndicator.getStockData()));
        } else {
            dischargeDataOil.setFuel(BigDecimal.valueOf(0));
        }
        dischargeDataOilService.update(new LambdaUpdateWrapper<DischargeDataOil>().
                set(DischargeDataOil::getGasoline, dischargeDataOil.getGasoline()).
                set(DischargeDataOil::getDiesel, dischargeDataOil.getDiesel()).
                set(DischargeDataOil::getCrude, dischargeDataOil.getCrude()).
                set(DischargeDataOil::getKerosene, dischargeDataOil.getKerosene()).
                set(DischargeDataOil::getFuel, dischargeDataOil.getFuel()).
                eq(DischargeDataOil::getCompanyId, companyId).
                eq(DischargeDataOil::getReportTime, reportTime));
        //更新水数据
        DischargeDataWater dischargeDataWater = new DischargeDataWater();
        DischargeEnergyIndicatorVo waterIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "新水用量");
        if (ObjectUtil.isNotEmpty(waterIndicator)) {
            dischargeDataWater.setWater(getSum(waterIndicator.getGroupData(), waterIndicator.getStockData()));
        } else {
            dischargeDataWater.setWater(BigDecimal.valueOf(0));
        }
        dischargeDataWaterService.update(new LambdaUpdateWrapper<DischargeDataWater>().
                set(DischargeDataWater::getWater, dischargeDataWater.getWater()).
                eq(DischargeDataWater::getCompanyId, companyId).
                eq(DischargeDataWater::getReportTime, reportTime));
        //更新热力数据
        DischargeDataThermal dischargeDataThermal = new DischargeDataThermal();
        DischargeEnergyIndicatorVo thermalIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "热力");
        if (ObjectUtil.isNotEmpty(thermalIndicator)) {
            dischargeDataThermal.setThermal(getSum(thermalIndicator.getGroupData(), thermalIndicator.getStockData()).multiply(BigDecimal.valueOf(1000)));
        } else {
            dischargeDataThermal.setThermal(BigDecimal.valueOf(0));
        }
        dischargeDataThermalService.update(new LambdaUpdateWrapper<DischargeDataThermal>().
                set(DischargeDataThermal::getThermal, dischargeDataThermal.getThermal()).
                eq(DischargeDataThermal::getCompanyId, companyId).
                eq(DischargeDataThermal::getReportTime, reportTime));
        //更新煤碳数据
        DischargeDataCoal dischargeDataCoal = new DischargeDataCoal();
        DischargeEnergyIndicatorVo coalIndicator = getIndicatorByName(dischargeEnergyIndicatorVos, "煤炭");
        if (ObjectUtil.isNotEmpty(coalIndicator)) {
            dischargeDataCoal.setCoal(getSum(coalIndicator.getGroupData(), coalIndicator.getStockData()));
        } else {
            dischargeDataCoal.setCoal(BigDecimal.valueOf(0));
        }
        dischargeDataCoalService.update(new LambdaUpdateWrapper<DischargeDataCoal>().
                set(DischargeDataCoal::getCoal, dischargeDataCoal.getCoal()).
                eq(DischargeDataCoal::getCompanyId, companyId).
                eq(DischargeDataCoal::getReportTime, reportTime));
    }

    @Override
    @SneakyThrows
    public void download(HttpServletRequest request, HttpServletResponse response, DischargeDataEnergyQuery query) {
        Workbook workbook = null;
        if (DownType.ALL.getValue().equals(query.getDownType())) {
            if (ObjectUtil.isEmpty(query.getReportTime())) {
                throw new BusinessException("数据时间不能为空！");
            }
            if (CollectionUtil.isNotEmpty(query.getCompanyIds())) {
                query.setReportCompany(DownType.ALL.getName());
                query.setCompanyId(null);
                // 全省数据汇总
                List<DischargeDataEnergyVo> allDataList = this.getAllDataList(query);

                // 查询各个分公司的能源数据
                List<DischargeDataEnergyVo> companyDataList = dischargeDataEnergyMapper.queryCompanyRepList(query);
                //多个map，对应了多个sheet
                List<Map<String, Object>> listMap = new ArrayList<>();

                Map<String, Object> allMap = new HashMap<>();
                ExportParams allExportParams = new ExportParams(null, query.getReportCompany(), ExcelType.HSSF);
//				allExportParams.setTitle(query.getReportCompany()+" "+ "填报月份： "+query.getReportMonth());
                allExportParams.setStyle(ExcelExportStyler.class);
                allExportParams.setSheetName(query.getReportCompany());
                allMap.put("title", allExportParams);
                //表格对应实体
                allMap.put("entity", DischargeDataEnergyVo.class);
                allMap.put("data", allDataList);
                listMap.add(allMap);
                query.getCompanyIds().forEach(node -> {
                    List<DischargeDataEnergyVo> collect = companyDataList.stream().filter(item -> {
                        return node.equals(item.getCompanyId());
                    }).collect(Collectors.toList());
                    Map<String, Object> map = new HashMap<>();
                    String sheetName = collect.get(0).getCompanyName();
                    ExportParams exportParams = new ExportParams(null, sheetName, ExcelType.HSSF);
//					exportParams.setTitle(sheetName+"汇总数据 "+ "填报月份： "+query.getReportMonth());
                    exportParams.setStyle(ExcelExportStyler.class);
                    exportParams.setSheetName(sheetName);
                    map.put("title", exportParams);
                    //表格对应实体
                    map.put("entity", DischargeDataEnergyVo.class);
                    map.put("data", collect);
                    listMap.add(map);
                });
                workbook = ExcelExportUtil.exportExcel(listMap, ExcelType.HSSF);
            }
        } else {
            List<DischargeDataEnergyVo> allDataList = this.getAllDataList(query);
            if (CollectionUtil.isNotEmpty(allDataList)) {
                ExportParams exportParams = new ExportParams();
                //exportParams.setTitle(query.getReportCompany()+" "+ "填报月份： "+query.getReportMonth());
                exportParams.setStyle(ExcelExportStyler.class);
                exportParams.setSheetName("能源数据汇总");
                workbook = ExcelExportUtil.exportExcel(exportParams,
                        DischargeDataEnergyVo.class, allDataList);
            }
        }
        //实现页面下载
        ExcelUtil.setResponseHeader(request, response, "能源数据汇总.xls");
        //创建页面输出流对象
        ServletOutputStream outputStream = response.getOutputStream();
        //把文件写入输出流的对象中
        assert workbook != null;
        workbook.write(outputStream);
        outputStream.close();
    }

    private void saveReportRecord(Long companyId, Date reportTime) {
        //保存上报记录
        DischargeDataEnergyUpdateRecord record = new DischargeDataEnergyUpdateRecord();
        record.setCompanyId(companyId);
        record.setOperateType(DischargeDataEnergyReportFlag.FLAG_REPORT.getValue());
        record.setReportTime(reportTime);
        dischargeDataEnergyUpdateRecordService.save(record);
    }

    @Override
    @SneakyThrows
    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response) {
        ClassPathResource resource = new ClassPathResource("/template/能源数据填报模板.xlsx");
        InputStream inputStream = resource.getInputStream();
        byte[] fileBytes = IOUtils.toByteArray(inputStream);
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/octet-stream");
//        response.setContentType("application/vnd.ms-excel");
//        response.addHeader("Content-Disposition", "attachment;filename=".concat(String.valueOf(URLEncoder.encode("能源数据填报模板.xlsx", "UTF-8"))));
        response.getOutputStream().write(fileBytes);
    }

    @Override
    public HashMap<String, Object> getPowerStruct() {
        DischargeDataEnergyQuery query = new DischargeDataEnergyQuery();
        query.setYear(String.valueOf(LocalDate.now().getYear() - 1));
        List<HashMap<String, Object>> dataList = baseMapper.getPowerStruct(query);
        BigDecimal powerTotal = BigDecimal.valueOf(0);
        BigDecimal powerProduction = BigDecimal.valueOf(0);
        BigDecimal powerManager = BigDecimal.valueOf(0);
        BigDecimal powerChannel = BigDecimal.valueOf(0);
        BigDecimal powerCommunicationRoom  = BigDecimal.valueOf(0);
        BigDecimal powerBaseStation = BigDecimal.valueOf(0);
        BigDecimal powerDataCenter = BigDecimal.valueOf(0);
        BigDecimal powerAccessRoom = BigDecimal.valueOf(0);
        for (HashMap<String, Object> item: dataList) {
            String indicatorId = item.get("energy_indicator_id").toString();
            if ("1638436929641324545".equals(indicatorId)) {
                //总耗电量
                powerTotal = new BigDecimal(item.get("power").toString());
            } else if ("1638437031290281985".equals(indicatorId)) {
                //生产用房耗电量
                powerProduction = new BigDecimal(item.get("power").toString());
            } else if ("1638440383172517889".equals(indicatorId)) {
                //管理用房耗电量
                powerManager = new BigDecimal(item.get("power").toString());
            } else if ("1638440502479495170".equals(indicatorId)) {
                //渠道用房耗电量
                powerChannel = new BigDecimal(item.get("power").toString());
            } else if ("1638437845111087106".equals(indicatorId)) {
                //通信机房耗电量
                powerCommunicationRoom = new BigDecimal(item.get("power").toString());
            } else if ("1638437978435428353".equals(indicatorId)) {
                //基站耗电量
                powerBaseStation = new BigDecimal(item.get("power").toString());
            } else if ("1638439495439687682".equals(indicatorId)) {
                //数据中心耗电量
                powerDataCenter = new BigDecimal(item.get("power").toString());
            } else if ("1638440039327670273".equals(indicatorId)) {
                //接入机房耗电量
                powerAccessRoom = new BigDecimal(item.get("power").toString());
            }
        }
        BigDecimal powerOther = powerTotal.subtract(powerProduction).subtract(powerManager).subtract(powerChannel);
        List<HashMap<String, Object>> powerStructList = new ArrayList<>();
        List<HashMap<String, Object>> powerProportionList = new ArrayList<>();
        HashMap<String, Object> powerProductionMap = new HashMap<>();
        powerProductionMap.put("name", "生产用电");
        powerProductionMap.put("cnt", powerProduction);
        powerProductionMap.put("num", MathUtils.division(powerProduction.multiply(BigDecimal.valueOf(100)), powerTotal));
        powerStructList.add(powerProductionMap);
        HashMap<String, Object> powerManagerMap = new HashMap<>();
        powerManagerMap.put("name", "办公室管理用电");
        powerManagerMap.put("cnt", powerManager);
        powerManagerMap.put("num", MathUtils.division(powerManager.multiply(BigDecimal.valueOf(100)), powerTotal));
        powerStructList.add(powerManagerMap);
        HashMap<String, Object> powerChannelMap = new HashMap<>();
        powerChannelMap.put("name", "营业渠道用电");
        powerChannelMap.put("cnt", powerChannel);
        powerChannelMap.put("num", MathUtils.division(powerChannel.multiply(BigDecimal.valueOf(100)), powerTotal));
        powerStructList.add(powerChannelMap);
        HashMap<String, Object> powerOtherMap = new HashMap<>();
        powerOtherMap.put("name", "其他用电");
        powerOtherMap.put("cnt", powerOther);
        powerOtherMap.put("num", MathUtils.division(powerOther.multiply(BigDecimal.valueOf(100)), powerTotal));
        powerStructList.add(powerOtherMap);
        HashMap<String, Object> powerCommunicationRoomMap = new HashMap<>();
        powerCommunicationRoomMap.put("name", "通信机房");
        powerCommunicationRoomMap.put("cnt", powerCommunicationRoom);
        powerCommunicationRoomMap.put("num", MathUtils.division(powerCommunicationRoom.multiply(BigDecimal.valueOf(100)), powerProduction));
        powerProportionList.add(powerCommunicationRoomMap);
        HashMap<String, Object> powerBaseStationMap = new HashMap<>();
        powerBaseStationMap.put("name", "基站");
        powerBaseStationMap.put("cnt", powerBaseStation);
        powerBaseStationMap.put("num", MathUtils.division(powerBaseStation.multiply(BigDecimal.valueOf(100)), powerProduction));
        powerProportionList.add(powerBaseStationMap);
        HashMap<String, Object> powerDataCenterMap = new HashMap<>();
        powerDataCenterMap.put("name", "数据中心");
        powerDataCenterMap.put("cnt", powerDataCenter);
        powerDataCenterMap.put("num", MathUtils.division(powerDataCenter.multiply(BigDecimal.valueOf(100)), powerProduction));
        powerProportionList.add(powerDataCenterMap);
        HashMap<String, Object> powerAccessRoomMap = new HashMap<>();
        powerAccessRoomMap.put("name", "接入机房");
        powerAccessRoomMap.put("cnt", powerAccessRoom);
        powerAccessRoomMap.put("num", MathUtils.division(powerAccessRoom.multiply(BigDecimal.valueOf(100)), powerProduction));
        powerProportionList.add(powerAccessRoomMap);
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("powerStructList", powerStructList);
        resultMap.put("powerProportionList", powerProportionList);
        return resultMap;
    }

    /**
     * 在列表中通过名字查找对象
     *
     * @param dischargeEnergyIndicatorVos 列表
     * @param indicatorName 名字
     * @return 返回查找到的对象，否则为null
     */
    private DischargeEnergyIndicatorVo getIndicatorByName(List<DischargeEnergyIndicatorVo> dischargeEnergyIndicatorVos,
                                                          String indicatorName) {
        DischargeEnergyIndicatorVo dischargeEnergyIndicatorVo = dischargeEnergyIndicatorVos.stream().filter(item ->
                item.getIndicatorName().contains(indicatorName)).findAny().orElse(null);
        return dischargeEnergyIndicatorVo;
    }

    @Override
    public List<DischargeDataEnergyVo> getIndicatorDataList(DischargeDataEnergyBo dischargeDataEnergyBo) {
        Long companyId = ObjectUtil.isNotEmpty(dischargeDataEnergyBo.getCompanyId()) ?
                dischargeDataEnergyBo.getCompanyId() : JwtUtils.getCurrentUserCompanyId();
        return baseMapper.getIndicatorDataList(companyId, dischargeDataEnergyBo.getReportTime());
    }

    @Override
    public List<DischargeDataEnergyVo> reCountDataList(DischargeDataEnergyBo dischargeDataEnergyBo) {
        Long companyId = ObjectUtil.isNotEmpty(dischargeDataEnergyBo.getCompanyId()) ?
                dischargeDataEnergyBo.getCompanyId() : JwtUtils.getCurrentUserCompanyId();
        EnergyCountData energyCountData = new EnergyCountData();
        energyCountData.setCompanyId(companyId);
        energyCountData.setReportTime(dischargeDataEnergyBo.getReportTime());
        energyCountData.setCountType(dischargeDataEnergyBo.getCountType());
        energyCountDataService.countEnergyData(energyCountData);
        if (DischargeDataTotalEnum.EnergyCountType.原始数据.getValue().equals(energyCountData.getCountType())) {
            return baseMapper.getIndicatorDataList(companyId, dischargeDataEnergyBo.getReportTime());
        } else {
            DischargeDataEnergyCalculate dischargeDataEnergyCalculate = new DischargeDataEnergyCalculate();
            dischargeDataEnergyCalculate.setCompanyId(companyId);
            dischargeDataEnergyCalculate.setReportTime(dischargeDataEnergyBo.getReportTime());
            return dischargeDataEnergyCalculateService.getAllDataList(dischargeDataEnergyCalculate);
        }
    }

    /**
     * 获取流水号
     *
     * @return 流水号
     */
    private String getSerialNbr() {
        // 毫秒时间戳
        String timestampString = Long.toString(System.currentTimeMillis());
        Random random = new Random();
        int randomNumber = random.nextInt(10000000);
        //返回：毫秒时间戳+随机数(7位)
        return timestampString + String.format("%07d", randomNumber);
    }

    private BigDecimal getSum(BigDecimal addition, BigDecimal addend) {
        if (ObjectUtil.isNotEmpty(addition)) {
            return ObjectUtil.isNotEmpty(addend) ? addition.add(addend) : addition;
        } else {
            return ObjectUtil.isNotEmpty(addend) ? addend : BigDecimal.ZERO;
        }
    }
}
