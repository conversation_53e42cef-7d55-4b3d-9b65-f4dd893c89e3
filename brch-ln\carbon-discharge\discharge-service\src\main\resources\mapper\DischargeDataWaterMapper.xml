<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeDataWaterMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.company_id,
            t.report_time,
            t.water,
            t.del_flag
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataWaterVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_data_water t
        ${ew.customSqlSegment}
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataWaterVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_data_water t
        ${ew.customSqlSegment}
        limit 1
    </select>
	<select id="getCompanyCarbonList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataWaterVo">
		SELECT COALESCE( SUM( water ) * (
			select
			   factor
			FROM
				discharge_energy_factor
			WHERE
				energy_type_id = '1'
			AND del_flag = '0'
			and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 )  AS carbonWater,
			   DATE_FORMAT( report_time, '%m' ) AS dataMonth
		FROM
			discharge_data_water
		WHERE
			DATE_FORMAT( report_time, '%Y' ) = #{bo.year}
		AND company_id = #{bo.companyId}
		and del_flag = '0'
		GROUP BY
			report_time
		ORDER BY
			report_time ASC
	</select>

	<select id="countCompanyData" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataWaterVo">
		SELECT company_id,
		report_time,
		sum(water) as water,
		CAST(COALESCE( SUM( water )  * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '1'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS carbonEmissions,
		CAST(COALESCE( SUM( water ) * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '1'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS energyConsumption,
		DATE_FORMAT( report_time, '%m月' )  as dataMonth,
		DATE_FORMAT( report_time, '%Y' )  as dataYear
		FROM
		discharge_data_water
		WHERE
			del_flag = '0'
			<if test="queryBo.queryStartTime != null and queryBo.queryEndTime!= null">
				AND report_time BETWEEN #{queryBo.queryStartTime} AND #{queryBo.queryEndTime}
			</if>
			<if test="queryBo.companyId != null and queryBo.companyId != 0">
				AND company_id = #{queryBo.companyId}
			</if>
		GROUP BY report_time, company_id
		ORDER BY report_time asc
		<if test="queryBo.size != null">
			LIMIT #{queryBo.size}
		</if>
		<if test="queryBo.offset != null">
			OFFSET #{queryBo.offset}
		</if>
	</select>
	<select id="getCompanyDataList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataWaterVo">
		SELECT id,
		       company_id,
		       report_time,
		       water,
		CAST(COALESCE(water * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '1'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS carbon_emissions,
		CAST(COALESCE(water * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '1'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS energy_consumption,
		DATE_FORMAT( report_time, '%m月' )  as dataMonth,
		DATE_FORMAT( report_time, '%Y' )  as dataYear
		FROM
		discharge_data_water
		WHERE
			del_flag = '0'
			<if test="queryBo.queryStartTime != null and queryBo.queryEndTime!= null">
				AND report_time BETWEEN #{queryBo.queryStartTime} AND #{queryBo.queryEndTime}
			</if>
			<if test="queryBo.companyId != null">
				AND company_id = #{queryBo.companyId}
			</if>
		ORDER BY report_time asc
		<if test="queryBo.size != null">
			LIMIT #{queryBo.size}
		</if>
		<if test="queryBo.offset != null">
			OFFSET #{queryBo.offset}
		</if>
	</select>
	<select id="countCarbonCompare" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataTotalVo">
		SELECT
		z.text,
		z.code AS data_month,
		COALESCE(a.report_time, cast(CONCAT(z.code, '-01 00:00:00') as datetime)) as report_time,
		COALESCE(a.data_year, substr(z.code, 1, 4)) as data_year,
		COALESCE(a.water, 0) as water,
		COALESCE(a.carbon_water, 0) as carbon_water,
		COALESCE(a.consumption_water, 0) as consumption_water
		FROM (SELECT
		tab."month" || '月' as text,
		tab."month" as code
		FROM (WITH RECURSIVE T ( n ) AS (SELECT
		DATE( cast(#{queryBo.queryStartTime} as DATE))
		UNION ALL
		SELECT n + 1
		FROM T
		WHERE n <![CDATA[ < ]]> DATE ( cast(#{queryBo.queryEndTime} as DATE) ) )
		SELECT
		DATE_FORMAT( n, '%Y-%m' ) AS "month"
		FROM T
		GROUP BY "month"
		ORDER BY "month" desc) tab) z
		LEFT JOIN (
		SELECT
		report_time,
		COALESCE(SUM(water ), 0) as water,
		CAST(COALESCE(sum(water) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '1'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS carbon_water,
		CAST(COALESCE(sum(water) * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '1'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_water,
		DATE_FORMAT( report_time, '%Y-%m' )  as data_month,
		DATE_FORMAT( report_time, '%Y' )  as data_year
		FROM
		discharge_data_water
		WHERE
		del_flag = '0'
		<if test="queryBo.companyId != 0 and queryBo.companyId != null">
			AND company_id = #{queryBo.companyId}
		</if>
		<if test="queryBo.queryStartTime != null and queryBo.queryEndTime!= null">
			AND report_time BETWEEN #{queryBo.queryStartTime} AND #{queryBo.queryEndTime}
		</if>
		GROUP BY report_time
		ORDER BY report_time asc) a on a.data_month=z.code
		ORDER BY z.code asc
	</select>
</mapper>