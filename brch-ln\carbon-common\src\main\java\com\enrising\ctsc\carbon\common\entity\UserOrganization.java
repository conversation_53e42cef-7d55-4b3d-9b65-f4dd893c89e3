package com.enrising.ctsc.carbon.common.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户和岗位关联 sys_r_user_organizations
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserOrganization extends Model<UserOrganization>
{

    /** 基准岗位（传递末级） */
    private String basicPosition;
    /** 职务 */
    private String position;
    /** 任职类型：1主职、2兼职、3借调、4外部用户 */
    private String positionType;
    /** 用户id */
    private Long userId;
    /** 部门编码 */
    private String departmentNo;
    /** 公司编码 */
    private String companyNo;
    /** 部门岗位名称 */
    private String positionName;
    /** 部门岗位编码 */
    private String positionNo;
    /** 显示顺序 */
    private Integer idxNum;
    /** 座机 */
    private String phone;
    /** 邮箱 */
    private String mail;
    /** 首选手机号 */
    private String mobile;
    /** 状态（0正常 1停用） */
    private String status;
    /** hr主键 */
    private String hrId;
    /** 主职人员可能存在多个手机号以,分割 */
    private String preferredMobile;
    /** 省份 */
    private String provinceCode;
    /** 岗位体系 1:新岗位体系   2:旧岗位体系 */
    private String poslevelType;
    /** 根据posleveltype 查看对应的岗位等级数据字典 */
    private String positionLevel;
    /** 岗位层级 */
    private String poslevelLayer;
    /** 岗位序列(1管理序列,2专业序列) */
    private String poslevelSequence;
    /** 岗位层级体系1  新岗位层级体系2  旧岗位层级体系 */
    private String poslevelLayerType;
    /** 统一认证账号 */
    private String hrLoginId;
    /** 系统登录账号 */
    private String loginId;
    /** 专 1/兼职 2*/
    private String ralacd;

}
