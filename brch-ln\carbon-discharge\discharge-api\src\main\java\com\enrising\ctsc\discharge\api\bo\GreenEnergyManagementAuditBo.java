package com.enrising.ctsc.discharge.api.bo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 绿电管理审核
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2024-09-18
 */
@Data
public class GreenEnergyManagementAuditBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 审核id
     */
    private Long id;

    /**
     * 绿电id
     */
    private Long greenId;

    /**
     * 审核人
     */
    private Long auditUser;

    /**
     * 证明材料文件id
     */
    private Long supportingDocument;

    /**
     * 审核意见
     */
    private String auditRemark;


    /**
     * 所属分公司
     */
    private Long companies;

    /**
     * 所属部门
     */
    private Long companyBranch;

    /**
     * 审核结果
     */
    private String auditResult;

    /**
     * 绿电信息
     */
    private GreenEnergyManagementBo greenEnergyManagement;
}
