package com.enrising.ctsc.discharge.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
* 能量类型
*
* <AUTHOR>
* @since 1.0.0 2023-1-3
*/
@Getter
@AllArgsConstructor
public enum EnergyType {
	/***/
	WATER(1L, "水"),
	GREEN_POWER(2L, "外购绿电"),
	THERMAL_POWER(3L, "外购火电"),
	OWN_POWER(4L, "自有新能源发电"),
	NG(5L, "天然气"),
	LPG(6L, "液化石油气"),
	GASOLINE(7L, "汽油"),
	DIESEL(8L, "柴油"),
	KEROSENE(9L, "煤油"),
	CRUDE(10L, "原油"),
	FUEL(11L, "燃料油"),
	THERMAL(12L, "热力"),
	COAL(13L, "煤碳"),
	;
	private final Long id;
	private final String name;

}
