package com.enrising.ctsc.assess.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.assess.api.bo.AssessTargetBo;
import com.enrising.ctsc.assess.api.query.AssessTargetQuery;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.assess.api.vo.AssessTargetVo;
import com.enrising.ctsc.assess.service.AssessTargetService;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 考核指标
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2022-12-8
 */
@RestController
@RequestMapping("/assess/assessTarget")
@RequiredArgsConstructor
public class AssessTargetController {
	private final AssessTargetService assessTargetService;

	@GetMapping("/list")
	public TableDataInfo<AssessTargetVo> page(Page<AssessTargetVo> page, AssessTargetQuery query) {
		return assessTargetService.findList(page, query);
	}

	@GetMapping("/detail")
	public R<AssessTargetVo> get(AssessTargetQuery query) {
		AssessTargetVo detail = assessTargetService.detail(query);
		return R.success(detail, "查询成功");
	}

	@PostMapping(value = "/save")
	@Transactional(rollbackFor = Exception.class)
	public R<String> save(@RequestBody @Valid AssessTargetBo bo) {
		assessTargetService.add(bo);
		return R.success("保存成功");
	}

	@PostMapping(value = "/update")
	public R<String> update(@RequestBody @Valid AssessTargetBo bo) {
		assessTargetService.edit(bo);
		return R.success("修改成功");
	}

	@PostMapping(value = "/delete/{id}")
	public R<String> delete(@PathVariable Long id) {
		assessTargetService.del(id);
		return R.success("删除成功");
	}
}
