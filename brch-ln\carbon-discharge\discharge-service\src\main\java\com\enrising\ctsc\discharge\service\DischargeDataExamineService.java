package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.discharge.api.bo.DischargeDataExamineBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataExamine;
import com.enrising.ctsc.discharge.api.vo.DischargeDataExamineVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 碳盘查数据表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
public interface DischargeDataExamineService extends IService<DischargeDataExamine> {


	/**
	* 获取列表数据
	* */
	List<DischargeDataExamineVo> getList(DischargeDataExamineBo bo);

	/**
	 * 批量保存列表数据
	 * */
	void saveList(List<DischargeDataExamineBo> boList);

	/**
	 * 导出列表数据
	 * */
	void download(HttpServletRequest request, HttpServletResponse response, DischargeDataExamineBo bo);
}