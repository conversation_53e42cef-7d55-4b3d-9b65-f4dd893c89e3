package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.discharge.api.bo.DischargeDataElectricBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataElectric;
import com.enrising.ctsc.discharge.api.vo.DischargeDataElectricVo;

import java.util.Date;
import java.util.List;

/**
 * 碳排放数据填报表（电）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
public interface DischargeDataElectricService extends IService<DischargeDataElectric> {
	/**
	 * 列表查询
	 *
	 * @param queryPage 查询参数
	 * @return 列表
	 */
	Page<DischargeDataElectricVo> getElectricListPage(QueryPage<DischargeDataElectricBo> queryPage);

	/**
	 * 列表查询
	 *
	 * @param dischargeDataElectricBo 查询参数
	 * @return 列表
	 */
	List<DischargeDataElectricVo> getElectricListToExcel(DischargeDataElectricBo dischargeDataElectricBo);

	/**
	 * 列表查询
	 *
	 * @param dataYear 数据年份
	 * @return 列表
	 */
	List<DischargeDataElectricVo> getDataList(Integer dataYear, Long companyId);

	/**
	 * 列表查询
	 *
	 * @param dateStart 自定义数据开始时间
	 * @param dateEnd 自定义数据结束时间
	 * @return 列表
	 */
	List<DischargeDataElectricVo> getDataListByDate(Date dateStart, Date dateEnd, Long companyId);

	/**
	 * 详情
	 *
	 * @param id 参数
	 * @return 详情
	 */
	DischargeDataElectricVo detail(Long id);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	String add(DischargeDataElectricBo bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	void edit(DischargeDataElectricBo bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	void del(Long id);

	/**
	 *
	 * 碳排放画像--用电分析
	 * */
	DischargeDataElectricVo getAllElectricity(DischargeDataElectricBo queryBo);

	/**
	 * 统计数据列表查询
	 *
	 * @param dateStart 自定义数据开始时间
	 * @param dateEnd 自定义数据结束时间
	 * @return 列表
	 */
	List<DischargeDataElectricVo> countCompanyData(Date dateStart, Date dateEnd, Long companyId);
}
