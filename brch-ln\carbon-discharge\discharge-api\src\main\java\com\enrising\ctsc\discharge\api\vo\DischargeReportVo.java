package com.enrising.ctsc.discharge.api.vo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 碳排放报告
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeReportVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
	private Long id;

	/**
	 * 创建者id
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date createTime;

	/**
	 * 更新者id
	 */
	private Long updateBy;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date updateTime;

	/**
	 * 报告年份
	 */
	private String year;

	/**
	 * 报告名称
	 */
	private String reportName;

	/**
	 * 报告内容
	 */
	private String content;

	/**
	 * 是否通知公告，1-否，2-是
	 */
	private String isNotification;

	/**
	 * 报告落款人
	 */
	private String reporter;

	/**
	 * 报告时间
	 */
	private String reportTime;


}
