package com.enrising.ctsc.assess.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.assess.api.bo.AssessTargetSecondaryRuleBo;
import com.enrising.ctsc.assess.api.entity.AssessTargetSecondaryRule;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.assess.api.query.AssessTargetSecondaryRuleQuery;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.assess.api.vo.AssessTargetSecondaryRuleVo;
import com.enrising.ctsc.assess.mapper.AssessTargetSecondaryRuleMapper;
import com.enrising.ctsc.assess.service.AssessTargetSecondaryRuleService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 考核二级指标规则
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Service
@AllArgsConstructor
public class AssessTargetSecondaryRuleServiceImpl extends ServiceImpl<AssessTargetSecondaryRuleMapper, AssessTargetSecondaryRule> implements AssessTargetSecondaryRuleService {

    @Override
    public TableDataInfo<AssessTargetSecondaryRuleVo> findList(Page<AssessTargetSecondaryRuleVo> page, AssessTargetSecondaryRuleQuery query) {
        QueryWrapper<AssessTargetSecondaryRuleQuery> wrapper = this.getWrapper(query);
        IPage<AssessTargetSecondaryRuleVo> resultPage = baseMapper.findList(page, wrapper);
        return TableDataInfo.build(resultPage);
    }

    /**
     * 列表查询
     *
     * @param secondaryId 二级指标id
     * @return 列表
     */
    @Override
    public List<AssessTargetSecondaryRule> getRuleListBySecondaryId(Long secondaryId) {
        return baseMapper.selectList(Wrappers.<AssessTargetSecondaryRule>lambdaQuery()
                .eq(AssessTargetSecondaryRule::getSecondaryTargetId, secondaryId)
                .orderByAsc(AssessTargetSecondaryRule::getId));
    }

    @Override
    public AssessTargetSecondaryRuleVo detail(AssessTargetSecondaryRuleQuery query) {
        if (ObjectUtil.allFieldIsNull(query)) {
            throw new BusinessException("查询参数不能为空");
        }
        QueryWrapper<AssessTargetSecondaryRuleQuery> wrapper = this.getWrapper(query);
        return baseMapper.detail(wrapper);
    }

    private QueryWrapper<AssessTargetSecondaryRuleQuery> getWrapper(AssessTargetSecondaryRuleQuery query) {
        QueryWrapper<AssessTargetSecondaryRuleQuery> wrapper = new QueryWrapper<>();
        wrapper.eq(query.getId() != null, "t.id", query.getId());
        return wrapper;
    }

    @Override
    public void add(AssessTargetSecondaryRuleBo bo) {
        AssessTargetSecondaryRule entity = new AssessTargetSecondaryRule();
        BeanUtils.copyProperties(bo, entity);
        baseMapper.insert(entity);
    }

    @Override
    public void edit(AssessTargetSecondaryRuleBo bo) {
        AssessTargetSecondaryRule entity = new AssessTargetSecondaryRule();
        BeanUtils.copyProperties(bo, entity);
        baseMapper.updateById(entity);
    }

    @Override
    public void del(Long id) {
        baseMapper.deleteById(id);
    }

}
