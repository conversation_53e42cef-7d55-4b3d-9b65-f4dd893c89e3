package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.discharge.api.bo.DischargeEnergyFactorBo;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyFactor;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyFactorOpenBo;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyFactorQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyFactorOpenVo;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyFactorVo;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 碳排放能源转换因子表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
public interface DischargeEnergyFactorService extends IService<DischargeEnergyFactor> {

	/**
	 * 分页查询
	 *
	 * @param page 分页
	 * @return 列表
	 */
	TableDataInfo<DischargeEnergyFactorVo> findList(QueryPage<DischargeEnergyFactorQuery> page);

	/**
	 * 根据时间查询类型列表，判断是否有重叠
	 *
	 * @param query 查询条件
	 * @return 列表
	 */
	List<DischargeEnergyFactor> selectTypeByDate(DischargeEnergyFactorQuery query);

	/**
	 * 导出excel
	 *
	 * @param query 查询条件
	 * @param response 响应
	 */
	void exportExcel(DischargeEnergyFactorQuery query, HttpServletResponse response);

	/**
	 * 详情
	 *
	 * @param query 参数
	 * @return 详情
	 */
	DischargeEnergyFactorVo detail(DischargeEnergyFactorQuery query);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	void add(DischargeEnergyFactorBo bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	void edit(DischargeEnergyFactorBo bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	void del(Long id);

	/**
	 * 根据时间获取有效factor
	 *
	 * @param energyTypeId 能量类型id
	 * @param reportTime   数据时间
	 * @return 有效factor
	 */
	BigDecimal getFactorByTime(Long energyTypeId, Date reportTime);

	DischargeEnergyFactorOpenVo getGainFactor(DischargeEnergyFactorOpenBo bo);
}
