module.log=com.p6spy.engine.logging.P6LogFactory,com.p6spy.engine.outage.P6OutageFactory
# ???????
logMessageFormat=com.baomidou.mybatisplus.extension.p6spy.P6SpyLogger
#????????
appender=com.baomidou.mybatisplus.extension.p6spy.StdoutLogger
# ???????? sql
#appender=com.p6spy.engine.spy.appender.Slf4JLogger
# ?? p6spy driver ??
deregisterdrivers=true
# ??JDBC URL??
useprefix=true
# ???? Log ??,????????error,info,batch,debug,statement,commit,rollback,result,resultset.
#excludecategories=info,debug,result,batch,resultset
# ????
dateformat=yyyy-MM-dd HH:mm:ss
# ??JDBC driver , ??? ?? ?? ????
#driverlist=org.h2.Driver
driverlist=com.mysql.cj.jdbc.Driver
# ?????SQL??
outagedetection=true
# ?SQL???? 2 ?
outagedetectioninterval=2
