package com.enrising.ctsc.assess.controller;


import cn.hutool.core.collection.CollectionUtil;
import com.enrising.ctsc.assess.service.AssessReportService;
import com.enrising.ctsc.carbon.common.entity.CarbonAttachment;
import com.enrising.ctsc.carbon.common.service.CommonService;
import com.enrising.ctsc.carbon.common.service.UploadService;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.vo.FileVo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 碳考核附件管理
 * <AUTHOR>
 */
@RestController
@RequestMapping("/assess/file")
@AllArgsConstructor
public class AssessFileController {


	private final UploadService uploadService;
	private final CommonService commonService;

	private final AssessReportService assessReportService;
	/**
	 * 批量上传文件
	 *	 * <AUTHOR>
	 * 	 * @date 2022/10/1 16:15
	 * 	 * @param response
	 * 	 * @param fileName
	 * 	 * @return R
	 * */
	@PostMapping("/uploadFiles")
	public R uploadFiles(@RequestParam("files") MultipartFile[] files){
		List<CarbonAttachment> attachmentList = uploadService.uploadFiles(files);
		List<FileVo> fileVoList = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(attachmentList)) {
			for (CarbonAttachment attachment : attachmentList) {
				assessReportService.saveCarbonAttachment(attachment);
				FileVo fileVo = new FileVo();
				fileVo.setAttachmentId(attachment.getId().toString());
				fileVo.setUrl(attachment.getSavedUrl());
				fileVo.setFileName(attachment.getFileName());
				fileVo.setSize(attachment.getFileSize());
				fileVoList.add(fileVo);
			}
		}
		return R.success(fileVoList);
	}

	/**
	 * 上传单个文件
	 *	 * <AUTHOR>
	 * 	 * @date 2022/10/1 16:15
	 * 	 * @param response
	 * 	 * @param fileName
	 * 	 * @return R
	 * */
	@PostMapping("/uploadFile")
	public R uploadFile(@RequestParam("file") MultipartFile file){
		CarbonAttachment attachment = uploadService.uploadFile(file);
		FileVo fileVo = new FileVo();
		if (ObjectUtil.isNotEmpty(attachment)) {
			assessReportService.saveCarbonAttachment(attachment);
			fileVo.setAttachmentId(attachment.getId().toString());
			fileVo.setUrl(attachment.getSavedUrl());
			fileVo.setFileName(attachment.getFileName());
			fileVo.setSize(attachment.getFileSize());
		}
		return R.success(fileVo);
	}

	/**
	 * 文件下载
	 *
	 * <AUTHOR>
	 * @date 2022/10/1 16:15
	 * @param response
	 * @param attachment
	 * @return R
	 */
	@PostMapping("/downloadFile")
	public void downloadFile(HttpServletResponse response, @RequestBody CarbonAttachment attachment){
		uploadService.downloadFile(response,attachment);
	}

	/**
	 * 附件预览
	 * @param id 附件id
	 * @return	预览url
	 */
	@GetMapping("/attachmentPreview")
	public R<String> attachmentPreview(@RequestParam("id") Long id){
		CarbonAttachment attachment = assessReportService.getAttachmentById(id);
		return R.success(commonService.previewAttachmentFile(attachment),"临时授权路径");
	}

}
