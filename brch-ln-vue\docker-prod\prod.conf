server {
    listen       80;

    # 下面的配置主要解决minio访问问题
    # 允许请求头中包含特殊字符
    ignore_invalid_headers off;
    # 取消上传文件大小限制（设为0表示无限制）
    client_max_body_size 0;
    # 关闭代理缓冲功能
    proxy_buffering off;
    # 关闭代理请求缓冲功能
    proxy_request_buffering off;

    # 访问对象存储服务
    location /ln-nh-oss/ {
      proxy_set_header Host $http_host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;

      proxy_connect_timeout 300;
      # Default is HTTP/1, keepalive is only enabled in HTTP/1.1
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;

        proxy_pass http://***********:9000;
    }
    location /energy-cost/discharge-api/ {
        proxy_pass http://discharge-prod/discharge-api/;
        proxy_set_header Host $host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;
    }
    location /energy-cost/library-api/ {
        proxy_pass http://library-prod/library-api/;
        proxy_set_header Host $host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;
    }
    location /energy-cost/assess-api/ {
        proxy_pass http://assess-prod/assess-api/;
        proxy_set_header Host $host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;
    }
    location /energy-cost/business-api/ {
        proxy_pass http://business-prod/business-api/;
        proxy_set_header Host $host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;
    }

    location /energy-interface/ {
        proxy_pass http://interface-prod;
        proxy_set_header Host $host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;
    }

    location /energy-cost/ {
        proxy_pass http://web-prod;
        proxy_set_header Host $host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;
    }

    # 工作流web入口
    location /act-pd-webapp/ {
        proxy_pass http://wf-prod;
        proxy_set_header Host $host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;
    }

    # 生产环境禁止访问api文档
    # 匹配末尾请求为/v3/api-docs的所有请求。~*修饰符表示不区分大小写匹配。
    # 此配置将匹配所有以 /api-docs 开头的请求，例如 /api-docs, /api-docsabc, /api-docs/some/path 等。
    location ~* /api-docs.* {
        return 404;
    }
    location ~* /swagger-resources {
        return 404;
    }

    # 静态资源文件，直接读取
    location / {
        root /data/;
        try_files $uri $uri/ /index.html;
        index  index.html;
    }
}
