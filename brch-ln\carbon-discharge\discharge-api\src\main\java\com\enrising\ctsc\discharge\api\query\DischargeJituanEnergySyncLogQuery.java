package com.enrising.ctsc.discharge.api.query;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.enrising.ctsc.discharge.api.entity.DischargeJituanEnergySyncLog;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 集团能耗同步日志查询
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2024-01-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DischargeJituanEnergySyncLogQuery extends DischargeJituanEnergySyncLog {

	/**
	 * 关键字查询
	 */
	private String keys;

	/**
	 * 查询上报时间
	 */
//	@JsonFormat(pattern = DateUtils.YYYY_MM_DD)
	@DateTimeFormat(pattern = DateUtils.YYYY_MM_DD)
	private Date queryReportDate;
}
