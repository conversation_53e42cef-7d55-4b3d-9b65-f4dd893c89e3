package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.entity.DischargeJituanEnergySyncLog;
import com.enrising.ctsc.discharge.api.query.DischargeJituanEnergySyncLogQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeJituanEnergySyncLogVo;

/**
 * 集团能耗同步日志
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2024-01-03
 */
public interface DischargeJituanEnergySyncLogService extends IService<DischargeJituanEnergySyncLog> {

    /**
    * 分页查询
    * @param page  分页
    * @param query	参数
    * @return	列表
    */
    TableDataInfo<DischargeJituanEnergySyncLogVo> findList(Page<DischargeJituanEnergySyncLogVo> page, DischargeJituanEnergySyncLogQuery query);

//    /**
//    * 详情
//    *
//    * @param query 参数
//    * @return 详情
//    */
//    DischargeJituanEnergySyncLogVo detail(DischargeJituanEnergySyncLogQuery query);
//
//    /**
//    * 新增
//    * @param bo 参数
//    */
//    void add(DischargeJituanEnergySyncLogBo bo);
//
//    /**
//    * 修改
//    * @param bo 参数
//    */
//    void edit(DischargeJituanEnergySyncLogBo bo);
//
//    /**
//    * 删除
//    * @param id 主键id
//    */
//    void del(Long id);
}
