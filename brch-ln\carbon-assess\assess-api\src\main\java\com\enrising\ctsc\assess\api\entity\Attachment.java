/*
 *    Copyright (c) 2021-2030, CTSC-ENRISING ctsc-cloudx-team All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the com.enrising.ctsc developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: CTSC-ENRISING ctsc-cloudx-team
 */

package com.enrising.ctsc.assess.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 附件表
 *
 * <AUTHOR>
 * @date 2022-06-06 11:41:38
 */
@Data
@TableName("attachment")
@EqualsAndHashCode(callSuper = true)
public class Attachment extends Model<Attachment> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新人名称
     */
    private String updaterName;
    /**
     * 年份
     */
    private Integer year;
    /**
     * 状态
     */
    private String status;
    /**
     * 删除状态
     */
    private String delFlag;
    /**
     * 文件业务类型
     */
    private String busiType;
    /**
     * 省份编码
     */
    private String provinceCode;
    /**
     * 原始文件名
     */
    private String fileName;
    /**
     * 扩展名
     */
    private String fileExt;
    /**
     * 文件大小
     */
    private String fileSize;
    /**
     * 储存路径
     */
    private String savedUrl;
    /**
     * 存储文件名
     */
    private String savedName;
    /**
     * 描述信息
     */
    private String description;
    /**
     * 资源id
     */
    private String resourceId;
    /**
     * 业务id
     */
    private String businessId;


}











