/*
 * Copyright (c) 2021 ctsc-cloudx Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.enrising.ctsc.discharge.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.discharge.api.entity.SysDict;
import com.enrising.ctsc.discharge.api.entity.SysDictItem;
import com.enrising.ctsc.discharge.api.enums.DictTypeEnum;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.discharge.mapper.SysDictItemMapper;
import com.enrising.ctsc.discharge.service.SysDictItemService;
import com.enrising.ctsc.discharge.service.SysDictService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 字典项
 *
 * <AUTHOR>
 * @date 2021/03/19
 */
@Service
@RequiredArgsConstructor
public class SysDictItemServiceImpl extends ServiceImpl<SysDictItemMapper, SysDictItem> implements SysDictItemService {

    private final SysDictService dictService;

    /**
     * 删除字典项
     *
     * @param id 字典项ID
     * @return
     */
    @Override
    public R removeDictItem(Long id) {
        // 根据ID查询字典ID
        SysDictItem dictItem = this.getById(id);
        SysDict dict = dictService.getById(dictItem.getDictId());
        // 系统内置
        Assert.state(!DictTypeEnum.SYSTEM.getType().equals(dict.getSystem()), "系统内置字典项目不能删除");
        return R.success(this.removeById(id));
    }

    /**
     * 更新字典项
     *
     * @param item 字典项
     * @return
     */
    @Override
    public R updateDictItem(SysDictItem item) {
        // 查询字典
        SysDict dict = dictService.getById(item.getDictId());
        // 系统内置
        Assert.state(!DictTypeEnum.SYSTEM.getType().equals(dict.getSystem()), "系统内置字典项目不能修改");
        return R.success(this.updateById(item));
    }


    @Override
    public Map<String, List<SysDictItem>> listDictItemMap(List<String> types) {
        if (types == null || types.isEmpty()) {
            return null;
        }
        Map<String, List<SysDictItem>> listMap = new HashMap<>();
        for (String type : types) {
            List<SysDictItem> items = this.listItems(type);
            listMap.put(type, items);
        }
        return listMap;
    }

    @Override
    public List<SysDictItem> listItems(String type) {
        return this.list(Wrappers.<SysDictItem>query().lambda()
                .eq(SysDictItem::getType, type)
                .orderByAsc(SysDictItem::getSort));
    }

    @Override
    public Map<String, List<SysDictItem>> listDictItemMap() {
        return list().stream().collect(Collectors.groupingBy(SysDictItem::getType));
    }

}
