package com.enrising.ctsc.carbon.common.utils.request;

import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;


/**
 * Servlet相关的操作方法，支持全局的请求响应处理
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 16:49
 * @email <EMAIL>
 */
public class ServletUtil {
    /**
     * 写入一个InheritableThreadLocal里面，防止子线程无法获取
     */
    private static final InheritableThreadLocal<ServletRequestAttributes> requestAttributesThreadLocal = new InheritableThreadLocal<>();

    /**
     * 如果请求线程需要启动其他子线程，请在主线程执行该方法，否则子线程无法获取到对应的请求参数
     *
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/16 15:48
     */
    public static void bind() {
        getRequestAttributes();
    }

    /**
     * 获取request
     */
    public static HttpServletRequest getRequest() {
        HttpServletRequest request = getRequestAttributes().getRequest();
        return request;
    }

    /**
     * 获取response
     */
    public static HttpServletResponse getResponse() {
        HttpServletResponse response = getRequestAttributes().getResponse();
        return response;
    }

    /**
     * 获取session
     */
    public static HttpSession getSession() {
        return getRequest().getSession();
    }

    public static ServletRequestAttributes getRequestAttributes() {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            requestAttributesThreadLocal.set((ServletRequestAttributes) attributes);
        } else if (requestAttributesThreadLocal.get() != null) {
            return requestAttributesThreadLocal.get();
        }
        return (ServletRequestAttributes) attributes;
    }

    /**
     * 将字符串渲染到客户端
     *
     * @param string 待渲染的字符串
     * @return null
     */
    public static String renderString(String string) {
        try {
            HttpServletResponse response = getResponse();
            if (response == null) {
                throw new NullPointerException("当前无与线程绑定的响应");
            }
            response.setContentType("application/json;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            response
                    .getWriter()
                    .print(string);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

}
