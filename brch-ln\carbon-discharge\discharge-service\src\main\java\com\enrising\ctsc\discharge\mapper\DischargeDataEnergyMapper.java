package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergy;
import com.enrising.ctsc.discharge.api.query.DischargeDataEnergyQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 碳排放数据填报表（能源）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Mapper
public interface DischargeDataEnergyMapper extends BaseMapper<DischargeDataEnergy> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
	IPage<DischargeDataEnergyVo> findList(Page<DischargeDataEnergyVo> page, @Param(Constants.WRAPPER) Wrapper<DischargeDataEnergyQuery> wrapper);

	/**
	 * 列表查询
	 *
	 * @param wrapper 条件
	 * @return 列表
	 */
	List<DischargeDataEnergyVo> findList(@Param(Constants.WRAPPER) Wrapper<DischargeDataEnergyQuery> wrapper);

	/**
	 * 详情查询
	 *
	 * @param wrapper 条件
	 * @return 结果
	 */
	DischargeDataEnergyVo detail(@Param(Constants.WRAPPER) Wrapper<DischargeDataEnergyQuery> wrapper);

	/**
	 * 上报数据列表查询
	 *
	 * @param companyId  填报单位id
	 * @param reportTime 填报时间
	 * @return 列表
	 */
	List<DischargeDataEnergyVo> getDataList(@Param("companyId") Long companyId,
											@Param("reportTime") Date reportTime);

	/**
	 * 所有数据列表查询
	 *
	 * @param companyId  填报单位id
	 * @param reportTime 填报时间
	 * @return 列表
	 */
//	List<DischargeDataEnergyVo> getSaveDataList(@Param("companyId") Long companyId,
//											@Param("reportTime") Date reportTime);

	/**
	 * 所有数据列表查询
	 *
	 * @param query  查询条件
	 * @return 列表
	 */
	List<DischargeDataEnergyVo> queryDataList(@Param("query") DischargeDataEnergyQuery query);

	/**
	 * 所有上报列表查询
	 *
	 * @param query  查询条件
	 * @return 列表
	 */
	List<DischargeDataEnergyVo> getReportList(@Param("query") DischargeDataEnergyQuery query);

	/**
	 * 所有已上报公司的能源情况
	 *
	 * @param query  查询条件
	 * @return 列表
	 */
	List<DischargeDataEnergyVo> queryCompanyRepList(@Param("query") DischargeDataEnergyQuery query);

	/**
	 * 查询年度用电消费结构
	 * @param query 查询参数
	 * @return 年度用电消费列表
	 */
	List<HashMap<String, Object>> getPowerStruct(@Param("query") DischargeDataEnergyQuery query);

	/**
	 * 上报数据列表查询
	 *
	 * @param companyId  填报单位id
	 * @param reportTime 填报时间
	 * @return 列表
	 */
	List<DischargeDataEnergyVo> getIndicatorDataList(@Param("companyId") Long companyId,
													 @Param("reportTime") Date reportTime);
}