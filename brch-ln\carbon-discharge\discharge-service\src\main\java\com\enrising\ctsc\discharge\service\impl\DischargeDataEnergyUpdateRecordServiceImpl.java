package com.enrising.ctsc.discharge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.discharge.api.bo.DischargeDataEnergyBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergyUpdateRecord;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyUpdateLogVo;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyUpdateRecordVo;
import com.enrising.ctsc.discharge.mapper.DischargeDataEnergyUpdateRecordMapper;
import com.enrising.ctsc.discharge.service.DischargeDataEnergyUpdateRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;






/**
 * 碳排放数据填报表（能源）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DischargeDataEnergyUpdateRecordServiceImpl extends ServiceImpl<DischargeDataEnergyUpdateRecordMapper, DischargeDataEnergyUpdateRecord> implements DischargeDataEnergyUpdateRecordService {

	private final DischargeDataEnergyUpdateRecordMapper dischargeDataEnergyUpdateRecordMapper;

	@Override
	public List<DischargeDataEnergyUpdateRecordVo> getUpdateRecordList(DischargeDataEnergyBo bo) {
		if(ObjectUtil.isNull(bo.getCompanyId())){
			log.info("碳排放数据操作记录表（能源）---->缺少companyId");
			return null;
		}
		return dischargeDataEnergyUpdateRecordMapper.getUpdateRecordList(bo);
	}

	@Override
	public List<DischargeDataEnergyUpdateLogVo> getDataUpdateList(DischargeDataEnergyBo bo) {
		return dischargeDataEnergyUpdateRecordMapper.getDataUpdateList(bo);
	}
}
