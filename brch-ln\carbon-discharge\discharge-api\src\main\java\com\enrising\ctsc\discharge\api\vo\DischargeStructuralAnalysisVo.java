package com.enrising.ctsc.discharge.api.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class DischargeStructuralAnalysisVo {

	/**
	 * 月份
	 */
	private String month;

	/**
	 * 电力数据
	 */
	private BigDecimal carbonElectric;

	/**
	 * 用气总量
	 */
	private BigDecimal carbonGas;

	/**
	 * 用油总量
	 */
	private BigDecimal carbonOil;

	/**
	 * 用水总量
	 */
	private BigDecimal carbonWater;

	/**
	 * 热力总量
	 */
	private BigDecimal carbonThermal;

	/**
	 * 煤炭总量
	 */
	private BigDecimal carbonCoal;

	/**
	 * 总排放量
	 */
	private BigDecimal carbonTotal;


}
