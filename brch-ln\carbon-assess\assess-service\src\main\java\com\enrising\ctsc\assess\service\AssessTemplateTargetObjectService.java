package com.enrising.ctsc.assess.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.assess.api.bo.AssessTemplateTargetObjectBo;
import com.enrising.ctsc.assess.api.entity.AssessTargetSecondaryRule;
import com.enrising.ctsc.assess.api.entity.AssessTemplateTargetObject;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.assess.api.vo.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 考核模板对象
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-08
 */
public interface AssessTemplateTargetObjectService extends IService<AssessTemplateTargetObject> {

	/**
	 * 查询某个分公司的考核任务
	 * @param bo  分页查询参数
	 * @return 查询结果
	 */
	List<AssessTemplateTargetObjectVo> getTaskInfoByOrgId(AssessTemplateTargetObjectBo bo);


	/**
	 * 省级--左侧部门列表
	 * @param bo  分页查询参数
	 * @return 查询结果
	 */
	List<AssessProvinceComVo> getCompanyInfoList(AssessTemplateTargetObjectBo bo);

	/**
	 * 查询某个模板下所有指标
	 * @param bo  分页查询参数
	 * @return 查询结果
	 */
	List<AssessTemplateTargetObjectVo> getTaskInfoByTemplateId(AssessTemplateTargetObjectBo bo);

	/**
	 * 查询某个模板下所有指标
	 * @param bo  分页查询参数
	 * @return 查询结果
	 */
	AssessProvinceComVo getScoreByTemplateId(AssessTemplateTargetObjectBo bo);

	/**
	 * 查询某个分公司的考核任务下某个指标的详情
	 * @param id  分页查询参数
	 * @return 查询结果
	 */
	AssessTaskInfoVo getTaskInfoById(AssessTemplateTargetObjectBo id);

	/**
	 * 按任务得分率排名--列表
	 * @param bo 查询参数
	 * @return 查询结果
	 */
	Page<AssessRankVo> getTargetListByTemplateId(QueryPage<AssessTemplateTargetObjectBo> bo);

	/**
	 * 按任务指标得分排名--列表
	 * @param bo 查询参数
	 * @return 查询结果
	 */
	Page<AssessRankVo> getScoreListByTemplateId(QueryPage<AssessTemplateTargetObjectBo> bo);

	/**
	 * 按任务指标得分排名--列表
	 * @param bo 查询参数
	 * @return 查询结果
	 */
	List<AssessScoreVo> getHeaderScoreList(AssessTemplateTargetObjectBo bo);

	/**
	 * 按任务得分率排名--单个详情
	 * @param bo 查询参数
	 * @return 查询结果
	 */
	AssessRankRateVo getRankRateInfoById(AssessTemplateTargetObjectBo bo);

	/***
	 * 转换考核时间格式
	 * @param templateStartTime 考核开始时间
	 * @param period 考核周期
	 * @return 返回考核时间
	 */
	 String getTemplateTime(Date templateStartTime, String period);

	/**
	 *  获取公司的排名信息通过指标和模板id
	 * @return List<AssessScoreVo>
	 */
	List<AssessScoreVo> getComRankByTarAndTemId(AssessTemplateTargetObjectBo queryBo);

	/***
	 *  获取每个公司下的各个指标的得分情况
	 * @return List<AssessScoreVo>
	 */
	List<AssessScoreVo> getTargetScoreByCompanyIds(AssessTemplateTargetObjectBo queryBo);



	double getAutoCalScoreByEnergyUp(DischargeMonitorSettingVo vo, List<AssessTargetSecondaryRule> ruleList);

	HashMap<String, Object> getAssessRanking(AssessTemplateTargetObjectBo bo);
}
