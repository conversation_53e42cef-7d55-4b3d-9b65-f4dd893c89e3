package com.enrising.ctsc.discharge.api.vo;

import cn.zhxu.bs.bean.DbField;
import cn.zhxu.bs.bean.DbIgnore;
import cn.zhxu.bs.bean.SearchBean;
import com.enrising.ctsc.carbon.common.entity.Attachments;
import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 绿电管理审核
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2024-09-18
 */
@Data
@SearchBean(tables = "green_energy_management_audit t " +
        "left join rmp.sys_user su ON su.id = t.submit_user " +
        "left join rmp.sys_user su_audit ON su_audit.id = t.audit_user " +
        "left join green_energy_management gem ON t.green_id = gem.id " +
        "left join rmp.sys_organizations org ON org.id = t.companies",
        autoMapTo = "t"
)
public class GreenEnergyManagementAuditVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 审核id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 绿电id
     */
    private Long greenId;

    /**
     * 审核人
     */
    private Long auditUser;

    /**
     * 审核人姓名
     */
    @DbField("su_audit.user_name")
    private String auditUserName;

    /**
     * 证明材料文件id
     */
    private Long supportingDocument;

    /**
     * 审核意见
     */
    private String auditRemark;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date submitTime;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date auditTime;

    /**
     * 提交人
     */
    private Long submitUser;

    /**
     * 提交人姓名
     */
    @DbField("su.user_name")
    private String submitUserName;

    /**
     * 审核结果
     */
    private String auditResult;

    /**
     * 所属分公司
     */
    private Long companies;

    /**
     * 所属分公司名称
     */
    @DbIgnore
    private String companiesName;

    /**
     * 所属部门
     */
    private Long companyBranch;

    /**
     * 所属部门名称
     */
    @DbIgnore
    private String companyBranchName;

    /**
     * 附件
     */
    @DbIgnore
    private Attachments attachments;

    /**
     * 绿电信息
     */
    @DbIgnore
    private GreenEnergyManagementVo greenEnergyManagement;

    /**
     * 所属主体
     */
    @DbField("gem.subject_entity")
    private String subjectEntity;

    /**
     * 电源所属地
     */
    @DbField("gem.power_source_location")
    private String powerSourceLocation;

    /**
     * 能源类型
     */
    @DbField("gem.energy_type")
    private String energyType;

    /**
     * 所属分公司名称
     */
    @DbField("org.org_name")
    private String orgName;
}
