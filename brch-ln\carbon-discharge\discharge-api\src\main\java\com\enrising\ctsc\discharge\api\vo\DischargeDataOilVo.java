package com.enrising.ctsc.discharge.api.vo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放数据填报表（油）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeDataOilVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
	private Long id;

	/**
	 * 创建者id
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date createTime;

	/**
	 * 更新者id
	 */
	private Long updateBy;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date updateTime;

	/**
	 * 填报单位id
	 */
	private Long companyId;

	/**
	 * 填报时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date reportTime;

	/**
	 * 汽油
	 */
	private BigDecimal gasoline = BigDecimal.ZERO;

	/**
	 * 柴油
	 */
	private BigDecimal diesel = BigDecimal.ZERO;

	/**
	 * 原油
	 */
	private BigDecimal crude = BigDecimal.ZERO;

	/**
	 * 燃料油
	 */
	private BigDecimal fuel = BigDecimal.ZERO;

	/**
	 * 煤油
	 */
		private BigDecimal kerosene = BigDecimal.ZERO;

	/**
	 * 数据月份
	 */
	private String dataMonth;

	/**
	 * 数据年份
	 */
	private String dataYear;

	/**
	 * 碳排放量
	 */
	private BigDecimal carbonEmissions;

	/**
	 * 能源消耗总量
	 */
	private BigDecimal energyConsumption;


	/**
	 * 汽油碳排数据
	 */
	private BigDecimal carbonGasoline = new BigDecimal(0);

	/**
	 * 柴油碳排数据
	 */
	private BigDecimal carbonDiesel = new BigDecimal(0);

	/**
	 * 原油碳排数据
	 */
	private BigDecimal carbonCrude = new BigDecimal(0);

	/**
	 * 燃料油碳排数据
	 */
	private BigDecimal carbonFuel = new BigDecimal(0);

	/**
	 * 煤油碳排数据
	 */
	private BigDecimal carbonKerosene = new BigDecimal(0);

	/**
	 * 汽油能耗
	 */
	private BigDecimal consumptionGasoline = new BigDecimal(0);

	/**
	 * 柴油能耗
	 */
	private BigDecimal consumptionDiesel = new BigDecimal(0);

	/**
	 * 原油能耗
	 */
	private BigDecimal consumptionCrude = new BigDecimal(0);

	/**
	 * 燃料油能耗
	 */
	private BigDecimal consumptionFuel = new BigDecimal(0);

	/**
	 * 煤油能耗
	 */
	private BigDecimal consumptionKerosene = new BigDecimal(0);

	public BigDecimal getCarbonEmissions() {
		return carbonGasoline.add(carbonDiesel).add(carbonCrude).add(carbonFuel).add(carbonKerosene);
	}

	public BigDecimal getEnergyConsumption() {
		return consumptionGasoline.add(consumptionDiesel).add(consumptionCrude).add(consumptionFuel).add(consumptionKerosene);
	}

}
