package com.enrising.ctsc.discharge.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.discharge.api.bo.DischargeDataGasBo;
import com.enrising.ctsc.discharge.api.vo.DischargeDataGasVo;
import com.enrising.ctsc.discharge.service.DischargeDataGasService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 碳排放数据填报表（气）
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-01-03
 */
@RestController
@RequestMapping("/discharge/gas")
@AllArgsConstructor
public class DischargeDataGasController {
	private final DischargeDataGasService dischargeDataGasService;

	@PostMapping("/getGasListPage")
		public R<Page<DischargeDataGasVo>> getGasListPage(@RequestBody QueryPage<DischargeDataGasBo> queryPage) {
		return R.success(dischargeDataGasService.getGasListPage(queryPage));
	}

	@PostMapping("/getGasListToExcel")
		public R<List<DischargeDataGasVo>> getGasListToExcel(@RequestBody DischargeDataGasBo dischargeDataGasBo) {
		return R.success(dischargeDataGasService.getGasListToExcel(dischargeDataGasBo));
	}

	@GetMapping("/getDataList")
		public R<List<DischargeDataGasVo>> getDataList(Integer dataYear, Long companyId) {
		return R.success(dischargeDataGasService.getDataList(dataYear, companyId));
	}

	@GetMapping("/detail")
		public R<DischargeDataGasVo> get(Long id) {
		DischargeDataGasVo detail = dischargeDataGasService.detail(id);
		return R.success(detail, "查询成功");
	}

		@PostMapping(value = "/save")
		public R<String> save(@RequestBody DischargeDataGasBo bo) {
		String sRet = dischargeDataGasService.add(bo);
		if (StrUtil.isBlank(sRet)){
			return R.success("保存成功");
		}
		return R.failed(sRet);
	}

		@PostMapping(value = "/update")
		public R<String> update(@RequestBody DischargeDataGasBo bo) {
		dischargeDataGasService.edit(bo);
		return R.success("修改成功");
	}

		@PostMapping(value = "/delete/{id}")
		public R<String> delete(@PathVariable Long id) {
		dischargeDataGasService.del(id);
		return R.success("删除成功");
	}
}
