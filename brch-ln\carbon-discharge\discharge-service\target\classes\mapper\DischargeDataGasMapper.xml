<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeDataGasMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.company_id,
            t.report_time,
            t.ng,
            t.lpg,
            t.del_flag
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataGasVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_data_gas t
        ${ew.customSqlSegment}
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataGasVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_data_gas t
        ${ew.customSqlSegment}
        limit 1
    </select>
	<select id="getCompanyCarbonList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataGasVo">
		SELECT COALESCE(SUM(ng) * (
			SELECT
				factor
			FROM
				discharge_energy_factor
			WHERE
				energy_type_id = '5'
			AND del_flag = '0'
			and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) AS carbonNg,
			COALESCE(SUM(lpg) * (
			SELECT
				factor
			FROM
				discharge_energy_factor
			WHERE
				energy_type_id = '6'
			AND del_flag = '0'
			and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) AS carbonLpg,
			   DATE_FORMAT( report_time, '%m' ) AS dataMonth
		FROM
			discharge_data_gas
		WHERE
			DATE_FORMAT( report_time, '%Y' ) = #{bo.year}
			AND company_id = #{bo.companyId}
			and del_flag = '0'
		GROUP BY
			report_time
		ORDER BY
			report_time ASC
	</select>
	<select id="countCompanyData" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataGasVo">
		SELECT company_id,
		report_time,
		sum(ng) as ng,
		sum(lpg) as lpg,
		CAST(COALESCE(SUM( ng )  * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '5'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS carbonNg,
		CAST(COALESCE(SUM(lpg)  * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '6'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS carbonLpg,
		CAST(COALESCE(SUM(ng) * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '5'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 )  as DECIMAL(18,4)) AS consumption_ng,
		CAST(COALESCE(SUM( lpg ) * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '6'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_lpg,
		DATE_FORMAT( report_time, '%m月' )  as dataMonth,
		DATE_FORMAT( report_time, '%Y' )  as dataYear
		FROM
		discharge_data_gas
		WHERE
			del_flag = '0'
			<if test="queryBo.queryStartTime != null and queryBo.queryEndTime!= null">
				AND report_time BETWEEN #{queryBo.queryStartTime} AND #{queryBo.queryEndTime}
			</if>
			<if test="queryBo.companyId != null and queryBo.companyId != 0">
				AND company_id = #{queryBo.companyId}
			</if>
		GROUP BY report_time, company_id
		ORDER BY report_time asc
		<if test="queryBo.size != null">
			LIMIT #{queryBo.size}
		</if>
		<if test="queryBo.offset != null">
			OFFSET #{queryBo.offset}
		</if>
	</select>
	<select id="getCompanyDataList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataGasVo">
		SELECT id,
		       company_id,
		       report_time,
		       ng,
		       lpg,
		CAST( COALESCE
		(ng * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '5'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS carbonNg,
		CAST(COALESCE
		(lpg * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '6'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS carbonLpg,
		CAST(COALESCE
		( ng * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '5'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_ng,
		CAST(COALESCE
		( lpg * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '6'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_lpg,
		DATE_FORMAT( report_time, '%m月' )  as dataMonth,
		DATE_FORMAT( report_time, '%Y' )  as dataYear
		FROM
		discharge_data_gas
		WHERE
			del_flag = '0'
			<if test="queryBo.queryStartTime != null and queryBo.queryEndTime!= null">
				AND report_time BETWEEN #{queryBo.queryStartTime} AND #{queryBo.queryEndTime}
			</if>
			<if test="queryBo.companyId != null">
				AND company_id = #{queryBo.companyId}
			</if>
		ORDER BY report_time asc
		<if test="queryBo.size != null">
			LIMIT #{queryBo.size}
		</if>
		<if test="queryBo.offset != null">
			OFFSET #{queryBo.offset}
		</if>
	</select>
	<select id="countCarbonCompare" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataTotalVo">
		SELECT
		z.text,
		z.code AS data_month,
		COALESCE(a.report_time, cast(CONCAT(z.code, '-01 00:00:00') as datetime)) as report_time,
		COALESCE(a.data_year, substr(z.code, 1, 4)) as data_year,
		COALESCE(a.ng, 0) as ng,
		COALESCE(a.lpg, 0) as lpg,
		COALESCE(a.carbon_ng, 0) as carbon_ng,
		COALESCE(a.carbon_lpg, 0) as carbon_lpg,
		COALESCE(a.consumption_ng, 0) as consumption_ng,
		COALESCE(a.consumption_lpg, 0) as consumption_lpg
		FROM (SELECT
		tab."month" || '月' as text,
		tab."month" as code
		FROM (WITH RECURSIVE T ( n ) AS (SELECT
		DATE( cast(#{queryBo.queryStartTime} as DATE))
		UNION ALL
		SELECT n + 1
		FROM T
		WHERE n <![CDATA[ < ]]> DATE ( cast(#{queryBo.queryEndTime} as DATE) ) )
		SELECT
		DATE_FORMAT( n, '%Y-%m' ) AS "month"
		FROM T
		GROUP BY "month"
		ORDER BY "month" desc) tab) z
		LEFT JOIN (
		SELECT
		report_time,
		COALESCE(SUM(ng ), 0) as ng,
		COALESCE(SUM(lpg ), 0) as lpg,
		CAST( COALESCE(sum(ng) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '5'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18, 4)) AS carbon_ng,
		CAST(COALESCE(sum(lpg) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '6'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18, 4)) AS carbon_lpg,
		CAST(COALESCE(sum(ng) * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '5'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18, 4)) AS consumption_ng,
		CAST(COALESCE(sum(lpg) * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '6'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18, 4)) AS consumption_lpg,
		DATE_FORMAT( report_time, '%Y-%m' )  as data_month,
		DATE_FORMAT( report_time, '%Y' )  as data_year
		FROM
		discharge_data_gas
		WHERE
		del_flag = '0'
		<if test="queryBo.companyId != 0 and queryBo.companyId != null">
			AND company_id = #{queryBo.companyId}
		</if>
		<if test="queryBo.queryStartTime != null and queryBo.queryEndTime!= null">
			AND report_time BETWEEN #{queryBo.queryStartTime} AND #{queryBo.queryEndTime}
		</if>
		GROUP BY report_time
		ORDER BY report_time asc) a on a.data_month=z.code
		ORDER BY z.code asc
	</select>
</mapper>