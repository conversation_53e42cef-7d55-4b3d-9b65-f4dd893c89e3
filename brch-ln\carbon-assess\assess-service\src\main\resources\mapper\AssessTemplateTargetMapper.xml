<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.assess.mapper.AssessTemplateTargetMapper">
	<resultMap id="BaseResultMap" type="com.enrising.ctsc.assess.api.entity.AssessTemplateTarget">
		<result column="id" property="id" />
		<result column="template_id" property="templateId" />
		<result column="target_id" property="targetId" />
		<result column="secondary_target_id" property="secondaryTargetId" />
		<result column="data_source" property="dataSource" />
		<result column="assess_method" property="assessMethod" />
		<result column="object_type" property="objectType" />
	</resultMap>
    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.template_id,
            t.target_id,
            t.secondary_target_id,
            t.data_source,
            t.assess_method,
            t.object_type
    </sql>

	<!-- 查询模板指标列表 -->
	<select id="getTemplateTargetList" parameterType="Long" resultType="java.util.HashMap">
		SELECT
			*
		FROM (
				 SELECT assess_template_target.id,
						assess_template_target.template_id,
						assess_template_target.data_source,
						assess_template_target.assess_method,
						assess_template_target.object_type,
						assess_target_secondary.id AS secondary_target_id,
						assess_target_secondary.primary_target_id AS target_id,
						assess_target_secondary.target_name AS secondary_target_name,
						assess_target_secondary.score,
						assess_target_secondary.formula,
						assess_target_secondary.assess_period,
						assess_target.target_year,
						assess_target.target_type,
						assess_target.target_category
				 FROM (assess_template_target
					 LEFT JOIN assess_target_secondary ON ((assess_template_target.secondary_target_id = assess_target_secondary.id))
					 LEFT JOIN assess_target ON ((assess_target_secondary.primary_target_id = assess_target.id)))
			 ) as t
		WHERE t.template_id = #{id}
		order by t.secondary_target_id asc
	</select>
	<!-- 查询模板指标对象列表 -->
	<select id="getTemplateTargetObjectList" parameterType="Long" resultType="java.util.HashMap">
		SELECT
			atto.id,
			atto.template_target_id,
			atto.company_id,
			atto.dept_id,
			atto.object_type,
			soc.org_name as name,
			sod.org_name as dept
		FROM assess_template_target_object atto
				 LEFT JOIN rmp.sys_organizations soc ON atto.company_id = soc.id
				 LEFT JOIN rmp.sys_organizations sod ON atto.dept_id = sod.id
		WHERE atto.template_target_id = #{id}
		order by company_id,dept_id asc
	</select>
	<select id="getTargetAndTemInfo" resultType="com.enrising.ctsc.assess.api.vo.AssessTemplateTargetVo">
		SELECT
		    A.template_id,
			A.secondary_target_id,
			A.assess_method,
			b.formula,
			b.target_name as secondary_target_name,
			b.assess_period,
			b.score
		FROM
			assess_template_target A
			LEFT JOIN assess_target_secondary b ON A.secondary_target_id = b.id
		WHERE
			A.template_id = #{bo.templateId}
	</select>
</mapper>