package com.enrising.ctsc.assess.api.vo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 考核指标
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
public class AssessTargetVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建人姓名
     */
    private String createByName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date updateTime;

    /**
     * 指标类型
     */
    private String targetType;

    /**
     * 指标年份
     */
    private String targetYear;

    /**
     * 指标类别
     */
    private String targetCategory;

    /**
     * 考核周期
     */
    private String assessPeriod;

    /**
     * 指标状态
     */
    private String status;

    /**
     * 二级指标id
     */
    private Long secondaryId;

    /**
     * 二级指标名称
     */
    private String secondaryTargetName;

    /**
     * 二级指标分值
     */
    private Double secondaryTargetScore;

    /**
     * 二级指标公式
     */
    private String secondaryTargetFormula;

    /**
     * 二级指标列表
     */
    private List<AssessTargetSecondaryVo> targetSecondaryList;

    public List<AssessTargetSecondaryVo> getTargetSecondaryList() {
        if (targetSecondaryList == null) {
            return Lists.newArrayList();
        }
        return targetSecondaryList;
    }
}
