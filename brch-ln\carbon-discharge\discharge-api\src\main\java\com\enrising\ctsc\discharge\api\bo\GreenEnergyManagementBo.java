package com.enrising.ctsc.discharge.api.bo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 绿电管理
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2024-09-15
 */
@Data
public class GreenEnergyManagementBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 交易年月
     */
    @NotNull(message = "交易年月不能为空")
    @JsonFormat(pattern = DateUtils.YYYY_MM)
    private Date transactionMonth;

    /**
     * 所属分公司
     */
    @NotBlank(message = "所属分公司不能为空")
    @Size(max = 30, message = "所属分公司长度不能超过30")
    private String companies;

    /**
     * 所属部门
     */
    @Size(max = 100, message = "所属部门长度不能超过100")
    private String companyBranch;

    /**
     * 分月合同绿电量(兆瓦时)
     */
    @NotNull(message = "分月合同绿电量不能为空")
    @DecimalMin(value = "0.000", inclusive = false, message = "分月合同绿电量必须大于0")
    private BigDecimal monthlyContractPower;

    /**
     * 所属主体
     */
    @NotBlank(message = "所属主体不能为空")
    @Size(max = 50, message = "所属主体长度不能超过50")
    private String subjectEntity;

    /**
     * 电源所属地
     */
    @NotBlank(message = "电源所属地不能为空")
    @Size(max = 50, message = "电源所属地长度不能超过50")
    private String powerSourceLocation;

    /**
     * 能源类型
     */
    @NotBlank(message = "能源类型不能为空")
    @Size(max = 50, message = "能源类型长度不能超过50")
    private String energyType;

    /**
     * 价格
     */
    @NotNull(message = "价格不能为空")
    @DecimalMin(value = "0.00", inclusive = false, message = "价格必须大于0")
    private BigDecimal price;

    /**
     * 证明材料文件路径
     */
    private Long supportingDocument;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500")
    private String remarks;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date updateTime;

    /**
     * 抵扣碳排放（tCO2)
     */
    @DecimalMin(value = "0.000", inclusive = false, message = "抵扣碳排放必须大于0")
    private BigDecimal deduction;

    /**
     * 审核结果
     */
    @Size(max = 1, message = "审核结果长度必须为1")
    private String auditResult;
}
