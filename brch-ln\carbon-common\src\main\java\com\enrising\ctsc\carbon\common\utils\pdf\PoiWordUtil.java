package com.enrising.ctsc.carbon.common.utils.pdf;

import cn.hutool.core.util.StrUtil;
import com.documents4j.api.DocumentType;
import com.documents4j.api.IConverter;
import com.documents4j.job.LocalConverter;
import fr.opensagres.poi.xwpf.converter.pdf.PdfConverter;
import fr.opensagres.poi.xwpf.converter.pdf.PdfOptions;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTJc;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTc;

import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/3/17
 * @note
 */
public class PoiWordUtil {
	/**
	 * 替换内容
	 *
	 * @param textMap 需要替换的信息集合
	 */
	public static ByteArrayOutputStream replaceWord(InputStream is,
								   Map<String, String> textMap) throws IOException {
		XWPFDocument doc = new XWPFDocument(is);
		return replaceWord1(doc,textMap);
	}

	/**
	 * 替换内容
	 *
	 * @param textMap 需要替换的信息集合
	 */
	public static ByteArrayOutputStream replaceWord1(XWPFDocument doc,
									Map<String, String> textMap) throws IOException {

		//解析替换文本段落对象
		changeText(doc, textMap);
		//解析替换表格对象
		changeTable(doc, textMap);
		ByteArrayOutputStream os = new ByteArrayOutputStream();
		doc.write(os);
		ByteArrayInputStream in = new ByteArrayInputStream(os.toByteArray());
		close(os);
		close(in);
		//changeDocToPdf(in)
		return os;
	}

	/**
	 * 将doc转换为pdf
	 */
	private static ByteArrayOutputStream changeDocToPdf(InputStream docxInputStream) throws IOException {
		ByteArrayOutputStream outputStream = null;
		try  {
			outputStream = new ByteArrayOutputStream();
			IConverter converter = LocalConverter.builder().build();
			converter.convert(docxInputStream).as(DocumentType.DOCX).to(outputStream).as(DocumentType.PDF).execute();
		} catch (Exception e) {
			e.printStackTrace();
		}finally {
			close(outputStream);
			close(docxInputStream);
		}
		return outputStream;
	}

	/**
	 * 替换段落文本
	 *
	 * @param document docx解析对象
	 * @param textMap  需要替换的信息集合
	 */
	private static void changeText(XWPFDocument document, Map<String, String> textMap) {
		//获取段落集合
		List<XWPFParagraph> paragraphs = document.getParagraphs();
		for (XWPFParagraph paragraph : paragraphs) {
			//判断此段落时候需要进行替换
			String text = paragraph.getText();
			if (StrUtil.isNotBlank(text) && text.contains("${")) {
				List<XWPFRun> runs = paragraph.getRuns();
				for (XWPFRun run : runs) {
					//替换模板原来位置
					String textValue = changeValue(run.toString(), textMap);
					run.setText(textValue, 0);
				}
			}
		}
	}

	/**
	 * 替换表格对象方法
	 *
	 * @param document docx解析对象
	 * @param textMap  需要替换的信息集合
	 */
	private static void changeTable(XWPFDocument document, Map<String, String> textMap) {
		//获取表格对象集合
		List<XWPFTable> tables = document.getTables();
		for (int i = 0; i < tables.size(); i++) {
			//只处理行数大于等于2的表格，且不循环表头
			XWPFTable table = tables.get(i);
			if (table.getRows().size() > 1) {
				//判断表格内容是否可以替换
//				String cellText = table.getText();
//				if (StrUtil.isNotBlank(cellText) && cellText.contains("${")){
					List<XWPFTableRow> rows = table.getRows();
					//遍历表格,并替换模板
					eachTable(rows, textMap);
//				}
			}
		}
	}

	/**
	 * 遍历表格
	 *
	 * @param rows    表格行对象
	 * @param textMap 需要替换的信息集合
	 */
	private static void eachTable(List<XWPFTableRow> rows, Map<String, String> textMap) {
		for (XWPFTableRow row : rows) {
			List<XWPFTableCell> cells = row.getTableCells();
			for (XWPFTableCell cell : cells) {
				//判断单元格内容是否可以替换
				String cellText = cell.getText();
				if (StrUtil.isNotBlank(cellText) && cellText.contains("${")) {
					List<XWPFParagraph> paragraphs = cell.getParagraphs();
					for (XWPFParagraph paragraph : paragraphs) {
						List<XWPFRun> runs = paragraph.getRuns();
						for (XWPFRun run : runs) {
							run.setText(changeValue(run.toString(), textMap), 0);
						}
					}
				}
			}
		}
	}

	/**
	 * 匹配传入信息集合与模板
	 *
	 * @param value   模板需要替换的区域
	 * @param textMap 传入信息集合
	 * @return 模板需要替换区域信息集合对应值
	 */
	private static String changeValue(String value, Map<String, String> textMap) {
		Set<Map.Entry<String, String>> textSets = textMap.entrySet();
		for (Map.Entry<String, String> textSet : textSets) {
			//匹配模板与替换值 格式${key}
			String key = "${" + textSet.getKey() + "}";
			if (value.indexOf(key) != -1) {
				value = value.replace(key, textSet.getValue());
			}
		}
		return value;
	}


	/**
	 * 关闭输入流
	 * @param is
	 */
	private static void close(InputStream is) {
		if (is != null) {
			try {
				is.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * 关闭输出流
	 * @param os
	 */
	private static void close(OutputStream os) {
		if (os != null) {
			try {
				os.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * 将doc转换为pdf
	 */
	public static ByteArrayOutputStream changeDocToPdf(ByteArrayOutputStream out) throws IOException {
		InputStream inputStream = null;
		ByteArrayOutputStream outputStream=null;
		try {
			// 读取docx文件
			inputStream = new ByteArrayInputStream(out.toByteArray());
			XWPFDocument xwpfDocument = new XWPFDocument(inputStream);
			PdfOptions pdfOptions = PdfOptions.create();
			// 输出路径
			outputStream = new ByteArrayOutputStream();
			// 调用转换
			PdfConverter.getInstance().convert(xwpfDocument,outputStream,pdfOptions);
		} catch (IOException e) {
			e.printStackTrace();
		}finally {
			out.close();
			inputStream.close();
			outputStream.close();
		}
		return outputStream;

	}

	/**
	 * des:表末尾添加行(表，要复制样式的行，添加行数，插入的行下标索引)
	 * @param table
	 * @param source
	 * @param rows
	 */
	public static void addRows(XWPFTable table, int source, int rows, int insertRowIndex, Map<String, String> textMap){
		try{
			//循环添加行和和单元格
			for(int i=1;i<=rows;i++) {
				//获取要复制样式的行
				XWPFTableRow sourceRow = table.getRow(source);
				//添加新行
				XWPFTableRow targetRow = table.insertNewTableRow(insertRowIndex++);
				//复制行的样式给新行
				targetRow.getCtRow().setTrPr(sourceRow.getCtRow().getTrPr());
				//获取要复制样式的行的单元格
				List<XWPFTableCell> sourceCells = sourceRow.getTableCells();
				//循环复制单元格
				for (XWPFTableCell sourceCell : sourceCells) {
					//添加新列
					XWPFTableCell newCell = targetRow.addNewTableCell();
					//复制单元格的样式给新单元格
					newCell.getCTTc().setTcPr(sourceCell.getCTTc().getTcPr());
					//设置垂直居中
					newCell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);//垂直居中
					//复制单元格的居中方式给新单元格
					CTPPr pPr = sourceCell.getCTTc().getPList().get(0).getPPr();
					if(pPr!=null&&pPr.getJc()!=null&&pPr.getJc().getVal()!=null){
						CTTc cttc = newCell.getCTTc();
						CTP ctp = cttc.getPList().get(0);
						CTPPr ctppr = ctp.getPPr();
						if (ctppr == null) {
							ctppr = ctp.addNewPPr();
						}
						CTJc ctjc = ctppr.getJc();
						if (ctjc == null) {
							ctjc = ctppr.addNewJc();
						}
						ctjc.setVal(pPr.getJc().getVal()); //水平居中
					}
					// 单元格是否存在字符
					if (StrUtil.isEmpty(sourceCell.getText())) {
						continue;
					} else {
						String value = changeValue(sourceCell.getText(), textMap);
						XWPFParagraph xwpfParagraph = newCell.getParagraphs().get(0);
						xwpfParagraph.createRun().setText("${"+value+i+"}",0);
					}
				}
			}
		}catch (Exception e){
			e.getMessage();
		}
	}

	/**
	 * 考核对象x个 有几个就展示
	 *
	 * @param document docx解析对象
	 * @param textMap  需要替换的信息集合
	 * @param companyNum  公司数量
	 * @param holdChar  占位字符
	 * @param supply 需要补充的语句
	 */
	public static void fillText(XWPFDocument document, Map<String, String> textMap,int companyNum,
								  String holdChar,
								  String supply) {
		//获取段落集合
		List<XWPFParagraph> paragraphs = document.getParagraphs();
		for (XWPFParagraph paragraph : paragraphs) {
			//判断此段落时候需要进行替换
			String text = paragraph.getText();
			if (StrUtil.isNotBlank(text) && text.contains("${")) {
				List<XWPFRun> runs = paragraph.getRuns();
				for (XWPFRun run : runs) {
					//替换模板原来位置
					Boolean aBoolean = checkValue(run.toString(),textMap,holdChar);
					if(aBoolean){
						StringBuilder textValue = new StringBuilder();
						textValue.append(supply);
						// 部门名称（多个对象以、分隔）
						for (int i = 0; i < companyNum; i++) {
							textValue.append("${").append(holdChar).append(i).append("}").append("、");
						}
						String substring = textValue.substring(0, textValue.length() - 1);
						run.setText(substring, 0);
					}
				}
			}
		}
	}

	/**
	 * 匹配传入信息集合与模板
	 *
	 * @param value   模板需要替换的区域
	 * @param textMap 传入信息集合
	 * @return 模板需要替换区域信息集合对应值
	 */
	public static Boolean checkValue(String value, Map<String, String> textMap,String holdChar) {
		Set<Map.Entry<String, String>> textSets = textMap.entrySet();
		for (Map.Entry<String, String> textSet : textSets) {
			//匹配模板与替换值 格式${key}
			String key = "${" + textSet.getKey() + "}";
			if (value.contains(key) && holdChar.equals(textSet.getKey())) {
				return true;
			}
		}
		return false;
	}


	/**
	 * des:添加表格并延用上一个表格的样式
	 */
	public static void addTables(XWPFTableRow sourceRow,XWPFDocument doc, String param1,
								 String param2, String param3){
		try{
			// 创建一个 2行3列 的表
			XWPFTable table1 = doc.createTable(2, 3);
			//获取要复制样式的行
			List<XWPFTableRow> rows = table1.getRows();
			for (int i = 0; i < rows.size(); i++) {
				rows.get(i).getCtRow().setTrPr(sourceRow.getCtRow().getTrPr());
				//获取要复制样式的行的单元格
				List<XWPFTableCell> sourceCells = sourceRow.getTableCells();
				//循环复制单元格 设置样式和内容
				for (int j = 0; j < sourceCells.size(); j++) {
					XWPFTableCell newCell = rows.get(i).getTableCells().get(j);
					// 设置表头
					if(i == 0){
						// 设置字体的样式 加粗
						XWPFParagraph xwpfParagraph = newCell.getParagraphs().get(0);
						XWPFRun run = xwpfParagraph.createRun();
						run.setBold(true);
						run.setText(sourceCells.get(j).getText());
					}
					if(i == 1){
						// 第二行第一列
						if(j == 0){
							XWPFParagraph xwpfParagraph = newCell.getParagraphs().get(0);
							XWPFRun run = xwpfParagraph.createRun();
							run.setText("${"+param1+"}");
						}
						if(j == 1){
							XWPFParagraph xwpfParagraph = newCell.getParagraphs().get(0);
							XWPFRun run = xwpfParagraph.createRun();
							run.setText("${"+param2+"}");
						}
						if(j == 2){
							XWPFParagraph xwpfParagraph = newCell.getParagraphs().get(0);
							XWPFRun run = xwpfParagraph.createRun();
							run.setText("${"+param3+"}");
						}
					}
					//复制单元格的样式给新单元格
					newCell.getCTTc().setTcPr(sourceCells.get(j).getCTTc().getTcPr());
					//设置垂直居中
					newCell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);//垂直居中
					//复制单元格的居中方式给新单元格
					CTPPr pPr = sourceCells.get(j).getCTTc().getPList().get(0).getPPr();
					if(pPr!=null&&pPr.getJc()!=null&&pPr.getJc().getVal()!=null){
						CTTc cttc = newCell.getCTTc();
						CTP ctp = cttc.getPList().get(0);
						CTPPr ctppr = ctp.getPPr();
						if (ctppr == null) {
							ctppr = ctp.addNewPPr();
						}
						CTJc ctjc = ctppr.getJc();
						if (ctjc == null) {
							ctjc = ctppr.addNewJc();
						}
						ctjc.setVal(pPr.getJc().getVal()); //水平居中
					}
				}
			}
		}catch (Exception e){
			e.getMessage();
		}
	}
}
