package com.enrising.ctsc.discharge.api.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.enrising.ctsc.carbon.common.utils.MathUtils;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 数据通报
 *
 * <AUTHOR>
 * @since 1.0.0 2024-06-19
 */

@Data
public class DataReportVo extends Model<DataReportVo> {

    /**
     * 填报单位id
     */
    private Long companyId;

    /**
     * 填报单位名称
     */
    @Excel(name = "单位名称", width = 15, orderNum = "0")
    private String companyName;

    /**
     * 填报单位城市编码
     */
    private String cityCode;

    /**
     * 汉字排名
     */
    private String rankNum;

    /**
     * 总碳排放量（tCO2）
     */
    @Excel(name = "碳排", width = 25, orderNum = "4")
    private BigDecimal carbonEmissions = BigDecimal.valueOf(0);

    /**
     * 去年总碳排放量（tCO2）
     */
    @Excel(name = "去年碳排", width = 25, orderNum = "2")
    private BigDecimal carbonEmissionsLastYear = BigDecimal.valueOf(0);

    /**
     * 能源消耗总量（tce）
     */
    private BigDecimal energyConsumption = BigDecimal.valueOf(0);

    /**
     * 电信业务总量（万元）
     */
    @Excel(name = "电信业务总量", width = 25, orderNum = "3")
    private BigDecimal telecomBusinessTotal = BigDecimal.valueOf(0);

    /**
     * 去年电信业务总量（万元）
     */
    @Excel(name = "去年电信业务总量", width = 25, orderNum = "1")
    private BigDecimal telecomBusinessTotalLastYear = BigDecimal.valueOf(0);

    /**
     * 业务流量总量（TB）
     */
    private BigDecimal businessFlowTotal = BigDecimal.valueOf(0);

    /**
     * 碳排强度（kgCO2/万元）
     */
    private BigDecimal carbonStrength;

    /**
     * 去年碳排强度（kgCO2/万元）
     */
    private BigDecimal carbonStrengthLastYear;

    /**
     * 业务碳排强度（kgCO2/TB）
     */
    private BigDecimal carbonBusinessStrength;

    /**
     * 碳排放量同比增幅
     */
    @Excel(name = "碳排总量\n增幅比例", width = 15, dict = "BigDecimal.percentage", orderNum = "7")
    private BigDecimal carbonEmissionsGrowthRate;

    /**
     * 碳排强度同比降幅
     */
    @Excel(name = "同比下降率", width = 15, dict = "BigDecimal.percentage", orderNum = "5")
    private BigDecimal carbonStrengthDecreaseRate;

    /**
     * 电信业务总量同比增幅
     */
    @Excel(name = "业务总量\n增幅比例", width = 15, dict = "BigDecimal.percentage", orderNum = "6")
    private Double telecomBusinessTotalGrowthRate;

    public BigDecimal getCarbonStrength() {
        return MathUtils.division(carbonEmissions.multiply(BigDecimal.valueOf(1000)), telecomBusinessTotal);
    }

    public BigDecimal getCarbonStrengthLastYear() {
        return MathUtils.division(carbonEmissionsLastYear.multiply(BigDecimal.valueOf(1000)), telecomBusinessTotalLastYear);
    }

    public BigDecimal getCarbonBusinessStrength() {
        return MathUtils.division(carbonEmissions.multiply(BigDecimal.valueOf(1000)), businessFlowTotal);
    }

    public BigDecimal getCarbonEmissionsGrowthRate() {
        return new BigDecimal(MathUtils.getUpRate(carbonEmissions, carbonEmissionsLastYear));
    }

    public BigDecimal getCarbonStrengthDecreaseRate() {
        return new BigDecimal(MathUtils.getUpRate(getCarbonStrength(), getCarbonStrengthLastYear()));
    }

    public Double getTelecomBusinessTotalGrowthRate() {
        return new BigDecimal(MathUtils.getUpRate(telecomBusinessTotal, telecomBusinessTotalLastYear)).doubleValue();
    }

}
