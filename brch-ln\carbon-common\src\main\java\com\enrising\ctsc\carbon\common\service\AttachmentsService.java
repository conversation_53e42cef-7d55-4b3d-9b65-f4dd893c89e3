package com.enrising.ctsc.carbon.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.carbon.common.entity.Attachments;

import java.util.List;

/**
 * 附件服务接口
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public interface AttachmentsService extends IService<Attachments> {

    /**
     * 保存附件信息
     *
     * @param attachments 附件信息
     * @return 保存结果
     */
    boolean saveAttachment(Attachments attachments);

    /**
     * 根据业务ID和业务别名查询附件列表
     *
     * @param busiId    业务ID
     * @param busiAlias 业务别名
     * @return 附件列表
     */
    List<Attachments> getByBusiIdAndAlias(Long busiId, String busiAlias);

    /**
     * 根据业务ID列表批量查询附件
     *
     * @param busiIds 业务ID列表
     * @return 附件列表
     */
    List<Attachments> getByBusiIds(List<Long> busiIds);

    /**
     * 根据ID删除附件（逻辑删除）
     *
     * @param id 附件ID
     * @return 删除结果
     */
    boolean deleteAttachment(Long id);

    /**
     * 批量删除附件（逻辑删除）
     *
     * @param ids 附件ID列表
     * @return 删除结果
     */
    boolean batchDeleteAttachments(List<Long> ids);
} 