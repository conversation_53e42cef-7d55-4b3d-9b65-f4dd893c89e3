<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sccl.modules.business.cost.mapper.StationElectricMapper">

	<resultMap type="com.sccl.modules.business.cost.domain.StationElectric" id="StationElectricResult">
		<id property="id" column="id"/>
		<result property="tjsj" column="tjsj"/>
		<result property="year" column="year"/>
		<result property="month" column="month"/>
		<result property="source" column="source"/>
		<result property="cityCode" column="cityCode"/>
		<result property="cityName" column="cityName"/>
		<result property="countyCode" column="countyCode"/>
		<result property="countryName" column="countryName"/>
		<result property="stationCode" column="stationCode"/>
		<result property="pueCode" column="pueCode"/>
		<result property="stationName" column="stationName"/>
		<result property="stationcode5gr" column="stationcode5gr"/>
		<result property="yzdl" column="yzdl"/>
		<result property="rjdl" column="rjdl"/>
		<result property="createdBy" column="createdBy"/>
		<result property="createTime" column="createTime"/>
		<result property="updateTime" column="updateTime"/>
		<result property="delFlag" column="del_flag"/>
	</resultMap>


	<sql id="selectVo">
        select id,
            tjsj,
            `year`,
            `month`,
            source,
            cityCode,
            cityName,
            countyCode,
            countryName,
            stationCode,
            pueCode,
            stationName,
            stationcode5gr,
            yzdl,
            rjdl,
            createdBy,
            createTime,
            updateTime,
            del_flag
        from station_electric
    </sql>

	<sql id="other-condition">
		<if test="id != null">and id = #{id}</if>
		<if test="tjsj != null">and tjsj = #{tjsj}</if>
		<if test="year != null">and `year` = #{year}</if>
		<if test="month != null">and `month` = #{month}</if>
		<if test="source != null">and source = #{source}</if>
		<if test="cityCode != null">and cityCode = #{cityCode}</if>
		<if test="cityName != null">and cityName = #{cityName}</if>
		<if test="countyCode != null">and countyCode = #{countyCode}</if>
		<if test="countryName != null">and countryName = #{countryName}</if>
		<if test="stationCode != null">and stationCode = #{stationCode}</if>
		<if test="pueCode != null">and pueCode = #{pueCode}</if>
		<if test="stationName != null">and stationName = #{stationName}</if>
		<if test="stationcode5gr != null">and stationcode5gr = #{stationcode5gr}</if>
		<if test="yzdl != null">and yzdl = #{yzdl}</if>
		<if test="rjdl != null">and rjdl = #{rjdl}</if>
		<if test="createdBy != null and createdBy !=''">and createdBy = #{createdBy}</if>
		<if test="createTime != null">and createTime = #{createTime}</if>
		<if test="updateTime != null">and updateTime = #{updateTime}</if>
		<if test="delFlag != null">and del_flag = #{delFlag}</if>
	</sql>

	<sql id="like-condition">
		<if test="id != null">and id like concat('%', #{id}, '%')</if>
		<if test="tjsj != null">and tjsj like concat('%', #{tjsj}, '%')</if>
		<if test="year != null">and `year` like concat('%', #{year}, '%')</if>
		<if test="month != null">and `month` like concat('%', #{month}, '%')</if>
		<if test="source != null">and source like concat('%', #{source}, '%')</if>
		<if test="cityCode != null">and cityCode like concat('%', #{cityCode}, '%')</if>
		<if test="cityName != null">and cityName like concat('%', #{cityName}, '%')</if>
		<if test="countyCode != null">and countyCode like concat('%', #{countyCode}, '%')</if>
		<if test="countryName != null">and countryName like concat('%', #{countryName}, '%')</if>
		<if test="stationCode != null">and stationCode like concat('%', #{stationCode}, '%')</if>
		<if test="pueCode != null">and pueCode like concat('%', #{pueCode}, '%')</if>
		<if test="stationName != null">and stationName like concat('%', #{stationName}, '%')</if>
		<if test="stationcode5gr != null">and stationcode5gr like concat('%', #{stationcode5gr}, '%')</if>
		<if test="yzdl != null">and yzdl like concat('%', #{yzdl}, '%')</if>
		<if test="rjdl != null">and rjdl like concat('%', #{rjdl}, '%')</if>
		<if test="createdBy != null and createdBy !=''">and createdBy like concat('%', #{createdBy}, '%')</if>
		<if test="createTime != null">and createTime like concat('%', #{createTime}, '%')</if>
		<if test="updateTime != null">and updateTime like concat('%', #{updateTime}, '%')</if>
		<if test="delFlag != null">and del_flag like concat('%', #{delFlag}, '%')</if>
	</sql>


	<select id="selectByLike" parameterType="com.sccl.modules.business.cost.domain.StationElectric" resultMap="StationElectricResult">
		<include refid="selectVo"/>
		<where>
			del_flag = '0'
			<include refid="like-condition"/>
		</where>
	</select>

	<select id="selectByMap" resultMap="StationElectricResult">
		<include refid="selectVo"/>
		<where>
			del_flag = '0'
			<if test="findBy != null">
				<include refid="other-condition"/>
			</if>
			<if test="findLikeBy != null">
				<include refid="like-condition"/>
			</if>
		</where>
	</select>

	<select id="selectByPrimaryKey" parameterType="Map" resultMap="StationElectricResult">
		<include refid="selectVo"/>
		where del_flag = '0' and id = #{id}
	</select>

	<select id="count" parameterType="com.sccl.modules.business.cost.domain.StationElectric" resultType="Integer">
		select count(*) from station_electric
		<where>
			del_flag = '0'
			<include refid="other-condition"/>
		</where>
	</select>

	<select id="selectModle" resultMap="StationElectricResult">
		<include refid="selectVo"/>
		<where>
			del_flag = 0
			<include refid="other-condition"/>
		</where>
	</select>


	<insert id="insert" parameterType="com.sccl.modules.business.cost.domain.StationElectric" useGeneratedKeys="true"
			keyProperty="id" keyColumn="id">
		<selectKey keyProperty="id" resultType="Long" order="BEFORE">
			select ${@com.sccl.framework.service.IdGenerator@getNextId()} as id from dual
		</selectKey>
		insert into station_electric
		<trim prefix="(" suffix=")" suffixOverrides=",">
			id, tjsj,`year`,`month`,source,cityCode,cityName,countyCode,countryName,
			stationCode,pueCode,stationName,stationcode5gr,yzdl,rjdl,
			createdBy,createTime,updateTime,del_flag
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			#{id},
			#{tjsj},
			#{year},
			#{month},
			#{source},
			#{cityCode},
			#{cityName},
			#{countyCode},
			#{countryName},
			#{stationCode},
			#{pueCode},
			#{stationName},
			#{stationcode5gr},
			#{yzdl},
			#{rjdl},
			#{createdBy},
			#{createTime},
			#{updateTime},
			#{delFlag}
		</trim>
	</insert>

	<!-- 批量插入 -->
	<insert id="insertList" parameterType="java.util.List" useGeneratedKeys="false">
		insert into station_electric
		<trim prefix="(" suffix=")" suffixOverrides=",">
			tjsj,
			`year`,
			`month`,
			source,
			cityCode,
			cityName,
			countyCode,
			countryName,
			stationCode,
			pueCode,
			stationName,
			stationcode5gr,
			yzdl,
			rjdl,
			createdBy,
			createTime,
			updateTime,
			del_flag
		</trim>
		values
		<foreach collection="list" item="item" index="index" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
				#{item.tjsj},
				#{item.year},
				#{item.month},
				#{item.source},
				#{item.cityCode},
				#{item.cityName},
				#{item.countyCode},
				#{item.countryName},
				#{item.stationCode},
				#{item.pueCode},
				#{item.stationName},
				#{item.stationcode5gr},
				#{item.yzdl},
				#{item.rjdl},
				#{item.createdBy},
				#{item.createTime},
				#{item.updateTime},
				#{item.delFlag}
			</trim>
		</foreach>
	</insert>


	<update id="updateByPrimaryKey" parameterType="com.sccl.modules.business.cost.domain.StationElectric">
		update station_electric
		<trim prefix="SET" suffixOverrides=",">
			tjsj = #{tjsj},
			`year` = #{year},
			`month` = #{month},
			source = #{source},
			cityCode = #{cityCode},
			cityName = #{cityName},
			countyCode = #{countyCode},
			countryName = #{countryName},
			stationCode = #{stationCode},
			pueCode = #{pueCode},
			stationName = #{stationName},
			stationcode5gr = #{stationcode5gr},
			yzdl = #{yzdl},
			rjdl = #{rjdl},
			createdBy = #{createdBy},
			createTime = #{createTime},
			updateTime = #{updateTime},
			del_flag = #{delFlag},
		</trim>
		where id = #{id}
	</update>

	<update id="updateForModel" parameterType="com.sccl.modules.business.cost.domain.StationElectric">
		update station_electric
		<trim prefix="SET" suffixOverrides=",">
			<if test="tjsj != null">tjsj = #{tjsj},</if>
			<if test="year != null">`year` = #{year},</if>
			<if test="month != null">`month` = #{month},</if>
			<if test="source != null">source = #{source},</if>
			<if test="cityCode != null">cityCode = #{cityCode},</if>
			<if test="cityName != null">cityName = #{cityName},</if>
			<if test="countyCode != null">countyCode = #{countyCode},</if>
			<if test="countryName != null">countryName = #{countryName},</if>
			<if test="stationCode != null">stationCode = #{stationCode},</if>
			<if test="pueCode != null">pueCode = #{pueCode},</if>
			<if test="stationName != null">stationName = #{stationName},</if>
			<if test="stationcode5gr != null">stationcode5gr = #{stationcode5gr},</if>
			<if test="yzdl != null">yzdl = #{yzdl},</if>
			<if test="rjdl != null">rjdl = #{rjdl},</if>
			<if test="createdBy != null">createdBy = #{createdBy},</if>
			<if test="createTime != null">createTime = #{createTime},</if>
			<if test="updateTime != null">updateTime = #{updateTime},</if>
			<if test="delFlag != null">del_flag = #{delFlag},</if>
		</trim>
		where id = #{id}
	</update>

	<update id="updateForModelBatch" parameterType="com.sccl.modules.business.cost.domain.StationElectric">
		update station_electric
		<trim prefix="SET" suffixOverrides=",">
			<if test="tjsj != null">tjsj = #{tjsj},</if>
			<if test="year != null">`year` = #{year},</if>
			<if test="month != null">`month` = #{month},</if>
			<if test="source != null">source = #{source},</if>
			<if test="cityCode != null">cityCode = #{cityCode},</if>
			<if test="cityName != null">cityName = #{cityName},</if>
			<if test="countyCode != null">countyCode = #{countyCode},</if>
			<if test="countryName != null">countryName = #{countryName},</if>
			<if test="stationCode != null">stationCode = #{stationCode},</if>
			<if test="pueCode != null">pueCode = #{pueCode},</if>
			<if test="stationName != null">stationName = #{stationName},</if>
			<if test="stationcode5gr != null">stationcode5gr = #{stationcode5gr},</if>
			<if test="yzdl != null">yzdl = #{yzdl},</if>
			<if test="rjdl != null">rjdl = #{rjdl},</if>
			<if test="createdBy != null">createdBy = #{createdBy},</if>
			<if test="createTime != null">createTime = #{createTime},</if>
			<if test="updateTime != null">updateTime = #{updateTime},</if>
			<if test="delFlag != null">del_flag = #{delFlag},</if>
		</trim>
		where id in
		<foreach item="id" collection="ids" open="(" separator="," close=")">
			#{id}
		</foreach>
	</update>

	<!-- 逻辑删除 -->
	<update id="deleteByPrimaryKey" parameterType="Map">
        UPDATE station_electric SET DEL_FLAG='1' where id = #{id}
    </update>

	<update id="deleteByIds" parameterType="String">
		UPDATE station_electric SET DEL_FLAG='1' where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</update>

	<!-- 物理删除 -->
	<delete id="deleteByPrimaryKeyDB" parameterType="Map">
        delete from station_electric where id = #{id}
    </delete>

	<delete id="deleteByIdsDB" parameterType="String">
		delete from station_electric where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>

	<delete id="delByTjsj">
        delete from station_electric
        where tjsj = #{tjsj}
        <if test="stationCode != null and stationCode != ''">
			and stationCode = #{stationCode}
		</if>
		<choose>
			<when test="type != null and type != ''">
				and `type` = #{type}
			</when>
			<otherwise>
				and `type` = '0'
			</otherwise>
		</choose>
    </delete>

	<insert id="statisticsAuto" parameterType="com.sccl.modules.business.cost.vo.StationElectricParamVo">
		 insert into station_electric(`type`,tjsj,`year`,`month`,cityCode,cityName,countyCode,countryName,
 			stationCode,stationName,yzdl,rjdl,
 			createdBy,createTime,updateTime,del_flag)
		 select '0' type,
  		    #{tjsj} tjsj,
  		    #{year} `year`,
  		    #{month} `month`,
		    a.cityCode,
		    (select c0.org_name from department_id c0 where c0.twc_code = a.cityCode limit 1) cityName,
		    a.countyCode,
			(select d0.area_name from zndb_department_id d0 where d0.area_code = a.countyCode limit 1) countryName,
			a.stationCode stationCode,
			a.stationName stationName,
			round(sum(a.energyData),4) yzdl,
			round(sum(a.energyData)/#{days},4) rjdl,
  			#{createdBy} createdBy,
  			#{createTime} createTime,
  			#{updateTime} updateTime,
  			#{delFlag} delFlag
		from station_electric_detail a
		where a.del_flag = 0
		and a.tjsj like #{tjsjLike}
		<if test="stationCode != null and stationCode != ''">
			and a.stationCode = #{stationCode}
		</if>
		group by a.cityCode,a.countyCode,a.stationCode
	</insert>

	<insert id="statisticsAutoByType" parameterType="com.sccl.modules.business.cost.vo.StationElectricParamVo">
		insert into station_electric(type,tjsj,`year`,`month`,cityCode,cityName,countyCode,countryName,
		stationCode,stationName,yzdl,rjdl,
		createdBy,createTime,updateTime,del_flag)
		select
		<choose>
			<when test="type != null and type != ''">
				#{type} type,
			</when>
			<otherwise>
				'0' type,
			</otherwise>
		</choose>
		#{tjsj} tjsj,
		#{year} `year`,
		#{month} `month`,
		a.cityCode,
		(select c0.org_name from department_id c0 where c0.twc_code = a.cityCode limit 1) cityName,
		a.countyCode,
		(select d0.area_name from zndb_department_id d0 where d0.area_code = a.countyCode limit 1) countryName,
		a.stationCode stationCode,
		a.stationName stationName,
		round(sum(a.energyData),4) yzdl,
		round(sum(a.energyData)/#{days},4) rjdl,
		#{createdBy} createdBy,
		#{createTime} createTime,
		#{updateTime} updateTime,
		#{delFlag} delFlag
		from fail_sync_collectmeter a
		inner join (select max(id) id
		from fail_sync_collectmeter b0
		where b0.del_flag = 0
		and b0.syncFlag = 1
		and b0.collectTime like #{tjsjLike}
		<if test="stationCode != null and stationCode != ''">
			and b0.stationCode = #{stationCode}
		</if>
		<choose>
			<when test="type == '1'.toString()">
				and (b0.budget is null or b0.budget = '')
			</when>
			<when test="type == '2'.toString()">
				and LENGTH(b0.budget) = 7 and str_to_date(concat(b0.budget,'-01'), '%Y-%m-%d') is not null
			</when>
			<when test="type == '3'.toString()">
				and (b0.budget like 'sta_%' or b0.budget like 'STA_%')
			</when>
		</choose>
		group by b0.collectTime,b0.stationCode) b on b.id = a.id
		where a.del_flag = 0
		and a.syncFlag = 1
		and a.stationCode is not null
		and a.collectTime like #{tjsjLike}
		<if test="stationCode != null and stationCode != ''">
			and a.stationCode = #{stationCode}
		</if>
		<choose>
			<when test="type == '1'.toString()">
				and (a.budget is null or a.budget = '')
			</when>
			<when test="type == '2'.toString()">
				and LENGTH(a.budget) = 7 and str_to_date(concat(a.budget,'-01'), '%Y-%m-%d') is not null
			</when>
			<when test="type == '3'.toString()">
				and (a.budget like 'sta_%' or a.budget like 'STA_%')
			</when>
		</choose>
		group by a.stationCode
	</insert>


	<select id="list" resultType="com.sccl.modules.business.cost.vo.StationElectricResultVo"
            parameterType="com.sccl.modules.business.cost.vo.StationElectricSearchVo">
		select a.cityCode,
		    a.cityName,
		    a.countyCode,
			a.countryName,
			a.stationCode,
			a.pueCode,
			a.stationName station,
			a.stationcode5gr,
			a.source,
			'' sourceName,
			date_format(str_to_date(CONCAT(a.tjsj,'01'),'%Y%m%d'),'%Y年%m月') yf,
			a.tjsj tjyf,
			a.yzdl ywdl,
			a.rjdl
		from station_electric a
		where a.del_flag = '0'
		and a.type = '0'
		<if test="cityCode != null and cityCode != ''">
			and a.cityCode = #{cityCode}
		</if>
		<if test="countyCode != null and countyCode != ''">
			and a.countyCode = #{countyCode}
		</if>
		<if test="countryName != null and countryName != ''">
			and a.countryName like concat('%', #{countryName}, '%')
		</if>
		<if test="stationCode != null and stationCode != ''">
			and (a.stationCode = #{stationCode}
			or a.stationcode5gr = #{stationCode}
			or a.pueCode = #{stationCode}
			)
		</if>
		<if test="tjyfks != null and tjyfks != ''">
			and a.tjsj >= #{tjyfks}
		</if>
		<if test="tjyfjs != null and tjyfjs != ''">
			and a.tjsj &lt;= #{tjyfjs}
		</if>
		<if test="source != null and source != ''">
			and a.source = #{source}
		</if>
		<if test="station != null and station != ''">
			and a.stationName like concat('%', #{station}, '%')
		</if>
		order by a.rjdl desc
	</select>

	<select id="xqList" resultType="com.sccl.modules.business.cost.vo.StationElectricXqResultVo"
			parameterType="com.sccl.modules.business.cost.vo.StationElectricXqSearchVo">
		select a.stationCode stationCode,
			a.stationName station,
			date_format(str_to_date(a.tjsj,'%Y%m%d'),'%Y年%m月%d日') rq,
			round(sum(a.energyData),4) ywdl
		from station_electric_detail a
		where a.del_flag = '0'
		and a.cityCode = #{cityCode}
		and a.countyCode = #{countyCode}
		and a.stationCode = #{stationCode}
		<if test="tjsjLike != null and tjsjLike != ''">
			and a.tjsj like  #{tjsjLike}
		</if>
		group by a.stationCode,a.tjsj
		order by a.tjsj
	</select>

	<select id="getCitys" resultType="com.sccl.framework.web.domain.IdNameVO">
		select twc_code id, org_name `name`
		from `department_id`
		order by twc_code
	</select>
</mapper>
