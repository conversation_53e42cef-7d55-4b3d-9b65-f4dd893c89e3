package com.enrising.ctsc.assess.api.query;

import com.enrising.ctsc.assess.api.entity.AssessTarget;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 考核指标查询
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AssessTargetQuery extends AssessTarget {

    /**
     * 关键字
     */
    private String keys;

    /**
     * 考核周期
     */
    private String assessPeriod;
}
