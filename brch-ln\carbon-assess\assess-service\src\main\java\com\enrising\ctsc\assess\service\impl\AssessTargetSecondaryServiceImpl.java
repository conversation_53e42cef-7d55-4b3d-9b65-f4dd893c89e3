package com.enrising.ctsc.assess.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.assess.api.bo.AssessTargetSecondaryBo;
import com.enrising.ctsc.assess.api.entity.AssessTarget;
import com.enrising.ctsc.assess.api.entity.AssessTargetSecondary;
import com.enrising.ctsc.assess.api.entity.AssessTargetSecondaryRule;
import com.enrising.ctsc.assess.api.enums.TargetType;
import com.enrising.ctsc.assess.api.feign.RemoteDischargeService;
import com.enrising.ctsc.assess.api.query.AssessTargetSecondaryQuery;
import com.enrising.ctsc.assess.api.vo.AssessTargetSecondaryVo;
import com.enrising.ctsc.assess.api.vo.CompanyCarbonExcelOneVo;
import com.enrising.ctsc.assess.api.vo.CompanyCarbonExcelTwoVo;
import com.enrising.ctsc.assess.api.vo.CompanyCarbonVo;
import com.enrising.ctsc.assess.mapper.AssessTargetSecondaryMapper;
import com.enrising.ctsc.assess.service.AssessTargetSecondaryRuleService;
import com.enrising.ctsc.assess.service.AssessTargetSecondaryService;
import com.enrising.ctsc.carbon.common.enums.DelFlagEnum;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.*;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 考核二级指标
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Service
@AllArgsConstructor
public class AssessTargetSecondaryServiceImpl extends ServiceImpl<AssessTargetSecondaryMapper, AssessTargetSecondary> implements AssessTargetSecondaryService {
	private final AssessTargetSecondaryRuleService assessTargetSecondaryRuleService;

	private final RemoteDischargeService remoteDischargeService;

//	todo 1
//	private final RemoteAdminService remoteAdminService;

	@Override
	public TableDataInfo<AssessTargetSecondaryVo> findList(Page<AssessTargetSecondaryVo> page, AssessTargetSecondaryQuery query) {
		QueryWrapper<AssessTargetSecondaryQuery> wrapper = this.getWrapper(query);
		IPage<AssessTargetSecondaryVo> resultPage = baseMapper.findList(page, wrapper);
		return TableDataInfo.build(resultPage);
	}

	@Override
	public Page<CompanyCarbonVo> getCompanyCarbonByPage(QueryPage<AssessTargetSecondaryQuery> queryPage) {
		AssessTargetSecondaryQuery query = queryPage.getModel();
		Page<CompanyCarbonVo> companyCarbonVoPage = new Page<>(1, -1, true);
		// 查询各个分公司碳排（总碳排 / 化石能源）
		Page<CompanyCarbonVo> page = baseMapper.getCompanyCarbonByPage(companyCarbonVoPage,query);
		if(CollectionUtil.isNotEmpty(page.getRecords())){
			List<Long> companyIds = page.getRecords().stream().map(CompanyCarbonVo::getCompanyId).collect(Collectors.toList());
			query.setCompanyIds(companyIds);
			List<CompanyCarbonVo> companyCarbonByTarget = remoteDischargeService.getCompanyCarbonByTarget(query);
			if(CollectionUtil.isNotEmpty(companyCarbonByTarget)){
				final BigDecimal[] preAllCompanyTotal = {new BigDecimal("0")};
				final BigDecimal[] nowAllCompanyTotal = {new BigDecimal("0")};
				companyCarbonByTarget.forEach(node->{
					preAllCompanyTotal[0] = preAllCompanyTotal[0].add(node.getPreCarbonTotal());
					nowAllCompanyTotal[0] = nowAllCompanyTotal[0].add(node.getNowCarbonTotal());
				});
				List<CompanyCarbonVo> finalCompanyCarbonByTarget = companyCarbonByTarget;
				page.getRecords().forEach(item->{
					CompanyCarbonVo companyCarbonVo = finalCompanyCarbonByTarget.stream().filter(node -> {
						return node.getCompanyId().equals(item.getCompanyId());
					}).collect(Collectors.toList()).get(0);
					item.setNowCarbonTotal(companyCarbonVo.getNowCarbonTotal());
					item.setPreCarbonTotal(companyCarbonVo.getPreCarbonTotal());
					item.setDownRate(companyCarbonVo.getDownRate());
					item.setNowAllCompanyTotal(nowAllCompanyTotal[0]);
					item.setPreAllCompanyTotal(preAllCompanyTotal[0]);
					item.setAllCompanyDownRate(MathUtils.d2dRate(item.getNowAllCompanyTotal(),item.getPreAllCompanyTotal()));
				});
			}
			return  page;
		}
		return companyCarbonVoPage;
	}

	@Override
	public List<CompanyCarbonVo> getMonthCarbonByPage(AssessTargetSecondaryQuery query) {
		return remoteDischargeService.getMonthCarbonByTarget(query);
	}

	@Override
	public List<AssessTargetSecondaryVo> targetSecondaryList(AssessTargetSecondaryQuery query) {
		if(StrUtil.isBlank(query.getYear())){
			query.setYear(String.valueOf(DateUtil.year(new Date())));
		}
		return baseMapper.targetSecondaryList(query);
	}

	@Override
	public AssessTargetSecondaryVo detail(AssessTargetSecondaryQuery query) {
		if (ObjectUtil.allFieldIsNull(query)) {
			throw new BusinessException("查询参数不能为空");
		}
		QueryWrapper<AssessTargetSecondaryQuery> wrapper = this.getWrapper(query);
		return baseMapper.detail(wrapper);
	}

	private QueryWrapper<AssessTargetSecondaryQuery> getWrapper(AssessTargetSecondaryQuery query) {
		QueryWrapper<AssessTargetSecondaryQuery> wrapper = new QueryWrapper<>();
		wrapper.eq(query.getId() != null, "t.id", query.getId());
		wrapper.eq("t.del_flag", DelFlagEnum.NOT_DELETED.getValue());
		return wrapper;
	}

	@Override
	public void add(AssessTargetSecondaryBo bo) {
		AssessTargetSecondary entity = new AssessTargetSecondary();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.insert(entity);
	}

	@Override
	public void edit(AssessTargetSecondaryBo bo) {
		AssessTargetSecondary entity = new AssessTargetSecondary();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void del(Long id) {
		//如果最后一条，同时删除一级指标
		AssessTargetSecondary secondary = getById(id);
		long count = count(Wrappers.<AssessTargetSecondary>lambdaQuery().eq(AssessTargetSecondary::getPrimaryTargetId, secondary.getPrimaryTargetId()));
		if (count <= 1) {
			new AssessTarget().deleteById(secondary.getPrimaryTargetId());
		}

		// 删除二级指标规则
		new AssessTargetSecondaryRule().delete(Wrappers.<AssessTargetSecondaryRule>lambdaQuery().eq(AssessTargetSecondaryRule::getSecondaryTargetId, id));
		baseMapper.deleteById(id);
	}

	@Override
	public AssessTargetSecondaryVo getFullDetail(AssessTargetSecondaryQuery query) {
		if (ObjectUtil.allFieldIsNull(query.getId())) {
			throw new BusinessException("查询参数不能为空");
		}
		AssessTargetSecondaryVo assessTargetSecondaryVo = baseMapper.getFullDetail(query.getId());
		assessTargetSecondaryVo.setRuleList(assessTargetSecondaryRuleService.
				getRuleListBySecondaryId(assessTargetSecondaryVo.getSecondaryTargetId()));
		return assessTargetSecondaryVo;
	}

	@Override
	@SneakyThrows
	public void downloadExcel(HttpServletRequest request, HttpServletResponse response, QueryPage<AssessTargetSecondaryQuery> query) {

		AssessTargetSecondaryQuery model = query.getModel();
		Page<CompanyCarbonVo> companyCarbonByPage = getCompanyCarbonByPage(query);
		if(CollectionUtil.isNotEmpty(companyCarbonByPage.getRecords())){
			List<CompanyCarbonVo> list = companyCarbonByPage.getRecords();
			ExportParams exportParams = new ExportParams();
			exportParams.setSheetName("指标数据");
			Workbook workbook = null;
			if(TargetType.FOSSIL_ENERGY.getValue().equals(model.getType())){
				List<CompanyCarbonExcelTwoVo> dataList = new ArrayList<>();
				list.forEach(node-> {
					CompanyCarbonExcelTwoVo vo = new CompanyCarbonExcelTwoVo();
					BeanUtils.copyProperties(node,vo);
					dataList.add(vo);
				});
				workbook = ExcelExportUtil.exportExcel(exportParams,
						CompanyCarbonExcelTwoVo .class, dataList);

			} else if(TargetType.CARBON_TOTAL.getValue().equals(model.getType())) {
				List<CompanyCarbonExcelOneVo> dataList = new ArrayList<>();
				list.forEach(node-> {
					CompanyCarbonExcelOneVo vo = new CompanyCarbonExcelOneVo();
					BeanUtils.copyProperties(node,vo);
					dataList.add(vo);
				});
				workbook = ExcelExportUtil.exportExcel(exportParams,
						CompanyCarbonExcelOneVo .class, dataList);
			}

			//实现页面下载
			ExcelUtil.setResponseHeader(request,response,"指标数据管理.xlsx");
			//创建页面输出流对象
			ServletOutputStream outputStream = response.getOutputStream();
			//把文件写入输出流的对象中
			workbook.write(outputStream);
			outputStream.close();
		}
	 }
}
