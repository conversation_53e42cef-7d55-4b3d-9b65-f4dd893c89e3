package com.enrising.ctsc.carbon.common.entity;

import cn.zhxu.bs.bean.SearchBean;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 角色表
 */
@Data
@TableName(value = "rmp.sys_role")
@SearchBean(dataSource = "rmpDs", tables = "sys_role")
public class Role {
    /**
     * 角色ID
     */
    private Long id;

    /**
     * 角色名称
     */
    private String name;

    /**
     * 角色代码
     */
    private String code;

    /**
     * 角色状态（0正常 1停用）
     */
    private String status;

    /**
     * 角色描述
     */
    private String remark;
}
