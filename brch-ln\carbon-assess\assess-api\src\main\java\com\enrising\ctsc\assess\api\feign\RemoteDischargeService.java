package com.enrising.ctsc.assess.api.feign;

import com.enrising.ctsc.assess.api.bo.DischargeDataTotalBo;
import com.enrising.ctsc.assess.api.query.AssessTargetSecondaryQuery;
import com.enrising.ctsc.assess.api.utils.FeignConfigure;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.assess.api.vo.CompanyCarbonVo;
import com.enrising.ctsc.assess.api.vo.DischargeDataTotalVo;
import com.enrising.ctsc.assess.api.vo.DischargeMonitorSettingVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 碳排放接口
 * <AUTHOR>
 * @date 2024-10-16
 */
@FeignClient(contextId = "remoteDischargeService",
		value = "discharge-api",
		url = "${feign.webUrl:http://127.0.0.1:8080/energy-cost}",
		configuration = FeignConfigure.class)
public interface RemoteDischargeService {

	/**
	 * 查询碳考核数据
	 * @param dischargeDataTotalBo
	 * @return
	 */
	@PostMapping("/discharge-api/discharge/total/getCarbonAssessDataList")
	R<List<DischargeDataTotalVo>> getCarbonAssessDataList(@RequestBody DischargeDataTotalBo dischargeDataTotalBo);


	@PostMapping("/discharge-api/discharge/setting/getCompanyCarbonByTarget")
	List<CompanyCarbonVo> getCompanyCarbonByTarget(@RequestBody AssessTargetSecondaryQuery query);

	@PostMapping("/discharge-api/discharge/setting/getMonthCarbonByTarget")
	List<CompanyCarbonVo> getMonthCarbonByTarget(@RequestBody AssessTargetSecondaryQuery query);

//	@RequestMapping(value = "/discharge-api/discharge/setting/getCarbonDownByTemplateId", method = RequestMethod.POST, consumes = "application/json")
//	List<DischargeMonitorSettingVo> getCarbonDownByTemplateId(@RequestBody List<Long> companyIds);
	@PostMapping(value = "/discharge-api/discharge/setting/getCarbonDownByTemplateId")
	List<DischargeMonitorSettingVo> getCarbonDownByTemplateId(@RequestBody List<Long> companyIds);

	/**
	 * 碳排放总量数据列表查询
	 *
	 * @param dischargeDataTotalBo 碳排放总量参数对象
	 * @return 返回碳排放总量数据列表
	 */
	@PostMapping("/discharge-api/discharge/total/getDataList")
	R<List<DischargeDataTotalVo>> getDataList(@RequestBody DischargeDataTotalBo dischargeDataTotalBo);
}
