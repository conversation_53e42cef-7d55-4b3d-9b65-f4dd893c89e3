<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeDataEnergyMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.company_id,
            t.report_time,
            t.energy_indicator_id,
            t.group_data,
            t.stock_data,
            t.large_data,
            t.medium_data,
            t.mobile_data,
            t.report_flag,
            t.apply_reason,
            t.return_reason,
            t.del_flag
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_data_energy t
        ${ew.customSqlSegment}
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_data_energy t
        ${ew.customSqlSegment}
        limit 1
    </select>

	<!-- 查询上报数据列表 -->
	<select id="getDataList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo">
		SELECT
		    dde.id,
			dde.create_by,
			dde.create_time,
			dde.update_by,
			dde.update_time,
			dde.company_id,
			dde.report_time,
			dde.energy_indicator_id,
			dde.group_data,
			dde.stock_data,
			dde.large_data,
			dde.medium_data,
			dde.mobile_data,
			dde.report_flag,
			dde.return_reason,
			dde.del_flag,
			CONCAT(dei.indicator_code,'、', dei.indicator_name) as indicator_name,
			dei.indicator_code,
			dei.energy_type_id,
			dei.group_input_type,
			dei.stock_input_type,
			dei.large_input_type,
			dei.medium_input_type,
			dei.mobile_input_type,
			IFNULL((SELECT
						coefficient
					FROM
						discharge_energy_coefficient
					WHERE
						energy_type_id = dei.energy_type_id
					  AND validity_start <![CDATA[<=]]> #{reportTime}
					  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1), 0) as coefficient
		FROM discharge_data_energy dde
		 LEFT JOIN discharge_energy_indicator_new dei ON dde.energy_indicator_id = dei.id
		WHERE dde.del_flag = '0'
		  and dde.company_id = #{companyId}
		  and dde.report_time = #{reportTime}
		order by dei.indicator_code asc
	</select>
	<!-- 查询保存数据列表 -->
<!--	<select id="getSaveDataList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo">-->
<!--		SELECT-->
<!--			*-->
<!--		FROM (-->
<!--				 SELECT discharge_data_energy.id,-->
<!--						discharge_data_energy.create_by,-->
<!--						discharge_data_energy.create_time,-->
<!--						discharge_data_energy.update_by,-->
<!--						discharge_data_energy.update_time,-->
<!--						discharge_data_energy.company_id,-->
<!--						discharge_data_energy.report_time,-->
<!--						discharge_data_energy.energy_indicator_id,-->
<!--						discharge_data_energy.group_data,-->
<!--						discharge_data_energy.stock_data,-->
<!--						discharge_data_energy.large_data,-->
<!--						discharge_data_energy.medium_data,-->
<!--						discharge_data_energy.mobile_data,-->
<!--						discharge_data_energy.report_flag,-->
<!--						discharge_data_energy.return_reason,-->
<!--						discharge_data_energy.del_flag,-->
<!--						discharge_energy_indicator.indicator_name,-->
<!--						sys_dict_item.description AS unit_description,-->
<!--						sys_dict_item.label AS unit_name,-->
<!--						discharge_energy_indicator.unit,-->
<!--						discharge_energy_indicator.sort-->
<!--				 FROM (discharge_data_energy-->
<!--					 LEFT JOIN discharge_energy_indicator ON ((discharge_data_energy.energy_indicator_id = discharge_energy_indicator.id))-->
<!--					 LEFT JOIN sys_dict_item ON (((sys_dict_item.type = 'energy_type_unit') AND (sys_dict_item.value = discharge_energy_indicator.unit))))) as t-->
<!--		WHERE t.del_flag = '0' and t.report_flag='3' and t.company_id = #{companyId} and t.report_time = #{reportTime}-->
<!--		order by t.sort asc-->
<!--	</select>-->
	<select id="queryDataList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo">
		SELECT
		    T.*,
			CONCAT(b.indicator_code,'、', b.indicator_name) as indicator_name,
			b.indicator_code,
			b.energy_type_id,
			IFNULL((
				SELECT
					coefficient
				FROM
					discharge_energy_coefficient
				WHERE
					energy_type_id = b.energy_type_id
					AND validity_start <![CDATA[<=]]> #{query.reportTime}
					AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1), 0) as coefficient
		FROM
			(
				SELECT
					A.energy_indicator_id,
					sum( A.group_data ) AS group_data,
					sum( A.stock_data ) AS stock_data,
					sum( A.large_data ) AS large_data,
					sum( A.medium_data ) AS medium_data,
					sum( A.mobile_data ) AS mobile_data
				FROM
					discharge_data_energy A
				WHERE
					A.del_flag = '0'
				and (report_flag = '2' or report_flag = '4')
				AND a.report_time = #{ query.reportTime }
				<if test="query.companyId != null">
					and a.company_id = #{ query.companyId }
				</if>
				GROUP BY A.energy_indicator_id
			) AS T
			    LEFT JOIN discharge_energy_indicator_new b ON T.energy_indicator_id = b.ID
		ORDER BY b.indicator_code ASC
	</select>
	<select id="getReportList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo">
		SELECT
		    so.id as company_id,
			so.org_name AS company_name,
			T.c_time as create_time,
			T.report_flag,
			T.apply_reason,
			T.return_reason,
			COALESCE(R.record_count, 0) as record_count,
			CASE
					WHEN T.company_id IS NOT NULL THEN
					'已上报' ELSE'未上报'
			END AS report_status
		FROM rmp.sys_organizations so
		    LEFT JOIN ( SELECT company_id,
							   	DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:00') as c_time,
			                     report_flag,
			                     apply_reason,
			                     return_reason
							FROM discharge_data_energy
							WHERE del_flag = '0'
							AND (report_flag = '2' OR report_flag = '4')
							AND report_time = #{query.reportTime}
							GROUP BY company_id, c_time, report_flag, apply_reason, return_reason) T ON so.id = T.company_id
			LEFT JOIN (
				SELECT company_id, count(*) AS record_count
				FROM discharge_data_energy_update_record
				WHERE del_flag='0' AND report_time = #{query.reportTime}
				GROUP BY company_id) R ON so.id = R.company_id
		WHERE so.del_flag='0'
			  AND so.`status`='1'
			  AND so.org_type='1'
			  AND so.parent_company_no = '2600000000'
	</select>
	<select id="queryCompanyRepList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo">
		SELECT
		T.*,
			so.org_name as company_name,
			CONCAT(b.indicator_code,'、', b.indicator_name) as indicator_name,
			b.indicator_code,
			b.energy_type_id,
			IFNULL((
				SELECT
					coefficient
				FROM
					discharge_energy_coefficient
				WHERE
					energy_type_id = b.energy_type_id
				  AND validity_start <![CDATA[<=]]> #{query.reportTime}
				  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1), 0) as coefficient
		FROM
			(
			SELECT A
				.company_id,
				A.energy_indicator_id,
				sum( A.group_data ) AS group_data,
				sum( A.stock_data ) AS stock_data,
				sum( A.large_data ) AS large_data,
				sum( A.medium_data ) AS medium_data,
				sum( A.mobile_data ) AS mobile_data
			FROM
				discharge_data_energy A
			WHERE
				A.del_flag = '0'
				AND report_time = #{ query.reportTime }
				AND (report_flag = '2' or report_flag = '4')
			GROUP BY
				A.company_id,
				A.energy_indicator_id
			)
			T LEFT JOIN rmp.sys_organizations so ON so.`id` = T.company_id
			LEFT JOIN discharge_energy_indicator_new b ON T.energy_indicator_id = b.ID
		ORDER BY b.indicator_code ASC
	</select>
	<select id="getPowerStruct" resultType="java.util.HashMap">
		SELECT
		    t.energy_indicator_id,
			COALESCE(sum(t.stock_data), 0) as power
		FROM discharge_data_energy t
		LEFT JOIN rmp.sys_organizations a on a.id = t.company_id
		WHERE
		    t.del_flag='0'
		  AND a.parent_company_no = '2600000000'
			<if test="query.year != null and query.year != ''">
			  AND DATE_FORMAT(t.report_time, '%Y' ) = #{query.year}
			</if>
			<if test="query.companyId != null">
				AND t.company_id = #{query.companyId}
			</if>
		  AND t.energy_indicator_id IN (1638436929641324545,1638437031290281985,1638440383172517889,1638440502479495170,1638437845111087106,1638437978435428353,1638439495439687682,1638440039327670273)
		GROUP BY energy_indicator_id
	</select>
	<!-- 查询模板指数及能耗系统同步数据列表 -->
	<select id="getIndicatorDataList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo">
		SELECT
			dei.id,
			dei.id as energy_indicator_id,
			dei.create_by,
			dei.create_time,
			dei.update_by,
			dei.update_time,
			dei.indicator_code,
			CONCAT(dei.indicator_code,'、', dei.indicator_name) as indicator_name,
			dei.energy_type_id,
			dei.group_input_type,
			dei.stock_input_type,
			dei.large_input_type,
			dei.medium_input_type,
			dei.mobile_input_type,
			dei.`status`,
			dei.parent_id,
			dei.sort,
			dei.del_flag,
			det.second_name as energy_type_name,
			ecd.group_data,
			ecd.stock_data,
			IFNULL((SELECT
						coefficient
					FROM
						discharge_energy_coefficient
					WHERE
						energy_type_id = dei.energy_type_id
					  AND validity_start <![CDATA[<=]]> #{reportTime}
					  AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1), 0) as coefficient
		FROM discharge_energy_indicator_new dei
		    LEFT JOIN discharge_energy_type det ON dei.energy_type_id = det.id
		    LEFT JOIN energy_count_data ecd on dei.id = ecd.energy_indicator_id AND ecd.del_flag = '0' AND ecd.company_id = #{companyId}  AND ecd.report_time = #{reportTime}
		WHERE dei.del_flag='0'
		  and dei.status='1'
		order by dei.indicator_code asc
	</select>
</mapper>