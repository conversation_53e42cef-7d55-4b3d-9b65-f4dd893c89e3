package com.enrising.ctsc.assess.api.bo;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 生产业务数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-03-23
 */

@Data
public class BusinessProductionDataBo extends Model<BusinessProductionDataBo> {

	/**
	 * 主键id,采用雪花id
	 */
	private Long id;

	/**
	 * 填报单位id
	 */
	private Long companyId;


	/**
	 * 填报单位名称
	 */
	private String companyName;

	/**
	 * 填报时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date reportTime;

	/**
	 * 电信业务总量（万元）
	 */
	private BigDecimal telecomBusinessTotal;

	/**
	 * 业务流量总量（TB）
	 */
	private BigDecimal businessFlowTotal;

	/**
	 * 固定电话业务总量（万元）
	 */
	private BigDecimal fixedPhoneBusiness;

	/**
	 * 宽带接入业务总量（万元）
	 */
	private BigDecimal broadbandBusiness;

	/**
	 * 专线接入业务总量（万元）
	 */
	private BigDecimal specialBroadbandBusiness;

	/**
	 * IPTV业务总量（万元）
	 */
	private BigDecimal iptvBusiness;

	/**
	 * 移动电话业务总量（万元）
	 */
	private BigDecimal mobilePhonBusiness;

	/**
	 * 移动互联网业务总量（TB）
	 */
	private BigDecimal mobileInternetBusiness;

	/**
	 * 移动短信业务总量（万元）
	 */
	private BigDecimal mobileSmsBusiness;

	/**
	 * 物联网业务总量（万元）
	 */
	private BigDecimal iotBusiness;

	/**
	 * 互联网数据中心业务总量（万元）
	 */
	private BigDecimal idcBusiness;

	/**
	 * 其他业务总量（万元）
	 */
	private BigDecimal otherBusiness;

	/**
	 * 上报时间区间 [开始时间，结束时间]
	 */
	private Date[] reportTimeArea;
	/**
	 * 查询数据类型
	 */
	private String dataType;
	/**
	 * 时间类型，1-按月，2-按季度，3-自定义时间
	 */
	private String timeType;
	/**
	 * 数据年份
	 */
	private int dataYear;
	/**
	 * 数据年份
	 */
	private String reportYear;

	/**
	 * 查询开始时间
	 */
	private Date startTime;

	/**
	 * 查询结束时间
	 */
	private Date endTime;

	/**
	 * 上期查询开始时间
	 */
	private Date preStartTime;

	/**
	 * 上期查询结束时间
	 */
	private Date preEndTime;
}