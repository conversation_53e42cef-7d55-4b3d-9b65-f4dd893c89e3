package com.enrising.ctsc.discharge.api.vo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放监测设置表
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeMonitorSettingVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
	private Long id;

	/**
	 * 创建者id
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date createTime;

	/**
	 * 单位id
	 */
	private Long companyId;

	/**
	 * 监测对象
	 */
	private String companyName;

	/**
	 * 年份
	 */
	private String year;

	/**
	 * 定额值
	 */
	private String quota = "0";


	/**
	 * 监测选项：1-去年全省碳排放量均值；2-去年碳排放总量%；3-自定义
	 */
		private String quotaSelection = "3";

	/**
	 * 监测选择值
	 */
		private String selectionValue;

	/**
	 * 上年定额值
	 */
	private String preQuota = "0";

	/**
	 * 去年碳排放总量
	 */
	private BigDecimal preCarbonTotal = new BigDecimal(0);

	/**
	 * 去年碳排放总量
	 */
	private String preCarbonTotalStr = "0";

	/**
	 * 使用率
	 */
	private String userRate = "0";

	/**
	* 80%以下保持绿色，80%-100%用橙色，超过100%用红色。
	*
	* */
	private Integer colorType;

	/**
	 *  截止到目前碳排放总量（吨）
	 */
	private BigDecimal carbonTotal = new BigDecimal(0);

	/**
	 * 去年碳排放总量
	 */
	private String carbonTotalStr = "0";

	/**
	 *  超量时间
	 */
	private String overTime = "——";

	/**
	 *  截止到目前能源排放总量
	 */
	private BigDecimal energy = new BigDecimal(0);



}
