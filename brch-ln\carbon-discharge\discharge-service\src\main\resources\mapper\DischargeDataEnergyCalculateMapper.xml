<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeDataEnergyCalculateMapper">
	<resultMap id="BaseResultMap" type="com.enrising.ctsc.discharge.api.entity.DischargeDataEnergyCalculate">
		<result column="id" property="id" />
		<result column="create_by" property="createBy" />
		<result column="create_time" property="createTime" />
		<result column="update_by" property="updateBy" />
		<result column="update_time" property="updateTime" />
		<result column="company_id" property="companyId" />
		<result column="report_time" property="reportTime" />
		<result column="energy_indicator_id" property="energyIndicatorId" />
		<result column="group_data" property="groupData" />
		<result column="stock_data" property="stockData" />
		<result column="large_data" property="largeData" />
		<result column="mobile_data" property="mobileData" />
		<result column="medium_data" property="mediumData" />
		<result column="report_flag" property="reportFlag" />
		<result column="del_flag" property="delFlag" />
	</resultMap>

	<select id="getAllDataList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo">
		SELECT
			dei.id,
			dei.create_by,
			dei.create_time,
			dei.update_by,
			dei.update_time,
			dei.indicator_code,
			CONCAT(dei.indicator_code,'、', dei.indicator_name) as indicator_name,
			dei.energy_type_id,
			dei.group_input_type,
			dei.stock_input_type,
			dei.large_input_type,
			dei.medium_input_type,
			dei.mobile_input_type,
			dei.`status`,
			dei.parent_id,
			dei.sort,
			dei.del_flag,
			#{companyId} as company_id,
			ddec.group_data,
			ddec.stock_data,
			ddec.large_data,
			ddec.medium_data,
			ddec.mobile_data,
			ddec.report_flag,
			so.org_name as company_name,
			IFNULL((SELECT
				coefficient
			FROM
				discharge_energy_coefficient
			WHERE
				energy_type_id = dei.energy_type_id
				AND validity_start <![CDATA[<=]]> #{reportTime}
				AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1), 0) as coefficient
		FROM discharge_energy_indicator_new dei
		    LEFT JOIN (
		    	SELECT
					energy_indicator_id,
					report_flag,
					sum(group_data) as group_data,
					sum(stock_data) as stock_data,
					sum(large_data) as large_data,
					sum(medium_data) as medium_data,
					sum(mobile_data) as mobile_data
		        FROM discharge_data_energy_calculate
				<where>
					del_flag='0'
					AND report_time = #{reportTime}
					<if test="companyId != null">
						AND company_id = #{companyId}
					</if>
				</where>
		        GROUP BY energy_indicator_id
		        ) ddec on dei.id = ddec.energy_indicator_id
		    LEFT JOIN rmp.sys_organizations so on so.id = #{companyId} and so.del_flag = '0' and so.`status` = '1'
		<where>
			dei.del_flag='0'
			AND dei.`status`='1'
		</where>
		ORDER BY dei.indicator_code ASC
	</select>
	<!--删除统计数据-->
	<delete id="removeCalculateData">
		DELETE
		FROM
		    discharge_data_energy_calculate
		WHERE
			company_id = #{companyId}
			AND report_time = #{reportTime}
			AND energy_indicator_id IN (
				SELECT
				    id
				FROM discharge_energy_indicator_new
				WHERE indicator_code in
					<foreach collection="indicatorCodeList" item="indicatorCode" open="(" close=")" separator=",">
						#{indicatorCode}
					</foreach>
				)
	</delete>
	<select id="getEnergyCoefficient"  resultType="java.math.BigDecimal">
		SELECT
			IFNULL(coefficient, 0)
		FROM
			discharge_energy_coefficient
		WHERE
			energy_type_id = #{energyId}
		  AND validity_start <![CDATA[<=]]> #{reportTime}
		  AND del_flag = '0'
		ORDER BY validity_start DESC LIMIT 1
	</select>
	<select id="getCalcReportList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo">
		SELECT
			so.id AS company_id,
			so.org_name AS company_name,
			T.c_time create_time,
			CASE
				WHEN T.company_id IS NOT NULL THEN
					'已计算' ELSE '未计算'
				END AS report_status
		FROM
			rmp.sys_organizations so
				LEFT JOIN (SELECT company_id,DATE_FORMAT(max(create_time), '%Y-%m-%d %H:%i:00')  c_time FROM discharge_data_energy_calculate WHERE del_flag = '0' AND report_time = #{query.reportTime} GROUP BY company_id) T ON so.id = T.company_id
		WHERE
			so.del_flag = '0'
		  AND so.`status` = '1'
		  AND so.org_type = '1'
		  AND so.parent_company_no = '2600000000'
	</select>
</mapper>