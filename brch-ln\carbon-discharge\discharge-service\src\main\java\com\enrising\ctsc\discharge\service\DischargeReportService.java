package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.bo.DischargeReportBo;
import com.enrising.ctsc.discharge.api.entity.DischargeReport;
import com.enrising.ctsc.discharge.api.query.DischargeReportQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeReportVo;

import javax.servlet.http.HttpServletResponse;

/**
 * 碳排放报告
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
public interface DischargeReportService extends IService<DischargeReport> {

	/**
	 * 分页查询
	 *
	 * @param page  分页
	 * @param query 参数
	 * @return 列表
	 */
	TableDataInfo<DischargeReportVo> findList(Page<DischargeReportVo> page, DischargeReportQuery query);

	/**
	 * 详情
	 *
	 * @param query 参数
	 * @return 详情
	 */
	DischargeReportVo detail(DischargeReportQuery query);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	void add(DischargeReportBo bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	void edit(DischargeReportBo bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	void del(Long id);

	/**
	 * pdf预览
	 */
	String preview(HttpServletResponse response, DischargeReportBo bo);

	/**
	 * 下载pdf
	 */
	void download(HttpServletResponse response, DischargeReportBo bo);

	/**
	 * 检查是否存在
	 */
	Boolean checkExist(DischargeReportBo bo);
}
