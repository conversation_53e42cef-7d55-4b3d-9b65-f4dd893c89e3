package com.enrising.ctsc.discharge.api.bo;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 碳排放能源指标表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeEnergyIndicatorBo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id,采用雪花id
	 */
		private Long id;


	/**
	 * 能源指标类型名称
	 */
	@NotBlank(message = "类型不能为空")
	@Length(min = 1, max = 100, message = "类型最多输入100字符")
		private String indicatorName;

	/**
	 * 单位id
	 */
	@NotBlank(message = "单位不能为空")
		private String unit;

	/**
	 * 状态，1-启用，2-禁用
	 */
	@NotBlank(message = "状态不能为空")
		private String status;


	/**
	 * 父指标id
	 */
		private Long parentId;

	/**
	 * 排序值
	 */
		private Integer sort;
}
