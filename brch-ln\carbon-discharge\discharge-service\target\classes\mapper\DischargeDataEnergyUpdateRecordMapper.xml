<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeDataEnergyUpdateRecordMapper">


	<select id="getUpdateRecordList"
			resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyUpdateRecordVo">
		select
		  a.*,
		  b.nick_name as createName
		from  discharge_data_energy_update_record a
		left join sys_user b on a.create_by = b.id
		where a.company_id = #{bo.companyId}
		and a.report_time = #{bo.reportTime}
		and a.del_flag = '0'
		order by create_time asc
	</select>
	<select id="getDataUpdateList"
			resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyUpdateLogVo">
		select
          b.energy_id,
          b.num
		from  discharge_data_energy_update_record a
		left join discharge_data_energy_update_log b on a.id = b.record_id
		where a.id = #{bo.updateRecordId}
		and a.del_flag = '0'
	</select>
</mapper>