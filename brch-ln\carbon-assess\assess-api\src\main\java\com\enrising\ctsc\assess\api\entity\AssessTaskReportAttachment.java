package com.enrising.ctsc.assess.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 考核任务上报附件
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */

@Data
@TableName("assess_task_report_attachment")
public class AssessTaskReportAttachment extends Model<AssessTaskReportAttachment> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 上报id
     */
    private Long taskReportId;

    /**
     * 附件id
     */
    private Long attachmentId;

    /**
     * 下载次数
     */
    private Integer downloadCount;

}
