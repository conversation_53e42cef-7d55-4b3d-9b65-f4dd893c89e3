package com.enrising.ctsc.assess.api.query;

import com.enrising.ctsc.assess.api.entity.AssessTargetSecondary;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 考核二级指标查询
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AssessTargetSecondaryQuery extends AssessTargetSecondary {

    /*
     * 部门ids
     * */
    private List<Long> companyIds;

    /*
     * 部门id
     * */
    private Long companyId;

    /*
     * 查询时间
     * */
    private List<String> searchTime;

    /*
     * 查询开始时间
     * */
    private String beginTime;

    /*
     * 查询结束时间
     * */
    private String endTime;

    /*
     * 查询类型
     * */
    private String type;

    /*
     * 查询年份
     * */
    private String year;

    /*
     * 考核规则
     * */
    private String formula;
}
