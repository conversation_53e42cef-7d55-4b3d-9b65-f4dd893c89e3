package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.discharge.api.bo.DischargeDataEnergyBo;
import com.enrising.ctsc.discharge.api.bo.EnergyConsumptionReportToTheGroupBo;
import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergy;
import com.enrising.ctsc.discharge.api.query.DischargeDataEnergyQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyVo;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

/**
 * 碳排放数据填报表（能源）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
public interface DischargeDataEnergyService extends IService<DischargeDataEnergy> {

	/**
	 * 列表查询
	 *
	 * @param dischargeDataEnergyBo 数据时间
	 * @return 列表
	 */
	List<DischargeDataEnergyVo> getDataList(DischargeDataEnergyBo dischargeDataEnergyBo);

	/**
	 * 列表查询
	 *
	 * @param dischargeDataEnergyBo 数据时间
	 * @return 列表
	 */
	List<DischargeDataEnergyVo> getDataUpdateList(DischargeDataEnergyBo dischargeDataEnergyBo);

//	/**
//	 * 列表查询
//	 *
//	 * @param reportTime 数据时间
//	 * @return 列表
//	 */
//	String[][] getAllDataList(DischargeDataEnergyQuery query);


	 //	 * 列表查询
	 //	 *
	 //	 * @param reportTime 数据时间
	 //	 * @return 列表
	 //
	List<DischargeDataEnergyVo> getAllDataList(DischargeDataEnergyQuery query);

	//	 * 列表查询
	//	 *
	//	 * @param reportTime 数据时间
	//	 * @return 列表
	//
	List<DischargeDataEnergyVo>  getReportList(DischargeDataEnergyQuery query);

	/**
	 * 能耗上报到集团
	 * @param bo 入参
	 */
	void energyConsumptionReportToTheGroup(EnergyConsumptionReportToTheGroupBo bo);

	/**
	 * 详情
	 *
	 * @param query 参数
	 * @return 详情
	 */
	DischargeDataEnergyVo detail(DischargeDataEnergyQuery query);

	/**
	 * 批量新增
	 *
	 * @param dischargeEnergyIndicatorVos 参数
	 */
	boolean saveList(List<DischargeEnergyIndicatorVo> dischargeEnergyIndicatorVos);

	/**
	 * 批量更新
	 *
	 * @param dischargeDataEnergyBo 参数
	 */
	boolean updateList(DischargeDataEnergyBo dischargeDataEnergyBo);

	/**
	 * 退回数据
	 *
	 * @param dischargeDataEnergyBo 参数
	 */
    boolean rejectData(DischargeDataEnergyBo dischargeDataEnergyBo);


	void download(HttpServletRequest request, HttpServletResponse response, DischargeDataEnergyQuery query);

	void downloadTemplate(HttpServletRequest request, HttpServletResponse response);

	HashMap<String, Object> getPowerStruct();

	boolean applyReturn(DischargeDataEnergyBo dischargeDataEnergyBo);

	List<DischargeDataEnergyVo> getIndicatorDataList(DischargeDataEnergyBo dischargeDataEnergyBo);

	List<DischargeDataEnergyVo> reCountDataList(DischargeDataEnergyBo dischargeDataEnergyBo);
}