package com.enrising.ctsc.carbon.common.utils.pdf;

import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;

@Slf4j
public class PdfUtil {

	/**
	 * 通过html生成文件
	 *
	 * @param htmlContent html格式内容
	 * @param file        输出文件file
	 */
	public static void createdPdfByItextHtml(String htmlContent, File file) {
		InputStream inputStream = null;
		FileOutputStream outputStream = null;
		PdfWriter writer = null;
		try {
			// 1. 获取生成pdf的html内容
			inputStream = new ByteArrayInputStream(htmlContent.getBytes(StandardCharsets.UTF_8));
			outputStream = new FileOutputStream(file);
			Document document = new Document();
			writer = PdfWriter.getInstance(document, outputStream);
			document.open();
			// 2. 添加字体
//            XMLWorkerFontProvider fontImp = new XMLWorkerFontProvider(XMLWorkerFontProvider.DONTLOOKFORFONTS);
//            fontImp.register(getFontPath());
			// 3. 设置编码
			XMLWorkerHelper.getInstance().parseXHtml(writer, document, inputStream, StandardCharsets.UTF_8, new CustomXMLWorkerFontProvider());
			// 4. 关闭,(不关闭则会生成无效pdf)
			document.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			try {
				if (writer != null) {
					writer.close();
				}
				if (outputStream != null) {
					outputStream.close();
				}
				if (inputStream != null) {
					inputStream.close();
				}
			} catch (IOException ex) {
				ex.printStackTrace();
			}
		}
	}

	/**
	 * 通过html生成文件
	 *
	 * @param htmlContent html格式内容
	 */
	public static ByteArrayOutputStream createdPdfByItextHtml(String htmlContent) {
		InputStream inputStream = null;
		ByteArrayOutputStream outputStream = null;
		PdfWriter writer = null;
		try {
			// 1. 获取生成pdf的html内容
			inputStream = new ByteArrayInputStream(htmlContent.getBytes(StandardCharsets.UTF_8));
			outputStream = new ByteArrayOutputStream();
			Document document = new Document();
			writer = PdfWriter.getInstance(document, outputStream);
			document.open();
			// 2. 添加字体
//            XMLWorkerFontProvider fontImp = new XMLWorkerFontProvider(XMLWorkerFontProvider.DONTLOOKFORFONTS);
//            fontImp.register(getFontPath());
			// 3. 设置编码
			XMLWorkerHelper.getInstance().parseXHtml(writer, document, inputStream, StandardCharsets.UTF_8, new CustomXMLWorkerFontProvider());
			// 4. 关闭,(不关闭则会生成无效pdf)
			document.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			try {
				if (writer != null) {
					writer.close();
				}
				if (outputStream != null) {
					outputStream.close();
				}
				if (inputStream != null) {
					inputStream.close();
				}
			} catch (IOException ex) {
				ex.printStackTrace();
			}
		}
		return outputStream;
	}

//
//	public static void main(String[] args) {
//		Map<String, String> html = Maps.newHashMap();
//		html.put("html", "<p>项目概况</p><p>&nbsp;“数字碳资产项目”平台开发人力外包采购项目的潜在投标人应在深圳市福田区竹子林中国经贸大厦10A、B采联国际招标采购集团有限公司深圳分公司获取招标文件，并于2022年10月9日14：45（北京时间）前递交投标文件。</p><h2><font color=\"#ffffff\">一、项目基本情况</font></h2><p>1.项目编号：CLF0122SZ14QY48P</p><p>2.项目名称：“数字碳资产项目”平台开发人力外包采购项目</p><p>3.预算金额：人民币100.00万元</p><p>4.最高支付上限：人民币100.00万元</p><p>5.采购需求：详见招标文件</p><p>6.合同履行期限：自合同签订之日起，服务期限至2022年12月20日。</p><p>7.本项目不接受联合体投标。</p><p>8.不接受投标人选用进口产品参与投标。</p><p>9.其他：本项目属于采购人自行采购项目；采购监督管理部门为采购人的上级主管部门或采购人的纪检部门。</p><h2><code>二、供应商的资格要求：</code></h2><p>1.满足《中华人民共和国政府采购法》第二十二条规定（要求投标人提供营业执照或事业单位法人证等法人证明复印件以及《投标人资格声明函》）。</p><p>2.落实政府采购政策需满足的资格要求：无。</p><p>3.本项目的特定资格要求：无。</p><p>4.参与本项目投标前三年内，在经营活动中没有重大违法记录（由供应商在《投标人资格声明函》中作出声明）。</p><p>5.参与本项目政府采购活动时不存在被有关部门禁止参与政府采购活动且在有效期内的情况（由供应商在《投标人资格声明函》中作出声明）。</p><p>6.为采购项目提供整体设计、规范编制或者项目管理、监理、检测等服务的供应商，不得再参加该采购项目同一合同项下的其他采购活动。（由供应商在《投标人资格声明函》中作出声明）</p><p>7.单位负责人为同一人或者存在直接控股、管理关系的不同供应商，不得参加同一合同项下的政府采购活动。（由供应商在《投标人资格声明函》中作出声明）</p><p>8.未被列入失信被执行人、重大税收违法案件当事人名单（税收违法黑名单）、政府采购严重违法失信行为记录名单（由供应商在《投标人资格声明函》中作出声明）。注：“信用中国”、“中国政府采购网”、“深圳信用网”以及“深圳市政府采购监管网”为供应商信用信息的查询渠道，相关信息以开标当日的查询结果为准。</p><h2>三、获取招标文件</h2><p><!--[if-->1.&nbsp;<!--[endif]-->如采用线下获取招标文件方式：供应商应携带填写好的《采购文件领购登记表》（可在采购代理机构网站（www.chinapsp.cn）中“下载中心”下载）加盖供应商单位公章后，至（深圳市福田区竹子林中国经贸大厦10A、B采联国际招标采购集团有限公司深圳分公司）进行购买，缴纳标书款后即为成功获取招标文件。</p><p>如采用线上获取招标文件方式：供应商应填写并打印《采购文件领购登记表》后，加盖供应商公章扫描发至采购代理机构邮箱（<EMAIL>）。报名资料审核通过后并缴纳标书款后即为报名成功。</p><p><!--[if-->2.&nbsp;<!--[endif]-->获取招标文件过程问题咨询联系人：<u>许小姐</u>，联系电话：<u>0755-88377571-</u><u>2325</u>。</p><p><!--[if-->3.&nbsp;<!--[endif]-->符合资格的供应商应当在<u>2022</u>年<u>9</u>月<u>21</u>日至<u>2022</u>年<u>9</u>月<u>28</u>日期间（上午10：00-12：00，下午15：00-17：30，法定节假日除外，不少于5个工作日）到<u>采联国际招标采购集团有限公司（</u>详细地址：详见上述招标文件获取方式的线下地址、采购代理机构邮箱）购买招标文件，招标文件每套售价<u>6</u><u>00.00</u>元（人民币），售后不退。<s></s></p><p><b>如采用汇款方式购买招标文件请汇至以下账户：</b><b></b></p><p>户名：采联国际招标采购集团有限公司深圳分公司</p><p>开户行：广发银行广州白云机场支行</p><p>账号：9550880212556500152</p><h2>四、提交投标文件截止时间、开标时间和地点</h2><p>&nbsp;&nbsp;&nbsp;&nbsp;1.投标截止时间：所有投标文件应于2022年10月9日14:45（北京时间）时之前提交到深圳市福田区竹子林中国经贸大厦4E采联国际招标采购集团有限公司深圳分公司会议室。</p><p>2.开标时间和地点：定于2022年10月9日14:45<u>（北京时间）</u>时，在深圳市福田区竹子林中国经贸大厦4E采联国际招标采购集团有限公司深圳分公司会议室公开开标。 </p><p>备注：为避免病毒传染的风险，推荐各投标人法定代表人或其授权代表通过邮寄方式，按照规定的投标截止时间前向我司邮寄投标文件（应有一定时间的提前量保证在投标截止时间前送达），<b><u>快递单</u></b><b><u>（或快递外包装）</u></b><b><u>上写明投标人名称、项目编号</u></b>，通过邮寄方式递交的投标文件递交时间以我司代表签收时间为准。逾期或不符合规定的投标文件不予接受。通过邮寄方式递交投标文件，出现以下情况：寄错地址、逾期送达、未按照招标文件要求密封、邮寄过程中投标文件破损，我司将拒绝签收，由投标人自行承担相应责任与后果。</p><h2>五、公告期限</h2><p>自本公告发布之日起5个工作日。</p><h2>六、其他补充事宜</h2><p><!--[if-->1.&nbsp;<!--[endif]--><b>凡参与深圳市政府采购活动的供应商，应自行在深圳公共资源交易中心网站上办理注册（供应商注册网址：</b><b>www.szzfcg.cn</b><b>）。</b><b></b></p><p><!--[if-->2.&nbsp;<!--[endif]-->质疑期限：供应商认为采购文件的内容损害其权益的，应在采购文件公布之日起七个工作日内提出，具体要求详见招标文件第七章 投标人须知 第22条。</p><p><!--[if-->3.&nbsp;<!--[endif]-->采购人及采购代理机构有权对中标供应商就本项目要求提供的相关证明资料（原件）进行审查。供应商提供虚假资料被查实的，则可能面临被取消本项目中标资格、列入不良行为记录名单和三年内禁止参与深圳市政府采购活动的风险。 </p><p><!--[if-->4.&nbsp;<!--[endif]-->本招标公告及本项目招标文件所涉及的时间一律为北京时间。</p><p><!--[if-->5.&nbsp;<!--[endif]-->需要落实的政府采购政策：《政府采购促进中小企业发展管理办法》（财库〔2020〕46 号）、《关于政府采购支持监狱企业发展有关问题的通知》(财库〔2014〕68号)、《关于促进残疾人就业政府采购政策的通知》（财库〔2017〕141号)、《关于环境标志产品政府采购实施的意见》（财库〔2006〕90号）、《节能产品政府采购实施意见》的通知（财库〔2004〕185号）、《关于调整优化节能产品、环境标志产品政府采购执行机制的通知》（财库〔2019〕9号）、《快递包装政府采购需求标准（试行）》（财办库〔2020〕123号）等。</p><p><!--[if-->6.&nbsp;<!--[endif]-->本项目相关公告在以下媒体发布:</p><p><!--[if-->1)&nbsp;<!--[endif]-->法定媒体：法定媒体：中国政府采购网（www.ccgp.gov.cn）、深圳公共资源交易中心网站（www.szzfcg.cn）。相关公告在法定媒体上公布之日即视为有效送达，不再另行通知。</p><p><!--[if-->2)&nbsp;<!--[endif]-->采购代理机构网站（www.chinapsp.cn）。</p><p><!--[if-->7.&nbsp;<!--[endif]-->本项目不需要投标保证金。</p>");
//		String b = "<html><body>" + JSONObject.parseObject(JSONUtil.toJsonStr(html)).get("html").toString() + "</body></html>";
//		createdPdfByItextHtml(b, new File("D:/test.pdf"));
//	}

}
