package com.enrising.ctsc.discharge.api.vo;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 碳排放数据填报表（能源）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */

@Data
public class DischargeDataEnergyUpdateRecordVo extends Model<DischargeDataEnergyUpdateRecordVo> {

	/**
	 * 主键id,采用雪花id
	 */
		private Long id;

	/**
	 * 公司id
	 */
		private Long companyId;

	/**
	 * 创建者id
	 */
		private Long createBy;

	/**
	 * 创建者id
	 */
		private String createName;

	/**
	 * 创建时间
	 */
		private Date createTime;

	/**
	 * 更新者id
	 */
		private Long updateBy;

	/**
	 * 更新时间
	 */
		private Date updateTime;

	/**
	 * 操作类型 1-上报 2-修改
	 */
		private Integer operateType;

	/**
	 * 填报时间
	 */
		private Date reportTime;

	/**
	 * 是否显示
	 */
		private boolean isShow = false;

	/**
	 * 删除标志：0-正常；1-删除
	 */
	@TableLogic
		private String delFlag;


}
