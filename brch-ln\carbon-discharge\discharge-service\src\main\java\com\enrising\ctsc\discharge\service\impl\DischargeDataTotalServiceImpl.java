package com.enrising.ctsc.discharge.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.enrising.ctsc.carbon.common.utils.*;
import com.enrising.ctsc.discharge.api.bo.*;
import com.enrising.ctsc.discharge.api.entity.DischargeMonitorSetting;
import com.enrising.ctsc.discharge.api.enums.*;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.discharge.api.utils.*;
import com.enrising.ctsc.discharge.api.vo.*;
import com.enrising.ctsc.discharge.mapper.*;
import com.enrising.ctsc.discharge.service.*;
import com.enrising.ctsc.discharge.utils.ExcelCommonDictHandlerImpl;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/17
 * @note
 */
@Slf4j
@Service
@AllArgsConstructor
public class DischargeDataTotalServiceImpl implements DischargeDataTotalService {
    private final DischargeDataElectricService dischargeDataElectricService;
    private final DischargeDataOilService dischargeDataOilService;
    private final DischargeDataGasService dischargeDataGasService;
    private final DischargeDataThermalService dischargeDataThermalService;
    private final DischargeDataCoalService dischargeDataCoalService;
    private final DischargeDataWaterService dischargeDataWaterService;
//    private final RemoteAdminService remoteAdminService;
//    private final RemoteUserService remoteUserService;
//    private final RemoteBusinessService remoteBusinessService;
    private final DischargeMonitorSettingService dischargeMonitorSettingService;
//    private final CommonService commonService;
    private final DischargeDataElectricMapper dischargeDataElectricMapper;
    private final DischargeDataCoalMapper dischargeDataCoalMapper;
    private final DischargeDataGasMapper dischargeDataGasMapper;
    private final DischargeDataOilMapper dischargeDataOilMapper;
    private final DischargeDataThermalMapper dischargeDataThermalMapper;
    private final DischargeDataWaterMapper dischargeDataWaterMapper;

    private final DischargeDataTotalMapper dischargeDataTotalMapper;

    @Override
    public List<DischargeDataTotalVo> getDataList(DischargeDataTotalBo dischargeDataTotalBo) {
        Date[] dataDate = getDateByYear(dischargeDataTotalBo.getDataYear());
        if (ArrayUtil.isNotEmpty(dataDate)) {
            return getDataByTime(dischargeDataTotalBo.getCompanyId(), dataDate[0], dataDate[1]);
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public DischargeDataOpenVo getCarbonData(DischargeDataOpenBo bo) {
        // 查看条件是否规范 获取所有的省份列表
        List<SysDeptVO> deptList = dischargeDataTotalMapper.getSysDeptList();
        boolean contains = deptList.stream().map(SysDeptVO::getOrgName).collect(Collectors.toList())
                .contains(bo.getCompanyName());
        if (!contains) {
            throw new BusinessException("系统中无此分公司，请确认后查询！");
        }

        String match = "^\\d{4}-(0[1-9]|1[0-2])$";
        if (!Pattern.matches(match, bo.getStartTime()) || !Pattern.matches(match, bo.getEndTime())) {
            throw new BusinessException("时间格式不正确，请确认后查询（如 2023-01 ）！");
        }
        DateTime startMonth = DateUtil.parse(bo.getStartTime(), "yyyy-MM");
        DateTime endMonth = DateUtil.parse(bo.getEndTime(), "yyyy-MM");
        DateTime endTime = DateUtil.endOfMonth(endMonth);
        DateTime startTime = DateUtil.beginOfMonth(startMonth);
        if (startTime.isAfter(endTime)) {
            throw new BusinessException("开始时间不能大于结束时间，请确认后查询！");
        }
        deptList.stream().filter(node -> node.getOrgName().equals(bo.getCompanyName()))
                .findFirst().ifPresent(company -> bo.setCompanyId(company.getId())
                );

        DischargeDataOpenVo vo = new DischargeDataOpenVo();
        final BigDecimal[] carbonEmissions = {new BigDecimal(0)};
        final BigDecimal[] energyConsumption = {new BigDecimal(0)};
        // 电
        List<DischargeDataElectricVo> electricList = dischargeDataElectricService.countCompanyData(startTime, endTime, bo.getCompanyId());

        electricList.stream().map(DischargeDataElectricVo::getOutsourcingThermalPower).reduce(BigDecimal::add).ifPresent(
                vo::setOutsourcingThermalPower
        );
        electricList.stream().map(DischargeDataElectricVo::getOutsourcingGreenPower).reduce(BigDecimal::add).ifPresent(
                vo::setOutsourcingGreenPower
        );
        electricList.stream().map(DischargeDataElectricVo::getOwnGreenPower).reduce(BigDecimal::add).ifPresent(
                vo::setOwnGreenPower
        );
        electricList.stream().map(DischargeDataElectricVo::getCarbonEmissions).reduce(BigDecimal::add).ifPresent(
                node -> carbonEmissions[0] = carbonEmissions[0].add(node)
        );
        electricList.stream().map(DischargeDataElectricVo::getEnergyConsumption).reduce(BigDecimal::add).ifPresent(
                node -> energyConsumption[0] = energyConsumption[0].add(node)
        );
        // 气
        List<DischargeDataGasVo> gasList = dischargeDataGasService.countCompanyData(startTime, endTime, bo.getCompanyId());
        gasList.stream().map(DischargeDataGasVo::getLpg).reduce(BigDecimal::add).ifPresent(
                vo::setLpg
        );
        gasList.stream().map(DischargeDataGasVo::getNg).reduce(BigDecimal::add).ifPresent(
                vo::setNg
        );
        gasList.stream().map(DischargeDataGasVo::getCarbonEmissions).reduce(BigDecimal::add).ifPresent(
                node -> carbonEmissions[0] = carbonEmissions[0].add(node)
        );
        gasList.stream().map(DischargeDataGasVo::getEnergyConsumption).reduce(BigDecimal::add).ifPresent(
                node -> energyConsumption[0] = energyConsumption[0].add(node)
        );
        // 油
        List<DischargeDataOilVo> oilList = dischargeDataOilService.countCompanyData(startTime, endTime, bo.getCompanyId());
        oilList.stream().map(DischargeDataOilVo::getCrude).reduce(BigDecimal::add).ifPresent(
                vo::setCrude
        );
        oilList.stream().map(DischargeDataOilVo::getDiesel).reduce(BigDecimal::add).ifPresent(
                vo::setDiesel
        );
        oilList.stream().map(DischargeDataOilVo::getFuel).reduce(BigDecimal::add).ifPresent(
                vo::setFuel
        );
        oilList.stream().map(DischargeDataOilVo::getGasoline).reduce(BigDecimal::add).ifPresent(
                vo::setGasoline
        );
        oilList.stream().map(DischargeDataOilVo::getKerosene).reduce(BigDecimal::add).ifPresent(
                vo::setKerosene
        );
        oilList.stream().map(DischargeDataOilVo::getCarbonEmissions).reduce(BigDecimal::add).ifPresent(
                node -> carbonEmissions[0] = carbonEmissions[0].add(node)
        );
        oilList.stream().map(DischargeDataOilVo::getEnergyConsumption).reduce(BigDecimal::add).ifPresent(
                node -> energyConsumption[0] = energyConsumption[0].add(node)
        );
        // 水
        List<DischargeDataWaterVo> waterList = dischargeDataWaterService.countCompanyData(startTime, endTime, bo.getCompanyId());
        waterList.stream().map(DischargeDataWaterVo::getWater).reduce(BigDecimal::add).ifPresent(
                vo::setWater
        );
        waterList.stream().map(DischargeDataWaterVo::getCarbonEmissions).reduce(BigDecimal::add).ifPresent(
                node -> carbonEmissions[0] = carbonEmissions[0].add(node)
        );
        waterList.stream().map(DischargeDataWaterVo::getEnergyConsumption).reduce(BigDecimal::add).ifPresent(
                node -> energyConsumption[0] = energyConsumption[0].add(node)
        );
        // 热力
        List<DischargeDataThermalVo> thermalList = dischargeDataThermalService.countCompanyData(startTime, endTime, bo.getCompanyId());
        thermalList.stream().map(DischargeDataThermalVo::getThermal).reduce(BigDecimal::add).ifPresent(
                vo::setThermal
        );
        thermalList.stream().map(DischargeDataThermalVo::getCarbonEmissions).reduce(BigDecimal::add).ifPresent(
                node -> carbonEmissions[0] = carbonEmissions[0].add(node)
        );
        thermalList.stream().map(DischargeDataThermalVo::getEnergyConsumption).reduce(BigDecimal::add).ifPresent(
                node -> energyConsumption[0] = energyConsumption[0].add(node)
        );
        // 煤炭
        List<DischargeDataCoalVo> DataCoalList = dischargeDataCoalService.countCompanyData(startTime, endTime, bo.getCompanyId());
        DataCoalList.stream().map(DischargeDataCoalVo::getCoal).reduce(BigDecimal::add).ifPresent(
                vo::setCoal
        );
        DataCoalList.stream().map(DischargeDataCoalVo::getCarbonEmissions).reduce(BigDecimal::add).ifPresent(
                node -> carbonEmissions[0] = carbonEmissions[0].add(node)
        );
        DataCoalList.stream().map(DischargeDataCoalVo::getEnergyConsumption).reduce(BigDecimal::add).ifPresent(
                node -> energyConsumption[0] = energyConsumption[0].add(node)
        );
        vo.setCarbonEmissions(carbonEmissions[0]);
        vo.setEnergyConsumption(energyConsumption[0]);
        vo.setCompanyId(bo.getCompanyId());
        vo.setCompanyName(bo.getCompanyName());
        return vo;
    }

    @Override
    public List<DischargeDataTotalCompareVo> getDataCompareList(DischargeDataTotalBo dischargeDataTotalBo) {
        List<DischargeDataTotalCompareVo> dischargeDataTotalCompareVoList = new ArrayList<>();
        if (ObjectUtil.isEmpty(dischargeDataTotalBo.getCompanyId())) {
            dischargeDataTotalBo.setCompanyId(JwtUtils.getCurrentUserCompanyId());
        }
        Date dateStart;
        Date dateEnd;
        Date lastDateStart;
        Date lastDateEnd;
        if (dischargeDataTotalBo.getTimeType().equals(DischargeCarbonTimeType.CUSTOM.getValue()) &&
                ArrayUtil.isNotEmpty(dischargeDataTotalBo.getCustomTime())) {
            dateStart = DateUtil.beginOfMonth(StrToDate(dischargeDataTotalBo.getCustomTime()[0]));
            dateEnd = DateUtil.endOfMonth(StrToDate(dischargeDataTotalBo.getCustomTime()[1]));
            lastDateStart = getLastYearSameDay(dateStart);
            lastDateEnd = getLastYearSameDay(dateEnd);
        } else {
            Date[] dateData = this.getDateByYear(dischargeDataTotalBo.getDataYear());
            dateStart = dateData[0];
            dateEnd = dateData[1];
            lastDateStart = dateData[2];
            lastDateEnd = dateData[3];
        }
        List<DischargeDataTotalCompareVo> currentDataList = this.getDataCompareListByType(dateStart, dateEnd,
                dischargeDataTotalBo.getCompanyId(), dischargeDataTotalBo.getDataType(), dischargeDataTotalBo.getTimeType());
        List<DischargeDataTotalCompareVo> lastDataList = this.getDataCompareListByType(lastDateStart, lastDateEnd,
                dischargeDataTotalBo.getCompanyId(), dischargeDataTotalBo.getDataType(), dischargeDataTotalBo.getTimeType());
        for (int i = 0; i < currentDataList.size(); i++) {
            if (StrUtil.isNotBlank(currentDataList.get(i).getDataMonth())) {
                DischargeDataTotalCompareVo dischargeDataTotalCompareVo = new DischargeDataTotalCompareVo();
                dischargeDataTotalCompareVo.setCompanyId(dischargeDataTotalBo.getCompanyId());
                dischargeDataTotalCompareVo.setDataMonth(currentDataList.get(i).getDataMonth());
                dischargeDataTotalCompareVo.setDataYear(currentDataList.get(i).getDataYear());
                dischargeDataTotalCompareVo.setDataValue(currentDataList.get(i).getDataValue());
                //计算环比
                BigDecimal lastData = null;
                if (i == 0) {
                    Date[] lastDates = getLastDate(currentDataList.get(i).getDataMonth(),
                            currentDataList.get(i).getDataYear(), dischargeDataTotalBo.getTimeType());
                    List<DischargeDataTotalCompareVo> tmpDataList;
                    tmpDataList = this.getDataCompareListByType(lastDates[0], lastDates[1],
                            dischargeDataTotalBo.getCompanyId(), dischargeDataTotalBo.getDataType(), dischargeDataTotalBo.getTimeType());
                    if (tmpDataList.size() > 0 &&
                            StrUtil.isNotBlank(tmpDataList.get(tmpDataList.size() - 1).getDataMonth()) &&
                            ObjectUtil.isNotEmpty(tmpDataList.get(tmpDataList.size() - 1).getDataValue())) {
                        lastData = tmpDataList.get(tmpDataList.size() - 1).getDataValue();
                    }
                } else {
                    if (StrUtil.isNotBlank(currentDataList.get(i - 1).getDataMonth())) {
                        if (getMonthIndex(currentDataList.get(i - 1).getDataMonth()) + 1 == getMonthIndex(currentDataList.get(i).getDataMonth())) {
                            lastData = currentDataList.get(i - 1).getDataValue();
                        }
                    }
                }
                if (ObjectUtil.isNotEmpty(lastData) && lastData.intValue() > 0) {
                    BigDecimal dataYoY = dischargeDataTotalCompareVo.getDataValue().subtract(lastData).
                            divide(lastData, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).
                            setScale(2, RoundingMode.HALF_UP);
                    dischargeDataTotalCompareVo.setDataMomCoord(dataYoY.intValue());
                    dischargeDataTotalCompareVo.setDataMoM(dataYoY.toString() + "%");
                } else {
                    dischargeDataTotalCompareVo.setDataMoM("--");
                }
                //计算同比
                BigDecimal lastDataY = null;
                if (lastDataList.size() > i && StrUtil.isNotBlank(lastDataList.get(i).getDataMonth()) &&
                        ObjectUtil.isNotEmpty(lastDataList.get(i).getDataValue()) &&
                        lastDataList.get(i).getDataValue().compareTo(BigDecimal.valueOf(0)) != 0) {
                    lastDataY = lastDataList.get(i).getDataValue();
                    BigDecimal dataYoY = new BigDecimal(0);
                    if (lastDataY.intValue() > 0) {
                        dataYoY = dischargeDataTotalCompareVo.getDataValue().subtract(lastDataY).
                                divide(lastDataY, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).
                                setScale(2, RoundingMode.HALF_UP);
                    }
                    dischargeDataTotalCompareVo.setDataYoyCoord(dataYoY.intValue());
                    dischargeDataTotalCompareVo.setDataYoY(dataYoY.toString() + "%");
                } else {
                    dischargeDataTotalCompareVo.setDataYoY("--");
                }
                dischargeDataTotalCompareVoList.add(dischargeDataTotalCompareVo);
            }
        }
        return dischargeDataTotalCompareVoList;
    }

    @Override
    public List<DischargeDataTotalQueryVo> getDataQueryList(Integer dataYear, String dataType, String timeType) {
        List<DischargeDataTotalQueryVo> dataTotalQueryVoList = new ArrayList<>();

        Date[] dateData = this.getDateByYear(dataYear);
        List<List<DischargeDataTotalCompareVo>> currentDataList = this.getDataCompareListByTypeNew(dateData[0],
                dateData[1], 0L, dataType, timeType);
        if (CollectionUtil.isNotEmpty(currentDataList)) {
            currentDataList.forEach(dataTotalCompareVoList -> {
                DischargeDataTotalQueryVo dischargeDataTotalQueryVo = new DischargeDataTotalQueryVo();
                BigDecimal[] dataArray;
                if (timeType.equals(DischargeCarbonTimeType.QUARTER.getValue())) {
                    dataArray = new BigDecimal[4];
                } else {
                    dataArray = new BigDecimal[12];
                }
                dischargeDataTotalQueryVo.setCompanyId(dataTotalCompareVoList.get(0).getCompanyId());
                dischargeDataTotalQueryVo.setCityCode(dataTotalCompareVoList.get(0).getCityCode());
                dischargeDataTotalQueryVo.setCompanyName(dataTotalCompareVoList.get(0).getCompanyName());
                dischargeDataTotalQueryVo.setDataAllYear(BigDecimal.valueOf(0));
                for (int i = 0; i < dataTotalCompareVoList.size(); i++) {
                    if (StrUtil.isNotBlank(dataTotalCompareVoList.get(i).getDataMonth())) {
                        switch (i) {
                            case 0:
                                dischargeDataTotalQueryVo.setDataJan(dataTotalCompareVoList.get(i).getDataValue());
                                break;
                            case 1:
                                dischargeDataTotalQueryVo.setDataFeb(dataTotalCompareVoList.get(i).getDataValue());
                                break;
                            case 2:
                                dischargeDataTotalQueryVo.setDataMar(dataTotalCompareVoList.get(i).getDataValue());
                                break;
                            case 3:
                                dischargeDataTotalQueryVo.setDataApr(dataTotalCompareVoList.get(i).getDataValue());
                                break;
                            case 4:
                                dischargeDataTotalQueryVo.setDataMay(dataTotalCompareVoList.get(i).getDataValue());
                                break;
                            case 5:
                                dischargeDataTotalQueryVo.setDataJun(dataTotalCompareVoList.get(i).getDataValue());
                                break;
                            case 6:
                                dischargeDataTotalQueryVo.setDataJul(dataTotalCompareVoList.get(i).getDataValue());
                                break;
                            case 7:
                                dischargeDataTotalQueryVo.setDataAug(dataTotalCompareVoList.get(i).getDataValue());
                                break;
                            case 8:
                                dischargeDataTotalQueryVo.setDataSept(dataTotalCompareVoList.get(i).getDataValue());
                                break;
                            case 9:
                                dischargeDataTotalQueryVo.setDataOct(dataTotalCompareVoList.get(i).getDataValue());
                                break;
                            case 10:
                                dischargeDataTotalQueryVo.setDataNov(dataTotalCompareVoList.get(i).getDataValue());
                                break;
                            case 11:
                                dischargeDataTotalQueryVo.setDataDec(dataTotalCompareVoList.get(i).getDataValue());
                                break;
                        }
                        dataArray[i] = dataTotalCompareVoList.get(i).getDataValue();
                        dischargeDataTotalQueryVo.setDataAllYear(dischargeDataTotalQueryVo.getDataAllYear().add(dataArray[i]));
                    }
                }
                dischargeDataTotalQueryVo.setDataArray(dataArray);
                dataTotalQueryVoList.add(dischargeDataTotalQueryVo);
            });
        }
        return dataTotalQueryVoList;
    }

    private List<DischargeDataTotalVo> getAllDataList(Date dateStart, Date dateEnd, Long companyId) {
        List<DischargeDataTotalVo> dischargeDataTotalVoList = new ArrayList<>();
        DischargeDataTotalVo[] totalVos = new DischargeDataTotalVo[12];
        for (int i = 0; i < totalVos.length; i++) {
            totalVos[i] = new DischargeDataTotalVo();
            totalVos[i].setMonth(i);
            totalVos[i].setCompanyId(companyId);
        }
        //获取电力数据
        List<DischargeDataElectricVo> dischargeDataElectricVoList = dischargeDataElectricService.
                countCompanyData(dateStart, dateEnd, companyId);
        if (CollectionUtil.isNotEmpty(dischargeDataElectricVoList)) {
            dischargeDataElectricVoList.forEach(dischargeDataElectricVo -> {
                int iIndex = getMonthIndex(dischargeDataElectricVo.getDataMonth());
                if (iIndex > 0) {
                    totalVos[iIndex - 1].setDataMonth(dischargeDataElectricVo.getDataMonth());
                    totalVos[iIndex - 1].setDataYear(dischargeDataElectricVo.getDataYear());
                    totalVos[iIndex - 1].setOutsourcingThermalPower(totalVos[iIndex - 1].getOutsourcingThermalPower()
                            .add(dischargeDataElectricVo.getOutsourcingThermalPower()));
                    totalVos[iIndex - 1].setOwnGreenPower(totalVos[iIndex - 1].getOwnGreenPower()
                            .add(dischargeDataElectricVo.getOwnGreenPower()));
                    totalVos[iIndex - 1].setPower(totalVos[iIndex - 1].getPower().add(dischargeDataElectricVo
                            .getOutsourcingThermalPower()).subtract(dischargeDataElectricVo.getOwnGreenPower()));
                    totalVos[iIndex - 1].setCarbonPower(totalVos[iIndex - 1].getCarbonPower()
                            .add(dischargeDataElectricVo.getCarbonEmissions()));
                    totalVos[iIndex - 1].setConsumptionPower(totalVos[iIndex - 1].getConsumptionPower().
                            add(dischargeDataElectricVo.getEnergyConsumption()));
                }
            });
        }
        //获取油类数据
        List<DischargeDataOilVo> dischargeDataOilVoList = dischargeDataOilService.countCompanyData(
                dateStart, dateEnd, companyId);
        if (CollectionUtil.isNotEmpty(dischargeDataOilVoList)) {
            dischargeDataOilVoList.forEach(dischargeDataOilVo -> {
                int iIndex = getMonthIndex(dischargeDataOilVo.getDataMonth());
                if (iIndex > 0) {
                    totalVos[iIndex - 1].setDataMonth(dischargeDataOilVo.getDataMonth());
                    totalVos[iIndex - 1].setDataYear(dischargeDataOilVo.getDataYear());
                    totalVos[iIndex - 1].setGasoline(totalVos[iIndex - 1].getGasoline().add(dischargeDataOilVo.getGasoline()));
                    totalVos[iIndex - 1].setCarbonGasoline(totalVos[iIndex - 1].getCarbonGasoline()
                            .add(dischargeDataOilVo.getCarbonGasoline()));
                    totalVos[iIndex - 1].setConsumptionGasoline(totalVos[iIndex - 1].getConsumptionGasoline()
                            .add(dischargeDataOilVo.getConsumptionGasoline()));
                    totalVos[iIndex - 1].setDiesel(totalVos[iIndex - 1].getDiesel().add(dischargeDataOilVo.getDiesel()));
                    totalVos[iIndex - 1].setCarbonDiesel(totalVos[iIndex - 1].getCarbonDiesel().add(dischargeDataOilVo
                            .getCarbonDiesel()));
                    totalVos[iIndex - 1].setConsumptionDiesel(totalVos[iIndex - 1].getConsumptionDiesel().add(dischargeDataOilVo
                            .getConsumptionDiesel()));
                    totalVos[iIndex - 1].setCrude(totalVos[iIndex - 1].getCrude().add(dischargeDataOilVo.getCrude()));
                    totalVos[iIndex - 1].setCarbonCrude(totalVos[iIndex - 1].getCarbonCrude().add((dischargeDataOilVo
                            .getCarbonCrude())));
                    totalVos[iIndex - 1].setConsumptionCrude(totalVos[iIndex - 1].getConsumptionCrude().add((dischargeDataOilVo
                            .getConsumptionCrude())));
                    totalVos[iIndex - 1].setFuel(totalVos[iIndex - 1].getFuel().add(dischargeDataOilVo.getFuel()));
                    totalVos[iIndex - 1].setCarbonFuel(totalVos[iIndex - 1].getCarbonFuel().add(dischargeDataOilVo
                            .getCarbonFuel()));
                    totalVos[iIndex - 1].setConsumptionFuel(totalVos[iIndex - 1].getConsumptionFuel().add(dischargeDataOilVo
                            .getConsumptionFuel()));
                    totalVos[iIndex - 1].setKerosene(totalVos[iIndex - 1].getKerosene().add(dischargeDataOilVo.getKerosene()));
                    totalVos[iIndex - 1].setCarbonKerosene(totalVos[iIndex - 1].getCarbonKerosene().add(dischargeDataOilVo
                            .getCarbonKerosene()));
                    totalVos[iIndex - 1].setConsumptionKerosene(totalVos[iIndex - 1].getConsumptionKerosene().add(dischargeDataOilVo
                            .getConsumptionKerosene()));
                }
            });
        }
        //获取汽类数据
        List<DischargeDataGasVo> dischargeDataGasVoList = dischargeDataGasService.
                countCompanyData(dateStart, dateEnd, companyId);
        if (CollectionUtil.isNotEmpty(dischargeDataGasVoList)) {
            dischargeDataGasVoList.forEach(dischargeDataGasVo -> {
                int iIndex = getMonthIndex(dischargeDataGasVo.getDataMonth());
                if (iIndex > 0) {
                    totalVos[iIndex - 1].setDataMonth(dischargeDataGasVo.getDataMonth());
                    totalVos[iIndex - 1].setDataYear(dischargeDataGasVo.getDataYear());
                    totalVos[iIndex - 1].setNg(totalVos[iIndex - 1].getNg().add(dischargeDataGasVo.getNg()));
                    totalVos[iIndex - 1].setCarbonNg(totalVos[iIndex - 1].getCarbonNg().add(dischargeDataGasVo.getCarbonNg()));
                    totalVos[iIndex - 1].setConsumptionNg(totalVos[iIndex - 1].getConsumptionNg()
                            .add(dischargeDataGasVo.getConsumptionNg()));
                    totalVos[iIndex - 1].setLpg(totalVos[iIndex - 1].getLpg().add(dischargeDataGasVo.getLpg()));
                    totalVos[iIndex - 1].setCarbonLpg(totalVos[iIndex - 1].getCarbonLpg().add(dischargeDataGasVo
                            .getCarbonLpg()));
                    totalVos[iIndex - 1].setConsumptionLpg(totalVos[iIndex - 1].getConsumptionLpg().add(dischargeDataGasVo
                            .getConsumptionLpg()));
                }
            });
        }
        //获取热力数据
        List<DischargeDataThermalVo> dischargeDataThermalVoList = dischargeDataThermalService.
                countCompanyData(dateStart, dateEnd, companyId);
        if (CollectionUtil.isNotEmpty(dischargeDataThermalVoList)) {
            dischargeDataThermalVoList.forEach(dischargeDataThermalVo -> {
                int iIndex = getMonthIndex(dischargeDataThermalVo.getDataMonth());
                if (iIndex > 0) {
                    totalVos[iIndex - 1].setDataMonth(dischargeDataThermalVo.getDataMonth());
                    totalVos[iIndex - 1].setDataYear(dischargeDataThermalVo.getDataYear());
                    totalVos[iIndex - 1].setThermal(totalVos[iIndex - 1].getThermal().add(dischargeDataThermalVo.getThermal()));
                    totalVos[iIndex - 1].setCarbonThermal(totalVos[iIndex - 1].getCarbonThermal()
                            .add(dischargeDataThermalVo.getCarbonEmissions()));
                    totalVos[iIndex - 1].setConsumptionThermal(totalVos[iIndex - 1].getConsumptionThermal().
                            add(dischargeDataThermalVo.getEnergyConsumption()));
                }
            });
        }
        //获取水量数据
        List<DischargeDataWaterVo> dischargeDataWaterVoList = dischargeDataWaterService.
                countCompanyData(dateStart, dateEnd, companyId);
        if (CollectionUtil.isNotEmpty(dischargeDataWaterVoList)) {
            dischargeDataWaterVoList.forEach(dischargeDataWaterVo -> {
                int iIndex = getMonthIndex(dischargeDataWaterVo.getDataMonth());
                if (iIndex > 0) {
                    totalVos[iIndex - 1].setDataMonth(dischargeDataWaterVo.getDataMonth());
                    totalVos[iIndex - 1].setDataYear(dischargeDataWaterVo.getDataYear());
                    totalVos[iIndex - 1].setWater(totalVos[iIndex - 1].getWater().add(dischargeDataWaterVo.getWater()));
                    totalVos[iIndex - 1].setCarbonWater(totalVos[iIndex - 1].getCarbonWater().add(dischargeDataWaterVo
                            .getCarbonEmissions()));
                    totalVos[iIndex - 1].setConsumptionWater(totalVos[iIndex - 1].getConsumptionWater().
                            add(dischargeDataWaterVo.getEnergyConsumption()));
                }
            });
        }
        //获取煤碳数据
        List<DischargeDataCoalVo> dischargeDataCoalVoList = dischargeDataCoalService.
                countCompanyData(dateStart, dateEnd, companyId);
        if (CollectionUtil.isNotEmpty(dischargeDataCoalVoList)) {
            dischargeDataCoalVoList.forEach(dischargeDataCoalVo -> {
                int iIndex = getMonthIndex(dischargeDataCoalVo.getDataMonth());
                if (iIndex > 0) {
                    totalVos[iIndex - 1].setDataMonth(dischargeDataCoalVo.getDataMonth());
                    totalVos[iIndex - 1].setDataYear(dischargeDataCoalVo.getDataYear());
                    totalVos[iIndex - 1].setCoal(totalVos[iIndex - 1].getCoal().add(dischargeDataCoalVo.getCoal()));
                    totalVos[iIndex - 1].setCarbonCoal(totalVos[iIndex - 1].getCarbonCoal().add(dischargeDataCoalVo
                            .getCarbonEmissions()));
                    totalVos[iIndex - 1].setConsumptionCoal(totalVos[iIndex - 1].getConsumptionCoal().
                            add(dischargeDataCoalVo.getEnergyConsumption()));
                }
            });
        }
        for (int i = 0; i < totalVos.length; i++) {
            dischargeDataTotalVoList.add(totalVos[i]);
        }
        return dischargeDataTotalVoList;
    }

    private List<DischargeDataTotalVo[]> getAllDataListByCompany(Date dateStart, Date dateEnd, Long companyId, String carbonDataType) {
        List<DischargeDataTotalVo[]> tmpList = new ArrayList<>();
        List<SysDeptVO> sysDeptList = dischargeDataTotalMapper.getSysDeptList();
        if (CollectionUtil.isNotEmpty(sysDeptList)) {
            for (int k = 0; k < sysDeptList.size(); k++) {
                if (companyId != 0 && !companyId.equals(sysDeptList.get(k).getId())) {
                    continue;
                }
                DischargeDataTotalVo[] totalVos = new DischargeDataTotalVo[12];
                for (int i = 0; i < totalVos.length; i++) {
                    totalVos[i] = new DischargeDataTotalVo();
                    totalVos[i].setMonth(i);
                    totalVos[i].setCompanyId(sysDeptList.get(k).getId());
                    totalVos[i].setCompanyName(sysDeptList.get(k).getOrgName());
                    totalVos[i].setCityCode(sysDeptList.get(k).getParentGroupNo());
                }
                tmpList.add(totalVos);
            }
            ;
        }

        if (carbonDataType.equals(DischargeCarbonDataType.TYPE_ALL.getValue()) ||
                carbonDataType.equals(DischargeCarbonDataType.POWER.getValue())) {
            //获取电力数据
            List<DischargeDataElectricVo> dischargeDataElectricVoList = dischargeDataElectricService.
                    countCompanyData(dateStart, dateEnd, companyId);
            if (CollectionUtil.isNotEmpty(dischargeDataElectricVoList)) {
                dischargeDataElectricVoList.forEach(dischargeDataElectricVo -> {
                    Long cId = dischargeDataElectricVo.getCompanyId();
                    DischargeDataTotalVo[] totalVos = tmpList.stream()
                            .filter(dischargeDataTotalVos -> dischargeDataTotalVos[0].getCompanyId().equals(cId))
                            .findAny()
                            .orElse(null);
                    if (ObjectUtil.isNotEmpty(totalVos)) {
                        int iIndex = getMonthIndex(dischargeDataElectricVo.getDataMonth());
                        if (iIndex > 0) {
                            totalVos[iIndex - 1].setDataMonth(dischargeDataElectricVo.getDataMonth());
                            totalVos[iIndex - 1].setDataYear(dischargeDataElectricVo.getDataYear());
                            totalVos[iIndex - 1].setReportTime(dischargeDataElectricVo.getReportTime());
                            totalVos[iIndex - 1].setPower(dischargeDataElectricVo.getOutsourcingThermalPower().
                                    subtract(dischargeDataElectricVo.getOwnGreenPower()));
                            totalVos[iIndex - 1].setCarbonPower(dischargeDataElectricVo.getCarbonEmissions());
                            totalVos[iIndex - 1].setConsumptionPower(totalVos[iIndex - 1].getConsumptionPower().
                                    add(dischargeDataElectricVo.getEnergyConsumption()));
                        }
                    }
                });
            }
        }
        if (carbonDataType.equals(DischargeCarbonDataType.TYPE_ALL.getValue()) || carbonDataType.equals(DischargeCarbonDataType.OIL.getValue())) {
            //获取油类数据
            List<DischargeDataOilVo> dischargeDataOilVoList = dischargeDataOilService
                    .countCompanyData(dateStart, dateEnd, companyId);
            if (CollectionUtil.isNotEmpty(dischargeDataOilVoList)) {
                dischargeDataOilVoList.forEach(dischargeDataOilVo -> {
                    Long cId = dischargeDataOilVo.getCompanyId();
                    DischargeDataTotalVo[] totalVos = tmpList.stream()
                            .filter(dischargeDataTotalVos -> dischargeDataTotalVos[0].getCompanyId().equals(cId))
                            .findAny()
                            .orElse(null);
                    if (ObjectUtil.isNotEmpty(totalVos)) {
                        int iIndex = getMonthIndex(dischargeDataOilVo.getDataMonth());
                        if (iIndex > 0) {
                            dischargeDataOilVo.setCarbonEmissions(dischargeDataOilVo.getCarbonGasoline()
                                    .add(dischargeDataOilVo.getCarbonDiesel()).add(dischargeDataOilVo.getCarbonCrude())
                                    .add(dischargeDataOilVo.getCarbonFuel()).add(dischargeDataOilVo.getCarbonKerosene()));
                            totalVos[iIndex - 1].setReportTime(dischargeDataOilVo.getReportTime());
                            totalVos[iIndex - 1].setDataMonth(dischargeDataOilVo.getDataMonth());
                            totalVos[iIndex - 1].setDataYear(dischargeDataOilVo.getDataYear());
                            totalVos[iIndex - 1].setGasoline(dischargeDataOilVo.getGasoline());
                            totalVos[iIndex - 1].setCarbonGasoline(dischargeDataOilVo.getCarbonGasoline());
                            totalVos[iIndex - 1].setConsumptionGasoline(dischargeDataOilVo.getConsumptionGasoline());
                            totalVos[iIndex - 1].setDiesel(dischargeDataOilVo.getDiesel());
                            totalVos[iIndex - 1].setCarbonDiesel(dischargeDataOilVo.getCarbonDiesel());
                            totalVos[iIndex - 1].setConsumptionDiesel(dischargeDataOilVo.getConsumptionDiesel());
                            totalVos[iIndex - 1].setCrude(dischargeDataOilVo.getCrude());
                            totalVos[iIndex - 1].setCarbonCrude(dischargeDataOilVo.getCarbonCrude());
                            totalVos[iIndex - 1].setConsumptionCrude(dischargeDataOilVo.getConsumptionCrude());
                            totalVos[iIndex - 1].setFuel(dischargeDataOilVo.getFuel());
                            totalVos[iIndex - 1].setCarbonFuel(dischargeDataOilVo.getCarbonFuel());
                            totalVos[iIndex - 1].setConsumptionFuel(dischargeDataOilVo.getConsumptionFuel());
                            totalVos[iIndex - 1].setKerosene(dischargeDataOilVo.getKerosene());
                            totalVos[iIndex - 1].setCarbonKerosene(dischargeDataOilVo.getCarbonKerosene());
                            totalVos[iIndex - 1].setConsumptionKerosene(dischargeDataOilVo.getConsumptionKerosene());
                        }
                    }
                });
            }
        }
        if (carbonDataType.equals(DischargeCarbonDataType.TYPE_ALL.getValue()) || carbonDataType.equals(DischargeCarbonDataType.GAS.getValue())) {
            //获取汽类数据
            List<DischargeDataGasVo> dischargeDataGasVoList = dischargeDataGasService.
                    countCompanyData(dateStart, dateEnd, companyId);
            if (CollectionUtil.isNotEmpty(dischargeDataGasVoList)) {
                dischargeDataGasVoList.forEach(dischargeDataGasVo -> {
                    Long cId = dischargeDataGasVo.getCompanyId();
                    DischargeDataTotalVo[] totalVos = tmpList.stream()
                            .filter(dischargeDataTotalVos -> dischargeDataTotalVos[0].getCompanyId().equals(cId))
                            .findAny()
                            .orElse(null);
                    if (ObjectUtil.isNotEmpty(totalVos)) {
                        int iIndex = getMonthIndex(dischargeDataGasVo.getDataMonth());
                        if (iIndex > 0) {
                            dischargeDataGasVo.setCarbonEmissions(dischargeDataGasVo.getCarbonNg()
                                    .add(dischargeDataGasVo.getCarbonLpg()));
                            totalVos[iIndex - 1].setReportTime(dischargeDataGasVo.getReportTime());
                            totalVos[iIndex - 1].setDataMonth(dischargeDataGasVo.getDataMonth());
                            totalVos[iIndex - 1].setDataYear(dischargeDataGasVo.getDataYear());
                            totalVos[iIndex - 1].setNg(dischargeDataGasVo.getNg());
                            totalVos[iIndex - 1].setCarbonNg(dischargeDataGasVo.getCarbonNg());
                            totalVos[iIndex - 1].setConsumptionNg(dischargeDataGasVo.getConsumptionNg());
                            totalVos[iIndex - 1].setLpg(dischargeDataGasVo.getLpg());
                            totalVos[iIndex - 1].setCarbonLpg(dischargeDataGasVo.getCarbonLpg());
                            totalVos[iIndex - 1].setConsumptionLpg(dischargeDataGasVo.getConsumptionLpg());
                        }
                    }
                });
            }
        }
        if (carbonDataType.equals(DischargeCarbonDataType.TYPE_ALL.getValue()) || carbonDataType.equals(DischargeCarbonDataType.THERMAL.getValue())) {
            //获取热力数据
            List<DischargeDataThermalVo> dischargeDataThermalVoList = dischargeDataThermalService.
                    countCompanyData(dateStart, dateEnd, companyId);
            if (CollectionUtil.isNotEmpty(dischargeDataThermalVoList)) {
                dischargeDataThermalVoList.forEach(dischargeDataThermalVo -> {
                    Long cId = dischargeDataThermalVo.getCompanyId();
                    DischargeDataTotalVo[] totalVos = tmpList.stream()
                            .filter(dischargeDataTotalVos -> dischargeDataTotalVos[0].getCompanyId().equals(cId))
                            .findAny()
                            .orElse(null);
                    if (ObjectUtil.isNotEmpty(totalVos)) {
                        int iIndex = getMonthIndex(dischargeDataThermalVo.getDataMonth());
                        if (iIndex > 0) {
                            totalVos[iIndex - 1].setReportTime(dischargeDataThermalVo.getReportTime());
                            totalVos[iIndex - 1].setDataMonth(dischargeDataThermalVo.getDataMonth());
                            totalVos[iIndex - 1].setDataYear(dischargeDataThermalVo.getDataYear());
                            totalVos[iIndex - 1].setThermal(dischargeDataThermalVo.getThermal());
                            totalVos[iIndex - 1].setCarbonThermal(dischargeDataThermalVo.getCarbonEmissions());
                            totalVos[iIndex - 1].setConsumptionThermal(dischargeDataThermalVo.getEnergyConsumption());
                        }
                    }
                });
            }
        }
        if (carbonDataType.equals(DischargeCarbonDataType.TYPE_ALL.getValue()) || carbonDataType.equals(DischargeCarbonDataType.WATER.getValue())) {
            //获取水量数据
            List<DischargeDataWaterVo> dischargeDataWaterVoList = dischargeDataWaterService.
                    countCompanyData(dateStart, dateEnd, companyId);
            if (CollectionUtil.isNotEmpty(dischargeDataWaterVoList)) {
                dischargeDataWaterVoList.forEach(dischargeDataWaterVo -> {
                    Long cId = dischargeDataWaterVo.getCompanyId();
                    DischargeDataTotalVo[] totalVos = tmpList.stream()
                            .filter(dischargeDataTotalVos -> dischargeDataTotalVos[0].getCompanyId().equals(cId))
                            .findAny()
                            .orElse(null);
                    if (ObjectUtil.isNotEmpty(totalVos)) {
                        int iIndex = getMonthIndex(dischargeDataWaterVo.getDataMonth());
                        if (iIndex > 0) {
                            totalVos[iIndex - 1].setReportTime(dischargeDataWaterVo.getReportTime());
                            totalVos[iIndex - 1].setDataMonth(dischargeDataWaterVo.getDataMonth());
                            totalVos[iIndex - 1].setDataYear(dischargeDataWaterVo.getDataYear());
                            totalVos[iIndex - 1].setWater(dischargeDataWaterVo.getWater());
                            totalVos[iIndex - 1].setCarbonWater(dischargeDataWaterVo.getCarbonEmissions());
                            totalVos[iIndex - 1].setConsumptionWater(dischargeDataWaterVo.getEnergyConsumption());
                        }
                    }
                });
            }
        }
        if (carbonDataType.equals(DischargeCarbonDataType.TYPE_ALL.getValue()) || carbonDataType.equals(DischargeCarbonDataType.COAL.getValue())) {
            //获取煤碳数据
            List<DischargeDataCoalVo> dischargeDataCoalVoList = dischargeDataCoalService.
                    countCompanyData(dateStart, dateEnd, companyId);
            if (CollectionUtil.isNotEmpty(dischargeDataCoalVoList)) {
                dischargeDataCoalVoList.forEach(dischargeDataCoalVo -> {
                    Long cId = dischargeDataCoalVo.getCompanyId();
                    DischargeDataTotalVo[] totalVos = tmpList.stream()
                            .filter(dischargeDataTotalVos -> dischargeDataTotalVos[0].getCompanyId().equals(cId))
                            .findAny()
                            .orElse(null);
                    if (ObjectUtil.isNotEmpty(totalVos)) {
                        int iIndex = getMonthIndex(dischargeDataCoalVo.getDataMonth());
                        if (iIndex > 0) {
                            totalVos[iIndex - 1].setReportTime(dischargeDataCoalVo.getReportTime());
                            totalVos[iIndex - 1].setDataMonth(dischargeDataCoalVo.getDataMonth());
                            totalVos[iIndex - 1].setDataYear(dischargeDataCoalVo.getDataYear());
                            totalVos[iIndex - 1].setCoal(dischargeDataCoalVo.getCoal());
                            totalVos[iIndex - 1].setCarbonCoal(dischargeDataCoalVo.getCarbonEmissions());
                            totalVos[iIndex - 1].setConsumptionCoal(dischargeDataCoalVo.getEnergyConsumption());
                        }
                    }
                });
            }
        }
        return tmpList;
    }

    private int getMonthIndex(String dataMonth) {
        if (StrUtil.isNotBlank(dataMonth)) {
            String regEx = "[^0-9]";
            Pattern p = Pattern.compile(regEx);
            Matcher m = p.matcher(dataMonth);
            String sIndex = m.replaceAll("").trim();
            if (StrUtil.isNotBlank(sIndex)) {
                return Integer.parseInt(sIndex);
            }
        }
        return -1;
    }

    private List<DischargeDataTotalCompareVo> getDataCompareListByType(Date dateStart, Date dateEnd, Long companyId,
                                                                       String dataType, String timeType) {
        if (ObjectUtil.isEmpty(companyId)) {
            companyId = 0L;
        }
        List<List<DischargeDataTotalCompareVo>> currentDataList = this.getDataCompareListByTypeNew(dateStart,
                dateEnd, companyId, dataType, timeType);

        DischargeDataTotalCompareVo[] dischargeDataTotalCompareVos = initDischargeDataTotalCompareVoArray(timeType);
        if (CollectionUtil.isNotEmpty(currentDataList)) {
            currentDataList.forEach(dischargeDataTotalCompareVoList -> {
                if (CollectionUtil.isNotEmpty(dischargeDataTotalCompareVoList)) {
                    for (int i = 0; i < dischargeDataTotalCompareVoList.size(); i++) {
                        if (StrUtil.isNotBlank(dischargeDataTotalCompareVoList.get(i).getDataMonth())) {
                            dischargeDataTotalCompareVos[i].setDataMonth(dischargeDataTotalCompareVoList.get(i).getDataMonth());
                            dischargeDataTotalCompareVos[i].setDataYear(dischargeDataTotalCompareVoList.get(i).getDataYear());
                            dischargeDataTotalCompareVos[i].setReportTime(dischargeDataTotalCompareVoList.get(i).getReportTime());
                            dischargeDataTotalCompareVos[i].setDataValue(dischargeDataTotalCompareVos[i].
                                    getDataValue().add(dischargeDataTotalCompareVoList.get(i).getDataValue()));
                        }
                    }
                }
            });
        }
        List<DischargeDataTotalCompareVo> dataTotalCompareVoList = new ArrayList<>();
        for (int i = 0; i < dischargeDataTotalCompareVos.length; i++) {
            dataTotalCompareVoList.add(dischargeDataTotalCompareVos[i]);
        }
        return dataTotalCompareVoList;
    }

    private List<List<DischargeDataTotalCompareVo>> getDataCompareListByTypeNew(Date dateStart, Date dateEnd, Long companyId,
                                                                                String dataType, String timeType) {
        List<List<DischargeDataTotalCompareVo>> lists = new ArrayList<>();
        String carbonDataType = dataType;
        if (dataType.equals(DischargeCarbonDataType.CARBON_EMISSIONS.getValue()) ||
                dataType.equals(DischargeCarbonDataType.STAND_COAL_CONSUMPTION.getValue())) {
            carbonDataType = DischargeCarbonDataType.TYPE_ALL.getValue();
        } else if (dataType.equals(DischargeCarbonDataType.GASOLINE.getValue()) ||
                dataType.equals(DischargeCarbonDataType.DIESEL.getValue()) ||
                dataType.equals(DischargeCarbonDataType.CRUDE.getValue()) ||
                dataType.equals(DischargeCarbonDataType.FUEL.getValue()) ||
                dataType.equals(DischargeCarbonDataType.KEROSENE.getValue())) {
            carbonDataType = DischargeCarbonDataType.OIL.getValue();
        } else if (dataType.equals(DischargeCarbonDataType.NG.getValue()) ||
                dataType.equals(DischargeCarbonDataType.LPG.getValue())) {
            carbonDataType = DischargeCarbonDataType.GAS.getValue();
        }

        //获取碳排放数据
        List<DischargeDataTotalVo[]> dischargeDataTotalVoList = this.getAllDataListByCompany(dateStart, dateEnd, companyId, carbonDataType);

        if (CollectionUtil.isNotEmpty(dischargeDataTotalVoList)) {
            dischargeDataTotalVoList.forEach(dischargeDataTotalVos -> {
                DischargeDataTotalCompareVo[] totalVos = new DischargeDataTotalCompareVo[12];
                for (int i = 0; i < totalVos.length; i++) {
                    totalVos[i] = new DischargeDataTotalCompareVo();
                    totalVos[i].setDataValue(BigDecimal.valueOf(0));
                }
                for (int i = 0; i < dischargeDataTotalVos.length; i++) {
                    totalVos[i].setCompanyId(dischargeDataTotalVos[i].getCompanyId());
                    totalVos[i].setCompanyName(dischargeDataTotalVos[i].getCompanyName());
                    totalVos[i].setCityCode(dischargeDataTotalVos[i].getCityCode());
                    totalVos[i].setDataMonth(dischargeDataTotalVos[i].getDataMonth());
                    totalVos[i].setDataYear(dischargeDataTotalVos[i].getDataYear());
                    totalVos[i].setReportTime(dischargeDataTotalVos[i].getReportTime());
                    if (dataType.equals(DischargeCarbonDataType.STAND_COAL_CONSUMPTION.getValue())) {
                        totalVos[i].setDataValue(dischargeDataTotalVos[i].getEnergyConsumption());
                    } else if (dataType.equals(DischargeCarbonDataType.GASOLINE.getValue())) {
                        totalVos[i].setDataValue(dischargeDataTotalVos[i].getCarbonGasoline());
                    } else if (dataType.equals(DischargeCarbonDataType.DIESEL.getValue())) {
                        totalVos[i].setDataValue(dischargeDataTotalVos[i].getCarbonDiesel());
                    } else if (dataType.equals(DischargeCarbonDataType.CRUDE.getValue())) {
                        totalVos[i].setDataValue(dischargeDataTotalVos[i].getCarbonCrude());
                    } else if (dataType.equals(DischargeCarbonDataType.FUEL.getValue())) {
                        totalVos[i].setDataValue(dischargeDataTotalVos[i].getCarbonFuel());
                    } else if (dataType.equals(DischargeCarbonDataType.KEROSENE.getValue())) {
                        totalVos[i].setDataValue(dischargeDataTotalVos[i].getCarbonKerosene());
                    } else if (dataType.equals(DischargeCarbonDataType.NG.getValue())) {
                        totalVos[i].setDataValue(dischargeDataTotalVos[i].getCarbonNg());
                    } else if (dataType.equals(DischargeCarbonDataType.LPG.getValue())) {
                        totalVos[i].setDataValue(dischargeDataTotalVos[i].getCarbonLpg());
                    } else {
                        totalVos[i].setDataValue(dischargeDataTotalVos[i].getCarbonEmissions());
                    }
                }
                lists.add(countCompareDataListNew(totalVos, timeType));
            });
        }
        return lists;
    }

    private List<DischargeDataTotalCompareVo> countCompareDataListNew(DischargeDataTotalCompareVo[] totalVos, String timeType) {
        List<DischargeDataTotalCompareVo> dischargeDataTotalCompareVoList = new ArrayList<>();
        if (timeType.equals(DischargeCarbonTimeType.QUARTER.getValue())) {
            //按季度统计
            DischargeDataTotalCompareVo[] quarterVos = new DischargeDataTotalCompareVo[4];
            for (int i = 0; i < quarterVos.length; i++) {
                quarterVos[i] = new DischargeDataTotalCompareVo();
                quarterVos[i].setDataValue(BigDecimal.valueOf(0));
            }
            for (int i = 0; i < totalVos.length; i++) {
                int iQuarter = i / 3;
                quarterVos[iQuarter].setCompanyId(totalVos[i].getCompanyId());
                quarterVos[iQuarter].setCompanyName(totalVos[i].getCompanyName());
                quarterVos[iQuarter].setCityCode(totalVos[i].getCityCode());
                if (StrUtil.isNotBlank(totalVos[i].getDataMonth())) {
                    quarterVos[iQuarter].setDataMonth("第" + (iQuarter + 1) + "季度");
                    if (StrUtil.isBlank(quarterVos[iQuarter].getDataYear())) {
                        quarterVos[iQuarter].setDataYear(totalVos[i].getDataYear());
                    }
                    if (ObjectUtil.isNotEmpty(totalVos[i].getDataValue())) {
                        quarterVos[iQuarter].setDataValue(quarterVos[iQuarter].getDataValue().add(totalVos[i].getDataValue()));
                    }
                }
            }
            for (int i = 0; i < quarterVos.length; i++) {
                dischargeDataTotalCompareVoList.add(quarterVos[i]);
            }
        } else {
            //按月统计
            for (int i = 0; i < totalVos.length; i++) {
                dischargeDataTotalCompareVoList.add(totalVos[i]);
            }
        }
        return dischargeDataTotalCompareVoList;
    }

    private Date[] getDateByYear(Integer dataYear) {
        String startYear = dataYear + "-01-01 0:00:00";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        //sdf将字符串转化成java.util.Date
        Date dateStart = null;
        Date dateEnd = null;
        try {
            dateStart = sdf.parse(startYear);
            dateEnd = DateUtil.endOfYear(dateStart);
            Date[] result = new Date[4];
            result[0] = dateStart;
            result[1] = dateEnd;
            Calendar c = Calendar.getInstance();
            c.setTime(dateStart);
            c.add(Calendar.YEAR, -1); //年份减1
            result[2] = c.getTime();
            result[3] = DateUtil.endOfYear(result[2]);
            return result;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    private DischargeDataTotalCompareVo[] initDischargeDataTotalCompareVoArray(String timeType) {
        DischargeDataTotalCompareVo[] dischargeDataTotalCompareVos;
        if (timeType.equals(DischargeCarbonTimeType.QUARTER.getValue())) {
            dischargeDataTotalCompareVos = new DischargeDataTotalCompareVo[4];
        } else {
            dischargeDataTotalCompareVos = new DischargeDataTotalCompareVo[12];
        }
        for (int i = 0; i < dischargeDataTotalCompareVos.length; i++) {
            dischargeDataTotalCompareVos[i] = new DischargeDataTotalCompareVo();
            dischargeDataTotalCompareVos[i].setDataValue(BigDecimal.valueOf(0));
            dischargeDataTotalCompareVos[i].setCompanyId(0L);
        }
        return dischargeDataTotalCompareVos;
    }

    private Date[] getLastDate(String dataMonth, String dataYear, String timeType) {
        Date[] result = new Date[2];
        Integer y = this.getMonthIndex(dataYear);
        if (timeType.equals(DischargeCarbonTimeType.QUARTER.getValue())) {
            result[1] = DateUtil.endOfYear(StrToDate(y - 1 + "-12-31"));
            result[0] = DateUtil.beginOfQuarter(result[1]);
        } else {
            Integer m = this.getMonthIndex(dataMonth);
            Date tmpDate = StrToDate(y + "-" + m + "-1");
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(tmpDate);
            calendar.add(Calendar.MONTH, -1);
            result[1] = DateUtil.endOfMonth(calendar.getTime());
            result[0] = DateUtil.beginOfMonth(result[1]);
        }
        return result;
    }

    private Date getLastYearSameDay(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.YEAR, -1); //年份减1
        return c.getTime();
    }

    private Date StrToDate(String sDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return sdf.parse(sDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public List<DischargeEmissionTrendVo> emissionTrendList(DischargeEmissionBo bo) {
        DischargeDataTotalBo dataTotalBo = new DischargeDataTotalBo();
        dataTotalBo.setCompanyId(CompanyType.PROVINCE.getValue().equals(bo.getCompanyType()) ?
                0L : JwtUtils.getCurrentUserCompanyId());

        dataTotalBo.setDataYear(DateUtil.year(new Date()));
        List<DischargeDataTotalVo> carbonData = getDataList(dataTotalBo);
        List<DischargeEmissionTrendVo> monthList = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            DischargeEmissionTrendVo emissionTrendVo = new DischargeEmissionTrendVo();
            emissionTrendVo.setCarbonTotal(carbonData.size() <= i ?
                    BigDecimal.valueOf(0) :
                    CompanyType.PROVINCE.getValue().equals(bo.getCompanyType()) ?
                            NumberFormatUtils.formatWanValue(carbonData.get(i).getCarbonEmissions()) :
                            carbonData.get(i).getCarbonEmissions());
            emissionTrendVo.setDataTime(i + 1 + "月");
            monthList.add(emissionTrendVo);
        }
        // 若是季度
        if (bo.getTrendType().equals(DateTimeType.QUARTER.getValue())) {
            List<DischargeEmissionTrendVo> quarterList = new ArrayList<>();
            quarterList.add(getQuarter(1, 0, 3, monthList));
            quarterList.add(getQuarter(2, 3, 6, monthList));
            quarterList.add(getQuarter(3, 6, 9, monthList));
            quarterList.add(getQuarter(4, 9, 12, monthList));
            return quarterList;
        }
        return monthList;
    }

    @Override
    public DischargeElectricityAnalysisVo electricityAnalysis(DischargeEmissionBo bo) {
        if (!StructureType.STRUCTURE.getValue().equals(bo.getStructType())) {
            return null;
        }
        DischargeElectricityAnalysisVo dataVo = new DischargeElectricityAnalysisVo();
        // 结构分析
        DischargeDataElectricBo queryBo = new DischargeDataElectricBo();
        queryBo.setCompanyId(CompanyType.CITY.getValue().equals(bo.getCompanyType()) ?
                JwtUtils.getCurrentUserCompanyId() : null);
        queryBo.setQueryYear(String.valueOf(DateUtil.year(new Date())));
        DischargeDataElectricVo vo = dischargeDataElectricService.getAllElectricity(queryBo);
        BeanUtils.copyProperties(vo, dataVo);
        dataVo.setElectrictTotal(vo.getOutsourcingThermalPower()
                .add(vo.getOwnGreenPower())
                .add(vo.getOutsourcingGreenPower()));
        return dataVo;
    }

    @Override
    public DischargePortraitVo carbonDataCompareList(DischargeEmissionBo bo) {
        DischargePortraitVo vo = new DischargePortraitVo();
        // 今年
        DischargeDataTotalBo dataTotalBo = new DischargeDataTotalBo();
        dataTotalBo.setCompanyId(CompanyType.PROVINCE.getValue().equals(bo.getCompanyType()) ? 0L : JwtUtils.getCurrentUserCompanyId());

        int nowYear = DateUtil.year(new Date());
        dataTotalBo.setDataYear(nowYear);
        List<DischargeDataTotalVo> nowYearCarbonData = getDataList(dataTotalBo);
        // 去年
        int preYear = new BigDecimal(nowYear).subtract(new BigDecimal("1")).intValue();
        dataTotalBo.setDataYear(preYear);
        List<DischargeDataTotalVo> preYearCarbonData = getDataList(dataTotalBo);

        List<DischargeEmissionTrendVo> dataList = new ArrayList<>();
        // 碳排放量 -- 省级
        DischargeStructuralAnalysisVo dataTotalVo = new DischargeStructuralAnalysisVo();
        // 各类型能源碳排放趋势 -- 地市公司
        List<DischargeStructuralAnalysisVo> energyCarbonTrendList = new ArrayList<>();

        BigDecimal carbonElectric = new BigDecimal(0);
        BigDecimal carbonGas = new BigDecimal(0);
        BigDecimal carbonOil = new BigDecimal(0);
        BigDecimal carbonWater = new BigDecimal(0);
        BigDecimal carbonThermal = new BigDecimal(0);
        BigDecimal carbonCoal = new BigDecimal(0);
        // 能源
        BigDecimal energyTotal = new BigDecimal(0);
        // 化石能源
        BigDecimal fossilTotal = new BigDecimal(0);
        DischargeFossilVo fossilVo = new DischargeFossilVo();
        for (int i = 0; i < 12; i++) {
            DischargeEmissionTrendVo emissionTrendVo = new DischargeEmissionTrendVo();

            if (nowYearCarbonData.size() <= i) {
                emissionTrendVo.setNowYearCarbon(BigDecimal.valueOf(0));
                // 各类型能源碳排放趋势 -- 地市公司
                if (CompanyType.CITY.getValue().equals(bo.getCompanyType())) {
                    DischargeStructuralAnalysisVo energyTrend = new DischargeStructuralAnalysisVo();
                    energyTrend.setMonth(i + 1 + "月");
                    energyTrend.setCarbonElectric(BigDecimal.valueOf(0));
                    energyTrend.setCarbonGas(BigDecimal.valueOf(0));
                    energyTrend.setCarbonOil(BigDecimal.valueOf(0));
                    energyTrend.setCarbonWater(BigDecimal.valueOf(0));
                    energyTrend.setCarbonThermal(BigDecimal.valueOf(0));
                    energyTrend.setCarbonCoal(BigDecimal.valueOf(0));
                    energyCarbonTrendList.add(energyTrend);
                }
            } else {
                DischargeDataTotalVo dischargeDataTotalVo = nowYearCarbonData.get(i);
                emissionTrendVo.setNowYearCarbon(CompanyType.PROVINCE.getValue().equals(bo.getCompanyType()) ?
                        formatValue(dischargeDataTotalVo.getCarbonEmissions()) :
                        dischargeDataTotalVo.getCarbonEmissions());

                carbonElectric = carbonElectric.add(dischargeDataTotalVo.getCarbonPower());
                carbonGas = carbonGas.add(dischargeDataTotalVo.getCarbonNg()).add(dischargeDataTotalVo.getCarbonLpg());
                carbonOil = carbonOil.add(dischargeDataTotalVo.getCarbonGasoline()).add(dischargeDataTotalVo.getCarbonDiesel())
                        .add(dischargeDataTotalVo.getCarbonCrude()).add(dischargeDataTotalVo.getCarbonFuel())
                        .add(dischargeDataTotalVo.getCarbonKerosene());
                carbonWater = carbonWater.add(dischargeDataTotalVo.getCarbonWater());
                carbonThermal = carbonThermal.add(dischargeDataTotalVo.getCarbonThermal());
                carbonCoal = carbonCoal.add(dischargeDataTotalVo.getCarbonCoal());
                energyTotal = energyTotal.add(dischargeDataTotalVo.getEnergyConsumption());
                fossilTotal = fossilTotal.add(dischargeDataTotalVo.getGasEnergyConsumption().add(dischargeDataTotalVo
                        .getOilEnergyConsumption()));
                // 各类型能源碳排放趋势 -- 地市公司
                if (CompanyType.CITY.getValue().equals(bo.getCompanyType())) {
                    DischargeStructuralAnalysisVo energyTrend = new DischargeStructuralAnalysisVo();
                    energyTrend.setMonth(i + 1 + "月");
                    energyTrend.setCarbonElectric(dischargeDataTotalVo.getCarbonPower());
                    energyTrend.setCarbonGas((dischargeDataTotalVo.getCarbonNg().add(dischargeDataTotalVo.getCarbonLpg())));
                    energyTrend.setCarbonOil(dischargeDataTotalVo.getCarbonGasoline().add(dischargeDataTotalVo.getCarbonDiesel())
                            .add(dischargeDataTotalVo.getCarbonCrude()).add(dischargeDataTotalVo.getCarbonFuel())
                            .add(dischargeDataTotalVo.getCarbonKerosene()));
                    energyTrend.setCarbonWater(dischargeDataTotalVo.getCarbonWater());
                    energyTrend.setCarbonThermal(dischargeDataTotalVo.getCarbonThermal());
                    energyTrend.setCarbonCoal(dischargeDataTotalVo.getCarbonCoal());
                    energyCarbonTrendList.add(energyTrend);
                }
            }
            emissionTrendVo.setPreYearCarbon(preYearCarbonData.size() <= i ?
                    BigDecimal.valueOf(0) :
                    CompanyType.PROVINCE.getValue().equals(bo.getCompanyType()) ?
                            NumberFormatUtils.formatWanValue(preYearCarbonData.get(i).getCarbonEmissions()) :
                            preYearCarbonData.get(i).getCarbonEmissions());
            // 设置增长率
            String upRate = MathUtils.getUpRate(emissionTrendVo.getNowYearCarbon(), emissionTrendVo.getPreYearCarbon());
            emissionTrendVo.setUpRate(upRate);
            emissionTrendVo.setDataTime(i + 1 + "月");
            dataList.add(emissionTrendVo);
        }
        // 设置 碳排放强度
        BigDecimal carbonTotal = carbonWater.add(carbonElectric).add(carbonGas)
                .add(carbonOil).add(carbonThermal).add(carbonCoal);
        List<BusinessProductionDataCompareVo> businessTotalList = getBusinessTotalList(nowYear);


        if (CompanyType.PROVINCE.getValue().equals(bo.getCompanyType())) {
            // 电信业务总量
            AtomicReference<BigDecimal> telecomBusinessTotal = new AtomicReference<>(new BigDecimal(0));
            businessTotalList.stream()
                    .map(BusinessProductionDataCompareVo::getTelecomBusinessTotal).reduce(BigDecimal::add)
                    .ifPresent(u -> telecomBusinessTotal.set(u.add(telecomBusinessTotal.get())));
            // 业务流量总量
            AtomicReference<BigDecimal> businessFlowTotal = new AtomicReference<>(new BigDecimal(0));
            businessTotalList.stream()
                    .map(BusinessProductionDataCompareVo::getBusinessFlowTotal).reduce(BigDecimal::add)
                    .ifPresent(u -> businessFlowTotal.set(u.add(businessFlowTotal.get())));

            fossilVo.setCarbonIntensity(MathUtils.division(carbonTotal.multiply(BigDecimal.valueOf(1000)), telecomBusinessTotal.get()));
            fossilVo.setBusCarbonIntensity(MathUtils.division(carbonTotal.multiply(BigDecimal.valueOf(1000)), businessFlowTotal.get()));

            // 碳排放结构分析 --省级
            dataTotalVo.setCarbonWater(formatValue(carbonWater));
            dataTotalVo.setCarbonElectric(formatValue(carbonElectric));
            dataTotalVo.setCarbonGas(formatValue(carbonGas));
            dataTotalVo.setCarbonOil(formatValue(carbonOil));
            dataTotalVo.setCarbonThermal(formatValue(carbonThermal));
            dataTotalVo.setCarbonCoal(formatValue(carbonCoal));
            // 能源
            fossilVo.setEnergyTotal(formatValue(energyTotal));
            // 化石能源
            fossilVo.setFossilTotal(formatValue(fossilTotal));
            // 碳排放强度
        } else {
            // 拿到具体的某个公司
            BusinessProductionDataCompareVo vo1 = getBusByCompanyId(businessTotalList,
                    dataTotalBo.getCompanyId());
            fossilVo.setCarbonIntensity(MathUtils.division(carbonTotal.multiply(BigDecimal.valueOf(1000)), vo1.getTelecomBusinessTotal()));
            fossilVo.setBusCarbonIntensity(MathUtils.division(carbonTotal.multiply(BigDecimal.valueOf(1000)), vo1.getBusinessFlowTotal()));
            // 碳排放结构分析 --地市公司
            dataTotalVo.setCarbonWater(carbonWater);
            dataTotalVo.setCarbonElectric(carbonElectric);
            dataTotalVo.setCarbonGas(carbonGas);
            dataTotalVo.setCarbonOil(carbonOil);
            dataTotalVo.setCarbonCoal(carbonCoal);
            dataTotalVo.setCarbonThermal(carbonThermal);
            // 能源
            fossilVo.setEnergyTotal(energyTotal);
            // 化石能源
            fossilVo.setFossilTotal(fossilTotal);
        }

        // 碳排总量
        dataTotalVo.setCarbonTotal(dataTotalVo.getCarbonWater()
                .add(dataTotalVo.getCarbonElectric())
                .add(dataTotalVo.getCarbonGas()).add(dataTotalVo.getCarbonOil())
                .add(dataTotalVo.getCarbonThermal())
                .add(dataTotalVo.getCarbonCoal()));

        // 若是地市公司、则还有个 截至目前碳排量达监测值百分比
        if (CompanyType.CITY.getValue().equals(bo.getCompanyType())) {
            // 碳排放量总额
            DischargeMonitorSetting dischargeMonitorSetting = dischargeMonitorSettingService
                    .getOne(new LambdaQueryWrapper<DischargeMonitorSetting>()
                            .eq(DischargeMonitorSetting::getYear, String.valueOf(nowYear))
                            .eq(DischargeMonitorSetting::getCompanyId, dataTotalBo.getCompanyId()));
            vo.setUseRate(ObjectUtil.isNotNull(dischargeMonitorSetting) ?
                    MathUtils.d2dPercent(dataTotalVo.getCarbonTotal(), new BigDecimal(dischargeMonitorSetting.getQuota()))
                    : MathUtils.d2dPercent(dataTotalVo.getCarbonTotal(), new BigDecimal(0)));
        }
        // 中部展示内容  化石能源  碳排放量
        fossilVo.setCarbonTotal(dataTotalVo.getCarbonTotal());
        // 间接
        fossilVo.setIndirectTotal(dataTotalVo.getCarbonElectric().add(dataTotalVo.getCarbonThermal()));
        // 直接
        fossilVo.setDirectTotal(dataTotalVo.getCarbonTotal().subtract(fossilVo.getIndirectTotal()));
        // 非化石能源
        fossilVo.setNonFossilTotal(fossilVo.getEnergyTotal().subtract(fossilVo.getFossilTotal()));
        vo.setFossilVo(fossilVo);
        vo.setDataCompareList(dataList);
        vo.setStructuralAnalysis(dataTotalVo);
        vo.setEnergyCarbonTrendList(energyCarbonTrendList);
        return vo;
    }

    private List<BusinessProductionDataCompareVo> getBusinessTotalList(int nowYear) {
        // 查询所有地市公司的 业务总量 / 业务流量总量
//        BusinessProductionDataBo businessProductionDataBo = new BusinessProductionDataBo();
//        businessProductionDataBo.setReportYear(String.valueOf(nowYear));
//        return remoteBusinessService.getCompanyBusList(businessProductionDataBo).getData();
        return null;
    }

    @Override
    public List<DischargeEmissionTrendVo> mapCompanyData() {
//        List<DischargeEmissionTrendVo> list = new ArrayList<>();
//        DischargeDataTotalBo queryBo = new DischargeDataTotalBo();
//        int nowYear = DateUtil.year(new Date());
//        queryBo.setDataYear(nowYear);
//        String provinceCode = CityCodeConstant.SI_BEGIN;
//        // 查询所有地市公司
//        List<Eprovince> provinceList = remoteAdminService.getProvinceAndCityList(provinceCode).getData();
//        // 查询所有地市公司的 业务总量 / 业务流量总量
//        List<BusinessProductionDataCompareVo> businessTotalList = getBusinessTotalList(nowYear);
//        // 拿到所有存在的分公司
//        List<SysDept> sysDeptList = remoteAdminService.companyList(false).getData();
//        Page<DischargeMonitorSettingVo> page = new Page<>();
//        page.setCurrent(1);
//        page.setSize(50);
//        DischargeMonitorSettingBo bo = new DischargeMonitorSettingBo();
//        bo.setYear(String.valueOf(nowYear));
//        // 拿到各个公司的碳排放量
//        List<DischargeMonitorSettingVo> companyCarbonList = dischargeMonitorSettingService
//                .getRecordsList(page, bo).getRows();
//        // 拿到各个公司的能源消耗总量
//        List<DischargeMonitorSettingVo> companyEnergyList = dischargeMonitorSettingService
//                .getEnergyCompanyList(page, bo).getRows();
//        // 拿到除四川省的其它省份
//        List<Long> companyIds = sysDeptList.stream()
//                .filter(item -> {
//                    return !CityCodeConstant.SI_CHUAN.equals(item.getAreaCode());
//                }).map(SysDept::getId).collect(Collectors.toList());
//        // 拿到各个公司的定额值
//        List<DischargeMonitorSetting> quotaList = dischargeMonitorSettingService
//                .list(new LambdaQueryWrapper<DischargeMonitorSetting>()
//                        .eq(DischargeMonitorSetting::getYear, String.valueOf(nowYear))
//                        .in(DischargeMonitorSetting::getCompanyId, companyIds));
//
//        if (CollectionUtil.isNotEmpty(sysDeptList)) {
//            for (SysDept sysDept : sysDeptList) {
//                queryBo.setCompanyId(sysDept.getId());
//                DischargeEmissionTrendVo vo = new DischargeEmissionTrendVo();
//                vo.setCityCode(sysDept.getAreaCode());
//                DischargeMonitorSettingVo carbonVo = new DischargeMonitorSettingVo();
//                if (!CityCodeConstant.SI_CHUAN.equals(sysDept.getAreaCode())) {
//                    carbonVo = getVoByCompanyId(companyCarbonList, sysDept.getId());
//                }
//                DischargeMonitorSettingVo energyVo = getVoByCompanyId(companyEnergyList, sysDept.getId());
//                vo.setEnergyTotal(energyVo.getEnergy());
//                vo.setCarbonTotal(carbonVo.getCarbonTotal());
//
//                // 碳排放量总额在定额值80%以下保持底色，80%-100%用橙色，超过100%用红色。
//                List<DischargeMonitorSetting> collect = quotaList.stream().filter(node ->
//                                node.getCompanyId().equals(sysDept.getId()))
//                        .collect(Collectors.toList());
//                if (CollectionUtil.isNotEmpty(collect)) {
//                    DischargeMonitorSetting dischargeMonitorSetting = collect.get(0);
//                    // 设置使用率
//                    vo.setUseRate(MathUtils.d2dPercent(vo.getCarbonTotal(),
//                            new BigDecimal(dischargeMonitorSetting.getQuota())));
//                    // 计算颜色
//                    BigDecimal useRate = new BigDecimal(vo.getUseRate().replace("%", ""));
//                    if (useRate.compareTo(new BigDecimal(80)) < 0) {
//                        vo.setState("1");
//                    } else if (useRate.compareTo(new BigDecimal(100)) <= 0) {
//                        vo.setState("2");
//                    } else {
//                        vo.setState("3");
//                    }
//                } else {
//                    vo.setState("1");
//                }
//                // 设置碳排放强度
//                BusinessProductionDataCompareVo vo1 = getBusByCompanyId(businessTotalList,
//                        sysDept.getId());
//                vo.setCarbonIntensity(MathUtils.division(carbonVo.getCarbonTotal(), vo1.getTelecomBusinessTotal()));
//                // 业务碳排放强度  计算方式：碳排放量（kg）/业务流量总量（TB）
//                vo.setBusCarbonIntensity(MathUtils.division(carbonVo.getCarbonTotal(), vo1.getBusinessFlowTotal()));
//                vo.setCarbonTotal(formatValue(carbonVo.getCarbonTotal()));
//                vo.setCompanyName(sysDept.getName());
//                list.add(vo);
//            }
//            provinceList.forEach(item -> {
//                List<DischargeEmissionTrendVo> collect = list.stream().filter(node ->
//                        item.getCode().equals(node.getCityCode())).collect(Collectors.toList());
//                if (CollectionUtil.isEmpty(collect)) {
//                    DischargeEmissionTrendVo vo = new DischargeEmissionTrendVo();
//                    vo.setCityCode(item.getCode());
//                    vo.setCompanyName(item.getName());
//                    vo.setState("1");
//                    vo.setCarbonTotal(BigDecimal.valueOf(0));
//                    vo.setEnergyTotal(BigDecimal.valueOf(0));
//                    list.add(vo);
//                }
//            });
//            list.sort(Comparator.comparing(DischargeEmissionTrendVo::getCarbonTotal).reversed());
//            for (int i = 0; i < list.size(); i++) {
//                DischargeEmissionTrendVo emissionTrendVo = list.get(i);
//                emissionTrendVo.setRankNum(NumberFormatUtils.arabicNumToChineseNum(i + 1));
//            }
//        }
//        return list;
        return null;
    }

    @Override
    public HashMap<String, Object> getTwoYearsDataList(DischargeDataTotalBo dischargeDataTotalBo) {
        HashMap<String, Object> resultMap = new HashMap<>();
        // 获取当前日期
        Date currentDate = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String formattedDate = sdf.format(currentDate);
        int currentMonth = Integer.parseInt(formattedDate.substring(5));
        int year = Integer.parseInt(formattedDate.substring(0, 4));
        if (year == dischargeDataTotalBo.getDataYear().intValue()) {
            //年份为当前年份
            if (currentMonth == 1) {
                //月份为1月，则获取去年前年数据
                currentMonth = 12;
                year -= 1;
            } else {
                //月份不为1月，则获取至上月数据
                currentMonth -= 1;
            }
        }
        int lastYear = year - 1;
        Date startDate = DateUtil.beginOfYear(StrToDate(year + "-1-1"));
        Date endDate = DateUtil.endOfMonth(StrToDate(year + "-" + currentMonth + "-1"));
        List<DischargeDataTotalVo> thisYearDataList = getDataByTime(dischargeDataTotalBo.getCompanyId(), startDate, endDate);
        resultMap.put("thisYearDataList", thisYearDataList);
        startDate = DateUtil.beginOfYear(StrToDate(lastYear + "-1-1"));
        endDate = DateUtil.endOfMonth(StrToDate(lastYear + "-" + currentMonth + "-1"));
        List<DischargeDataTotalVo> lastYearDataList = getDataByTime(dischargeDataTotalBo.getCompanyId(), startDate, endDate);
        resultMap.put("lastYearDataList", lastYearDataList);
        return resultMap;
    }

    @Override
    public List<DischargeDataTotalVo> getCarbonAssessDataList(DischargeDataTotalBo dischargeDataTotalBo) {
        Date[] dataDate = getDateByYear(dischargeDataTotalBo.getDataYear());
        List<DischargeDataTotalVo> dischargeDataTotalVoList = this.getAllDataList(dataDate[0], dataDate[1],
                dischargeDataTotalBo.getCompanyId());
        List<HashMap<String, Object>> businessDataList = dischargeDataTotalMapper
                .getCompanyBusinessTotalList(dischargeDataTotalBo.getCompanyId(), String.valueOf(dischargeDataTotalBo.getDataYear()));
        for (int i = 0; i < dischargeDataTotalVoList.size(); i ++) {
            String dataMonth = String.format("%02d月", i + 1);
            HashMap<String, Object> businessData = businessDataList.stream().
                    filter(item -> dataMonth.equals(item.get("data_month").toString())).findAny().orElse(null);
            if (StrUtil.isNotBlank(dischargeDataTotalVoList.get(i).getDataMonth())) {
                if (ObjectUtil.isNotEmpty(businessData)) {
                    dischargeDataTotalVoList.get(i).setTelecomBusinessTotal(new BigDecimal(businessData.get("telecom_business_total").toString()));
                }
            } else {
                if (ObjectUtil.isNotEmpty(businessData)) {
                    dischargeDataTotalVoList.get(i).setDataMonth(dataMonth);
                    dischargeDataTotalVoList.get(i).setTelecomBusinessTotal(new BigDecimal(businessData.get("telecom_business_total").toString()));
                }
            }
        }
        int i = 0;
        while (i < dischargeDataTotalVoList.size()) {
            if (StrUtil.isNotBlank(dischargeDataTotalVoList.get(i).getDataMonth())) {
                i += 1;
            } else {
                dischargeDataTotalVoList.remove(i);
            }
        }
        return dischargeDataTotalVoList;
    }

    @Override
    public HashMap<String, Object> getCarbonOverview() {
//        HashMap<String, Object> carbonEmissionsMap = new HashMap<>();
//        Calendar calendar = Calendar.getInstance();
//        calendar.add(Calendar.MONTH,-1);
//        Date currentDate = calendar.getTime();
//        calendar.add(Calendar.MONTH,-1);
//        Date preMontDate  = calendar.getTime();
//        calendar.add(Calendar.MONTH,-2);
//        Date preQuarterDate  = calendar.getTime();
//        calendar.add(Calendar.MONTH,-9);
//        Date lastMonthDate  = calendar.getTime();
//        List<DischargeDataTotalVo> monthDataList = getDataByTime(null,
//                DateUtil.beginOfMonth(currentDate), DateUtil.endOfMonth(currentDate));
//        List<DischargeDataTotalVo> preMonthDataList = getDataByTime(null,
//                DateUtil.beginOfMonth(preMontDate), DateUtil.endOfMonth(preMontDate));
//        List<DischargeDataTotalVo> lastMonthDataList = getDataByTime(null,
//                DateUtil.beginOfMonth(lastMonthDate), DateUtil.endOfMonth(lastMonthDate));
//        BigDecimal monthCarbonEmissions = getTotalCarbonEmissions(monthDataList);
//        BigDecimal preMonthData = getTotalCarbonEmissions(preMonthDataList);
//        BigDecimal lastMonthData = getTotalCarbonEmissions(lastMonthDataList);
//        HashMap<String, Object> monthDataMap = new HashMap<>();
//        monthDataMap.put("carbonEmissions", monthCarbonEmissions.setScale(2, RoundingMode.HALF_UP));
//        monthDataMap.put("preMonthCarbonEmissions", preMonthData.setScale(2, RoundingMode.HALF_UP));
//        monthDataMap.put("LastMonthCarbonEmissions", lastMonthData.setScale(2, RoundingMode.HALF_UP));
//        monthDataMap.put("YoY", MathUtils.getUpRate(monthCarbonEmissions, lastMonthData));
//        monthDataMap.put("MoM", MathUtils.getUpRate(monthCarbonEmissions, preMonthData));
//        List<DischargeDataTotalVo> quarterDataList = getDataByTime(null,
//                DateUtil.beginOfQuarter(currentDate), DateUtil.endOfQuarter(currentDate));
//        List<DischargeDataTotalVo> preQuarterDataList = getDataByTime(null,
//                DateUtil.beginOfQuarter(preQuarterDate), DateUtil.endOfQuarter(preQuarterDate));
//        List<DischargeDataTotalVo> lastQuarterDataList = getDataByTime(null,
//                DateUtil.beginOfQuarter(lastMonthDate), DateUtil.endOfQuarter(lastMonthDate));
//
//        BigDecimal quarterData = getTotalCarbonEmissions(quarterDataList);
//        BigDecimal preQuarterData = getTotalCarbonEmissions(preQuarterDataList);
//        BigDecimal lastQuarterData = getTotalCarbonEmissions(lastQuarterDataList);
//        HashMap<String, Object> quarterDataMap = new HashMap<>();
//        quarterDataMap.put("carbonEmissions", quarterData.setScale(2, RoundingMode.HALF_UP));
//        quarterDataMap.put("preQuarterCarbonEmissions", preQuarterData.setScale(2, RoundingMode.HALF_UP));
//        quarterDataMap.put("LastQuarterCarbonEmissions", lastQuarterData.setScale(2, RoundingMode.HALF_UP));
//        quarterDataMap.put("YoY", MathUtils.getUpRate(quarterData, lastQuarterData));
//        quarterDataMap.put("MoM", MathUtils.getUpRate(quarterData, preQuarterData));
//        List<DischargeDataTotalVo> yearDataList = getDataByTime(null,
//                DateUtil.beginOfYear(currentDate), DateUtil.endOfMonth(currentDate));
//        List<DischargeDataTotalVo> preYearDataList = getDataByTime(null,
//                DateUtil.beginOfYear(lastMonthDate), DateUtil.endOfMonth(lastMonthDate));
//        BigDecimal yearData = getTotalCarbonEmissions(yearDataList);
//        BigDecimal preYearData = getTotalCarbonEmissions(preYearDataList);
//
//        HashMap<String, Object> yearDataMap = countCarbonEmissionsData(yearDataList);
//        yearDataMap.put("preYearCarbonEmissions", preYearData);
//        yearDataMap.put("YoY", MathUtils.getUpRate(yearData, preYearData));
//        carbonEmissionsMap.put("yearData", yearDataMap);
//        List<DischargeDataTotalVo> allDataList = getDataByTime(null, null, null);
//        carbonEmissionsMap.put("monthData", monthDataMap);
//        carbonEmissionsMap.put("quarterData", quarterDataMap);
//        carbonEmissionsMap.put("accumulativeTotal", countCarbonEmissionsData(allDataList));
//        HashMap<String, Object> businessDataOverview = remoteBusinessService.getBusinessOverview().getData();
//        String yearDataStr = businessDataOverview.get("yearData").toString().replace("=", ":");
//        HashMap<String, Object> yearDataBusiness = JSON.parseObject(yearDataStr,
//                new TypeReference<HashMap<String, Object>>() {
//                });
//        BigDecimal carbonStrength = MathUtils.division(yearData.multiply(BigDecimal.valueOf(1000)),
//                new BigDecimal(yearDataBusiness.get("telecom_business_total").toString()));
//        BigDecimal carbonStrengthLast = MathUtils.division(preYearData.multiply(BigDecimal.valueOf(1000)),
//                new BigDecimal(yearDataBusiness.get("last_telecom_business_total").toString()));
//        BigDecimal flowCarbonStrength = MathUtils.division(yearData.multiply(BigDecimal.valueOf(1000)),
//                new BigDecimal(yearDataBusiness.get("business_flow_total").toString()));
//        BigDecimal flowCarbonStrengthLast = MathUtils.division(preYearData.multiply(BigDecimal.valueOf(1000)),
//                new BigDecimal(yearDataBusiness.get("last_business_flow_total").toString()));
//        HashMap<String, Object> carbonBusinessData = new HashMap<>();
//        carbonBusinessData.put("carbonStrength", carbonStrength);
//        carbonBusinessData.put("carbonStrengthYoY", MathUtils.getUpRate(carbonStrength, carbonStrengthLast));
//        carbonBusinessData.put("flowCarbonStrength", flowCarbonStrength);
//        carbonBusinessData.put("flowCarbonStrengthYoY", MathUtils.getUpRate(flowCarbonStrength, flowCarbonStrengthLast));
//        businessDataOverview.put("carbonBusinessData", carbonBusinessData);
//        HashMap<String, Object> result = new HashMap<>();
//        result.put("carbonEmissionsOverview", carbonEmissionsMap);
//        result.put("businessDataOverview", businessDataOverview);
//        return result;
        return null;
    }

    @Override
    public HashMap<String, Object> getCarbonCompare(String timeType) {
        HashMap<String, Object> result = new HashMap<>();
        DischargeDataElectricBo query = new DischargeDataElectricBo();
//        UserInfo userInfo = commonService.getCurrentUser();
//        int roleLevel = userInfo.getMaxRoleLevel();
//        if (roleLevel != SysRoleEnums.ROLE_ADMIN.getLevel()) {
//            query.setCompanyId(remoteUserService.getCityDeptId());
//        }
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH,-1);
        if (DischargeDataTotalEnum.CarbonCompareTimeType.按年.getValue().equals(timeType)) {
            //按年查询
            query.setQueryEndTime(DateUtil.endOfYear(calendar.getTime()));
            calendar.add(Calendar.YEAR, -5);
            query.setQueryStartTime(DateUtil.beginOfYear(calendar.getTime()));
            List<DischargeDataTotalVo> allVoList = getCompareAllData(query);
            List<DischargeDataTotalVo> carbonCompareVoList = mergeCarbonCompareYearList(allVoList);
            carbonCompareVoList.sort(Comparator.comparing(DischargeDataTotalVo::getDataYear));
            String[] labelArray = new String[5];
            BigDecimal[] thisYearDataArray = new BigDecimal[5];
            BigDecimal[] lastYearDataArray = new BigDecimal[5];
            String[] upRateArray = new String[5];
            for (int i = 0; i < carbonCompareVoList.size() - 1; i ++) {
                labelArray[i] = carbonCompareVoList.get(i + 1).getDataYear() + "年";
                thisYearDataArray[i] = carbonCompareVoList.get(i + 1).getCarbonEmissions()
                        .divide(BigDecimal.valueOf(10000)).setScale(2, RoundingMode.HALF_UP);
                lastYearDataArray[i] = carbonCompareVoList.get(i).getCarbonEmissions()
                        .divide(BigDecimal.valueOf(10000)).setScale(2, RoundingMode.HALF_UP);
                upRateArray[i] = MathUtils.getUpRate(thisYearDataArray[i], lastYearDataArray[i]);
            }
            result.put("labelList", labelArray);
            result.put("thisYearDataList", thisYearDataArray);
            result.put("lastYearDataList", lastYearDataArray);
            result.put("upRateList", upRateArray);
        } else {
            //按月查询
            query.setQueryEndTime(DateUtil.endOfMonth(calendar.getTime()));
            calendar.add(Calendar.MONTH, -11);
            Date thisYearBegin = DateUtil.beginOfMonth(calendar.getTime());
            calendar.add(Calendar.MONTH, -12);
            query.setQueryStartTime(DateUtil.beginOfMonth(calendar.getTime()));
            List<DischargeDataTotalVo> allVoList = getCompareAllData(query);
            List<DischargeDataTotalVo> carbonCompareVoList = mergeCarbonCompareMonthList(allVoList);
            carbonCompareVoList.sort(Comparator.comparing(DischargeDataTotalVo::getReportTime));
            List<DischargeDataTotalVo> thisYearList = carbonCompareVoList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) >= 0).collect(Collectors.toList());
            List<DischargeDataTotalVo> lastYearList = carbonCompareVoList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) < 0).collect(Collectors.toList());
            List<BigDecimal> thisYearDataList = thisYearList.stream()
                    .map(data->data.getCarbonEmissions().divide(BigDecimal.valueOf(10000))
                            .setScale(2, RoundingMode.HALF_UP)).collect(Collectors.toList());
            List<String> labelList = thisYearList.stream()
                    .map(data->data.getDataMonth().substring(5) + "月").collect(Collectors.toList());
            List<BigDecimal> lastYearDataList = lastYearList.stream()
                    .map(data->data.getCarbonEmissions().divide(BigDecimal.valueOf(10000))
                            .setScale(2, RoundingMode.HALF_UP)).collect(Collectors.toList());
            List<String> upRateList = new ArrayList<>();
            for (int i = 0; i < thisYearDataList.size(); i ++) {
                upRateList.add(MathUtils.getUpRate(thisYearDataList.get(i), lastYearDataList.get(i)));
            }
            result.put("labelList", labelList);
            result.put("thisYearDataList", thisYearDataList);
            result.put("lastYearDataList", lastYearDataList);
            result.put("upRateList", upRateList);
        }
        return result;
    }

    @Override
    public HashMap<String, Object> getEnergyConsumptionOverview() {
        HashMap<String, Object> result = new HashMap<>();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH,-1);
        Date currentDate = calendar.getTime();
        calendar.add(Calendar.MONTH,-1);
        Date preMontDate  = calendar.getTime();
        calendar.add(Calendar.MONTH,-2);
        Date preQuarterDate  = calendar.getTime();
        calendar.add(Calendar.MONTH,-9);
        Date lastMonthDate  = calendar.getTime();
        List<DischargeDataTotalVo> monthDataList = getDataByTime(null,
                DateUtil.beginOfMonth(currentDate), DateUtil.endOfMonth(currentDate));
        List<DischargeDataTotalVo> preMonthDataList = getDataByTime(null,
                DateUtil.beginOfMonth(preMontDate), DateUtil.endOfMonth(preMontDate));
        List<DischargeDataTotalVo> lastMonthDataList = getDataByTime(null,
                DateUtil.beginOfMonth(lastMonthDate), DateUtil.endOfMonth(lastMonthDate));
        DischargeDataTotalVo monthDataVo;
        DischargeDataTotalVo preMonthDataVo;
        DischargeDataTotalVo lastMonthDataVo;
        if (CollectionUtil.isNotEmpty(monthDataList)) {
            monthDataVo = monthDataList.get(0);
        } else {
            monthDataVo = new DischargeDataTotalVo();
        }
        if (CollectionUtil.isNotEmpty(preMonthDataList)) {
            preMonthDataVo = preMonthDataList.get(0);
        } else {
            preMonthDataVo = new DischargeDataTotalVo();
        }
        if (CollectionUtil.isNotEmpty(lastMonthDataList)) {
            lastMonthDataVo = lastMonthDataList.get(0);
        } else {
            lastMonthDataVo = new DischargeDataTotalVo();
        }
        HashMap<String, Object> monthDataMap = countMonthConsumptionData(monthDataVo, preMonthDataVo, lastMonthDataVo);

        List<DischargeDataTotalVo> quarterDataList = getDataByTime(null,
                DateUtil.beginOfQuarter(currentDate), DateUtil.endOfQuarter(currentDate));
        List<DischargeDataTotalVo> preQuarterDataList = getDataByTime(null,
                DateUtil.beginOfQuarter(preQuarterDate), DateUtil.endOfQuarter(preQuarterDate));
        List<DischargeDataTotalVo> lastQuarterDataList = getDataByTime(null,
                DateUtil.beginOfQuarter(lastMonthDate), DateUtil.endOfQuarter(lastMonthDate));

        BigDecimal quarterData = getTotalEnergyConsumption(quarterDataList);
        BigDecimal preQuarterData = getTotalEnergyConsumption(preQuarterDataList);
        BigDecimal lastQuarterData = getTotalEnergyConsumption(lastQuarterDataList);
        HashMap<String, Object> quarterDataMap = new HashMap<>();
        quarterDataMap.put("quarterEnergyConsumption", quarterData.setScale(2, RoundingMode.HALF_UP));
        quarterDataMap.put("preQuarterEnergyConsumption", preQuarterData.setScale(2, RoundingMode.HALF_UP));
        quarterDataMap.put("LastQuarterEnergyConsumption", lastQuarterData.setScale(2, RoundingMode.HALF_UP));
        quarterDataMap.put("YoY", MathUtils.getUpRate(quarterData, lastQuarterData));
        quarterDataMap.put("MoM", MathUtils.getUpRate(quarterData, preQuarterData));
        List<DischargeDataTotalVo> yearDataList = getDataByTime(null,
                DateUtil.beginOfYear(currentDate), DateUtil.endOfMonth(currentDate));
        List<DischargeDataTotalVo> preYearDataList = getDataByTime(null,
                DateUtil.beginOfYear(lastMonthDate), DateUtil.endOfMonth(lastMonthDate));
        HashMap<String, Object> yearDataMap = countYearConsumptionData(yearDataList, preYearDataList);
        result.put("yearData", yearDataMap);
        List<DischargeDataTotalVo> allDataList = getDataByTime(null, null, null);
        result.put("monthData", monthDataMap);
        result.put("quarterData", quarterDataMap);
        result.put("accumulativeTotal", countCarbonEmissionsData(allDataList));
        return result;
    }

    @Override
    public HashMap<String, Object> getEnergyConsumptionCompare() {
        HashMap<String, Object> result = new HashMap<>();
        DischargeDataElectricBo query = new DischargeDataElectricBo();
//        UserInfo userInfo = commonService.getCurrentUser();
//        int roleLevel = userInfo.getMaxRoleLevel();
//        if (roleLevel != SysRoleEnums.ROLE_ADMIN.getLevel()) {
//            query.setCompanyId(remoteUserService.getCityDeptId());
//        }
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH,-1);

        query.setQueryEndTime(DateUtil.endOfMonth(calendar.getTime()));
        calendar.add(Calendar.MONTH, -11);
        Date thisYearBegin = DateUtil.beginOfMonth(calendar.getTime());
        calendar.add(Calendar.MONTH, -12);
        query.setQueryStartTime(DateUtil.beginOfMonth(calendar.getTime()));
        List<DischargeDataTotalVo> electricVoList = getCompareAllData(query);
        List<DischargeDataTotalVo> carbonCompareVoList = mergeCarbonCompareMonthList(electricVoList);
        carbonCompareVoList.sort(Comparator.comparing(DischargeDataTotalVo::getReportTime));
        List<DischargeDataTotalVo> thisYearList = carbonCompareVoList.stream().filter(
                o ->o.getReportTime().compareTo(thisYearBegin) >= 0).collect(Collectors.toList());
        List<DischargeDataTotalVo> lastYearList = carbonCompareVoList.stream().filter(
                o ->o.getReportTime().compareTo(thisYearBegin) < 0).collect(Collectors.toList());
        List<BigDecimal> thisYearDataList = thisYearList.stream()
                .map(DischargeDataTotalVo::getEnergyConsumption).collect(Collectors.toList());
        List<String> labelList = thisYearList.stream()
                .map(data-> data.getDataMonth().substring(5)).collect(Collectors.toList());
        List<BigDecimal> lastYearDataList = lastYearList.stream()
                .map(DischargeDataTotalVo::getEnergyConsumption).collect(Collectors.toList());
        List<String> upRateList = new ArrayList<>();
        for (int i = 0; i < thisYearDataList.size(); i ++) {
            upRateList.add(MathUtils.getUpRate(thisYearDataList.get(i), lastYearDataList.get(i)));
        }
        result.put("labelList", labelList);
        result.put("thisYearDataList", thisYearDataList);
        result.put("lastYearDataList", lastYearDataList);
        result.put("upRateList", upRateList);
        return result;
    }

    @Override
    public HashMap<String, Object> getEnergyTrend(String energyType) {
        if (StrUtil.isBlank(energyType)) {
            throw new BusinessException("能源类型参数不能为空");
        }
        HashMap<String, Object> result = new HashMap<>();
        DischargeDataElectricBo query = new DischargeDataElectricBo();
//        UserInfo userInfo = commonService.getCurrentUser();
//        int roleLevel = userInfo.getMaxRoleLevel();
//        if (roleLevel != SysRoleEnums.ROLE_ADMIN.getLevel()) {
//            query.setCompanyId(remoteUserService.getCityDeptId());
//        }
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH,-1);
        //按月查询
        query.setQueryEndTime(DateUtil.endOfMonth(calendar.getTime()));
        calendar.add(Calendar.MONTH, -11);
        Date thisYearBegin = DateUtil.beginOfMonth(calendar.getTime());
        calendar.add(Calendar.MONTH, -12);
        query.setQueryStartTime(DateUtil.beginOfMonth(calendar.getTime()));
        List<String> labelList;
        List<BigDecimal> thisYearOriginDataList;
        List<BigDecimal> thisYearCarbonList;
        List<BigDecimal> thisYearConsumptionList;
        List<BigDecimal> lastYearOriginDataList;
        List<BigDecimal> lastYearCarbonList;
        List<BigDecimal> lastYearConsumptionList;
        if (DischargeCarbonDataType.POWER.getValue().equals(energyType)) {
            List<DischargeDataTotalVo> dataList = dischargeDataElectricMapper.countCarbonCompare(query);
            List<DischargeDataTotalVo> thisYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) >= 0).collect(Collectors.toList());
            List<DischargeDataTotalVo> lastYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) < 0).collect(Collectors.toList());
            labelList = thisYearList.stream()
                    .map(data->data.getDataMonth().substring(5) + "月").collect(Collectors.toList());
            thisYearOriginDataList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getPower).collect(Collectors.toList());
            thisYearCarbonList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonPower).collect(Collectors.toList());
            thisYearConsumptionList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionPower).collect(Collectors.toList());
            lastYearOriginDataList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getPower).collect(Collectors.toList());
            lastYearCarbonList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonPower).collect(Collectors.toList());
            lastYearConsumptionList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionPower).collect(Collectors.toList());

        } else if (DischargeCarbonDataType.WATER.getValue().equals(energyType)) {
            List<DischargeDataTotalVo> dataList = dischargeDataWaterMapper.countCarbonCompare(query);
            List<DischargeDataTotalVo> thisYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) >= 0).collect(Collectors.toList());
            List<DischargeDataTotalVo> lastYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) < 0).collect(Collectors.toList());
            labelList = thisYearList.stream()
                    .map(data->data.getDataMonth().substring(5) + "月").collect(Collectors.toList());
            thisYearOriginDataList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getWater).collect(Collectors.toList());
            thisYearCarbonList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonWater).collect(Collectors.toList());
            thisYearConsumptionList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionWater).collect(Collectors.toList());
            lastYearOriginDataList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getWater).collect(Collectors.toList());
            lastYearCarbonList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonWater).collect(Collectors.toList());
            lastYearConsumptionList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionWater).collect(Collectors.toList());

        } else if (DischargeCarbonDataType.THERMAL.getValue().equals(energyType)) {
            List<DischargeDataTotalVo> dataList = dischargeDataThermalMapper.countCarbonCompare(query);
            List<DischargeDataTotalVo> thisYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) >= 0).collect(Collectors.toList());
            List<DischargeDataTotalVo> lastYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) < 0).collect(Collectors.toList());
            labelList = thisYearList.stream()
                    .map(data->data.getDataMonth().substring(5) + "月").collect(Collectors.toList());
            thisYearOriginDataList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getThermal).collect(Collectors.toList());
            thisYearCarbonList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonThermal).collect(Collectors.toList());
            thisYearConsumptionList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionThermal).collect(Collectors.toList());
            lastYearOriginDataList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getThermal).collect(Collectors.toList());
            lastYearCarbonList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonThermal).collect(Collectors.toList());
            lastYearConsumptionList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionThermal).collect(Collectors.toList());
        } else if (DischargeCarbonDataType.COAL.getValue().equals(energyType)) {
            List<DischargeDataTotalVo> dataList = dischargeDataCoalMapper.countCarbonCompare(query);
            List<DischargeDataTotalVo> thisYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) >= 0).collect(Collectors.toList());
            List<DischargeDataTotalVo> lastYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) < 0).collect(Collectors.toList());
            labelList = thisYearList.stream()
                    .map(data->data.getDataMonth().substring(5) + "月").collect(Collectors.toList());
            thisYearOriginDataList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getCoal).collect(Collectors.toList());
            thisYearCarbonList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonCoal).collect(Collectors.toList());
            thisYearConsumptionList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionCoal).collect(Collectors.toList());
            lastYearOriginDataList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getCoal).collect(Collectors.toList());
            lastYearCarbonList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonCoal).collect(Collectors.toList());
            lastYearConsumptionList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionCoal).collect(Collectors.toList());
        } else if (DischargeCarbonDataType.NG.getValue().equals(energyType)) {
            List<DischargeDataTotalVo> dataList = dischargeDataGasMapper.countCarbonCompare(query);
            List<DischargeDataTotalVo> thisYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) >= 0).collect(Collectors.toList());
            List<DischargeDataTotalVo> lastYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) < 0).collect(Collectors.toList());
            labelList = thisYearList.stream()
                    .map(data->data.getDataMonth().substring(5) + "月").collect(Collectors.toList());
            thisYearOriginDataList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getNg).collect(Collectors.toList());
            thisYearCarbonList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonNg).collect(Collectors.toList());
            thisYearConsumptionList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionNg).collect(Collectors.toList());
            lastYearOriginDataList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getNg).collect(Collectors.toList());
            lastYearCarbonList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonNg).collect(Collectors.toList());
            lastYearConsumptionList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionNg).collect(Collectors.toList());
        } else if (DischargeCarbonDataType.LPG.getValue().equals(energyType)) {
            List<DischargeDataTotalVo> dataList = dischargeDataGasMapper.countCarbonCompare(query);
            List<DischargeDataTotalVo> thisYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) >= 0).collect(Collectors.toList());
            List<DischargeDataTotalVo> lastYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) < 0).collect(Collectors.toList());
            labelList = thisYearList.stream()
                    .map(data->data.getDataMonth().substring(5) + "月").collect(Collectors.toList());
            thisYearOriginDataList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getLpg).collect(Collectors.toList());
            thisYearCarbonList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonLpg).collect(Collectors.toList());
            thisYearConsumptionList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionLpg).collect(Collectors.toList());
            lastYearOriginDataList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getLpg).collect(Collectors.toList());
            lastYearCarbonList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonLpg).collect(Collectors.toList());
            lastYearConsumptionList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionLpg).collect(Collectors.toList());
        } else if (DischargeCarbonDataType.GASOLINE.getValue().equals(energyType)) {
            List<DischargeDataTotalVo> dataList = dischargeDataOilMapper.countCarbonCompare(query);
            List<DischargeDataTotalVo> thisYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) >= 0).collect(Collectors.toList());
            List<DischargeDataTotalVo> lastYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) < 0).collect(Collectors.toList());
            labelList = thisYearList.stream()
                    .map(data->data.getDataMonth().substring(5) + "月").collect(Collectors.toList());
            thisYearOriginDataList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getGasoline).collect(Collectors.toList());
            thisYearCarbonList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonGasoline).collect(Collectors.toList());
            thisYearConsumptionList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionGasoline).collect(Collectors.toList());
            lastYearOriginDataList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getGasoline).collect(Collectors.toList());
            lastYearCarbonList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonGasoline).collect(Collectors.toList());
            lastYearConsumptionList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionGasoline).collect(Collectors.toList());
        } else if (DischargeCarbonDataType.DIESEL.getValue().equals(energyType)) {
            List<DischargeDataTotalVo> dataList = dischargeDataOilMapper.countCarbonCompare(query);
            List<DischargeDataTotalVo> thisYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) >= 0).collect(Collectors.toList());
            List<DischargeDataTotalVo> lastYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) < 0).collect(Collectors.toList());
            labelList = thisYearList.stream()
                    .map(data->data.getDataMonth().substring(5) + "月").collect(Collectors.toList());
            thisYearOriginDataList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getDiesel).collect(Collectors.toList());
            thisYearCarbonList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonDiesel).collect(Collectors.toList());
            thisYearConsumptionList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionDiesel).collect(Collectors.toList());
            lastYearOriginDataList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getDiesel).collect(Collectors.toList());
            lastYearCarbonList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonDiesel).collect(Collectors.toList());
            lastYearConsumptionList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionDiesel).collect(Collectors.toList());
        } else if (DischargeCarbonDataType.FUEL.getValue().equals(energyType)) {
            List<DischargeDataTotalVo> dataList = dischargeDataOilMapper.countCarbonCompare(query);
            List<DischargeDataTotalVo> thisYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) >= 0).collect(Collectors.toList());
            List<DischargeDataTotalVo> lastYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) < 0).collect(Collectors.toList());
            labelList = thisYearList.stream()
                    .map(data->data.getDataMonth().substring(5) + "月").collect(Collectors.toList());
            thisYearOriginDataList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getFuel).collect(Collectors.toList());
            thisYearCarbonList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonFuel).collect(Collectors.toList());
            thisYearConsumptionList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionFuel).collect(Collectors.toList());
            lastYearOriginDataList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getFuel).collect(Collectors.toList());
            lastYearCarbonList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonFuel).collect(Collectors.toList());
            lastYearConsumptionList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionFuel).collect(Collectors.toList());
        } else if (DischargeCarbonDataType.CRUDE.getValue().equals(energyType)) {
            List<DischargeDataTotalVo> dataList = dischargeDataOilMapper.countCarbonCompare(query);
            List<DischargeDataTotalVo> thisYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) >= 0).collect(Collectors.toList());
            List<DischargeDataTotalVo> lastYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) < 0).collect(Collectors.toList());
            labelList = thisYearList.stream()
                    .map(data->data.getDataMonth().substring(5) + "月").collect(Collectors.toList());
            thisYearOriginDataList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getCrude).collect(Collectors.toList());
            thisYearCarbonList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonCrude).collect(Collectors.toList());
            thisYearConsumptionList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionCrude).collect(Collectors.toList());
            lastYearOriginDataList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getCrude).collect(Collectors.toList());
            lastYearCarbonList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonCrude).collect(Collectors.toList());
            lastYearConsumptionList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionCrude).collect(Collectors.toList());
        } else if (DischargeCarbonDataType.KEROSENE.getValue().equals(energyType)) {
            List<DischargeDataTotalVo> dataList = dischargeDataOilMapper.countCarbonCompare(query);
            List<DischargeDataTotalVo> thisYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) >= 0).collect(Collectors.toList());
            List<DischargeDataTotalVo> lastYearList = dataList.stream().filter(
                    o ->o.getReportTime().compareTo(thisYearBegin) < 0).collect(Collectors.toList());
            labelList = thisYearList.stream()
                    .map(data->data.getDataMonth().substring(5) + "月").collect(Collectors.toList());
            thisYearOriginDataList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getKerosene).collect(Collectors.toList());
            thisYearCarbonList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonKerosene).collect(Collectors.toList());
            thisYearConsumptionList = thisYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionKerosene).collect(Collectors.toList());
            lastYearOriginDataList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getKerosene).collect(Collectors.toList());
            lastYearCarbonList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getCarbonKerosene).collect(Collectors.toList());
            lastYearConsumptionList = lastYearList.stream()
                    .map(DischargeDataTotalVo::getConsumptionKerosene).collect(Collectors.toList());
        } else {
            throw new BusinessException("能源类型错误");
        }
        result.put("labelList", labelList);
        result.put("thisYearOriginDataList", thisYearOriginDataList);
        result.put("thisYearCarbonList", thisYearCarbonList);
        result.put("thisYearConsumptionList", thisYearConsumptionList);
        result.put("lastYearOriginDataList", lastYearOriginDataList);
        result.put("lastYearCarbonList", lastYearCarbonList);
        result.put("lastYearConsumptionList", lastYearConsumptionList);
        return result;
    }

    @Override
    public HashMap<String, Object> getCompanyConsumption(String timeType) {
//        Calendar calendar = Calendar.getInstance();
//        calendar.add(Calendar.MONTH,-1);
//        Date currentDate = calendar.getTime();
//        DateTime startDate = null;
//        DateTime endDate = null;
//        if (DischargeDataTotalEnum.CarbonCompareTimeType.按年.getValue().equals(timeType)) {
//            startDate = DateUtil.beginOfYear(currentDate);
//            endDate = DateUtil.endOfMonth(currentDate);
//        } else if (DischargeDataTotalEnum.CarbonCompareTimeType.按月.getValue().equals(timeType)) {
//            startDate = DateUtil.beginOfMonth(currentDate);
//            endDate = DateUtil.endOfMonth(currentDate);
//        } else {
//            //累计
//
//        }
//        List<CarbonRankingVo> companyList = remoteBusinessService.getCompanyList().getData();
//        List<DischargeDataTotalVo> dataList= companyList.stream().map(res ->new DischargeDataTotalVo(res.getDeptId(),
//                res.getDeptName())).collect(Collectors.toList());
//        List<DischargeDataTotalVo> dataElectricList = getCompanyElectricData(startDate, endDate, null);
//        List<DischargeDataTotalVo> dataWaterList = getCompanyWaterData(startDate, endDate, null);
//        List<DischargeDataTotalVo> dataThermalList = getCompanyThermalData(startDate, endDate, null);
//        List<DischargeDataTotalVo> dataCoalList = getCompanyCoalData(startDate, endDate, null);
//        List<DischargeDataTotalVo> dataGasList = getCompanyGasData(startDate, endDate, null);
//        List<DischargeDataTotalVo> dataOilList = getCompanyOilData(startDate, endDate, null);
//        dataList.addAll(dataElectricList);
//        dataList.addAll(dataWaterList);
//        dataList.addAll(dataThermalList);
//        dataList.addAll(dataCoalList);
//        dataList.addAll(dataGasList);
//        dataList.addAll(dataOilList);
//        //去除重复
//        List<DischargeDataTotalVo> resultList = dataList.stream()
//                .collect(Collectors.toMap(DischargeDataTotalVo::getCompanyId, a -> a, (o1, o2) -> {
//                    o1.setConsumptionPower(o1.getConsumptionPower().add(o2.getConsumptionPower()));
//                    o1.setConsumptionWater(o1.getConsumptionWater().add(o2.getConsumptionWater()));
//                    o1.setConsumptionThermal(o1.getConsumptionThermal().add(o2.getConsumptionThermal()));
//                    o1.setConsumptionCoal(o1.getConsumptionCoal().add(o2.getConsumptionCoal()));
//                    o1.setConsumptionNg(o1.getConsumptionNg().add(o2.getConsumptionNg()));
//                    o1.setConsumptionLpg(o1.getConsumptionLpg().add(o2.getConsumptionLpg()));
//                    o1.setConsumptionGasoline(o1.getConsumptionGasoline().add(o2.getConsumptionGasoline()));
//                    o1.setConsumptionDiesel(o1.getConsumptionDiesel().add(o2.getConsumptionDiesel()));
//                    o1.setConsumptionCrude(o1.getConsumptionCrude().add(o2.getConsumptionCrude()));
//                    o1.setConsumptionFuel(o1.getConsumptionFuel().add(o2.getConsumptionFuel()));
//                    o1.setConsumptionKerosene(o1.getConsumptionKerosene().add(o2.getConsumptionKerosene()));
//                    return o1;
//                })).values().stream().collect(Collectors.toList());
//        HashMap<String, Object> result = new HashMap<>();
//        String[] labelArray = {"电", "汽油", "柴油", "原油", "燃料油", "煤油", "天然气", "液化石油气", "煤炭", "热力", "水"};
//        result.put("labelList", labelArray);
//        List<HashMap<String, Object>> mapList = new ArrayList<>();
//        resultList.forEach(item-> {
//            HashMap<String, Object> companyMap = new HashMap<>();
//            companyMap.put("companyName", item.getCompanyName());
//            List<BigDecimal> list = new ArrayList<>();
//            list.add(item.getConsumptionPower());
//            list.add(item.getConsumptionGasoline());
//            list.add(item.getConsumptionDiesel());
//            list.add(item.getConsumptionCrude());
//            list.add(item.getConsumptionFuel());
//            list.add(item.getConsumptionKerosene());
//            list.add(item.getConsumptionNg());
//            list.add(item.getConsumptionLpg());
//            list.add(item.getConsumptionCoal());
//            list.add(item.getConsumptionThermal());
//            list.add(item.getConsumptionWater());
//            companyMap.put("dataList", list);
//            mapList.add(companyMap);
//        });
//        result.put("dataList", mapList);
//        return result;
        return null;
    }

    @Override
    public List<DataReportVo> getDataReportList(DataReportBo dataReportBo) {
        SimpleDateFormat sdfMonth = new SimpleDateFormat("MM"); // 设置日期格式为月份
        String thisYear = String.valueOf(DateUtil.year(dataReportBo.getStartDate()));
        String lastYear = String.valueOf(DateUtil.year(dataReportBo.getStartDate()) - 1);
        String startMonth = sdfMonth.format(dataReportBo.getStartDate());
        String endMonth = sdfMonth.format(dataReportBo.getEndDate());
        log.info("thisYear: {}, lastYear: {}, startMonth: {}, endMonth: {}", thisYear, lastYear, startMonth, endMonth);
        //获取分公司列表
//		List<DataReportVo> companyList = dischargeDataTotalMapper.getCompanyList();
        //获取营业数据
        List<DataReportVo> companyList = dischargeDataTotalMapper.getBusinessDataList(thisYear, lastYear, startMonth, endMonth);
        //获取电排放数据
        List<DataReportVo> electricDataList = dischargeDataTotalMapper.getElectricDataList(thisYear, lastYear, startMonth, endMonth);
        //获取气排放数据
        List<DataReportVo> gasDataList = dischargeDataTotalMapper.getGasDataList(thisYear, lastYear, startMonth, endMonth);
        //获取油排放数据
        List<DataReportVo> oilDataList = dischargeDataTotalMapper.getOilDataList(thisYear, lastYear, startMonth, endMonth);
        //获取煤碳排放数据
        List<DataReportVo> coalDataList = dischargeDataTotalMapper.getCoalDataList(thisYear, lastYear, startMonth, endMonth);
        //获取热能排放数据
        List<DataReportVo> thermalDataList = dischargeDataTotalMapper.getThermalDataList(thisYear, lastYear, startMonth, endMonth);
        //获取水排放数据
        List<DataReportVo> waterDataList = dischargeDataTotalMapper.getWaterDataList(thisYear, lastYear, startMonth, endMonth);

//		companyList.addAll(businessDataList);
        companyList.addAll(electricDataList);
        companyList.addAll(gasDataList);
        companyList.addAll(oilDataList);
        companyList.addAll(coalDataList);
        companyList.addAll(thermalDataList);
        companyList.addAll(waterDataList);
        List<DataReportVo> dataReportVoList = mergeDataReportList(companyList);
        //排序
        List<DataReportVo> resultList = dataReportVoList.stream().sorted(Comparator.comparing(DataReportVo::getCarbonStrengthDecreaseRate,
                Comparator.naturalOrder())).collect(Collectors.toList());
        //获取全省营业数据
        DataReportVo provinceBusinessData = dischargeDataTotalMapper.getProvinceBusinessData(thisYear, lastYear, startMonth, endMonth);
        if (ObjectUtil.isEmpty(provinceBusinessData)) {
            provinceBusinessData = new DataReportVo();
        }
        provinceBusinessData.setCompanyName("全省");
        provinceBusinessData.setCarbonEmissions(
                resultList.stream().map(dataReportVo -> dataReportVo.getCarbonEmissions())
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
        );
        provinceBusinessData.setCarbonEmissionsLastYear(
                resultList.stream().map(dataReportVo -> dataReportVo.getCarbonEmissionsLastYear())
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
        );
        provinceBusinessData.setEnergyConsumption(
                resultList.stream().map(dataReportVo -> dataReportVo.getEnergyConsumption())
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
        );
        resultList.add(provinceBusinessData);
        return resultList;
    }

    @Override
    public List<SysDeptVO> getCompanyList() {
        return dischargeDataTotalMapper.getSysDeptList();
    }

    @Override
    public void exportDataReport(HttpServletRequest request, HttpServletResponse response, DataReportBo dataReportBo) {
        List<DataReportVo> dataReportVoList = getDataReportList(dataReportBo);
        if(CollectionUtil.isNotEmpty(dataReportVoList)){
            SimpleDateFormat sdfDate = new SimpleDateFormat("MM月");
            String thisYear = DateUtil.year(dataReportBo.getStartDate()) + "年";
            String lastYear = String.valueOf(DateUtil.year(dataReportBo.getStartDate()) - 1) + "年";
            String sStartMonth = sdfDate.format(dataReportBo.getStartDate());
            String sEndMonth = sdfDate.format(dataReportBo.getEndDate());
            String thisYearLabel = "";
            String lastYearLabel = "";
            if (sStartMonth.compareTo(sEndMonth) == 0) {
                thisYearLabel = thisYear + sStartMonth;
                lastYearLabel = lastYear + sStartMonth;
            } else {
                thisYearLabel = thisYear + sStartMonth + "至" + thisYear + sEndMonth;
                lastYearLabel = lastYear + sStartMonth + "至" + lastYear + sEndMonth;
            }
            try {
                setExcelAnnotationValue("telecomBusinessTotalLastYear","name",
                        lastYearLabel + "\n电信业务总量(万元)");
                setExcelAnnotationValue("carbonEmissionsLastYear","name",
                        lastYearLabel + "\n碳排(吨)");
                setExcelAnnotationValue("telecomBusinessTotal","name",
                        thisYearLabel + "\n电信业务总量(万元)");
                setExcelAnnotationValue("carbonEmissions","name",
                        thisYearLabel + "\n碳排(吨)");
                ExportParams exportParams = new ExportParams("数据通报(" + thisYearLabel + ")", "数据通报");
                exportParams.setTitleHeight((short) 15);
                exportParams.setDictHandler(new ExcelCommonDictHandlerImpl());
                exportParams.setStyle(ExcelExportStyler.class);
                Workbook workbook = ExcelExportUtil.exportExcel(exportParams,
                        DataReportVo.class, dataReportVoList);
                //实现页面下载
                ExcelUtil.setResponseHeader(request,response," 数据通报.xls");
                //创建页面输出流对象
                ServletOutputStream outputStream = null;
                outputStream = response.getOutputStream();
                //把文件写入输出流的对象中
                workbook.write(outputStream);
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
                throw new BusinessException("导出数据通报错误：" + e.getMessage());
            }
        }
    }

    /**
     * 通过反射动态设置导出的Excel列名
     *
     * @param annotatedColumnName：实体类中被@Excel注解的字段名
     * @param annotationFieldName：实体类中被@Excel中注解的属性名
     * @param newAnnotationFieldValue：属性的新值
     */
    @SneakyThrows
    private void setExcelAnnotationValue(String annotatedColumnName, String annotationFieldName, String newAnnotationFieldValue){
        Class<DataReportVo> airQualityRankingRespClass = DataReportVo.class;
        Field classDeclaredField =  airQualityRankingRespClass.getDeclaredField(annotatedColumnName);
        Excel excel = classDeclaredField.getAnnotation(Excel.class);
        InvocationHandler excelInvocationHandler = Proxy.getInvocationHandler(excel);
        Field excelInvocationHandlerField = excelInvocationHandler.getClass().getDeclaredField("memberValues");
        excelInvocationHandlerField.setAccessible(true);
        Map map = (Map) excelInvocationHandlerField.get(excelInvocationHandler);
        map.put(annotationFieldName, newAnnotationFieldValue);
    }


    private List<DataReportVo> mergeDataReportList(List<DataReportVo> list) {
        List<DataReportVo> result = list.stream()
                // 表示id为key， 接着如果有重复的，那么从BillsNums对象o1与o2中筛选出一个，这里选择o1，
                // 并把id重复，需要将nums和sums与o1进行合并的o2, 赋值给o1，最后返回o1
                .collect(Collectors.toMap(DataReportVo::getCompanyId, a -> a, (o1, o2) -> {
                    o1.setTelecomBusinessTotal(o1.getTelecomBusinessTotal().add(o2.getTelecomBusinessTotal()));
                    o1.setTelecomBusinessTotalLastYear(o1.getTelecomBusinessTotalLastYear().add(o2.getTelecomBusinessTotalLastYear()));
                    o1.setBusinessFlowTotal(o1.getBusinessFlowTotal().add(o2.getBusinessFlowTotal()));
                    o1.setCarbonEmissions(o1.getCarbonEmissions().add(o2.getCarbonEmissions()));
                    o1.setCarbonEmissionsLastYear(o1.getCarbonEmissionsLastYear().add(o2.getCarbonEmissionsLastYear()));
                    o1.setEnergyConsumption(o1.getEnergyConsumption().add(o2.getEnergyConsumption()));
                    return o1;
                })).values().stream().collect(Collectors.toList());
        return result;
    }

    private List<DischargeDataTotalVo> getDataByTime(Long companyId, Date dateStart, Date dateEnd) {
        String companyName = "全省";
//        if (ObjectUtil.isEmpty(companyId)) {
//            UserInfo userInfo = commonService.getCurrentUser();
//            int roleLevel = userInfo.getMaxRoleLevel();
//            if (roleLevel == SysRoleEnums.ROLE_ADMIN.getLevel()) {
//                //系统管理员或省管理员
//                companyId = 0L;
//            } else {
//                companyId = remoteUserService.getCityDeptId();
//            }
//        } else {
//            SysDept sysDept = remoteAdminService.getDeptById(companyId).getData();
//            if (ObjectUtil.isNotEmpty(sysDept)) {
//                companyName = sysDept.getName();
//            }
//        }
        List<DischargeDataTotalVo> dischargeDataTotalVoList = this.getAllDataList(dateStart, dateEnd, companyId);
        if (ObjectUtil.isNotEmpty(dateStart) && ObjectUtil.isNotEmpty(dateEnd)) {
            int[] dataRange = getDataRange(dateStart, dateEnd);
            for (int i = dataRange[0]; i < dataRange[1]; i++) {
                dischargeDataTotalVoList.get(i).setDataYear(String.valueOf(DateUtil.year(dateStart)));
                dischargeDataTotalVoList.get(i).setDataMonth((i + 1) + "月");
                dischargeDataTotalVoList.get(i).setCompanyName(companyName);
            }
        }
        int i = 0;
        while (i < dischargeDataTotalVoList.size()) {
            if (StrUtil.isNotBlank(dischargeDataTotalVoList.get(i).getDataMonth())) {
                i += 1;
            } else {
                dischargeDataTotalVoList.remove(i);
            }
        }
        return dischargeDataTotalVoList;
    }

    private DischargeMonitorSettingVo getVoByCompanyId(List<DischargeMonitorSettingVo> list, Long companyId) {
        return list.stream().filter(node -> {
            return node.getCompanyId().equals(companyId);
        }).findFirst().orElse(new DischargeMonitorSettingVo());
    }

    /**
     * 获取某个公司的生产业务数据
     */
    private BusinessProductionDataCompareVo getBusByCompanyId(List<BusinessProductionDataCompareVo> businessTotalList,
                                                              Long companyId) {
        return businessTotalList.stream().filter(item -> {
            return item.getCompanyId().equals(companyId);
        }).findFirst().orElse(new BusinessProductionDataCompareVo());
    }

    private BigDecimal formatValue(BigDecimal value) {
        return NumberFormatUtils.formatWanValue(value);
    }

    /**
     * 设置季度列表
     */
    private DischargeEmissionTrendVo getQuarter(int quarter, int begin, int end, List<DischargeEmissionTrendVo> monthList) {
        DischargeEmissionTrendVo emissionTrendVo = new DischargeEmissionTrendVo();
        List<DischargeEmissionTrendVo> firstQuarter = monthList.subList(begin, end);
        emissionTrendVo.setDataTime("第" + NumberFormatUtils.arabicNumToChineseNum(quarter) + "季度");
        BigDecimal carbonTotal = firstQuarter.stream().map(DischargeEmissionTrendVo::getCarbonTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        emissionTrendVo.setCarbonTotal(carbonTotal);
        return emissionTrendVo;
    }

    private HashMap<String, Object> countCarbonEmissionsData(List<DischargeDataTotalVo> dataTotalVoList) {
        HashMap<String, Object> result = new HashMap<>();
        BigDecimal totalCarbonEmissions = new BigDecimal(0);
        BigDecimal directCarbonEmissions = new BigDecimal(0);
        BigDecimal powerCarbonEmissions = new BigDecimal(0);
        BigDecimal thermalCarbonEmissions = new BigDecimal(0);
        BigDecimal indirectCarbonEmissions = new BigDecimal(0);
        BigDecimal ngCarbonEmissions = new BigDecimal(0);
        BigDecimal lpgCarbonEmissions = new BigDecimal(0);
        BigDecimal coalCarbonEmissions = new BigDecimal(0);
        BigDecimal gasolineCarbonEmissions = new BigDecimal(0);
        BigDecimal fuelCarbonEmissions = new BigDecimal(0);
        BigDecimal dieselCarbonEmissions = new BigDecimal(0);
        BigDecimal crudeCarbonEmissions = new BigDecimal(0);
        BigDecimal keroseneCarbonEmissions = new BigDecimal(0);
        BigDecimal waterCarbonEmissions = new BigDecimal(0);
        BigDecimal totalEnergyConsumption = new BigDecimal(0);
        BigDecimal fossilEnergyConsumption = new BigDecimal(0);
        BigDecimal nonFossilEnergyConsumption = new BigDecimal(0);
        if (CollectionUtil.isNotEmpty(dataTotalVoList)) {
            for (DischargeDataTotalVo dischargeDataTotalVo : dataTotalVoList) {
                totalCarbonEmissions = totalCarbonEmissions.add(dischargeDataTotalVo.getCarbonEmissions());
                powerCarbonEmissions = powerCarbonEmissions.add(dischargeDataTotalVo.getCarbonPower());
                thermalCarbonEmissions = thermalCarbonEmissions.add(dischargeDataTotalVo.getCarbonThermal());
                ngCarbonEmissions = ngCarbonEmissions.add(dischargeDataTotalVo.getCarbonNg());
                lpgCarbonEmissions = lpgCarbonEmissions.add(dischargeDataTotalVo.getCarbonLpg());
                coalCarbonEmissions = coalCarbonEmissions.add(dischargeDataTotalVo.getCarbonCoal());
                gasolineCarbonEmissions = gasolineCarbonEmissions.add(dischargeDataTotalVo.getCarbonGasoline());
                fuelCarbonEmissions = fuelCarbonEmissions.add(dischargeDataTotalVo.getCarbonFuel());
                dieselCarbonEmissions = dieselCarbonEmissions.add(dischargeDataTotalVo.getCarbonDiesel());
                crudeCarbonEmissions = crudeCarbonEmissions.add(dischargeDataTotalVo.getCarbonCrude());
                keroseneCarbonEmissions = keroseneCarbonEmissions.add(dischargeDataTotalVo.getCarbonKerosene());
                waterCarbonEmissions = waterCarbonEmissions.add(dischargeDataTotalVo.getCarbonWater());
                totalEnergyConsumption = totalEnergyConsumption.add(dischargeDataTotalVo.getEnergyConsumption());
                fossilEnergyConsumption = fossilEnergyConsumption.add(dischargeDataTotalVo.getOilEnergyConsumption())
                        .add(dischargeDataTotalVo.getGasEnergyConsumption());
            }
            indirectCarbonEmissions = indirectCarbonEmissions.add(powerCarbonEmissions).add(thermalCarbonEmissions);
            directCarbonEmissions = totalCarbonEmissions.subtract(indirectCarbonEmissions);
            nonFossilEnergyConsumption = totalEnergyConsumption.subtract(fossilEnergyConsumption);
        }
        HashMap<String, Object> directMap = new HashMap<>();
        directMap.put("ngCarbonEmissions", ngCarbonEmissions.divide(BigDecimal.valueOf(10000))
                .setScale(2, RoundingMode.HALF_UP));
        directMap.put("lpgCarbonEmissions", lpgCarbonEmissions.divide(BigDecimal.valueOf(10000))
                .setScale(2, RoundingMode.HALF_UP));
        directMap.put("coalCarbonEmissions", coalCarbonEmissions.divide(BigDecimal.valueOf(10000))
                .setScale(2, RoundingMode.HALF_UP));
        directMap.put("gasolineCarbonEmissions", gasolineCarbonEmissions.divide(BigDecimal.valueOf(10000))
                .setScale(2, RoundingMode.HALF_UP));
        directMap.put("fuelCarbonEmissions", fuelCarbonEmissions.divide(BigDecimal.valueOf(10000))
                .setScale(2, RoundingMode.HALF_UP));
        directMap.put("dieselCarbonEmissions", dieselCarbonEmissions.divide(BigDecimal.valueOf(10000))
                .setScale(2, RoundingMode.HALF_UP));
        directMap.put("crudeCarbonEmissions", crudeCarbonEmissions.divide(BigDecimal.valueOf(10000))
                .setScale(2, RoundingMode.HALF_UP));
        directMap.put("keroseneCarbonEmissions", keroseneCarbonEmissions.divide(BigDecimal.valueOf(10000))
                .setScale(2, RoundingMode.HALF_UP));
        directMap.put("waterCarbonEmissions", waterCarbonEmissions.divide(BigDecimal.valueOf(10000))
                .setScale(2, RoundingMode.HALF_UP));
        HashMap<String, Object> indirectMap = new HashMap<>();
        indirectMap.put("powerCarbonEmissions", powerCarbonEmissions.divide(BigDecimal.valueOf(10000))
                .setScale(2, RoundingMode.HALF_UP));
        indirectMap.put("thermalCarbonEmissions", thermalCarbonEmissions.divide(BigDecimal.valueOf(10000))
                .setScale(2, RoundingMode.HALF_UP));
        HashMap<String, Object> carbonEmissionsMap = new HashMap<>();
        carbonEmissionsMap.put("totalCarbonEmissions", totalCarbonEmissions.divide(BigDecimal.valueOf(10000))
                .setScale(2, RoundingMode.HALF_UP));
        carbonEmissionsMap.put("indirectCarbonEmissions", indirectCarbonEmissions.divide(BigDecimal.valueOf(10000))
                .setScale(2, RoundingMode.HALF_UP));
        carbonEmissionsMap.put("directCarbonEmissions", directCarbonEmissions.divide(BigDecimal.valueOf(10000))
                .setScale(2, RoundingMode.HALF_UP));
        carbonEmissionsMap.put("direct", directMap);
        carbonEmissionsMap.put("indirect", indirectMap);
        result.put("carbonEmissions", carbonEmissionsMap);
        HashMap<String, Object> energyConsumptionMap = new HashMap<>();
        energyConsumptionMap.put("totalEnergyConsumption", totalEnergyConsumption.divide(BigDecimal.valueOf(10000))
                .setScale(2, RoundingMode.HALF_UP));
        energyConsumptionMap.put("fossilEnergyConsumption", fossilEnergyConsumption.divide(BigDecimal.valueOf(10000))
                .setScale(2, RoundingMode.HALF_UP));
        energyConsumptionMap.put("nonFossilEnergyConsumption", nonFossilEnergyConsumption.divide(BigDecimal.valueOf(10000))
                .setScale(2, RoundingMode.HALF_UP));
        result.put("energyConsumption", energyConsumptionMap);
        return result;
    }


    private HashMap<String, Object> countMonthConsumptionData(DischargeDataTotalVo thisData,
                                                              DischargeDataTotalVo preData,
                                                              DischargeDataTotalVo lastData) {
        HashMap<String, Object> powerMap = new HashMap<>();
        HashMap<String, Object> coalMap = new HashMap<>();
        HashMap<String, Object> thermalMap = new HashMap<>();
        HashMap<String, Object> waterMap = new HashMap<>();
        HashMap<String, Object> ngMap = new HashMap<>();
        HashMap<String, Object> lpgMap = new HashMap<>();
        HashMap<String, Object> gasolineMap = new HashMap<>();
        HashMap<String, Object> dieselMap = new HashMap<>();
        HashMap<String, Object> crudeMap = new HashMap<>();
        HashMap<String, Object> fuelMap = new HashMap<>();
        HashMap<String, Object> keroseneMap = new HashMap<>();
        HashMap<String, Object> monthDataMap = new HashMap<>();

        BigDecimal totalEnergyConsumption = thisData.getEnergyConsumption();
        monthDataMap.put("monthEnergyConsumption", totalEnergyConsumption.setScale(2, RoundingMode.HALF_UP));
        monthDataMap.put("preMonthEnergyConsumption", preData.getEnergyConsumption().setScale(2, RoundingMode.HALF_UP));
        monthDataMap.put("LastMonthEnergyConsumption", lastData.getEnergyConsumption().setScale(2, RoundingMode.HALF_UP));
        monthDataMap.put("YoY", MathUtils.getUpRate(totalEnergyConsumption, lastData.getEnergyConsumption()));
        monthDataMap.put("MoM", MathUtils.getUpRate(totalEnergyConsumption, preData.getEnergyConsumption()));

        powerMap.put("used", thisData.getPower().setScale(2, RoundingMode.HALF_UP));
        powerMap.put("carbonEmissions", thisData.getCarbonPower().setScale(2, RoundingMode.HALF_UP));
        powerMap.put("energyConsumption", thisData.getConsumptionPower().setScale(2, RoundingMode.HALF_UP));
        powerMap.put("YoY", MathUtils.getUpRate(thisData.getPower(), lastData.getPower()));
        powerMap.put("MoM", MathUtils.getUpRate(thisData.getPower(), preData.getPower()));

        coalMap.put("used", thisData.getCoal().setScale(2, RoundingMode.HALF_UP));
        coalMap.put("carbonEmissions", thisData.getCarbonCoal().setScale(2, RoundingMode.HALF_UP));
        coalMap.put("energyConsumption", thisData.getConsumptionCoal().setScale(2, RoundingMode.HALF_UP));
        coalMap.put("YoY", MathUtils.getUpRate(thisData.getCoal(), lastData.getCoal()));
        coalMap.put("MoM", MathUtils.getUpRate(thisData.getCoal(), preData.getCoal()));

        thermalMap.put("used", thisData.getThermal().setScale(2, RoundingMode.HALF_UP));
        thermalMap.put("carbonEmissions", thisData.getCarbonThermal().setScale(2, RoundingMode.HALF_UP));
        thermalMap.put("energyConsumption", thisData.getConsumptionThermal().setScale(2, RoundingMode.HALF_UP));
        thermalMap.put("YoY", MathUtils.getUpRate(thisData.getThermal(), lastData.getThermal()));
        thermalMap.put("MoM", MathUtils.getUpRate(thisData.getThermal(), preData.getThermal()));

        waterMap.put("used", thisData.getWater().setScale(2, RoundingMode.HALF_UP));
        waterMap.put("carbonEmissions", thisData.getCarbonWater().setScale(2, RoundingMode.HALF_UP));
        waterMap.put("energyConsumption", thisData.getConsumptionWater().setScale(2, RoundingMode.HALF_UP));
        waterMap.put("YoY", MathUtils.getUpRate(thisData.getThermal(), lastData.getThermal()));
        waterMap.put("MoM", MathUtils.getUpRate(thisData.getThermal(), preData.getThermal()));

        ngMap.put("used", thisData.getNg().setScale(2, RoundingMode.HALF_UP));
        ngMap.put("carbonEmissions", thisData.getCarbonNg().setScale(2, RoundingMode.HALF_UP));
        ngMap.put("energyConsumption", thisData.getConsumptionNg().setScale(2, RoundingMode.HALF_UP));
        ngMap.put("YoY", MathUtils.getUpRate(thisData.getNg(), lastData.getNg()));
        ngMap.put("MoM", MathUtils.getUpRate(thisData.getNg(), preData.getNg()));

        lpgMap.put("used", thisData.getLpg().setScale(2, RoundingMode.HALF_UP));
        lpgMap.put("carbonEmissions", thisData.getCarbonLpg().setScale(2, RoundingMode.HALF_UP));
        lpgMap.put("energyConsumption", thisData.getConsumptionLpg().setScale(2, RoundingMode.HALF_UP));
        lpgMap.put("YoY", MathUtils.getUpRate(thisData.getLpg(), lastData.getLpg()));
        lpgMap.put("MoM", MathUtils.getUpRate(thisData.getLpg(), preData.getLpg()));

        gasolineMap.put("used", thisData.getGasoline().setScale(2, RoundingMode.HALF_UP));
        gasolineMap.put("carbonEmissions", thisData.getCarbonGasoline().setScale(2, RoundingMode.HALF_UP));
        gasolineMap.put("energyConsumption", thisData.getConsumptionGasoline().setScale(2, RoundingMode.HALF_UP));
        gasolineMap.put("YoY", MathUtils.getUpRate(thisData.getGasoline(), lastData.getGasoline()));
        gasolineMap.put("MoM", MathUtils.getUpRate(thisData.getGasoline(), preData.getGasoline()));

        dieselMap.put("used", thisData.getDiesel().setScale(2, RoundingMode.HALF_UP));
        dieselMap.put("carbonEmissions", thisData.getCarbonDiesel().setScale(2, RoundingMode.HALF_UP));
        dieselMap.put("energyConsumption", thisData.getConsumptionDiesel().setScale(2, RoundingMode.HALF_UP));
        dieselMap.put("YoY", MathUtils.getUpRate(thisData.getDiesel(), lastData.getDiesel()));
        dieselMap.put("MoM", MathUtils.getUpRate(thisData.getDiesel(), preData.getDiesel()));

        crudeMap.put("used", thisData.getCrude().setScale(2, RoundingMode.HALF_UP));
        crudeMap.put("carbonEmissions", thisData.getCarbonCrude().setScale(2, RoundingMode.HALF_UP));
        crudeMap.put("energyConsumption", thisData.getConsumptionCrude().setScale(2, RoundingMode.HALF_UP));
        crudeMap.put("YoY", MathUtils.getUpRate(thisData.getCrude(), lastData.getCrude()));
        crudeMap.put("MoM", MathUtils.getUpRate(thisData.getCrude(), preData.getCrude()));

        fuelMap.put("used", thisData.getFuel().setScale(2, RoundingMode.HALF_UP));
        fuelMap.put("carbonEmissions", thisData.getCarbonFuel().setScale(2, RoundingMode.HALF_UP));
        fuelMap.put("energyConsumption", thisData.getConsumptionFuel().setScale(2, RoundingMode.HALF_UP));
        fuelMap.put("YoY", MathUtils.getUpRate(thisData.getFuel(), lastData.getFuel()));
        fuelMap.put("MoM", MathUtils.getUpRate(thisData.getFuel(), preData.getFuel()));

        keroseneMap.put("used", thisData.getKerosene().setScale(2, RoundingMode.HALF_UP));
        keroseneMap.put("carbonEmissions", thisData.getCarbonKerosene().setScale(2, RoundingMode.HALF_UP));
        keroseneMap.put("energyConsumption", thisData.getConsumptionKerosene().setScale(2, RoundingMode.HALF_UP));
        keroseneMap.put("YoY", MathUtils.getUpRate(thisData.getKerosene(), lastData.getKerosene()));
        keroseneMap.put("MoM", MathUtils.getUpRate(thisData.getKerosene(), preData.getKerosene()));

        HashMap<String, Object> result = new HashMap<>();
        result.put("month", monthDataMap);
        result.put("power", powerMap);
        result.put("coal", coalMap);
        result.put("thermal", thermalMap);
        result.put("water", waterMap);
        result.put("ng", ngMap);
        result.put("lpg", lpgMap);
        result.put("gasoline", gasolineMap);
        result.put("diesel", dieselMap);
        result.put("crude", crudeMap);
        result.put("fuel", fuelMap);
        result.put("kerosene", keroseneMap);
        return result;
    }


    private HashMap<String, Object> countYearConsumptionData(List<DischargeDataTotalVo> dataList,
                                                             List<DischargeDataTotalVo> preDataList) {
        /**
         * 汽油
         */
        BigDecimal consumptionGasoline = new BigDecimal("0");

        /**
         * 柴油
         */
        BigDecimal consumptionDiesel = new BigDecimal("0");

        /**
         * 原油
         */
        BigDecimal consumptionCrude = new BigDecimal("0");

        /**
         * 燃料油
         */
        BigDecimal consumptionFuel = new BigDecimal("0");

        /**
         * 煤油
         */
        BigDecimal consumptionKerosene = new BigDecimal("0");

        /**
         * 电力
         */
        BigDecimal consumptionPower = new BigDecimal("0");

        /**
         * 天然气
         */
        BigDecimal consumptionNg = new BigDecimal("0");

        /**
         * 液化石油气
         */
        BigDecimal consumptionLpg = new BigDecimal("0");

        /**
         * 热力总量
         */
        BigDecimal consumptionThermal = new BigDecimal("0");

        /**
         * 用水总量
         */
        BigDecimal consumptionWater = new BigDecimal("0");

        /**
         * 煤碳
         */
        BigDecimal consumptionCoal = new BigDecimal("0");

        BigDecimal totalEnergyConsumption = new BigDecimal(0);

        BigDecimal preEnergyConsumption = new BigDecimal(0);

        HashMap<String, Object> powerMap = new HashMap<>();
        HashMap<String, Object> coalMap = new HashMap<>();
        HashMap<String, Object> thermalMap = new HashMap<>();
        HashMap<String, Object> waterMap = new HashMap<>();
        HashMap<String, Object> ngMap = new HashMap<>();
        HashMap<String, Object> lpgMap = new HashMap<>();
        HashMap<String, Object> gasolineMap = new HashMap<>();
        HashMap<String, Object> dieselMap = new HashMap<>();
        HashMap<String, Object> crudeMap = new HashMap<>();
        HashMap<String, Object> fuelMap = new HashMap<>();
        HashMap<String, Object> keroseneMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(dataList)) {
            consumptionGasoline = dataList.stream()
                    .map(DischargeDataTotalVo::getConsumptionGasoline).reduce(BigDecimal.ZERO, BigDecimal::add);
            consumptionDiesel = dataList.stream()
                    .map(DischargeDataTotalVo::getConsumptionDiesel).reduce(BigDecimal.ZERO, BigDecimal::add);
            consumptionCrude = dataList.stream()
                    .map(DischargeDataTotalVo::getConsumptionCrude).reduce(BigDecimal.ZERO, BigDecimal::add);
            consumptionFuel = dataList.stream()
                    .map(DischargeDataTotalVo::getConsumptionFuel).reduce(BigDecimal.ZERO, BigDecimal::add);
            consumptionKerosene = dataList.stream()
                    .map(DischargeDataTotalVo::getConsumptionKerosene).reduce(BigDecimal.ZERO, BigDecimal::add);
            consumptionPower = dataList.stream()
                    .map(DischargeDataTotalVo::getConsumptionPower).reduce(BigDecimal.ZERO, BigDecimal::add);
            consumptionNg = dataList.stream()
                    .map(DischargeDataTotalVo::getConsumptionNg).reduce(BigDecimal.ZERO, BigDecimal::add);
            consumptionLpg = dataList.stream()
                    .map(DischargeDataTotalVo::getConsumptionLpg).reduce(BigDecimal.ZERO, BigDecimal::add);
            consumptionThermal = dataList.stream()
                    .map(DischargeDataTotalVo::getConsumptionThermal).reduce(BigDecimal.ZERO, BigDecimal::add);
            consumptionWater = dataList.stream()
                    .map(DischargeDataTotalVo::getConsumptionWater).reduce(BigDecimal.ZERO, BigDecimal::add);
            consumptionCoal = dataList.stream()
                    .map(DischargeDataTotalVo::getConsumptionCoal).reduce(BigDecimal.ZERO, BigDecimal::add);
            totalEnergyConsumption = dataList.stream()
                    .map(DischargeDataTotalVo::getEnergyConsumption).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        if (CollectionUtil.isNotEmpty(preDataList)) {
            preEnergyConsumption = preDataList.stream()
                    .map(DischargeDataTotalVo::getEnergyConsumption).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        HashMap<String, Object> yearDataMap = new HashMap<>();
        yearDataMap.put("energyConsumption", totalEnergyConsumption.setScale(2, RoundingMode.HALF_UP));
        yearDataMap.put("preEnergyConsumption", preEnergyConsumption.setScale(2, RoundingMode.HALF_UP));
        yearDataMap.put("YoY", MathUtils.getUpRate(totalEnergyConsumption, preEnergyConsumption));

        powerMap.put("used", dataList.stream()
                .map(DischargeDataTotalVo::getPower).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        powerMap.put("carbonEmissions", dataList.stream()
                .map(DischargeDataTotalVo::getCarbonPower).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        powerMap.put("energyConsumption", consumptionPower.setScale(2, RoundingMode.HALF_UP));
        powerMap.put("consumptionProportion", MathUtils.division(consumptionPower.multiply(BigDecimal.valueOf(100)),
                totalEnergyConsumption));
        coalMap.put("used", dataList.stream()
                .map(DischargeDataTotalVo::getCoal).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        coalMap.put("carbonEmissions", dataList.stream()
                .map(DischargeDataTotalVo::getCarbonCoal).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        coalMap.put("energyConsumption", consumptionCoal.setScale(2, RoundingMode.HALF_UP));
        coalMap.put("consumptionProportion", MathUtils.division(consumptionCoal.multiply(BigDecimal.valueOf(100)),
                totalEnergyConsumption));
        thermalMap.put("used", dataList.stream()
                .map(DischargeDataTotalVo::getThermal).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        thermalMap.put("carbonEmissions", dataList.stream()
                .map(DischargeDataTotalVo::getCarbonThermal).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        thermalMap.put("energyConsumption", consumptionThermal.setScale(2, RoundingMode.HALF_UP));
        thermalMap.put("consumptionProportion", MathUtils.division(consumptionThermal.multiply(BigDecimal.valueOf(100)),
                totalEnergyConsumption));
        waterMap.put("used", dataList.stream()
                .map(DischargeDataTotalVo::getWater).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        waterMap.put("carbonEmissions", dataList.stream()
                .map(DischargeDataTotalVo::getCarbonWater).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        waterMap.put("energyConsumption", consumptionWater.setScale(2, RoundingMode.HALF_UP));
        waterMap.put("consumptionProportion", MathUtils.division(consumptionWater.multiply(BigDecimal.valueOf(100)),
                totalEnergyConsumption));
        ngMap.put("used", dataList.stream()
                .map(DischargeDataTotalVo::getNg).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        ngMap.put("carbonEmissions", dataList.stream()
                .map(DischargeDataTotalVo::getCarbonNg).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        ngMap.put("energyConsumption", consumptionNg.setScale(2, RoundingMode.HALF_UP));
        ngMap.put("consumptionProportion", MathUtils.division(consumptionNg.multiply(BigDecimal.valueOf(100)),
                totalEnergyConsumption));
        lpgMap.put("used", dataList.stream()
                .map(DischargeDataTotalVo::getLpg).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        lpgMap.put("carbonEmissions", dataList.stream()
                .map(DischargeDataTotalVo::getCarbonLpg).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        lpgMap.put("energyConsumption", consumptionLpg.setScale(2, RoundingMode.HALF_UP));
        lpgMap.put("consumptionProportion", MathUtils.division(consumptionLpg.multiply(BigDecimal.valueOf(100)),
                totalEnergyConsumption));
        gasolineMap.put("used", dataList.stream()
                .map(DischargeDataTotalVo::getGasoline).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        gasolineMap.put("carbonEmissions", dataList.stream()
                .map(DischargeDataTotalVo::getCarbonGasoline).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        gasolineMap.put("energyConsumption", consumptionGasoline.setScale(2, RoundingMode.HALF_UP));
        gasolineMap.put("consumptionProportion", MathUtils.division(consumptionGasoline.multiply(BigDecimal.valueOf(100)),
                totalEnergyConsumption));
        dieselMap.put("used", dataList.stream()
                .map(DischargeDataTotalVo::getDiesel).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        dieselMap.put("carbonEmissions", dataList.stream()
                .map(DischargeDataTotalVo::getCarbonDiesel).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        dieselMap.put("energyConsumption", consumptionDiesel.setScale(2, RoundingMode.HALF_UP));
        dieselMap.put("consumptionProportion", MathUtils.division(consumptionDiesel.multiply(BigDecimal.valueOf(100)),
                totalEnergyConsumption));
        crudeMap.put("used", dataList.stream()
                .map(DischargeDataTotalVo::getCrude).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        crudeMap.put("carbonEmissions", dataList.stream()
                .map(DischargeDataTotalVo::getCarbonCrude).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        crudeMap.put("energyConsumption", consumptionCrude.setScale(2, RoundingMode.HALF_UP));
        crudeMap.put("consumptionProportion", MathUtils.division(consumptionCrude.multiply(BigDecimal.valueOf(100)),
                totalEnergyConsumption));
        fuelMap.put("used", dataList.stream()
                .map(DischargeDataTotalVo::getFuel).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        fuelMap.put("carbonEmissions", dataList.stream()
                .map(DischargeDataTotalVo::getCarbonFuel).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        fuelMap.put("energyConsumption", consumptionFuel.setScale(2, RoundingMode.HALF_UP));
        fuelMap.put("consumptionProportion", MathUtils.division(consumptionFuel.multiply(BigDecimal.valueOf(100)),
                totalEnergyConsumption));
        keroseneMap.put("used", dataList.stream()
                .map(DischargeDataTotalVo::getKerosene).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        keroseneMap.put("carbonEmissions", dataList.stream()
                .map(DischargeDataTotalVo::getCarbonKerosene).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        keroseneMap.put("energyConsumption", consumptionKerosene.setScale(2, RoundingMode.HALF_UP));
        keroseneMap.put("consumptionProportion", MathUtils.division(consumptionKerosene.multiply(BigDecimal.valueOf(100)),
                totalEnergyConsumption));
        HashMap<String, Object> result = new HashMap<>();
        result.put("year", yearDataMap);
        result.put("power", powerMap);
        result.put("coal", coalMap);
        result.put("thermal", thermalMap);
        result.put("water", waterMap);
        result.put("ng", ngMap);
        result.put("lpg", lpgMap);
        result.put("gasoline", gasolineMap);
        result.put("diesel", dieselMap);
        result.put("crude", crudeMap);
        result.put("fuel", fuelMap);
        result.put("kerosene", keroseneMap);
        return result;
    }

    private BigDecimal getTotalCarbonEmissions(List<DischargeDataTotalVo> dataTotalVoList) {
        BigDecimal result = new BigDecimal(0);
        if (CollectionUtil.isNotEmpty(dataTotalVoList)) {
            result = dataTotalVoList.stream()
                    .map(DischargeDataTotalVo::getCarbonEmissions).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return  result;
    }

    private BigDecimal getTotalEnergyConsumption(List<DischargeDataTotalVo> dataTotalVoList) {
        BigDecimal result = new BigDecimal(0);
        if (CollectionUtil.isNotEmpty(dataTotalVoList)) {
            result = dataTotalVoList.stream()
                    .map(DischargeDataTotalVo::getEnergyConsumption).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return  result;
    }

    private List<DischargeDataTotalVo> mergeCarbonCompareMonthList(List<DischargeDataTotalVo> list) {
        List<DischargeDataTotalVo> result = list.stream()
                // 表示id为key， 接着如果有重复的，那么从list对象o1与o2中筛选出一个，这里选择o1，
                // 并把id重复，需要将nums和sums与o1进行合并的o2, 赋值给o1，最后返回o1
                .collect(Collectors.toMap(DischargeDataTotalVo::getReportTime, a -> a, (o1, o2) -> {
                    o1.setCarbonCoal(o1.getCarbonCoal().add(o2.getCarbonCoal()));
                    o1.setCarbonPower(o1.getCarbonPower().add(o2.getCarbonPower()));
                    o1.setCarbonWater(o1.getCarbonWater().add(o2.getCarbonWater()));
                    o1.setCarbonThermal(o1.getCarbonThermal().add(o2.getCarbonThermal()));
                    o1.setCarbonNg(o1.getCarbonNg().add(o2.getCarbonNg()));
                    o1.setCarbonLpg(o1.getCarbonLpg().add(o2.getCarbonLpg()));
                    o1.setCarbonGasoline(o1.getCarbonGasoline().add(o2.getCarbonGasoline()));
                    o1.setCarbonDiesel(o1.getCarbonDiesel().add(o2.getCarbonDiesel()));
                    o1.setCarbonFuel(o1.getCarbonFuel().add(o2.getCarbonFuel()));
                    o1.setCarbonCrude(o1.getCarbonCrude().add(o2.getCarbonCrude()));
                    o1.setCarbonKerosene(o1.getCarbonKerosene().add(o2.getCarbonKerosene()));
                    o1.setConsumptionCoal(o1.getCarbonCoal().add(o2.getCarbonCoal()));
                    o1.setConsumptionPower(o1.getConsumptionPower().add(o2.getConsumptionPower()));
                    o1.setConsumptionWater(o1.getConsumptionWater().add(o2.getConsumptionWater()));
                    o1.setConsumptionThermal(o1.getConsumptionThermal().add(o2.getConsumptionThermal()));
                    o1.setConsumptionNg(o1.getConsumptionNg().add(o2.getConsumptionNg()));
                    o1.setConsumptionLpg(o1.getConsumptionLpg().add(o2.getConsumptionLpg()));
                    o1.setConsumptionGasoline(o1.getConsumptionGasoline().add(o2.getConsumptionGasoline()));
                    o1.setConsumptionDiesel(o1.getConsumptionDiesel().add(o2.getConsumptionDiesel()));
                    o1.setConsumptionFuel(o1.getConsumptionFuel().add(o2.getConsumptionFuel()));
                    o1.setConsumptionCrude(o1.getConsumptionCrude().add(o2.getConsumptionCrude()));
                    o1.setConsumptionKerosene(o1.getConsumptionKerosene().add(o2.getConsumptionKerosene()));
                    return o1;
                })).values().stream().collect(Collectors.toList());
        return result;
    }

    private List<DischargeDataTotalVo> mergeCarbonCompareYearList(List<DischargeDataTotalVo> list) {
        List<DischargeDataTotalVo> result = list.stream()
                // 表示id为key， 接着如果有重复的，那么从list对象o1与o2中筛选出一个，这里选择o1，
                // 并把id重复，需要将nums和sums与o1进行合并的o2, 赋值给o1，最后返回o1
                .collect(Collectors.toMap(DischargeDataTotalVo::getDataYear, a -> a, (o1, o2) -> {
                    o1.setCarbonCoal(o1.getCarbonCoal().add(o2.getCarbonCoal()));
                    o1.setCarbonPower(o1.getCarbonPower().add(o2.getCarbonPower()));
                    o1.setCarbonWater(o1.getCarbonWater().add(o2.getCarbonWater()));
                    o1.setCarbonThermal(o1.getCarbonThermal().add(o2.getCarbonThermal()));
                    o1.setCarbonNg(o1.getCarbonNg().add(o2.getCarbonNg()));
                    o1.setCarbonLpg(o1.getCarbonLpg().add(o2.getCarbonLpg()));
                    o1.setCarbonGasoline(o1.getCarbonGasoline().add(o2.getCarbonGasoline()));
                    o1.setCarbonDiesel(o1.getCarbonDiesel().add(o2.getCarbonDiesel()));
                    o1.setCarbonFuel(o1.getCarbonFuel().add(o2.getCarbonFuel()));
                    o1.setCarbonCrude(o1.getCarbonCrude().add(o2.getCarbonCrude()));
                    o1.setCarbonKerosene(o1.getCarbonKerosene().add(o2.getCarbonKerosene()));
                    o1.setConsumptionCoal(o1.getCarbonCoal().add(o2.getCarbonCoal()));
                    o1.setConsumptionPower(o1.getConsumptionPower().add(o2.getConsumptionPower()));
                    o1.setConsumptionWater(o1.getConsumptionWater().add(o2.getConsumptionWater()));
                    o1.setConsumptionThermal(o1.getConsumptionThermal().add(o2.getConsumptionThermal()));
                    o1.setConsumptionNg(o1.getConsumptionNg().add(o2.getConsumptionNg()));
                    o1.setConsumptionLpg(o1.getConsumptionLpg().add(o2.getConsumptionLpg()));
                    o1.setConsumptionGasoline(o1.getConsumptionGasoline().add(o2.getConsumptionGasoline()));
                    o1.setConsumptionDiesel(o1.getConsumptionDiesel().add(o2.getConsumptionDiesel()));
                    o1.setConsumptionFuel(o1.getConsumptionFuel().add(o2.getConsumptionFuel()));
                    o1.setConsumptionCrude(o1.getConsumptionCrude().add(o2.getConsumptionCrude()));
                    o1.setConsumptionKerosene(o1.getConsumptionKerosene().add(o2.getConsumptionKerosene()));
                    return o1;
                })).values().stream().collect(Collectors.toList());
        return result;
    }

    private int[] getDataRange(Date dateStart, Date dateEnd) {
        int[] result = new int[2];
        result[0] = DateUtil.month(dateStart);
        if (DateUtil.year(dateStart) == DateUtil.year(new Date())) {
            //当前年份
            result[1] = DateUtil.month(new Date());
        } else {
            //往年
            result[1] = DateUtil.month(dateEnd) + 1;
        }
        return result;
    }

    private List<DischargeDataTotalVo> getCompanyElectricData(Date dateStart, Date dateEnd, Long companyId) {
        //获取电力数据
        List<DischargeDataElectricVo> dischargeDataElectricVoList = dischargeDataElectricService.
                countCompanyData(dateStart, dateEnd, companyId);
        //去除重复
        List<DischargeDataElectricVo> electricVoList = dischargeDataElectricVoList.stream()
                .collect(Collectors.toMap(DischargeDataElectricVo::getCompanyId, a -> a, (o1, o2) -> {
                    o1.setCarbonEmissions(o1.getCarbonEmissions().add(o2.getCarbonEmissions()));
                    o1.setEnergyConsumption(o1.getEnergyConsumption().add(o2.getEnergyConsumption()));
                    return o1;
                })).values().stream().collect(Collectors.toList());
        List<DischargeDataTotalVo> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(electricVoList)) {
            electricVoList.forEach(electricVo -> {
                DischargeDataTotalVo dischargeDataTotalVo = new DischargeDataTotalVo();
                dischargeDataTotalVo.setCompanyId(electricVo.getCompanyId());
                dischargeDataTotalVo.setDataYear(electricVo.getDataYear());
                dischargeDataTotalVo.setConsumptionPower(electricVo.getEnergyConsumption());
                result.add(dischargeDataTotalVo);
            });
        }
        return result;
    }

    private List<DischargeDataTotalVo> getCompanyThermalData(Date dateStart, Date dateEnd, Long companyId) {
        //获取热力数据
        List<DischargeDataThermalVo> dischargeDataThermalVoList = dischargeDataThermalService.
                countCompanyData(dateStart, dateEnd, companyId);
        //去除重复
        List<DischargeDataThermalVo> dataVoList = dischargeDataThermalVoList.stream()
                .collect(Collectors.toMap(DischargeDataThermalVo::getCompanyId, a -> a, (o1, o2) -> {
                    o1.setCarbonEmissions(o1.getCarbonEmissions().add(o2.getCarbonEmissions()));
                    o1.setEnergyConsumption(o1.getEnergyConsumption().add(o2.getEnergyConsumption()));
                    return o1;
                })).values().stream().collect(Collectors.toList());
        List<DischargeDataTotalVo> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dataVoList)) {
            dataVoList.forEach(dataVo -> {
                DischargeDataTotalVo dischargeDataTotalVo = new DischargeDataTotalVo();
                dischargeDataTotalVo.setCompanyId(dataVo.getCompanyId());
                dischargeDataTotalVo.setDataYear(dataVo.getDataYear());
                dischargeDataTotalVo.setConsumptionThermal(dataVo.getEnergyConsumption());
                result.add(dischargeDataTotalVo);
            });
        }
        return result;
    }

    private List<DischargeDataTotalVo> getCompanyWaterData(Date dateStart, Date dateEnd, Long companyId) {
        //获取水量数据
        List<DischargeDataWaterVo> dischargeDataWaterVoList = dischargeDataWaterService.
                countCompanyData(dateStart, dateEnd, companyId);
        //去除重复
        List<DischargeDataWaterVo> dataVoList = dischargeDataWaterVoList.stream()
                .collect(Collectors.toMap(DischargeDataWaterVo::getCompanyId, a -> a, (o1, o2) -> {
                    o1.setCarbonEmissions(o1.getCarbonEmissions().add(o2.getCarbonEmissions()));
                    o1.setEnergyConsumption(o1.getEnergyConsumption().add(o2.getEnergyConsumption()));
                    return o1;
                })).values().stream().collect(Collectors.toList());
        List<DischargeDataTotalVo> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dataVoList)) {
            dataVoList.forEach(dataVo -> {
                DischargeDataTotalVo dischargeDataTotalVo = new DischargeDataTotalVo();
                dischargeDataTotalVo.setCompanyId(dataVo.getCompanyId());
                dischargeDataTotalVo.setDataYear(dataVo.getDataYear());
                dischargeDataTotalVo.setConsumptionWater(dataVo.getEnergyConsumption());
                result.add(dischargeDataTotalVo);
            });
        }
        return result;
    }

    private List<DischargeDataTotalVo> getCompanyGasData(Date dateStart, Date dateEnd, Long companyId) {
        //获取汽类数据
        List<DischargeDataGasVo> dischargeDataGasVoList = dischargeDataGasService.
                countCompanyData(dateStart, dateEnd, companyId);
        //去除重复
        List<DischargeDataGasVo> dataVoList = dischargeDataGasVoList.stream()
                .collect(Collectors.toMap(DischargeDataGasVo::getCompanyId, a -> a, (o1, o2) -> {
                    o1.setCarbonNg(o1.getCarbonNg().add(o2.getCarbonNg()));
                    o1.setCarbonLpg(o1.getCarbonLpg().add(o2.getCarbonLpg()));
                    o1.setConsumptionNg(o1.getConsumptionNg().add(o2.getConsumptionNg()));
                    o1.setConsumptionLpg(o1.getConsumptionLpg().add(o2.getConsumptionLpg()));
                    return o1;
                })).values().stream().collect(Collectors.toList());
        List<DischargeDataTotalVo> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dataVoList)) {
            dataVoList.forEach(dataVo -> {
                DischargeDataTotalVo dischargeDataTotalVo = new DischargeDataTotalVo();
                dischargeDataTotalVo.setCompanyId(dataVo.getCompanyId());
                dischargeDataTotalVo.setDataYear(dataVo.getDataYear());
                dischargeDataTotalVo.setConsumptionNg(dataVo.getConsumptionNg());
                dischargeDataTotalVo.setConsumptionLpg(dataVo.getConsumptionLpg());
                result.add(dischargeDataTotalVo);
            });
        }
        return result;
    }

    private List<DischargeDataTotalVo> getCompanyOilData(Date dateStart, Date dateEnd, Long companyId) {
        //获取油类数据
        List<DischargeDataOilVo> dischargeDataOilVoList = dischargeDataOilService.countCompanyData(
                dateStart, dateEnd, companyId);
        //去除重复
        List<DischargeDataOilVo> dataVoList = dischargeDataOilVoList.stream()
                .collect(Collectors.toMap(DischargeDataOilVo::getCompanyId, a -> a, (o1, o2) -> {
                    o1.setCarbonGasoline(o1.getCarbonGasoline().add(o2.getCarbonGasoline()));
                    o1.setCarbonDiesel(o1.getCarbonDiesel().add(o2.getCarbonDiesel()));
                    o1.setCarbonCrude(o1.getCarbonCrude().add(o2.getCarbonCrude()));
                    o1.setCarbonFuel(o1.getCarbonFuel().add(o2.getCarbonFuel()));
                    o1.setCarbonKerosene(o1.getCarbonKerosene().add(o2.getCarbonKerosene()));
                    o1.setConsumptionGasoline(o1.getConsumptionGasoline().add(o2.getConsumptionGasoline()));
                    o1.setConsumptionDiesel(o1.getConsumptionDiesel().add(o2.getConsumptionDiesel()));
                    o1.setConsumptionCrude(o1.getConsumptionCrude().add(o2.getConsumptionCrude()));
                    o1.setConsumptionFuel(o1.getConsumptionFuel().add(o2.getConsumptionFuel()));
                    o1.setConsumptionKerosene(o1.getConsumptionKerosene().add(o2.getConsumptionKerosene()));
                    return o1;
                })).values().stream().collect(Collectors.toList());
        List<DischargeDataTotalVo> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dataVoList)) {
            dataVoList.forEach(dataVo -> {
                DischargeDataTotalVo dischargeDataTotalVo = new DischargeDataTotalVo();
                dischargeDataTotalVo.setCompanyId(dataVo.getCompanyId());
                dischargeDataTotalVo.setDataYear(dataVo.getDataYear());
                dischargeDataTotalVo.setConsumptionGasoline(dataVo.getConsumptionGasoline());
                dischargeDataTotalVo.setConsumptionDiesel(dataVo.getConsumptionDiesel());
                dischargeDataTotalVo.setConsumptionCrude(dataVo.getConsumptionCrude());
                dischargeDataTotalVo.setConsumptionFuel(dataVo.getConsumptionFuel());
                dischargeDataTotalVo.setConsumptionKerosene(dataVo.getConsumptionKerosene());
                result.add(dischargeDataTotalVo);
            });
        }
        return result;
    }

    private List<DischargeDataTotalVo> getCompanyCoalData(Date dateStart, Date dateEnd, Long companyId) {
        //获取煤碳数据
        List<DischargeDataCoalVo> dischargeDataCoalVoList = dischargeDataCoalService.
                countCompanyData(dateStart, dateEnd, companyId);
        //去除重复
        List<DischargeDataCoalVo> dataVoList = dischargeDataCoalVoList.stream()
                .collect(Collectors.toMap(DischargeDataCoalVo::getCompanyId, a -> a, (o1, o2) -> {
                    o1.setCarbonEmissions(o1.getCarbonEmissions().add(o2.getCarbonEmissions()));
                    o1.setEnergyConsumption(o1.getEnergyConsumption().add(o2.getEnergyConsumption()));
                    return o1;
                })).values().stream().collect(Collectors.toList());
        List<DischargeDataTotalVo> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dataVoList)) {
            dataVoList.forEach(dataVo -> {
                DischargeDataTotalVo dischargeDataTotalVo = new DischargeDataTotalVo();
                dischargeDataTotalVo.setCompanyId(dataVo.getCompanyId());
                dischargeDataTotalVo.setDataYear(dataVo.getDataYear());
                dischargeDataTotalVo.setConsumptionCoal(dataVo.getEnergyConsumption());
                result.add(dischargeDataTotalVo);
            });
        }
        return result;
    }

    private List<DischargeDataTotalVo> getCompareAllData(DischargeDataElectricBo query) {
        List<DischargeDataTotalVo> electricVoList = dischargeDataElectricMapper.countCarbonCompare(query);
        List<DischargeDataTotalVo> coalVoList = dischargeDataCoalMapper.countCarbonCompare(query);
        List<DischargeDataTotalVo> gasVoList = dischargeDataGasMapper.countCarbonCompare(query);
        List<DischargeDataTotalVo> oilVoList = dischargeDataOilMapper.countCarbonCompare(query);
        List<DischargeDataTotalVo> thermalVoList = dischargeDataThermalMapper.countCarbonCompare(query);
        List<DischargeDataTotalVo> waterVoList = dischargeDataWaterMapper.countCarbonCompare(query);
        electricVoList.addAll(coalVoList);
        electricVoList.addAll(gasVoList);
        electricVoList.addAll(oilVoList);
        electricVoList.addAll(thermalVoList);
        electricVoList.addAll(waterVoList);
        return electricVoList;
    }
}
