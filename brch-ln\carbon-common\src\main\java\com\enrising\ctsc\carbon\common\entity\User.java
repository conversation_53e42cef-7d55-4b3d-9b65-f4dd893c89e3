package com.enrising.ctsc.carbon.common.entity;


import cn.zhxu.bs.bean.DbField;
import cn.zhxu.bs.bean.DbIgnore;
import cn.zhxu.bs.bean.SearchBean;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.enrising.ctsc.carbon.common.vo.IdNameVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "rmp.sys_user")
@SearchBean(dataSource = "rmpDs", tables =
        "sys_user su " +
                "left join sys_r_user_organizations user_org on su.id = user_org.user_id " +
                "left join sys_organizations org on org.id = user_org.department_no",
        autoMapTo = "su"
)
public class User extends Model<User>
{
    private static final long serialVersionUID = 1L;

    /** id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 登录账号 */
    private String loginId;
    /** 密码 */
    @DbIgnore
    private String password;
    /** 用户名称 */
    private String userName;
    /** 可选昵称 */
    private String name;
    /** 盐加密 */
    private String salt;
    /** 用户性别（0男 1女 2未知） */
    private String sex;
    /** 用户身份证号 */
    private String idCard;
    /** 统一认证账号(根据系统自身情况选择) */
    private String hrLoginId;
    /** 头像路径 */
    private String avatarUri;
    /** 帐号状态（0正常 1停用） */
    private String status;
    /** "账号锁定状态:  (0:未锁定 1:输入密码错误锁定  2:90天未登录锁定 初始化为0)" */
    private Integer lockStatus;

    /** 最后登陆IP */
    private String loginIp;
    /** 最后登陆时间 */
    private Date loginDate;

    /** 密码最后一次修改时间 */
    private Date pwdUpdateTime;
    /** 部门对象 */
    @DbIgnore
    private Organization organization;
    /** 公司对象 */
    @DbIgnore
    private Organization company;

    @DbIgnore
    private int userType;

    @DbIgnore
    private Set<String> perms;



    /** 岗位组 */
    @DbIgnore
    private Long[] postIds;

    //部门组
    @DbIgnore
    private List<IdNameVo> departments;

    //分公司组
    @DbIgnore
    private List<IdNameVo> companies;

    @DbIgnore
    private String phone;

    /**
     * 部门id
     */
    @DbField("user_org.department_no")
    @JsonProperty("companyBranch")
    private Long departmentNo;

    /**
     * 部门名称
     */
    @DbField("org.org_name")
    private String orgName;

    /**
     * 角色组
     */
    @DbIgnore
    private List<Long> roleIds;

    /**
     * 角色列表
     */
    @DbIgnore
    private List<Role> roleList;

}
