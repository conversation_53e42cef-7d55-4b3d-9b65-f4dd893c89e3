package com.enrising.ctsc.discharge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.discharge.api.bo.DischargeEnergyIndicatorNewBo;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyIndicatorNew;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyIndicatorQuery;
import com.enrising.ctsc.discharge.api.utils.IndicatorTree;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorNewVo;

import java.util.List;

/**
 * 碳排放能源指标表
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-23
 */
public interface DischargeEnergyIndicatorNewService extends IService<DischargeEnergyIndicatorNew> {

	/**
	 * 分页查询
	 *
	 * @param query 参数
	 * @return 列表
	 */
	TableDataInfo<DischargeEnergyIndicatorNewVo> findList(DischargeEnergyIndicatorQuery query);

	/**
	 * 列表查询
	 *
	 * @return 列表
	 */
	List<DischargeEnergyIndicatorNewVo> getIndicatorList();

	/**
	 * 构建树
	 * @param lazy 是否是懒加载
	 * @param parentId 父节点ID
	 * @return
	 */
	List<IndicatorTree> getTree(boolean lazy, Long parentId, String status, String indicatorName);

	/**
	 * 详情
	 *
	 * @param query 参数
	 * @return 详情
	 */
	DischargeEnergyIndicatorNewVo detail(DischargeEnergyIndicatorQuery query);

	/**
	 * 新增
	 *
	 * @param bo 参数
	 */
	void add(DischargeEnergyIndicatorNewBo bo);

	/**
	 * 修改
	 *
	 * @param bo 参数
	 */
	void edit(DischargeEnergyIndicatorNewBo bo);

	/**
	 * 删除
	 *
	 * @param id 主键id
	 */
	void del(Long id);
}
