package com.enrising.ctsc.assess.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.assess.api.bo.AssessTaskReportAttachmentBo;
import com.enrising.ctsc.assess.api.entity.AssessTaskReportAttachment;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.assess.mapper.AssessTaskReportAttachmentMapper;
import com.enrising.ctsc.assess.api.query.AssessTaskReportAttachmentQuery;
import com.enrising.ctsc.assess.service.AssessTaskReportAttachmentService;
import com.enrising.ctsc.assess.api.vo.AssessTaskReportAttachmentVo;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 考核任务上报附件
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Service
@AllArgsConstructor
public class AssessTaskReportAttachmentServiceImpl extends ServiceImpl<AssessTaskReportAttachmentMapper, AssessTaskReportAttachment> implements AssessTaskReportAttachmentService {

	@Override
	public TableDataInfo<AssessTaskReportAttachmentVo> findList(Page<AssessTaskReportAttachmentVo> page, AssessTaskReportAttachmentQuery query) {
		QueryWrapper<AssessTaskReportAttachmentQuery> wrapper = this.getWrapper(query);
		IPage<AssessTaskReportAttachmentVo> resultPage = baseMapper.findList(page, wrapper);
		return TableDataInfo.build(resultPage);
	}

	@Override
	public AssessTaskReportAttachmentVo detail(AssessTaskReportAttachmentQuery query) {
		if (ObjectUtil.allFieldIsNull(query)) {
			throw new BusinessException("查询参数不能为空");
		}
		QueryWrapper<AssessTaskReportAttachmentQuery> wrapper = this.getWrapper(query);
		return baseMapper.detail(wrapper);
	}

	private QueryWrapper<AssessTaskReportAttachmentQuery> getWrapper(AssessTaskReportAttachmentQuery query) {
		QueryWrapper<AssessTaskReportAttachmentQuery> wrapper = new QueryWrapper<>();
		wrapper.eq(query.getId() != null, "t.id", query.getId());
		return wrapper;
	}

	@Override
	public void add(AssessTaskReportAttachmentBo bo) {
		AssessTaskReportAttachment entity = new AssessTaskReportAttachment();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.insert(entity);
	}

	@Override
	public void edit(AssessTaskReportAttachmentBo bo) {
		AssessTaskReportAttachment entity = new AssessTaskReportAttachment();
		BeanUtils.copyProperties(bo, entity);
		baseMapper.updateById(entity);
	}

	@Override
	public void del(Long id) {
		baseMapper.deleteById(id);
	}

}
