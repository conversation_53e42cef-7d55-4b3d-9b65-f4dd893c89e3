package com.enrising.ctsc.discharge.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
* 碳排放能源数据提醒状态
*
* <AUTHOR>
* @since 1.0.0 2023-1-3
*/
@Getter
@AllArgsConstructor
public enum DischargeDataEnergyReportFlag {
	/***/
	FLAG_REPORTED("2", "已上报"),
	FLAG_UNREPORTED("3", "未上报"),
	FLAG_APPLY_RETURN("4", "申请退回"),

	FLAG_REPORT("1", "上报"),
	FLAG_UPDATE("2", "修改"),
	;


	private final String value;
	private final String name;

}
