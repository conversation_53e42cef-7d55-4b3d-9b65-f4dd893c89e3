package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 能源单价表
 * @Auther qinxinmin
 * @Date 2024/09/17
 */
@Data
@TableName("energy_calculate_price")
public class EnergyCalculatePrice {
    /**
     * 主键id,采用雪花id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 填报时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date reportTime;

    /**
     * 电价（元/千瓦时）
     */
    private BigDecimal electricityPrice;

    /**
     * 92号汽油价格（元/升）
     */
    private BigDecimal gasolinePriceTwo;

    /**
     * 95号汽油价格（元/升）
     */
    private BigDecimal gasolinePriceFive;

    /**
     * 98号汽油价格（元/升）
     */
    private BigDecimal gasolinePriceEight;

    /**
     * 柴油价格（元/升）
     */
    private BigDecimal dieselPrice;

    /**
     * 热力价格（元/平方米）
     */
    private BigDecimal thermalPrice;

    /**
     * 煤炭价格（元/吨）
     */
    private BigDecimal coalPrice;

    /**
     * 水价格（元/吨）
     */
    private BigDecimal waterPrice;

    /**
     * 创建者id
     */
    private Long createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    /**
     * 更新者id
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date updateTime;

    /**
     * 删除标志：0-正常；1-删除
     */
    @TableLogic
    private String delFlag;
}
