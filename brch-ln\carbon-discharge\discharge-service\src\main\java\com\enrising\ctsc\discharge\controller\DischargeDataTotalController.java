package com.enrising.ctsc.discharge.controller;

import com.enrising.ctsc.discharge.api.bo.DataReportBo;
import com.enrising.ctsc.discharge.api.bo.DischargeDataOpenBo;
import com.enrising.ctsc.discharge.api.bo.DischargeDataTotalBo;
import com.enrising.ctsc.discharge.api.bo.DischargeEmissionBo;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.discharge.api.vo.*;
import com.enrising.ctsc.discharge.service.DischargeDataTotalService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 碳排放总量数据接口
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-05
 */
@Slf4j
@RestController
@RequestMapping("/discharge/total")
@AllArgsConstructor
public class DischargeDataTotalController {

    private final DischargeDataTotalService dischargeDataTotalService;
//    private final RemoteBusinessService remoteBusinessService;


    @PostMapping("/getDataList")
    public R<List<DischargeDataTotalVo>> getDataList(@RequestBody DischargeDataTotalBo dischargeDataTotalBo) {
        return R.success(dischargeDataTotalService.getDataList(dischargeDataTotalBo));
    }

    @PostMapping("/getCarbonData")
    public R<DischargeDataOpenVo> getCarbonData(@Valid @RequestBody DischargeDataOpenBo bo) {
        return R.success(dischargeDataTotalService.getCarbonData(bo));
    }

    @PostMapping("/getDataCompareList")
    public R<List<DischargeDataTotalCompareVo>> getDataCompareList(@RequestBody DischargeDataTotalBo dischargeDataTotalBo) {
        return R.success(dischargeDataTotalService.getDataCompareList(dischargeDataTotalBo));
    }


    @GetMapping("/getDataQueryList")
    public R<List<DischargeDataTotalQueryVo>> getDataQueryList(Integer dataYear, String dataType, String timeType) {
        return R.success(dischargeDataTotalService.getDataQueryList(dataYear, dataType, timeType));
    }

    @PostMapping("/getTwoYearsDataList")
    public R getTwoYearsDataList(@RequestBody DischargeDataTotalBo dischargeDataTotalBo) {
        return R.success(dischargeDataTotalService.getTwoYearsDataList(dischargeDataTotalBo));
    }

    @PostMapping(value = "/emissionTrendList")
    public R<List<DischargeEmissionTrendVo>> emissionTrendList(@RequestBody DischargeEmissionBo bo) {
        return R.success(dischargeDataTotalService.emissionTrendList(bo));
    }

    @PostMapping(value = "/electricityAnalysis")
    public R<DischargeElectricityAnalysisVo> electricityAnalysis(@RequestBody DischargeEmissionBo bo) {
        return R.success(dischargeDataTotalService.electricityAnalysis(bo));
    }

    @PostMapping(value = "/compareAndStructuralList")
    public R<DischargePortraitVo> carbonDataCompareList(@RequestBody DischargeEmissionBo bo) {
        return R.success(dischargeDataTotalService.carbonDataCompareList(bo));
    }

//    @GetMapping(value = "/carbonCompanyRankList")
//    public R<Dict> carbonCompanyRankList() {
//        int year = DateUtil.year(new Date());
//        String nowYearStr = String.valueOf(year);
//        String lastYearStr = String.valueOf(year - 1);
//        Dict res = Dict.create();
//
//        // 使用 Comparator 对碳排放量进行排序
//        Comparator<CarbonRankingVo> comparator = (e1, e2) -> Double.compare(e2.getCarbonEmissions().doubleValue(), e1.getCarbonEmissions().doubleValue());
//
//        List<CarbonRankingVo> thisYear = remoteBusinessService.getCarbonRanking(nowYearStr).getData();
//        thisYear.sort(comparator);
//        List<CarbonRankingVo> lastYear = remoteBusinessService.getCarbonRanking(lastYearStr).getData();
//        // 去年的根据今年的名称排序
//        List<CarbonRankingVo> lastYearListNew = Lists.newArrayList();
//        for (CarbonRankingVo rankingVo : thisYear) {
//            CarbonRankingVo lastYearVo = lastYear.stream().filter(it -> it.getDeptName().equals(rankingVo.getDeptName())).findFirst().orElse(new CarbonRankingVo());
//            lastYearListNew.add(lastYearVo);
//        }
//
//        // 今年
//        List<Dict> thisYearList = getRankList(thisYear);
//        // 去年
//        List<Dict> lastYearList = getRankList(lastYearListNew);
//        res.set("thisYear", thisYearList.stream().limit(10).collect(Collectors.toList()));
//        res.set("lastYear", lastYearList.stream().limit(10).collect(Collectors.toList()));
//        return R.success(res);
//    }


//    private List<Dict> getRankList(List<CarbonRankingVo> yearList) {
//        List<Dict> list = Lists.newArrayList();
//        for (int i = 0; i < yearList.size(); i++) {
//            CarbonRankingVo it = yearList.get(i);
//            Dict dict = Dict.create();
//            dict.set("value", it.getCarbonEmissions());
//            dict.set("num", i + 1);
//            dict.set("name", it.getDeptName());
//            list.add(dict);
//        }
//        return list;
//    }

//    @GetMapping(value = "/carbonCompanyRank")
//    public R<Integer> carbonCompanyRank() {
//        // 获取当前年份
//        String nowYear = String.valueOf(DateUtil.year(new Date()));
//        // 获取当前登录用户的部门id
//        Long deptId = Objects.requireNonNull(SecurityUtils.getUser()).getDeptId();
//        // 创建碳排放量比较器
//        Comparator<CarbonRankingVo> comparator = (e1, e2) -> Double.compare(e2.getCarbonEmissions().doubleValue(), e1.getCarbonEmissions().doubleValue());
//        // 初始化排名为0
//        int num = 0;
//        // 获取今年的碳排放量排名列表
//        List<CarbonRankingVo> list = remoteBusinessService.getCarbonRanking(nowYear).getData();
//        // 对排名列表按照碳排放量进行排序
//        list.sort(comparator);
//        // 查找当前用户所在部门的排名
//        for (int i = 0; i < list.size(); i++) {
//            CarbonRankingVo rankingVo = list.get(i);
//            if (rankingVo.getDeptId().equals(deptId)) {
//                num = i + 1;
//                break;
//            }
//        }
//        // 返回当前用户所在部门的排名
//        return R.success(num);
//    }

    @GetMapping(value = "/mapCompanyData")
    public R<List<DischargeEmissionTrendVo>> mapCompanyData() {
        return R.success(dischargeDataTotalService.mapCompanyData());
    }

    @PostMapping("/getCarbonAssessDataList")
    public R<List<DischargeDataTotalVo>> getCarbonAssessDataList(@RequestBody DischargeDataTotalBo dischargeDataTotalBo) {
        return R.success(dischargeDataTotalService.getCarbonAssessDataList(dischargeDataTotalBo));
    }

    @GetMapping("/getCarbonOverview")
    public R getCarbonOverview() {
        return R.success(dischargeDataTotalService.getCarbonOverview());
    }

    @GetMapping("/getCarbonCompare")
    public R getCarbonCompare(String timeType) {
        return R.success(dischargeDataTotalService.getCarbonCompare(timeType));
    }

    @GetMapping("/getEnergyConsumptionOverview")
    public R getEnergyConsumptionOverview() {
        return R.success(dischargeDataTotalService.getEnergyConsumptionOverview());
    }

    @GetMapping("/getEnergyConsumptionCompare")
    public R getEnergyConsumptionCompare() {
        return R.success(dischargeDataTotalService.getEnergyConsumptionCompare());
    }

    /**
     * 双碳云脑--用能分析--用能趋势
     * @param energyType 能源类型：3-电,5-汽油,6-柴油,7-原油,8-燃料油,9-煤油,11-天然气,12-液化石油气,13-水,14-热力,15-煤炭
     * @return 查询数据
     */
    @GetMapping("/getEnergyTrend")
    public R getEnergyTrend(@RequestParam(value = "energyType", required = true, defaultValue = "3") String energyType) {
        return R.success(dischargeDataTotalService.getEnergyTrend(energyType));
    }

    @GetMapping("/getCompanyConsumption")
    public R getCompanyConsumption(String timeType) {
        return R.success(dischargeDataTotalService.getCompanyConsumption(timeType));
    }

    @PostMapping("/getDataReportList")
    public R<List<DataReportVo>> getDataReportList(@RequestBody DataReportBo dataReportBo) {
        return R.success(dischargeDataTotalService.getDataReportList(dataReportBo));
    }

    @PostMapping("/exportDataReport")
    public void downloadExcel(HttpServletRequest request, HttpServletResponse response,
                              @RequestBody DataReportBo dataReportBo) {
        dischargeDataTotalService.exportDataReport(request, response, dataReportBo);
    }

    /**
     * 碳排放--碳排放总量数据--获取分公司列表
     * @return 分公司列表
     */
    @GetMapping("/getCompanyList")
    public R getCompanyList() {
        return R.success(dischargeDataTotalService.getCompanyList());
    }
}