package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 碳排放能源指标表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */

@Data
@TableName("discharge_energy_indicator")
public class DischargeEnergyIndicator extends Model<DischargeEnergyIndicator> {

	/**
	 * 主键id,采用雪花id
	 */
		@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 创建者id
	 */
		private Long createBy;

	/**
	 * 创建时间
	 */
		private Date createTime;

	/**
	 * 更新者id
	 */
		private Long updateBy;

	/**
	 * 更新时间
	 */
		private Date updateTime;

	/**
	 * 能源指标类型名称
	 */
		private String indicatorName;

	/**
	 * 单位id
	 */
		private String unit;

	/**
	 * 状态，1-启用，2-禁用
	 */
		private String status;

	/**
	 * 父指标id
	 */
		private Long parentId;

	/**
	 * 排序值
	 */
		private Integer sort;

	/**
	 * 删除标志：0-正常；1-删除
	 */
	@TableLogic
		private String delFlag;

}
