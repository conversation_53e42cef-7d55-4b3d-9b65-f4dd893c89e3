package com.enrising.ctsc.assess.api.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AssessTemplateTargetObjectVo {

    /**
     * id
     */
    private Long id;

    /**
     * 所属模板指标id
     */
    private Long templateTargetId;

    /**
     * 指标id
     */
    private Long targetId;

    /**
     * 考核模板对象表 id
     */
    private Long templateObjectId;

    /**
     * 二级指标id
     */
    private Long secondaryTargetId;

    /**
     * 模板id
     */
    private Long templateId;


    /**
     * 指标类别，1-减分，2-加分
     */
    private String targetCategory;

    /**
     * 指标名称
     */
    private String targetName;

    /**
     * 指标类型
     */
    private String targetType;

    /**
     * 指标分值
     */
    private String score;

    /**
     * 指标分值总分
     */
    private BigDecimal scoreTotal;

    /**
     * 考核周期，1-年，2-季度，3-月
     */
    private String assessPeriod;

    /**
     * 考核方式
     */
    private String assessMethod;

    /**
     * 考核评分
     */
    private double assessScore;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 公司名称
     */
    private String name;

    /**
     * 部门名称
     */
    private String dept;

    /**
     * 考核对象，1-公司，2-指定部门
     */
    private String objectType;

    /**
     * 是否上报 1-无需上报 2-已上报 3-未上报
     */
    private String isReport;

    /**
     * 指标公式
     */
    private String formula;

}
