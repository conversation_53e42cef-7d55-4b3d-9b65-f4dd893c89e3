<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeEnergyIndicatorNewMapper">
	<resultMap id="BaseResultMap" type="com.enrising.ctsc.discharge.api.entity.DischargeEnergyIndicatorNew">
		<result column="id" property="id" />
		<result column="create_by" property="createBy" />
		<result column="create_time" property="createTime" />
		<result column="update_by" property="updateBy" />
		<result column="update_time" property="updateTime" />
		<result column="indicator_code" property="indicatorCode" />
		<result column="indicator_name" property="indicatorName" />
		<result column="energy_type_id" property="energyTypeId" />
		<result column="group_input_type" property="groupInputType" />
		<result column="stock_input_type" property="stockInputType" />
		<result column="large_input_type" property="largeInputType" />
		<result column="medium_input_type" property="mediumInputType" />
		<result column="mobile_input_type" property="mobileInputType" />
		<result column="status" property="status" />
		<result column="parent_id" property="parentId" />
		<result column="sort" property="sort" />
		<result column="del_flag" property="delFlag" />
	</resultMap>

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
			t.indicator_code,
            t.indicator_name,
            t.energy_type_id,
			t.group_input_type,
			t.stock_input_type,
			t.large_input_type,
			t.medium_input_type,
			t.mobile_input_type,
            t.status,
            t.parent_id,
            t.sort,
            t.del_flag
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorNewVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_energy_indicator_new t
        ${ew.customSqlSegment}
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorNewVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_energy_indicator_new t
        ${ew.customSqlSegment}
        limit 1
    </select>

	<!-- 查询模板指标列表 -->
	<select id="getIndicatorList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorNewVo">
		 SELECT
		 	dei.id,
			dei.create_by,
			dei.create_time,
			dei.update_by,
			dei.update_time,
			dei.indicator_code,
			dei.indicator_name,
			dei.energy_type_id,
			dei.group_input_type,
			dei.stock_input_type,
			dei.large_input_type,
			dei.medium_input_type,
			dei.mobile_input_type,
			dei.status,
			dei.parent_id,
			dei.sort,
			dei.del_flag,
			det.second_name as energy_type_name
		 FROM discharge_energy_indicator_new dei
			 LEFT JOIN discharge_energy_type det ON dei.energy_type_id = det.id
		WHERE dei.del_flag='0'
		  and dei.status='1'
		order by dei.indicator_code asc
	</select>
	<!-- 查询模板指标列表 -->
	<select id="getIndicatorTreeList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeEnergyIndicatorNewVo">
		SELECT
			dei.id,
			dei.create_by,
			dei.create_time,
			dei.update_by,
			dei.update_time,
			dei.indicator_code,
			dei.indicator_name,
			dei.energy_type_id,
			dei.group_input_type,
			dei.stock_input_type,
			dei.large_input_type,
			dei.medium_input_type,
			dei.mobile_input_type,
			dei.status,
			dei.parent_id,
			deit.indicator_code as parent_code,
			deit.indicator_name as parent_name,
			dei.sort,
			dei.del_flag,
			det.second_name as energy_type_name
		FROM discharge_energy_indicator_new dei
		LEFT JOIN discharge_energy_type det ON dei.energy_type_id = det.id
		LEFT JOIN discharge_energy_indicator_new deit ON dei.parent_id = deit.id
		WHERE dei.del_flag='0'
		<choose>
			<when test="parentId == null">
				AND dei.parent_id is not NULL
			</when>
			<otherwise>
				AND dei.parent_id = #{parentId}
			</otherwise>
		</choose>
		<if test="status != null and status != ''"> AND dei.status = #{status}</if>
		<if test="indicatorName != null and indicatorName != ''"> AND dei.indicator_name like concat('%', #{indicatorName}, '%')</if>
		order by dei.indicator_code asc
	</select>
</mapper>