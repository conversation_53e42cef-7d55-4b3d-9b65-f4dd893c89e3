package com.enrising.ctsc.assess.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 考核任务上报表 打分
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2022-12-21
 */
@Data
public class AssessTaskReportScoreVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 上报id
     */
    private Long taskReportId;

    /**
     * 考核模板名称
     */
    private String templateName;

    /**
     * 考核指标
     */
    private String targetName;

    /**
     * 考核分值
     */
    private Double targetSecondaryScore;

    /**
     * 指标类别
     */
    private String targetCategory;

    /**
     * 考核周期
     */
    private String assessPeriod;

    /**
     * 上报公司id
     */
    private Long companyId;

    /**
     * 上报部门
     */
    private String companyName;

    /**
     * 上报部门id
     */
    private Long deptId;

    /**
     * 按规则得分
     */
    private Double ruleScore;

    /**
     * 考评人员
     */
    private Long assesser;

    /**
     * 考评人员姓名
     */
    private String assesserName;

    /**
     * 上报时间
     */
    private Date createTime;

    /**
     * 考评时间
     */
    private Date assessTime;

    /**
     * 上报时间
     */
    private Date reportDate;
}
