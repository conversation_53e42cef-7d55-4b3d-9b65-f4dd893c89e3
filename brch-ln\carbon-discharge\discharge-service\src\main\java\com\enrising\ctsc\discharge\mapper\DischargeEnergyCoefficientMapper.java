package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyCoefficient;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyCoefficientQuery;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyFactorOpenBo;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyCoefficientVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 碳排放能源转换系数表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Mapper
public interface DischargeEnergyCoefficientMapper extends BaseMapper<DischargeEnergyCoefficient> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
	IPage<DischargeEnergyCoefficientVo> findList(Page<DischargeEnergyCoefficientVo> page, @Param(Constants.WRAPPER) Wrapper<DischargeEnergyCoefficientQuery> wrapper);

	/**
	 * 列表查询
	 *
	 * @param wrapper 条件
	 * @return 列表
	 */
	List<DischargeEnergyCoefficientVo> findList(@Param(Constants.WRAPPER) Wrapper<DischargeEnergyCoefficientQuery> wrapper);

	/**
	 * 根据报告时间查询列表
	 *
	 * @param reportTime 报告时间
	 * @return 列表
	 */
	List<DischargeEnergyCoefficient> findListByTime(@Param("reportTime") Date reportTime);

	/**
	 * 详情查询
	 *
	 * @param wrapper 条件
	 * @return 结果
	 */
	DischargeEnergyCoefficientVo detail(@Param(Constants.WRAPPER) Wrapper<DischargeEnergyCoefficientQuery> wrapper);

	DischargeEnergyCoefficientVo getGainFactor(@Param("bo") DischargeEnergyFactorOpenBo bo);
}