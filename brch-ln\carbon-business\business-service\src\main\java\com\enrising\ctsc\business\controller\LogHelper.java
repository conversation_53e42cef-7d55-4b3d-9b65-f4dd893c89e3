package com.enrising.ctsc.business.controller;

import cn.hutool.core.lang.Dict;
import cn.hutool.json.JSONUtil;
import com.sccl.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 统一日志工具类
 * <p>
 * KeyValue._SYS=CARBOND
 * KeyValue._CENTER=CARBOND
 * KeyValue._SAPP=business-service
 * KeyValue._SGRP=P
 * KeyValue._ITG=A
 * KeyValue.APP_PATH=/app/
 * KeyValue.APP_NAME=business-service
 * KeyValue.JAVA_OPTIONS=-Xms1048m -Xmx1048m -Xmn256m -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/heap.hprof
 * KeyValue.USE_HLOGAGENT=true
 * KeyValue.USE_PINPOINT=false
 * KeyValue.ppId=QX002936
 * KeyValue.KEY_STORE_PASSSWORD=px51sP2vSFyo
 * KeyValue.TRUST_STORE_PASSSWORD=px51sP2vSFyo
 * KeyValue.SPRING_PROFILES_ACTIVE=prod
 */
@Slf4j
@Component
public class LogHelper {

	/**
	 * 上传登录日志
	 * @return	登录日志
	 */
	public String writeLoginLog() {
		System.out.println(1);
		Date nowDate = new Date();
		System.out.println(2);
		Dict logObj = Dict.create();
		System.out.println(3);
		// 系统CMDB编码
		logObj.set("sysCode", "S000AN1623206594");
		System.out.println(4);
		// 流水号
		logObj.set("serialNbr", getSerialNbr(nowDate));
		System.out.println(5);
		// 终端IP
		logObj.set("remoteIp", "*************");
		// 登录手机号
		logObj.set("account", "***********");
		// 登录账号
		logObj.set("userCode", "***********");
		// 登录时间
		logObj.set("loginTime", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", nowDate));
		System.out.println(6);
		// 登录状态 0:失败、1:成功 展示状态
		logObj.set("loginStatus", "1");
		// 登出时间
		logObj.set("logoutTime", null);
		// 终端信息
		logObj.set("terminalInfo", null);
		//认证方式
		logObj.set("authType", "11");
		System.out.println(7);
		String logStr = JSONUtil.toJsonStr(JSONUtil.parseObj(logObj));
		log.info("生成登录日志:{}", logStr);
		System.out.println(8);
		System.out.println(logStr);
		return logStr;
	}

	/**
	 * 获取流水号
	 *
	 * @return 流水号
	 */
	private String getSerialNbr(Date nowDate) {
		// 将时间戳转换为字符串
		String timestampString = Long.toString(nowDate.getTime());
		return DateUtils.parseDateToStr("yyyyMMdd", nowDate) + timestampString.substring(Math.max(0, timestampString.length() - 10));
	}
}
