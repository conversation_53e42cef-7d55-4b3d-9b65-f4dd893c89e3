package com.enrising.ctsc.assess.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.enrising.ctsc.assess.api.bo.AssessReportBo;
import com.enrising.ctsc.assess.api.entity.AssessReport;
import com.enrising.ctsc.assess.api.query.AssessReportQuery;
import com.enrising.ctsc.carbon.common.entity.CarbonAttachment;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.assess.api.vo.AssessReportVo;
import com.enrising.ctsc.assess.api.vo.AssessVisualVo;

import javax.servlet.http.HttpServletResponse;

/**
 * 考核报告
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
public interface AssessReportService extends IService<AssessReport> {

    /**
    * 分页查询
    * @param page  分页
    * @param query	参数
    * @return	列表
    */
    TableDataInfo<AssessReportVo> findList(Page<AssessReportVo> page, AssessReportQuery query);
	/**
    * 详情
    *
    * @param query 参数
    * @return 详情
    */
    AssessReportVo detail(AssessReportQuery query);

    /**
    * 新增
    * @param bo 参数
    */
    void add(AssessReportBo bo);

    /**
    * 修改
    * @param bo 参数
    */
    void edit(AssessReportBo bo);

    /**
    * 删除
    * @param id 主键id
    */
    void del(Long id);

	/**
	 * 下载报告
	 * @param bo 主键id
	 */
	void download(HttpServletResponse response, AssessReportBo bo);

	/**
	 * 预览
	 * @param bo
	 */
	String preview(HttpServletResponse response, AssessReportBo bo);

	/**
	 * 预览
	 * @param bo
	 */
	AssessVisualVo visual(AssessReportBo bo);

    boolean saveCarbonAttachment(CarbonAttachment attachment);

    CarbonAttachment getAttachmentById(Long id);
}
