<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeDataOilMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.company_id,
            t.report_time,
            t.gasoline,
            t.diesel,
            t.crude,
            t.fuel,
            t.kerosene,
            t.del_flag
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataOilVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_data_oil t
        ${ew.customSqlSegment}
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataOilVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_data_oil t
        ${ew.customSqlSegment}
        limit 1
    </select>
	<select id="getCompanyCarbonList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataOilVo">
		SELECT COALESCE(SUM(gasoline) * (
		  SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '7'
			AND del_flag = '0'
			and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 )  AS carbonGasoline,
		COALESCE(SUM(diesel) * (
		  SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '8'
			AND del_flag = '0'
			and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 )  AS carbonDiesel,
		COALESCE(SUM( crude ) * (
		  SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '10'
			AND del_flag = '0'
			and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 )  AS carbonCrude,
		COALESCE(SUM( fuel ) * (
		  SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '11'
			AND del_flag = '0'
			and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 )  AS carbonFuel,
	   COALESCE(SUM(kerosene ) * (
		   SELECT
			   factor
		   FROM
			   discharge_energy_factor
		   WHERE
			   energy_type_id = '9'
		   AND del_flag = '0'
		   and DATE_FORMAT(validity_start, '%Y') &lt;= #{bo.year}  ORDER BY validity_start DESC LIMIT 1
		   ), 0 )  AS carbonKerosene,
			   DATE_FORMAT( report_time, '%m' )  as dataMonth
		FROM
		discharge_data_oil
		WHERE
			DATE_FORMAT( report_time, '%Y' ) = #{bo.year}
		and company_id = #{bo.companyId}
		and del_flag = '0'
		GROUP BY report_time
		ORDER BY report_time asc
	</select>

	<select id="countCompanyData" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataOilVo">
		SELECT company_id,
			report_time,
			sum(gasoline) as gasoline,
			sum(diesel) as diesel,
			sum(crude) as crude,
			sum(fuel) as fuel,
			sum(kerosene) as kerosene,
			CAST(COALESCE(SUM( gasoline )  * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '7'
			AND validity_start <![CDATA[<=]]> report_time
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			)  / 1000, 0 ) as DECIMAL(18,4)) AS carbonGasoline,
			CAST(COALESCE(SUM( diesel )  * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '8'
			AND validity_start <![CDATA[<=]]> report_time
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) / 1000, 0 ) as DECIMAL(18,4)) AS carbonDiesel,
			CAST(COALESCE(SUM( crude )  * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '10'
			AND validity_start <![CDATA[<=]]> report_time
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) / 1000, 0 ) as DECIMAL(18,4)) AS carbonCrude,
			CAST(COALESCE(SUM( fuel )  * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '11'
			AND validity_start <![CDATA[<=]]> report_time
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) / 1000, 0 ) as DECIMAL(18,4)) AS carbonFuel,
			CAST(COALESCE(SUM( kerosene )  * (
			SELECT
			factor
			FROM
			discharge_energy_factor
			WHERE
			energy_type_id = '9'
			AND validity_start <![CDATA[<=]]> report_time
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) / 1000, 0) as DECIMAL(18,4)) AS carbonKerosene,
			CAST(COALESCE(SUM( gasoline ) * (
			SELECT
			coefficient
			FROM
			discharge_energy_coefficient
			WHERE
			energy_type_id = '7'
			AND validity_start <![CDATA[<=]]> report_time
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_gasoline,
			CAST(COALESCE(SUM( diesel ) * (
			SELECT
			coefficient
			FROM
			discharge_energy_coefficient
			WHERE
			energy_type_id = '8'
			AND validity_start <![CDATA[<=]]> report_time
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_diesel,
			CAST(COALESCE(SUM( crude ) * (
			SELECT
			coefficient
			FROM
			discharge_energy_coefficient
			WHERE
			energy_type_id = '10'
			AND validity_start <![CDATA[<=]]> report_time
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_crude,
			CAST(COALESCE(SUM( fuel ) * (
			SELECT
			coefficient
			FROM
			discharge_energy_coefficient
			WHERE
			energy_type_id = '11'
			AND validity_start <![CDATA[<=]]> report_time
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_fuel,
		    CAST(COALESCE(SUM(kerosene ) * (
			SELECT
			coefficient
			FROM
			discharge_energy_coefficient
			WHERE
			energy_type_id = '9'
			AND validity_start <![CDATA[<=]]> report_time
			AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
			) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_kerosene,
		DATE_FORMAT( report_time, '%m月' )  as dataMonth,
		DATE_FORMAT( report_time, '%Y' )  as dataYear
			FROM
			discharge_data_oil
		WHERE
			del_flag = '0'
			<if test="queryBo.queryStartTime != null and queryBo.queryEndTime!= null">
				AND report_time BETWEEN #{queryBo.queryStartTime} AND #{queryBo.queryEndTime}
			</if>
			<if test="queryBo.companyId != null and queryBo.companyId != 0">
				AND company_id = #{queryBo.companyId}
			</if>
		GROUP BY report_time, company_id
		ORDER BY report_time asc
		<if test="queryBo.size != null">
			LIMIT #{queryBo.size}
		</if>
		<if test="queryBo.offset != null">
			OFFSET #{queryBo.offset}
		</if>
	</select>
	<select id="getCompanyDataList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataOilVo">
		SELECT id, company_id,
		report_time,
		gasoline,
		diesel,
		crude,
		fuel,
		kerosene,
		CAST(COALESCE
		( gasoline * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '7'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS carbonGasoline,
		CAST(COALESCE
		( diesel * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '8'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS carbonDiesel,
		CAST(COALESCE( crude * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '10'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS carbonCrude,
		CAST(COALESCE( fuel * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '11'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS carbonFuel,
		CAST(COALESCE
		( kerosene * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '9'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS carbonKerosene,
		CAST(COALESCE
		( gasoline * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '7'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_gasoline,
		CAST(COALESCE
		( diesel * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '8'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_diesel,
		CAST(COALESCE
		( crude * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '10'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_crude,
		CAST(COALESCE
		( fuel * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '11'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_fuel,
		CAST(COALESCE
		( kerosene * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '9'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_kerosene,
		DATE_FORMAT( report_time, '%m月' )  as dataMonth,
		DATE_FORMAT( report_time, '%Y' )  as dataYear
		FROM
		discharge_data_oil
		WHERE
			del_flag = '0'
			<if test="queryBo.queryStartTime != null and queryBo.queryEndTime!= null">
				AND report_time BETWEEN #{queryBo.queryStartTime} AND #{queryBo.queryEndTime}
			</if>
			<if test="queryBo.companyId != null">
				AND company_id = #{queryBo.companyId}
			</if>
		ORDER BY report_time asc
		<if test="queryBo.size != null">
			LIMIT #{queryBo.size}
		</if>
		<if test="queryBo.offset != null">
			OFFSET #{queryBo.offset}
		</if>
	</select>
	<select id="countCarbonCompare" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataTotalVo">
		SELECT
		z.text,
		z.code AS data_month,
		COALESCE(a.report_time, cast(CONCAT(z.code, '-01 00:00:00') as datetime)) as report_time,
		COALESCE(a.data_year, substr(z.code, 1, 4)) as data_year,
		COALESCE(a.gasoline, 0) as gasoline,
		COALESCE(a.diesel, 0) as diesel,
		COALESCE(a.crude, 0) as crude,
		COALESCE(a.fuel, 0) as fuel,
		COALESCE(a.kerosene, 0) as kerosene,
		COALESCE(a.carbon_gasoline, 0) as carbon_gasoline,
		COALESCE(a.carbon_diesel, 0) as carbon_diesel,
		COALESCE(a.carbon_crude, 0) as carbon_crude,
		COALESCE(a.carbon_fuel, 0) as carbon_fuel,
		COALESCE(a.carbon_kerosene, 0) as carbon_kerosene,
		COALESCE(a.consumption_gasoline, 0) as consumption_gasoline,
		COALESCE(a.consumption_diesel, 0) as consumption_diesel,
		COALESCE(a.consumption_crude, 0) as consumption_crude,
		COALESCE(a.consumption_fuel, 0) as consumption_fuel,
		COALESCE(a.consumption_kerosene, 0) as consumption_kerosene
		FROM (SELECT
		tab."month" || '月' as text,
		tab."month" as code
		FROM (WITH RECURSIVE T ( n ) AS (SELECT
		DATE( cast(#{queryBo.queryStartTime} as DATE))
		UNION ALL
		SELECT n + 1
		FROM T
		WHERE n <![CDATA[ < ]]> DATE ( cast(#{queryBo.queryEndTime} as DATE) ) )
		SELECT
		DATE_FORMAT( n, '%Y-%m' ) AS "month"
		FROM T
		GROUP BY "month"
		ORDER BY "month" desc) tab) z
		LEFT JOIN (
		SELECT
		report_time,
		COALESCE(SUM(gasoline ), 0) as gasoline,
		COALESCE(SUM(diesel ), 0) as diesel,
		COALESCE(SUM(crude ), 0) as crude,
		COALESCE(SUM(fuel ), 0) as fuel,
		COALESCE(SUM(kerosene ), 0) as kerosene,
		CAST(COALESCE(sum(gasoline) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '7'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS carbon_gasoline,
		CAST(COALESCE(sum(diesel) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '8'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS carbon_diesel,
		CAST(COALESCE(sum(crude) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '10'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS carbon_crude,
		CAST(COALESCE(sum(fuel) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '11'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS carbon_fuel,
		CAST(COALESCE(sum(kerosene) * (
		SELECT
		factor
		FROM
		discharge_energy_factor
		WHERE
		energy_type_id = '9'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS carbon_kerosene,
		CAST(COALESCE(sum(gasoline) * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '7'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_gasoline,
		CAST(COALESCE(sum(diesel) * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '8'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_diesel,
		CAST(COALESCE(sum(crude) * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '10'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_crude,
		CAST(COALESCE(sum(fuel) * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '11'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_fuel,
		CAST(COALESCE(sum(kerosene) * (
		SELECT
		coefficient
		FROM
		discharge_energy_coefficient
		WHERE
		energy_type_id = '9'
		AND validity_start <![CDATA[<=]]> report_time
		AND del_flag = '0' ORDER BY validity_start DESC LIMIT 1
		) / 1000, 0 ) as DECIMAL(18,4)) AS consumption_kerosene,
		DATE_FORMAT( report_time, '%Y-%m' )  as data_month,
		DATE_FORMAT( report_time, '%Y' )  as data_year
		FROM
		discharge_data_oil
		WHERE
		del_flag = '0'
		<if test="queryBo.companyId != 0 and queryBo.companyId != null">
			AND company_id = #{queryBo.companyId}
		</if>
		<if test="queryBo.queryStartTime != null and queryBo.queryEndTime!= null">
			AND report_time BETWEEN #{queryBo.queryStartTime} AND #{queryBo.queryEndTime}
		</if>
		GROUP BY report_time
		ORDER BY report_time asc) a on a.data_month=z.code
		ORDER BY z.code asc
	</select>
</mapper>