package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.enrising.ctsc.discharge.api.vo.DataReportVo;
import com.enrising.ctsc.discharge.api.vo.SysDeptVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 生产业务数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-03-23
 */
@Mapper
public interface DischargeDataTotalMapper extends BaseMapper<DataReportVo> {

	List<SysDeptVO> getSysDeptList();

	List<DataReportVo> getCompanyList();

	List<DataReportVo> getBusinessDataList(@Param("thisYear") String thisYear,
											  @Param("lastYear") String lastYear,
											  @Param("startMonth") String startMonth,
											  @Param("endMonth") String endMonth);

	List<DataReportVo> getElectricDataList(@Param("thisYear") String thisYear,
											  @Param("lastYear") String lastYear,
											  @Param("startMonth") String startMonth,
											  @Param("endMonth") String endMonth);

	List<DataReportVo> getGasDataList(@Param("thisYear") String thisYear,
										 @Param("lastYear") String lastYear,
										 @Param("startMonth") String startMonth,
										 @Param("endMonth") String endMonth);

	List<DataReportVo> getOilDataList(@Param("thisYear") String thisYear,
										 @Param("lastYear") String lastYear,
										 @Param("startMonth") String startMonth,
										 @Param("endMonth") String endMonth);

	List<DataReportVo> getCoalDataList(@Param("thisYear") String thisYear,
										  @Param("lastYear") String lastYear,
										  @Param("startMonth") String startMonth,
										  @Param("endMonth") String endMonth);

	List<DataReportVo> getThermalDataList(@Param("thisYear") String thisYear,
											 @Param("lastYear") String lastYear,
											 @Param("startMonth") String startMonth,
											 @Param("endMonth") String endMonth);

	List<DataReportVo> getWaterDataList(@Param("thisYear") String thisYear,
										   @Param("lastYear") String lastYear,
										   @Param("startMonth") String startMonth,
										   @Param("endMonth") String endMonth);
	DataReportVo getProvinceBusinessData(@Param("thisYear") String thisYear,
										@Param("lastYear") String lastYear,
										@Param("startMonth") String startMonth,
										@Param("endMonth") String endMonth);
	List<HashMap<String, Object>> getCompanyBusinessTotalList(@Param("companyId") Long companyId,
															  @Param("dataYear") String dataYear);

}