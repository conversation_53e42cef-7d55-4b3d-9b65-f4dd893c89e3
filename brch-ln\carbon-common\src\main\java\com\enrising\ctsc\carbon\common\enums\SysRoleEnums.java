package com.enrising.ctsc.carbon.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 系统角色
 *
 * <AUTHOR>
 * @since 1.0.0 2023-2-14
 */
@Getter
@AllArgsConstructor
public enum SysRoleEnums {
	/***/
	ROLE_ADMIN("admin", "系统管理员", 0),
	ROLE_PROVINCE_ADMIN("PROVI_ENERGY_ADMIN", "省级管理员", 0),
	ROLE_CITY_ADMIN("CITY_ADMIN", "地市管理员", 1),
	ROLE_ORDINARY("ORDINARY", "普通用户", 2),
	;
	private final String value;
	private final String name;
	private final int level;

	/**
	 * 根据code获取角色等级
	 *
	 * @param codeList code
	 * @return 等级
	 */
	public static boolean getLevelHasValue(List<String> codeList, int level) {
		for (String code : codeList) {
			for (SysRoleEnums role : SysRoleEnums.values()) {
				if (code.equals(role.value)) {
					if (role.level == level) {
						return true;
					}
				}
			}
		}
		return false;
	}

	/**
	 * 根据角色代码列表获取最大角色等级（等级数字越小权限越高）
	 *
	 * @param codeList 角色代码列表
	 * @return 最大角色等级，如果没有匹配的角色返回普通用户等级
	 */
	public static int getMaxRoleLevel(List<String> codeList) {
		if (codeList == null || codeList.isEmpty()) {
			return ROLE_ORDINARY.getLevel();
		}

		int maxLevel = ROLE_ORDINARY.getLevel(); // 默认为普通用户等级
		for (String code : codeList) {
			for (SysRoleEnums role : SysRoleEnums.values()) {
				if (code.equals(role.value)) {
					// 等级数字越小权限越高，所以取最小值
					if (role.level < maxLevel) {
						maxLevel = role.level;
					}
				}
			}
		}
		return maxLevel;
	}
}
