package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.enrising.ctsc.discharge.api.entity.DischargeDataEnergyNotification;
import com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyNotificationVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 碳排放数据填报（能源）提醒表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-12
 */
@Mapper
public interface DischargeDataEnergyNotificationMapper extends BaseMapper<DischargeDataEnergyNotification> {
	/**
	 * 列表查询
	 *
	 * @param reportTime 填报时间
	 * @return 列表
	 */
	List<DischargeDataEnergyNotificationVo> getAllRemindCountList(@Param("reportTime") Date reportTime);
}