package com.enrising.ctsc.assess.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.assess.api.bo.AssessTaskReportAttachmentBo;
import com.enrising.ctsc.assess.api.query.AssessTaskReportAttachmentQuery;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.assess.api.vo.AssessTaskReportAttachmentVo;
import com.enrising.ctsc.assess.service.AssessTaskReportAttachmentService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 考核任务上报附件
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@RestController
@RequestMapping("/assess/reportAttachment")
@AllArgsConstructor
public class AssessTaskReportAttachmentController {
	private final AssessTaskReportAttachmentService assessTaskReportAttachmentService;

	@GetMapping("/list")
	public TableDataInfo<AssessTaskReportAttachmentVo> page(Page<AssessTaskReportAttachmentVo> page, AssessTaskReportAttachmentQuery query) {
		return assessTaskReportAttachmentService.findList(page, query);
	}

	@GetMapping("/detail")
	public R<AssessTaskReportAttachmentVo> get(AssessTaskReportAttachmentQuery query) {
		AssessTaskReportAttachmentVo detail = assessTaskReportAttachmentService.detail(query);
		return R.success(detail, "查询成功");
	}

	@PostMapping(value = "/save")
	public R<String> save(@RequestBody AssessTaskReportAttachmentBo bo) {
		assessTaskReportAttachmentService.add(bo);
		return R.success("保存成功");
	}

	@PostMapping(value = "/update")
	public R<String> update(@RequestBody AssessTaskReportAttachmentBo bo) {
		assessTaskReportAttachmentService.edit(bo);
		return R.success("修改成功");
	}

	@PostMapping(value = "/delete/{id}")
	public R<String> delete(@PathVariable Long id) {
		assessTaskReportAttachmentService.del(id);
		return R.success("删除成功");
	}
}
