package com.enrising.ctsc.carbon.common.utils.request;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

public class RequestCachingFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        
        // 检查是否是multipart/form-data请求，如果是，则不进行缓存处理
        String contentType = httpRequest.getContentType();
        if (contentType != null && contentType.toLowerCase().startsWith("multipart/form-data")) {
            // 直接传递原始请求，不进行包装
            chain.doFilter(request, response);
        } else {
            // 对于非multipart请求，使用缓存包装器
            CachedBodyHttpServletRequest wrappedRequest = new CachedBodyHttpServletRequest(httpRequest);
            chain.doFilter(wrappedRequest, response);
        }
    }
}
