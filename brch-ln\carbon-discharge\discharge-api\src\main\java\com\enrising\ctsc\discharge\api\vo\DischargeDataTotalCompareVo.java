package com.enrising.ctsc.discharge.api.vo;

import com.enrising.ctsc.carbon.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 碳排放数据填报表（油）
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Data
public class DischargeDataTotalCompareVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 填报单位id
	 */
	private Long companyId;

	/**
	 * 数据填报单位名称
	 */
	private String companyName;

	/**
	 * 填报时间
	 */
	@JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
	private Date reportTime;

	/**
	 * 数据时间
	 */
	private String dataMonth;

	/**
	 * 数据年份
	 */
	private String dataYear;

	/**
	 * 数据值
	 */
	private BigDecimal dataValue;

	/**
	 * 数据同比
	 */
	private String dataYoY;

	/**
	 * 数据环比
	 */
	private String dataMoM;


	/**
	 * 数据同比 -- 坐标
	 */
	private Integer dataYoyCoord;

	/**
	 * 数据环比 -- 纵坐标
	 */
	private Integer dataMomCoord;

	/**
	 * 区划编码
	 */
	private String cityCode;
}
