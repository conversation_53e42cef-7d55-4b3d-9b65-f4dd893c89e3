package com.enrising.ctsc.discharge.controller;

import com.enrising.ctsc.discharge.api.bo.GreenEnergyManagementBo;
import com.enrising.ctsc.discharge.api.query.GreenEnergyManagementQuery;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.vo.GreenEnergyManagementVo;
import com.enrising.ctsc.discharge.service.GreenEnergyManagementService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
* 绿电管理
*
* <AUTHOR> <EMAIL>
* @since 1.0.0 2024-09-15
*/
@RestController
@RequestMapping("/greenManagement")
@AllArgsConstructor
public class GreenEnergyManagementController {
    private final GreenEnergyManagementService greenEnergyManagementService;

    /**
     * 分页
     * @param query 查询参数
     * @return 分页数据
     */
    @PostMapping("/list")
    public TableDataInfo<GreenEnergyManagementVo> page(@RequestBody GreenEnergyManagementQuery query){
        return greenEnergyManagementService.findList(query);
    }

    /**
     * 导出
     */
    @PostMapping("/exportAll")
    public void exportAll(@RequestBody GreenEnergyManagementQuery query){
        greenEnergyManagementService.export(query);
    }

    /**
     * 详情
     * @param query 查询参数
     * @return 结果
     */
    @PostMapping("/detail")
    public R<GreenEnergyManagementVo> get(@RequestBody GreenEnergyManagementQuery query){
        GreenEnergyManagementVo detail = greenEnergyManagementService.detail(query);
        return R.success(detail, "查询成功");
    }

    /**
     * 保存
     * @param bo 数据
     * @return 结果
     */
    @PostMapping(value = "/save")
    public R<String> save(@RequestBody @Valid GreenEnergyManagementBo bo){
        greenEnergyManagementService.add(bo);
        return R.success("保存成功");
    }

    /**
     * 修改
     * @param bo 数据
     * @return 结果
     */
    @PostMapping(value = "/update")
    public R<String> update(@RequestBody @Valid GreenEnergyManagementBo bo){
        greenEnergyManagementService.edit(bo);
        return R.success("修改成功");
    }

    /**
     * 删除
     * @param id 数据ID
     * @return 结果
     */
    @PostMapping(value = "/delete/{id}")
    public R<String> delete(@PathVariable Long id){
        greenEnergyManagementService.del(id);
        return R.success("删除成功");
    }
}
