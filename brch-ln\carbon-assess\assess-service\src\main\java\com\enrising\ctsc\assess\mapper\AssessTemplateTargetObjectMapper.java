package com.enrising.ctsc.assess.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.assess.api.bo.AssessTemplateTargetObjectBo;
import com.enrising.ctsc.assess.api.entity.AssessTemplateTargetObject;
import com.enrising.ctsc.assess.api.entity.Attachment;
import com.enrising.ctsc.carbon.common.entity.User;
import com.enrising.ctsc.assess.api.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 考核模板对象
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-08
 */
@Mapper
public interface AssessTemplateTargetObjectMapper extends BaseMapper<AssessTemplateTargetObject> {

	/**
	 * 查询某个分公司的考核任务
	 */
	List<AssessTemplateTargetObjectVo> getTaskInfoByOrgId(@Param("bo") AssessTemplateTargetObjectBo bo);


	/**
	 * 查询某个模板下所有指标 -- 地市
	 */
	List<AssessTemplateTargetObjectVo> getTaskInfoByTemplateId(@Param("bo") AssessTemplateTargetObjectBo bo);

	/**
	 * 查询某个模板下所有指标 -- 管理员
	 */
	List<AssessTemplateTargetObjectVo> getAllTaskByTemplateId(@Param("bo") AssessTemplateTargetObjectBo bo);

	/**
	 * 获取附件列表
	 */
	List<Attachment> getFileByIds(@Param("attachmentIds") List<Long> attachmentIds);

	/**
	* 根据模板id查询分公司的分值
	* */
	List<AssessTemplateTargetObjectVo> getAssessScoreByTemplateId(@Param("bo") AssessTemplateTargetObjectBo bo);

	/**
	* 根据模板id获取模板详情
	* */
	AssessRankRateVo getTemplateById(@Param("bo") AssessTemplateTargetObjectBo bo);

	/**
	 * 根据模板id和公司id查询详情
	 * */
	List<RankRateInfoVo> getRankRateInfoById(@Param("bo") AssessTemplateTargetObjectBo bo);

	/**
	* 获取当前公司的当前指标排在第几
	* */
	List<AssessTaskReportVo> getOrderByCompany(@Param("bo") RankRateInfoVo bo);

	/**
	 * 获取某个模板下考核部门对应的考核分值
	 * */
	List<AssessProvinceComVo> getCompanyAssScoreList(@Param("bo") AssessTemplateTargetObjectBo bo);

	/**
	 * 查询某个模板下所有指标 -- 具体某个公司
	 */
	AssessProvinceComVo getScoreByTemplateId(@Param("bo") AssessTemplateTargetObjectBo bo);

	Page<AssessScoreVo> getScoreListByTemplateId(@Param("page") Page<AssessScoreVo> page, @Param("bo") AssessTemplateTargetObjectBo bo);


	List<AssessScoreVo> getHeaderScoreList(@Param("bo") AssessTemplateTargetObjectBo bo);

	/**
	 * 根据模板指标id查询考核分数
	 */
	List<AssessTemplateTargetObjectVo> getAssessScoreByTemplateObjectIds(@Param("bo") AssessTemplateTargetObjectBo bo);

	/**
	 * 获取公司的排名信息通过指标和模板id
	 */
	List<AssessScoreVo> getComRankByTarAndTemId(@Param("bo") AssessTemplateTargetObjectBo bo);

	/**
	 * 获取每个公司下的各个指标的得分情况
	 */
	List<AssessScoreVo> getTargetScoreByCompanyIds(@Param("bo") AssessTemplateTargetObjectBo bo);

	/**
	 * 获取用户信息
	 */
	List<User> getUserByIds(@Param("ids") List<Long> ids);
}
