<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeDataEnergyNotificationMapper">
	<resultMap id="BaseResultMap" type="com.enrising.ctsc.discharge.api.entity.DischargeDataEnergyNotification">
		<result column="id" property="id" />
		<result column="create_by" property="createBy" />
		<result column="create_time" property="createTime" />
		<result column="update_by" property="updateBy" />
		<result column="update_time" property="updateTime" />
		<result column="company_id" property="companyId" />
		<result column="report_time" property="reportTime" />
		<result column="remind_time" property="remindTime" />
		<result column="status" property="status" />
		<result column="del_flag" property="delFlag" />
	</resultMap>

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.company_id,
            t.report_time,
            t.remind_time,
            t.status,
            t.del_flag
    </sql>
	<!-- 统计所有部门提醒次数列表 -->
	<select id="getAllRemindCountList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeDataEnergyNotificationVo">
		SELECT
			company_id,
			count(*) as remind_count
		FROM discharge_data_energy_notification
		WHERE del_flag = '0' and report_time = #{reportTime}
		GROUP BY company_id
		ORDER BY company_id asc
	</select>
</mapper>