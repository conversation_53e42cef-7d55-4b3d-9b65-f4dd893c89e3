package com.enrising.ctsc.business.api.vo;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 生产业务数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-03-23
 */

@Data
public class BusinessProductionDataVo extends Model<BusinessProductionDataVo> {

	/**
	 * 主键id,采用雪花id
	 */
	private Long id;

	/**
	 * 创建者id
	 */
	private Long createBy;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 填报单位id
	 */
	private Long companyId;

	/**
	 * 填报单位名称
	 */
	private String companyName;

	/**
	 * 填报时间
	 */
	private Date reportTime;

	/**
	 * 电信业务总量（万元）
	 */
	private BigDecimal telecomBusinessTotal;

	/**
	 * 业务流量总量（TB）
	 */
	private BigDecimal businessFlowTotal;

	/**
	 * 固定电话业务总量（万元）
	 */
	private BigDecimal fixedPhoneBusiness;

	/**
	 * 宽带接入业务总量（万元）
	 */
	private BigDecimal broadbandBusiness;

	/**
	 * 专线接入业务总量（万元）
	 */
	private BigDecimal specialBroadbandBusiness;

	/**
	 * IPTV业务总量（万元）
	 */
	private BigDecimal iptvBusiness;

	/**
	 * 移动电话业务总量（万元）
	 */
	private BigDecimal mobilePhonBusiness;

	/**
	 * 移动互联网业务总量（TB）
	 */
	private BigDecimal mobileInternetBusiness;

	/**
	 * 移动短信业务总量（万元）
	 */
	private BigDecimal mobileSmsBusiness;

	/**
	 * 物联网业务总量（万元）
	 */
	private BigDecimal iotBusiness;

	/**
	 * 互联网数据中心业务总量（万元）
	 */
	private BigDecimal idcBusiness;

	/**
	 * 其他业务总量（万元）
	 */
	private BigDecimal otherBusiness;

	/**
	 * 删除标志：0-正常；1-删除
	 */
	private String delFlag;

}