package com.enrising.ctsc.discharge.api.enums;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

/**
* 集团部门的主体编码,同步能耗数据使用
* 2024年9月24日
* <AUTHOR>
*/
@Getter
@AllArgsConstructor
public class BodyCodeInfo {
	private static final Map<String, String> data = Maps.newHashMap();

	static {
		data.put("2600", "省公司本部");
		data.put("2601", "沈阳市公司");
		data.put("2602", "大连市公司");
		data.put("2603", "鞍山市公司");
		data.put("2604", "抚顺市公司");
		data.put("2605", "本溪市公司");
		data.put("2606", "丹东市公司");
		data.put("2607", "锦州市公司");
		data.put("2608", "营口市公司");
		data.put("2609", "阜新市公司");
		data.put("2610", "辽阳市公司");
		data.put("2611", "铁岭市公司");
		data.put("2612", "朝阳市公司");
		data.put("2613", "盘锦市公司");
		data.put("2614", "葫芦岛市公司");
	}

	public static String getName(String code) {
		return data.get(code);
	}

}
