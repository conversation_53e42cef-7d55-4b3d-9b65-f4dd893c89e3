package com.enrising.ctsc.discharge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.enrising.ctsc.discharge.api.bo.DischargeEnergyCoefficientBo;
import com.enrising.ctsc.discharge.api.entity.DischargeEnergyCoefficient;
import com.enrising.ctsc.discharge.api.entity.SysDictItem;
import com.enrising.ctsc.discharge.api.enums.DelFlagEnum;
import com.enrising.ctsc.carbon.common.exception.BusinessException;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyCoefficientQuery;
import com.enrising.ctsc.discharge.api.query.DischargeEnergyFactorOpenBo;
import com.enrising.ctsc.carbon.common.utils.ObjectUtil;
import com.enrising.ctsc.carbon.common.utils.QueryPage;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyCoefficientExport;
import com.enrising.ctsc.discharge.api.vo.DischargeEnergyCoefficientVo;
import com.enrising.ctsc.discharge.mapper.DischargeEnergyCoefficientMapper;
import com.enrising.ctsc.discharge.service.DischargeEnergyCoefficientService;
import com.enrising.ctsc.discharge.service.DischargeEnergyTypeService;
import com.enrising.ctsc.discharge.service.SysDictItemService;
import com.github.liaochong.myexcel.core.DefaultExcelBuilder;
import com.github.liaochong.myexcel.utils.AttachmentExportUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.CharEncoding;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 碳排放能源转换系数表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Slf4j
@Service
@AllArgsConstructor
public class DischargeEnergyCoefficientServiceImpl extends ServiceImpl<DischargeEnergyCoefficientMapper, DischargeEnergyCoefficient> implements DischargeEnergyCoefficientService {

//	private final RemoteDictService dictService;

    private final SysDictItemService sysDictItemService;

    @Override
    public TableDataInfo<DischargeEnergyCoefficientVo> findList(QueryPage<DischargeEnergyCoefficientQuery> page) {
        QueryWrapper<DischargeEnergyCoefficientQuery> wrapper = this.getWrapper(page.getModel());
        IPage<DischargeEnergyCoefficientVo> resultPage = baseMapper.findList(new Page<>(page.getCurrent(), page.getSize()), wrapper);
        return TableDataInfo.build(resultPage);
    }

    @SneakyThrows
    @Override
    public void exportExcel(DischargeEnergyCoefficientQuery query, HttpServletResponse response) {
        QueryWrapper<DischargeEnergyCoefficientQuery> wrapper = this.getWrapper(query);
        List<DischargeEnergyCoefficientVo> list = baseMapper.findList(wrapper);
        List<DischargeEnergyCoefficientExport> exportList = Lists.newArrayList();
//		R<List<SysDictItem>> energyType = dictService.getDictListByType("energy_type");
//		R<List<SysDictItem>> energyTypeUnit = dictService.getDictListByType("energy_type_unit");
        List<SysDictItem> energyTypeList = sysDictItemService.listItems("energy_type");
        List<SysDictItem> energyTypeUnitList = sysDictItemService.listItems("energy_type_unit");
        list.forEach(it -> {
            DischargeEnergyCoefficientExport export = new DischargeEnergyCoefficientExport();
            BeanUtil.copyProperties(it, export);
            // 设置转换系数单位
			String energyTypeStr = energyTypeList.stream().filter(dict -> dict.getValue().equals(it.getEnergyType())).findFirst().orElse(new SysDictItem()).getDescription();

			String energyTypeUnitStr = energyTypeUnitList.stream().filter(dict -> dict.getValue().equals(it.getEnergyType())).findFirst().orElse(new SysDictItem()).getDescription();

			export.setCoefficientStr(StrUtil.format("{}{}/{}", it.getCoefficient(), energyTypeStr, energyTypeUnitStr));
            exportList.add(export);
        });
        Workbook workbook = DefaultExcelBuilder.of(DischargeEnergyCoefficientExport.class)
                .build(exportList);
        response.setCharacterEncoding(CharEncoding.UTF_8);
        AttachmentExportUtil.export(workbook, "能耗转换系数库", response);
    }

    @Override
    public DischargeEnergyCoefficientVo detail(DischargeEnergyCoefficientQuery query) {
        if (ObjectUtil.allFieldIsNull(query)) {
            throw new BusinessException("查询参数不能为空");
        }
        QueryWrapper<DischargeEnergyCoefficientQuery> wrapper = this.getWrapper(query);
        return baseMapper.detail(wrapper);
    }

    private QueryWrapper<DischargeEnergyCoefficientQuery> getWrapper(DischargeEnergyCoefficientQuery query) {
        QueryWrapper<DischargeEnergyCoefficientQuery> wrapper = new QueryWrapper<>();
        wrapper.eq(query.getId() != null, "t.id", query.getId());
        wrapper.eq("t.del_flag", DelFlagEnum.NOT_DELETED.getValue());
        // 关键字
        if (StrUtil.isNotBlank(query.getKeys())) {
            wrapper.and(w -> w.like("energy_type.second_name", query.getKeys()).or().like("t.source", query.getKeys()));
        }
        // 时间周期
//        if (CollUtil.isNotEmpty(query.getDaterange())) {
//            Date startTime = DateUtil.parse(query.getDaterange().get(0), "yyyy-MM-dd");
//            Date endTime = DateUtil.parse(query.getDaterange().get(1), "yyyy-MM-dd");
//            String endTimeFormat = DateUtil.format(endTime, "yyyy-12-31 23:59:59");
//            wrapper.and(w ->
//                    w.between("t.validity_start", startTime, DateUtil.parse(endTimeFormat)).or().between("t.validity_end", startTime, DateUtil.parse(endTimeFormat))
//            );
//
//        }
        wrapper.ge(ObjectUtil.isNotEmpty(query.getValidityStart()), "t.validity_start", query.getValidityStart());
        if (ObjectUtil.isNotEmpty(query.getValidityEnd())) {
            wrapper.le("t.validity_end", DateUtil.endOfYear(query.getValidityEnd()));
        }
        wrapper.orderByDesc("t.create_time");
        return wrapper;
    }

    private static DischargeEnergyCoefficient getDischargeEnergyFactor(DischargeEnergyCoefficientBo bo) {
        DischargeEnergyCoefficient entity = new DischargeEnergyCoefficient();
        BeanUtils.copyProperties(bo, entity);
        Date startTime = DateUtil.parse(bo.getDaterange().get(0), "yyyy-MM-dd");
        Date endTime = DateUtil.parse(bo.getDaterange().get(1), "yyyy-MM-dd");
        String endTimeFormat = DateUtil.format(endTime, "yyyy-12-31 23:59:59");
        entity.setValidityStart(startTime);
        entity.setValidityEnd(DateUtil.parse(endTimeFormat));
        return entity;
    }

    private void updateTimeOverlapData(DischargeEnergyCoefficientBo bo) {
        Date startTime = DateUtil.parse(bo.getDaterange().get(0), "yyyy-MM-dd");
        Date endTime = DateUtil.parse(bo.getDaterange().get(1).substring(0, 5) + "12-31 23:59:59", "yyyy-MM-dd HH:mm:ss");
        DischargeEnergyCoefficientQuery query = new DischargeEnergyCoefficientQuery();
        query.setEnergyTypeId(bo.getEnergyTypeId());
        query.setDaterange(bo.getDaterange());
        query.setId(bo.getId());
        List<DischargeEnergyCoefficient> overlappingData = this.selectTypeByDate(query);
        for (DischargeEnergyCoefficient coefficient : overlappingData) {
            // 开始时间减一秒 如:2023-01-01 00:00:00 结果：2022-12-31 23:59:59
//            DateTime endTime = new DateTime(startTime).offset(DateField.SECOND, -1);
            if (coefficient.getValidityStart().before(startTime)) {
                coefficient.setValidityEnd(new DateTime(startTime).offset(DateField.SECOND, -1));
                coefficient.updateById();
            } else {
                if (coefficient.getValidityEnd().after(endTime)) {
                    coefficient.setValidityStart(new DateTime(DateUtil.parse(bo.getDaterange().get(1), "yyyy-MM-dd")).offset(DateField.YEAR, 1));
                    coefficient.updateById();
                } else {
                    coefficient.deleteById();
                }
            }
        }
    }

    @Override
    public void add(DischargeEnergyCoefficientBo bo) {
        // 获取时间重叠的数据，进行更新
        this.updateTimeOverlapData(bo);

        DischargeEnergyCoefficient entity = getDischargeEnergyFactor(bo);
        baseMapper.insert(entity);
    }

    @Override
    public void edit(DischargeEnergyCoefficientBo bo) {
        // 获取时间重叠的数据，进行更新
        this.updateTimeOverlapData(bo);

        DischargeEnergyCoefficient entity = getDischargeEnergyFactor(bo);
        baseMapper.updateById(entity);
    }

    @Override
    public void del(Long id) {
        baseMapper.deleteById(id);
    }

    @Override
    public BigDecimal getCoefficientByTime(Long energyTypeId, Date reportTime) {
        List<DischargeEnergyCoefficient> dischargeEnergyCoefficientList = baseMapper.selectList(
                new LambdaQueryWrapper<DischargeEnergyCoefficient>()
                        .select(DischargeEnergyCoefficient::getEnergyTypeId, DischargeEnergyCoefficient::getValidityStart,
                                DischargeEnergyCoefficient::getCoefficient)
                        .eq(DischargeEnergyCoefficient::getEnergyTypeId, energyTypeId)
                        .le(DischargeEnergyCoefficient::getValidityStart, reportTime)
                        .orderByDesc(DischargeEnergyCoefficient::getValidityStart)
                        .last("limit 1"));
        if (CollectionUtil.isNotEmpty(dischargeEnergyCoefficientList)) {
            return dischargeEnergyCoefficientList.get(0).getCoefficient();
        }
        return new BigDecimal(0);
    }

    @Override
    public List<DischargeEnergyCoefficient> selectTypeByDate(DischargeEnergyCoefficientQuery query) {
        if (CollUtil.isEmpty(query.getDaterange())) {
            throw new BusinessException("时间区间不能为空");
        }
        Date startTime = DateUtil.parse(query.getDaterange().get(0), "yyyy-MM-dd");
        Date endTime = DateUtil.parse(query.getDaterange().get(1), "yyyy-MM-dd");
        List<DischargeEnergyCoefficient> list = this.list(Wrappers.<DischargeEnergyCoefficient>lambdaQuery()
                .select(DischargeEnergyCoefficient::getId, DischargeEnergyCoefficient::getEnergyTypeId, DischargeEnergyCoefficient::getSource,
                        DischargeEnergyCoefficient::getCoefficient, DischargeEnergyCoefficient::getValidityStart, DischargeEnergyCoefficient::getValidityEnd
                )
                .ge(DischargeEnergyCoefficient::getValidityEnd, startTime)
                .le(DischargeEnergyCoefficient::getValidityStart, endTime)
                .eq(DischargeEnergyCoefficient::getEnergyTypeId, query.getEnergyTypeId())
                .ne(ObjectUtil.isNotEmpty(query.getId()), DischargeEnergyCoefficient::getId, query.getId())
        );
//        for (DischargeEnergyCoefficient coefficient : list) {
//            String startTimeFormat = DateUtil.format(startTime, "yyyy");
//            String dbStartTimeFormat = DateUtil.format(coefficient.getValidityStart(), "yyyy");
//            if (startTimeFormat.equals(dbStartTimeFormat) && !Objects.equals(query.getBoId(), coefficient.getId())) {
//                throw new BusinessException("存在多条数据，请删除其余数据后操作！");
//            }
//            if (coefficient.getValidityStart().getTime() > startTime.getTime() && !Objects.equals(query.getBoId(), coefficient.getId())) {
//                throw new BusinessException("存在多条数据，请删除其余数据后操作！");
//            }
//        }
        return list;
    }

    @Override
    public List<DischargeEnergyCoefficient> getListByTime(Date reportTime) {
        return baseMapper.findListByTime(reportTime);
    }

    @Override
    public DischargeEnergyCoefficientVo getGainFactor(DischargeEnergyFactorOpenBo bo) {
        return baseMapper.getGainFactor(bo);
    }

}
