<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeCheckMapper">

    <!-- 表字段 -->
    <sql id="baseColumns">
            t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.company_id,
            t.check_company,
            to_char(t.check_time,'yyyy-MM-dd') as check_time,
            t.check_director,
            t.check_phone,
            t.remarks,
            t.report_attachment_id,
            t.del_flag
    </sql>

    <!-- 查询列表 -->
    <select id="findList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeCheckVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_check t
        ${ew.customSqlSegment}
    </select>

    <!-- 查询详情 -->
    <select id="detail" resultType="com.enrising.ctsc.discharge.api.vo.DischargeCheckVo">
        SELECT
        <include refid="baseColumns" />
        FROM discharge_check t
        ${ew.customSqlSegment}
        limit 1
    </select>
</mapper>