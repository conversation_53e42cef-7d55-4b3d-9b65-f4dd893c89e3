package com.enrising.ctsc.discharge.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.entity.DischargeReport;
import com.enrising.ctsc.discharge.api.query.DischargeReportQuery;
import com.enrising.ctsc.discharge.api.vo.DischargeReportVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 碳排放报告
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */
@Mapper
public interface DischargeReportMapper extends BaseMapper<DischargeReport> {

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
	IPage<DischargeReportVo> findList(Page<DischargeReportVo> page, @Param(Constants.WRAPPER) Wrapper<DischargeReportQuery> wrapper);

	/**
	 * 列表查询
	 *
	 * @param page    分页数据
	 * @param wrapper 条件
	 * @return 列表
	 */
	List<DischargeReportVo> findList(@Param(Constants.WRAPPER) Wrapper<DischargeReportQuery> wrapper);

	/**
	 * 详情查询
	 *
	 * @param wrapper 条件
	 * @return 结果
	 */
	DischargeReportVo detail(@Param(Constants.WRAPPER) Wrapper<DischargeReportQuery> wrapper);
}