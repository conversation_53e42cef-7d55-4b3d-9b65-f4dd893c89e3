package com.enrising.ctsc.discharge.api.bo;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 *  碳盘查数据表
 *
 * <AUTHOR>
 * @since 3/13
 */

@Data
public class DischargeDataExamineBo extends Model<DischargeDataExamineBo> {

	/**
	 * 主键id,采用雪花id
	 */
		private Long id;

	/**
	 * 公司id
	 */
		private Long companyId;

	/**
	 * 使用量
	 */
		private BigDecimal use;


	/**
	 * 碳盘查数据表主键id
	 */
		private Long energyExamineId;


	/**
	 * 填报时间
	 */
		private String reportTime;

	private List<Long> energyExamineIds;

}
