package com.enrising.ctsc.discharge.api.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 开放接口bo
 *
 * <AUTHOR>
 * @since 1.0.0 2023-06-26
 */
@Data
public class DischargeDataOpenBo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 部门id
	 */
	private Long companyId;

	/**
	 * 部门名称
	 */
	@NotBlank(message = "部门名称不能为空！")
	private String companyName;

	/**
	 * 填报时间
	 */
	@NotBlank(message = "开始时间不能为空，请输入如 2023-01 ")
	private String startTime;

	/**
	 * 填报时间
	 */
	@NotBlank(message = "结束时间不能为空，请输入如 2023-02 ")
	private String endTime;
}