package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 碳排放核查
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-01-03
 */

@Data
@TableName("discharge_check")
public class DischargeCheck extends Model<DischargeCheck> {

	/**
	 * 主键id
	 */
		@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 创建者id
	 */
		@TableField(fill = FieldFill.INSERT)
	private Long createBy;

	/**
	 * 创建时间
	 */
		@TableField(fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新者id
	 */
		@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updateBy;

	/**
	 * 更新时间
	 */
		@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

	/**
	 * 填报单位id
	 */
		private Long companyId;

	/**
	 * 核查单位名称
	 */
		private String checkCompany;

	/**
	 * 核查时间
	 */
		private Date checkTime;

	/**
	 * 核查负责人
	 */
		private String checkDirector;

	/**
	 * 核查联系电话
	 */
		private String checkPhone;

	/**
	 * 说明
	 */
		private String remarks;

	/**
	 * 报告附件id
	 */
		private Long reportAttachmentId;

	/**
	 * 删除标志：0-正常；1-删除
	 */
		@TableLogic
	private String delFlag;

}
