package com.enrising.ctsc.discharge.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 绿电管理
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2024-09-15
 */

@Data
@TableName("green_energy_management")
public class GreenEnergyManagement extends Model<GreenEnergyManagement> {

	/**
	* 主键ID
	*/
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	* 交易年月
	*/
	private Date transactionMonth;

	/**
	* 所属分公司
	*/
	private String companies;

	/**
	 * 所属部门
	 */
	private String companyBranch;

	/**
	* 分月合同绿电量(兆瓦时)
	*/
	private BigDecimal monthlyContractPower;

	/**
	* 所属主体
	*/
	private String subjectEntity;

	/**
	* 电源所属地
	*/
	private String powerSourceLocation;

	/**
	* 能源类型
	*/
	private String energyType;

	/**
	* 价格
	*/
	private BigDecimal price;

	/**
	* 证明材料文件路径
	*/
	private Long supportingDocument;

	/**
	 * 抵扣碳排放（tCO2)
	 */
	private BigDecimal deduction;

	/**
	* 备注
	*/
	private String remarks;

	/**
	* 创建人
	*/
	private Long createBy;

	/**
	* 创建时间
	*/
	private Date createTime;

	/**
	* 更新时间
	*/
	private Date updateTime;

	/**
	 * 审核结果
	 */
	private String auditResult;
}
