package com.enrising.ctsc.assess.controller;

import com.enrising.ctsc.assess.service.AssessTemplateTargetService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 考核模板指标
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-08
 */
@RestController
@RequestMapping("/assess/templateTarget")
@AllArgsConstructor
public class AssessTemplateTargetController {

	private final AssessTemplateTargetService assessTemplateTargetService;
}
