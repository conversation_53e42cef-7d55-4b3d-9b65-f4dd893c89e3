package com.enrising.ctsc.assess.api.feign;

import com.enrising.ctsc.assess.api.utils.FeignConfigure;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 附件接口
 * <AUTHOR>
 * @date 2024-10-21
 */
@FeignClient(contextId = "remoteAttachmentService",
		value = "common",
		url = "${feign.webUrl:http://127.0.0.1:8080/energy-cost}",
		configuration = FeignConfigure.class)
public interface RemoteAttachmentService {

	/**
	 * 上传文件
	 */
	@PostMapping(value = "/common/attachments/uploadAssessFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	Map<String, Object> uploadAssessFile(@RequestPart("file") MultipartFile file);
}
