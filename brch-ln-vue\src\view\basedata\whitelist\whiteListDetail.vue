<template>
  <!-- *****添加电表  <AUTHOR> -->
    <div class="testaa">
        <Spin size="large" fix v-if="loading"></Spin>
        <Card class="menu-card">
                <Collapse :value="['Panel1','Panel2','Panel3','Panel4','Panel5']">
                    <Panel name="Panel1">基本信息
                        <div slot='content' v-show="valueTL.typeList == 2">
                            <Form :model="ammeter" ref="ammeter1" :rules="ruleValidate" :label-width="80" 
                            
                                class="margin-right-width">
                            <Row>
                                <!-- <Col span="12" v-show="isAddAndModify == 1">
                                    <FormItem :class="{requireStar:isRequireFlag}" label="选择局站：" :label-width="120" prop="chooseMeter">
                                        <span @click="chooseJZ" style="cursor: pointer; color: blue;">选择局站</span>
                                    </FormItem>
                                </Col> -->
                                <Col span="12">
                                    <FormItem label="白名单类型：" :label-width="120" prop="typeList">
                                        <!-- <cl-select v-model="ammeter.whiteListType" filterable
                                                category="BUR_STAND_TYPE"
                                                labelField="typeName" valueField="typeCode">
                                        </cl-select> -->
                                        <Select
                                            disabled
                                            ref="selects"
                                            :clearable="true"
                                            v-model="ammeter.typeList">
                                            <Option value="1">一表多站</Option>
                                            <Option value="2">一站多表</Option>
                                            <Option value="3">单价</Option>
                                        </Select>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="12">
                                    <FormItem label="局站名称：" :label-width="120" prop="stationName">
                                        <cl-input :maxlength=50 v-model="ammeter.stationName" readonly
                                                    placeholder="选择电表后自动带出"></cl-input>
                                    </FormItem>
                                </Col>
                                <Col span="12">
                                    <FormItem label="局站编号：" :label-width="120" prop="stationcode">
                                        <cl-input placeholder="选择电表后自动带出" readonly :maxlength=50 v-model="ammeter.stationcode"></cl-input>
                                        <!-- <label v-if="oldData.ammetername != null &&oldData.ammetername != ammeter.ammetername"
                                                style="color: red;">历史数据：{{oldData.ammetername}}</label> -->
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="12">
                                    <FormItem label="局站类型：" :label-width="120" prop="stationtypename">
                                        
                                        <cl-input placeholder="选择电表后自动带出" readonly :maxlength=50 
                                        v-model="ammeter.stationtypename"></cl-input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <!-- <Row>
                                    :value="selected"
                                    @on-selection-change="onSelectionChange"
                                <Col span="24"> -->
                                    <div style="margin-left: 16px;">
                                <h5 style="margin-left: 16px;">申请人加入白名单电表/协议信息</h5>
                                <!-- <i-button @click="selectAll">全选</i-button> -->
                                <Table ref="listTable1" 
                                    style="margin-left: 16px;" 
                                    @on-row-click="handleRowClick"
                                    border :loading="jz.loading"
                                    :columns="jz.columns1"
                                    :data="jz.insideData1"
                                    :selection="selection" 
                                    @on-select-all="handleSelectAll"

                                    class="mytable">
                                    <!-- <template slot="selection" slot-scope="{row, index}"> 
                                        <i-checkbox v-model="row.checked" @click.native.stop="selectSingle(row)"></i-checkbox> 
                                    </template> -->
                                </Table>
                                    </div>
                                <!-- </Col>
                            </Row> -->
                            <Row style="margin-top: 20px;">
                                <Col span="24">
                                    <FormItem label="申请理由：" :label-width="120" prop="applyReason">
                                        <Input readonly type="textarea" :rows="3" v-model="ammeter.applyReason"></Input>
                                        
                                    </FormItem>
                                </Col>
                            </Row>
                            </Form>
                                
                                </div>
                                
                        <div slot='content' v-show="valueTL.typeList == 1">
                            <Form :model="ammeter" ref="ammeter" :label-width="80" 
                                class="margin-right-width">
                            <Row>
                                <!-- <Col span="12" v-show="isAddAndModify == 1">
                                    <FormItem :class="{requireStar:isRequireFlag}" label="选择电表/协议：" :label-width="120" prop="chooseMeter">
                                        <span @click="chooseOne" style="cursor: pointer; color: blue;">选择电表/协议</span>
                                    </FormItem>
                                </Col> -->
                                <Col span="12">
                                    <FormItem label="白名单类型：" :label-width="120" prop="typeList">
                                        <!-- <cl-select v-model="ammeter.whiteListType" filterable
                                                category="BUR_STAND_TYPE"
                                                labelField="typeName" valueField="typeCode">
                                        </cl-select> -->
                                        <Select
                                            disabled
                                            ref="selects"
                                            :clearable="true"
                                            v-model="ammeter.typeList">
                                            <Option value="1">一表多站</Option>
                                            <Option value="2">一站多表</Option>
                                            <Option value="3">单价</Option>
                                        </Select>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="12">
                                    <FormItem label="项目名称：" :label-width="120" prop="projectname">
                                        <cl-input :maxlength=50 v-model="ammeter.projectname" readonly
                                                    placeholder="选择电表后自动带出"></cl-input>
                                    </FormItem>
                                </Col>
                                <Col span="12">
                                    <FormItem label="电表编号：" :label-width="120" prop="ammetername">
                                        <cl-input placeholder="选择电表后自动带出" readonly :maxlength=50 v-model="ammeter.ammetername"></cl-input>
                                        <label v-if="oldData.ammetername != null &&oldData.ammetername != ammeter.ammetername"
                                                style="color: red;">历史数据：{{oldData.ammetername}}</label>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="12">
                                    <FormItem label="用电类型：" :label-width="120" prop="electrotypename">
                                        
                                        <cl-input placeholder="选择电表后自动带出" readonly :maxlength=50 
                                        v-model="ammeter.electrotypename"></cl-input>
                                    </FormItem>
                                </Col>
                            </Row>
                            </Form>
                        </div>
                        <div slot='content' v-show="valueTL.typeList == 3">
                            <Form :model="ammeter" ref="ammeter2" :rules="ruleValidate" :label-width="80" 
                                class="margin-right-width">
                            <Row>
                                <!-- <Col span="8" v-show="isAddAndModify == 1">
                                    <FormItem :class="{requireStar:isRequireFlag}" label="选择电表/协议：" :label-width="120" prop="chooseMeter">
                                        <span @click="chooseOne" style="cursor: pointer; color: blue;">选择电表/协议</span>
                                    </FormItem>
                                </Col> -->
                                <Col span="8">
                                    <FormItem label="白名单类型：" :label-width="120" prop="typeList">
                                        <!-- <cl-select v-model="ammeter.whiteListType" filterable
                                                category="BUR_STAND_TYPE"
                                                labelField="typeName" valueField="typeCode">
                                        </cl-select> -->
                                        <Select
                                            disabled
                                            ref="selects"
                                            :clearable="true"
                                            v-model="ammeter.typeList">
                                            <Option value="1">一表多站</Option>
                                            <Option value="2">一站多表</Option>
                                            <Option value="3">单价</Option>
                                        </Select>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem label="项目名称：" :label-width="120" prop="projectname">
                                        <cl-input :maxlength=50 v-model="ammeter.projectname" readonly
                                                    placeholder="选择电表后自动带出"></cl-input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="电表编号：" :label-width="120" prop="ammetername">
                                        <cl-input placeholder="选择电表后自动带出" readonly :maxlength=50 v-model="ammeter.ammetername"></cl-input>
                                        <!-- <label v-if="oldData.ammetername != null &&oldData.ammetername != ammeter.ammetername"
                                                style="color: red;">历史数据：{{oldData.ammetername}}</label> -->
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="用电类型：" :label-width="120" prop="electrotypename">
                                        
                                        <cl-input placeholder="选择电表后自动带出" readonly :maxlength=50 
                                        v-model="ammeter.electrotypename"></cl-input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem label="所属分公司：" :label-width="120" prop="company">
                                        
                                        <cl-input placeholder="选择电表后自动带出" readonly :maxlength=50 
                                        v-model="ammeter.companyName"></cl-input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                        <!--                                            <FormItem label="所属部门：" prop="countryName">-->
                        <!--                                                <Input icon="ios-archive" v-model="ammeter.countryName"-->
                        <!--                                                       placeholder="点击图标选择" @on-click="chooseResponseCenter()" readonly/>-->
                        <!--                                            </FormItem>-->
                                    <FormItem label="所属部门：" :label-width="120" prop="countryName" v-if="isAdmin == true">
                                        
                                        <cl-input placeholder="选择电表后自动带出" readonly :maxlength=50 
                                        v-model="ammeter.countryName"></cl-input>                                    
                                        <!-- <label v-if="oldData.countryName != null &&oldData.countryName != ammeter.countryName" style="color: red;">历史数据：{{oldData.countryName}}</label>-->
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="对外结算类型：" :label-width="120" prop="directsupplyflag">
                                        
                                        <cl-input placeholder="选择电表后自动带出" readonly :maxlength=50 
                                        v-model="ammeter.directsupplyflag"></cl-input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem label="关联局站：" :label-width="120" prop="stationName">
                                        <Input v-model="ammeter.stationName" readonly
                                                placeholder="选择电表后自动带出"/>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                        <FormItem label="局站编码：" :label-width="120" prop="stationcode">
                                            <Input v-model="ammeter.stationcode" placeholder="选择电表后自动带出" readonly></Input>
                                        </FormItem>
                                        </Col>
                                <Col span="8">
                                    <FormItem label="单价（元）：" :label-width="120" prop="price">
                                        
                                        <Input placeholder="选择电表后自动带出" readonly :maxlength=50 
                                        v-model="ammeter.price"></Input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="24">
                                    <FormItem label="申请理由：" :label-width="120" prop="applyReason">
                                        <Input readonly type="textarea" :rows="3" v-model="ammeter.applyReason"></Input>
                                        
                                    </FormItem>
                                </Col>
                            </Row>

                            </Form>
                        </div>
                    </Panel>
                    <Panel name="Panel2" v-show="valueTL.typeList == 1">关联局站信息
                        <div slot='content'>
                            <!-- <h5 style="margin-left: 16px;">申请人加入白名单电表/协议信息</h5> -->
                            <!-- <Row>
                                <Col span="24" style="position: relative;"> -->
                                    <!-- <div style="margin-left: 16px;"> -->
                                        <Table ref="listTable2" style="margin-left: 16px;"
                                        border :loading="jz.loading"
                                        :columns="jz.columns3"
                                        :data="jz.insideData2"
                                        class="mytable">
                                </Table>
                                    <!-- </div> -->
                                    
                                <!-- </Col>
                            </Row> -->
                            <Row style="margin-top: 20px;">
                                <Col span="24">
                            <Form :model="ammeter" ref="ammeter3" :rules="ruleValidate" :label-width="80"
                                class="margin-right-width">
                                    <FormItem label="申请理由：" :label-width="120" prop="applyReason">
                                        <Input readonly type="textarea" :rows="3" v-model="ammeter.applyReason"></Input>
                                        
                                    </FormItem>
                            </Form>
                                </Col>
                            </Row>
                            
                        </div>
                    </Panel>
                    <Panel name="Panel3">附件信息
                        <div slot='content'>
                            <Form :model="ammeter" ref="ammeter" :label-width="80"
                                class="margin-right-width">
                                <Row>
                                    <Col span="24" style="position: relative;">
                                        
                                        <attach-file :param="fileParam" :attachData="attachData" @getId="getId"
                                                            v-on:setAttachData="setAttachData"/>
                                        <!-- <span style="position: absolute; top: 28px; left: 417px;">支持pdf/word/jpg\png文件上传</span> -->
                                    </Col>
                                </Row>
                            </Form>
                        </div>
                    </Panel>

                </Collapse>
                </Card>
    </div>

</template>

<script>
import { whiteGetFileListByFileBusiId, whiteDetail, whiteOneTableMultiStationListInfo } from '@/api/account';
import {
  editAmmeter,
  getClassificationId,
  removeAttach, 
  attchList
} from '@/api/basedata/ammeter.js'
    // import {blist, btext} from "@/libs/tools";
    import attachFile from "./attachFile2";
    import SelectElectricType from "./selectElectricType";
    import countryModal from "./countryModal";
    import stationModal from "./stationModal";
    import {mapMutations} from "vuex";
    // import routers from '@/router/routers';
    // import {getHomeRoute} from '@/libs/util';
    import AmmeterProtocolList from "@/view/basedata/quota/listAmmeterProtocol";
    import customerList from "./customerModal";
    import ChooseAmmeterModel from "@/view/basedata/ammeter/chooseAmmeterModel";
    import ChooseModal from "@/view/business/gasBusiness/chooseModal";
    import axios from "@/libs/api.request";
    // import { widthstyle } from "@/view/business/mssAccountbill/mssAccountbilldata";


    export default {
        name: "whiteDetail",
        components: {
            attachFile,
            AmmeterProtocolList,
            customerList,
            stationModal,
            countryModal,
            SelectElectricType,
            ChooseAmmeterModel,
          ChooseModal,

        },
        data() {
            return {
                selected: [],
                selection: [ /* your default selection */ ],
                // isAddAndModify: "1",
                showModel: false,
                jz: {
                    columns1: [ 
                        // { type: "selection", width: 30, align: "center", },
                        
                        {
                        title: "序号", key: "index",  minWidth: 65, align: 'center'
                        },
                       
                        {
                        title: "电表/协议编号", key: "ammetername1",  minWidth: 65, align: 'center'
                        },
                        {
                            title: "项目名称", key: "projectname", minWidth: 90, align: 'center'
                        }, 
                        // {
                        //     title: "关联局站", key: "stationName", minWidth: 100, align: 'center'
                        // }, 
                        {
                            title: "所属分公司", key: "companyName", minWidth: 100, align: 'center'
                        }, {
                            title: "所属部门", key: "countryName", minWidth: 110, align: 'center'
                        }, 
                        {
                            title: "用电类型", key: "electrotypeName", minWidth: 100, className: "td-info", 
                            isShow: true, align: 'center'
                        }, 
                        {
                            title: "对外结算类型", key: "directsupplyflagName", minWidth: 100, className: "td-info", 
                            isShow: true, align: 'center'
                        }, 
                            {
                            title: "产权归属", key: "propertyName", minWidth: 100, isShow: true, align: 'center'
                        }, 
                        ],
                    columns3: [ 
                        // { type: "selection", width: 30, align: "center", },
                        {
                        title: "序号", key: "index",  minWidth: 65, align: 'center'
                        },
                         {
                            title: "局站名称", key: "stationname", minWidth: 90, align: 'center'
                        }, 
                        // className: "td-id",//编码设置颜色
                        {title: '局站编码',key: 'stationcode',align: 'center',minWidth: 110,},
                        {title: '资源局站/房屋/站址编码',key: 'resstationcode', align: 'center',minWidth: 110,},
                        // {
                        //     title: "关联局站", key: "stationName", minWidth: 100, align: 'center'
                        // }, 
                        {
                            title: "所属分公司", key: "companyName", minWidth: 100, align: 'center'
                        }, {
                            title: "所属部门", key: "countryName", minWidth: 110, align: 'center'
                        },{
                            title: "局站类型", key: "stationtypeName", minWidth: 110, align: 'center'
                        }, 
                        // {
                        //     title: "关联电表数", key: "ammeternum", minWidth: 110, align: 'center'
                        // },
                        {
                            title: "对外结算类型", key: "directsupplyflagName", minWidth: 100, className: "td-info", 
                            isShow: true, align: 'center'
                        }, 
                            {
                            title: "产权归属", key: "propertyrightName", minWidth: 100, isShow: true, align: 'center'
                        }, 
                        // {
                        // title: "电表/协议编号", key: "meterCode",  minWidth: 65, align: 'center'
                        // },
                        // {
                        //     title: "项目名称", key: "projectname", minWidth: 90, align: 'center'
                        // }, 
                        // {
                        //     title: "关联局站", key: "stationName", minWidth: 100, align: 'center'
                        // }, 
                        // {
                        //     title: "所属分公司", key: "companyName", minWidth: 100, align: 'center'
                        // }, {
                        //     title: "所属部门", key: "countryName", minWidth: 110, align: 'center'
                        // }, 
                        // // {
                        // //     title: "用电类型", key: "electrotypeName", minWidth: 100, className: "td-info", 
                        // //     isShow: true, align: 'center'
                        // // }, 
                        
                        // {
                        // //     title: "状态", key: "statusName", width: 100,  isShow: true, align: 'center'
                        // // }, 
                        // {title: "单价", key: "price", minWidth: 110, align: 'center'},
                        // {title: "单据状态", key: "billStatusName", width: 100,  isShow: true, align: 'center'
                        // },
                        // {title: "创建时间", key: "createTime", width: 100,  isShow: true, align: 'center'
                        // },
                        ],
                insideData1: [],
                insideData2: [],
                loading: false,
                },
          
                multiFiles: null,
                removeIds: [],
                attachData: [],
                fileParam: {
                    busiId: "",
                    busiAlias: "附件(协议管理)",
                    categoryCode: "file",
                    areaCode: "ln"
                },
                isRequireFlag: true,
                attach: {
                    fileForm: {
                        file: null
                    },
                    formLayout: [
                        {
                            label: '上传附件',
                            prop: 'file',
                            formItemType: 'file',
                            width: 300,
                            format: this.format
                        }
                    ],
                    loading: false,
                    columns: [],
                    data:[],
                },
                isAdmin: true,
                imgUrl: null,
                screenWidth: 800,
                show: false,
                showadd: true,
                display: false,
                loading: false,
                title: '上传图片(点击图片名字查看附件)',
                
    //             columns: [
    //                 {
    //                     key: 'fileName', title: '文件名',
    //                     render: (h, params) => {
    //                         let down =
    //                             h('Button', {
    //                                 props: {
    //                                     type: 'primary',
    //                                     size: 'small'
    //                                 },
    //                                 style: {
    //                                     marginRight: '3px'
    //                                 },
    //                                 on: {
    //                                     click: () => {
    //                                         this.download(params.row)
    //                                     }
    //                                 }
    //                             }, '下载')
    //                         let show = h('a', {
    //                             on: {
    //                                 click: () => {
    //                                     this.showPic(params.row)
    //                                 }
    //                             }
    //                         }, params.row.fileName)
    //                         let action = [show]

    //                          action.push(down)

    //                         return h('div', action)
    //   /*                      return h('a', {
    //                             on: {
    //                                 click: () => {
    //                                     this.showPic(params.row)
    //                                 }
    //                             }
    //                         }, params.row.fileName)*/
    //                     }
    //                 },
    //                 {key: 'creatorName', title: '上传人'},
    //                 {key: 'createTime', title: '上传日期'},
    //                 {key: 'fileSize', title: '文件大小(KB)'},
    //                 {
    //                     title: '操作',
    //                     key: 'action',
    //                     width: 200,
    //                     align: 'center',
    //                     fixed: 'right',
    //                     render: (h, params) => {
    //                         let down =
    //                             h('Button', {
    //                                 props: {
    //                                     type: 'primary',
    //                                     size: 'small'
    //                                 },
    //                                 style: {
    //                                     marginRight: '3px'
    //                                 },
    //                                 on: {
    //                                     click: () => {
    //                                         this.download(params.row)
    //                                     }
    //                                 }
    //                             }, '下载')
    //                         let remove = h('Button', {
    //                                 props: {
    //                                     type: 'error',
    //                                     size: 'small'
    //                                 },
    //                                 on: {
    //                                     click: () => {
    //                                         this.remove(params.row)
    //                                     }
    //                                 }
    //                             }
    //                             , '删除')
    //                         let action = [down]
    //                         if (!this.downloadOnly) {
    //                             action.push(remove)
    //                         }
    //                         return h('div', action)
    //                     }
    //                 }
    //             ],
                param: {
                    busiId: null,
                    busiAlias: "附件(台账)",
                    categoryCode: "file",
                    areaCode: "sc"
                },
                ammeter: {
                    stationtypename: "",
                    stationcode: "",
                    projectname: "", 
                    ammetername: "",
                    stationName: "",
                    directsupplyflag: "",
                    typeList: "",
                    typeList1: [
                    // {
                    // "whitelistType": "1" 
                    // },
                    ],
                    applyReason: "",
                    userunit:"",
                    id: null,
                    country: null,
                    company: null,
                    countryName: null,
                    billStatus: 0,
                    electricTypes: [],
                    electro: [],
                    electrotypename: "",//用电类型
                    iszgz: 0,
                    userunitCode: 0,
                    directFlag: 0,
                    officeFlag: 0,
                    transdistricompany: 1,
                    voltageClass: "",
                    issmartammeter: "0",
                    price: 0,
                },
                
                oldData: [],
                oldCategory: '',//原始数据
                oldPackagetype: '',//原始数据
                oldPayperiod: '',//原始数据
                oldPaytype: '',//原始数据
                oldElectronature: '',//原始数据
                oldElectrovalencenature: '',//原始数据
                oldElectrotype: '',//原始数据
                oldStatus: '',//原始数据
                oldProperty: '',//原始数据
                oldAmmetertype: '',//原始数据
                oldStationstatus: '',//原始数据
                oldStationtype: '',//原始数据
                oldAmmeteruse: '',//原始数据
                oldDirectsupplyflag: '',//原始数据
                ruleValidate: {
                    // chooseMeter: [
                    // // {required: true, message: '不能为空', trigger: 'click'}
                    // ],
                    // typeList: [
                    // {required: true, validator: valisheredepartname, trigger: 'change, blur'}//
                    // ],
                    applyReason: [
                        {required: true, message: "不能为空", trigger: 'blur'}
                    ],
                },
                selectValue: [],
                typeList: [],
                id1: "",
                valueTL: {},
            }
        },
        
        methods: {
            ...mapMutations(["closeTag", "closeTagByName"]),
            getId(data) {
                console.log(data, "getId(data) {");
            },
            handleSelectAll(selected) {
                if (selected) {
                    this.selection = this.jz.insideData1.map(item => item.id);
                } else {
                    this.selection = [];
                }
            },
            handleRowClick(row) {
            // Add your row click handling logic here
            },
            whiteFindByStationId(params) {
                whiteFindByStationId(params).then(res => {
                    console.log(res, "whiteFindByStationId(res) {");
                    res.data.forEach((item, index) => {
                        item.index = index*1+1;
                    })
                    this.jz.insideData1 = res.data;
                    this.jz.insideData2 = res.data;
                })
            },
            onModalOK1() {
                
                this.loading = true;
                console.log(this.id1, "this.id1");
                console.log(this.valueTL.typeList, "this.valueTL.typeList");
            if(this.isAddAndModify == 1) {
                if(this.id1 == "" || this.id1 == null) {
                this.$Message.error("需要先保存才能提交流程");
                // this.errorTips("请选择一条数据删除");
                }else {
                    let projectname = "";
                    if(this.valueTL.typeList == "1") {
                        projectname = "一表多站"
                    }else if(this.valueTL.typeList == "2") {
                        projectname = "一站多表"
                    }else if(this.valueTL.typeList == "3") {
                        projectname = "单价合理性"
                    }else{
                        projectname = "";
                    }
                this.startFlow({id: this.id1, billStatus: 0, projectname: projectname});
                }
                }else if(this.isAddAndModify == 2) {
                this.startFlow({id: this.ids, billStatus: 0, projectname: projectname});
                }
            },
            startFlow(row) {
                let that = this;
                isInTodoList(row.id,1).then(res => {
                    //存在于代办中时，报出提示
                    let ownername = "";
                    if (res.data.length > 0) {
                        for (let i = 0; i < res.data.length; i++) {
                            ownername += res.data[i].ownername + ' ';
                        }
                        that.$Modal.warning({title:"温馨提示",content: "该数据存在于" + ownername + "的流程代办中，处理后才可继续提交流程"});
                        that.loading = false;
                    }else if(row.billStatus == 3 || row.billStatus == 4){
                        checkStartFlow({id:row.id}).then(res1 => {
                            /*提交流程验证用户是否有数据需要提交*/
                            that.loading = false;
                            if (res1.data.id == null || res1.data.id == undefined) {
                                that.$Modal.warning({title:"温馨提示",content: "您没有可提交的数据"});
                            }else{
                                that.startFlowSubmit(row);
                            }
                        });
                    }else{
                        that.loading = false;
                        that.startFlowSubmit(row);
                    }
                });
            },
            OK(){
                let arr = [];
                // this.selectV = this.selectValue;
                    this.ammeter.typeList.forEach(i => {
                    arr.push({
                        "whitelistType": i
                    })
                    })
                    // if (this.loading == true) {
                    //   return;
                    // }
                    // this.loading = true;
                    let flag = false;
                    var a, b;
                    // this.ammeter.electricTypes = this.electro.data;
                    this.$refs.ammeter.validate((valid) => {
                    if (valid) {
                        flag = true;
                    }
                    });
                    console.log(flag, "flag");
                    if (flag) {
                    if(this.isAddAndModify == 2) {//修改
                        if(this.selectValue.length != 0) {
                        a = this.selectValue[0].ammetername;
                        b = this.selectValue[0].directsupplyflag;
                        }else {
                        a = this.selectV[0].meterCode;
                        b = this.selectV[0].directsupplyflag;
                        }
                    }else if(this.isAddAndModify == 1) {//新增
                        if(this.selectValue.length != 0) {
                        a = this.selectValue[0].ammetername;
                        b = this.selectValue[0].directsupplyflag;
                        }else {
                        a = "";
                        b = "";
                        }
                    }//新增
                    let data = 
                        {"meterCode": a, //电表表号
                        "dwjslx": b == 0?"直供电":b == 0?"转供电":"", //对外结算类型
                        // "dxsfqz": "是", //电信是否起租
                        "sftydxxg": "未上传附件；", //附件-文件地址，多个“，”隔开
                        // "type": selectV.type, //0-白名单 1-一表对多站 2-一站对多表
                        // "createTime": selectV.createTime, //录入时间
                        "typeList": arr,
                        "applyArgument": this.ammeter.applyReason,
                        };
                    console.log(data, "data");
                    
                    if(this.isAddAndModify == 1) {
                        whiteInsertAdd(data)
                    .then(res => {
                        // debugger
                        console.log(res, "白名单新增res");
                        if(res.data.code == 500) {
                        this.loading = false;
                        // this.$Message.error(res.data.msg);
                        }else{
                        this.$Message.success("保存成功");
                        this.loading = false;
                        this.id1 = res.data.id;
                        console.log(this.id1, "this.id1");
                        console.log(this.ammeter.projectname, "onModalOK1(projectname) {");
                        }
                    })
                    
                    // .catch(err => {
                        
                    //   console.log(err, "err5555555555");
                    // })
                    }
                    else if(this.isAddAndModify == 2) {
                    data.id = this.ids;
                        whiteUpdate(data)
                        .then(res => {
                        this.loading = false;
                        // if(res.data.code == 0) {
                            console.log(res, "白名单修改res");
                            if(res.data.code == 500) {
                            this.loading = false;
                            // this.$Message.error(res.data.msg);
                            }else{
                            this.$Message.success("保存成功");
                            this.loading = false;
                            this.id1 = res.data.id;
                            }
                        // this.$Message.success("保存成功");
                        // this.id1 = res.data.id;
                        // }else {
                        //   this.$Message.error("修改失败");
                        // }
                        
                    })
                    }
                    } else {
                    this.$Message.error("验证没通过");
                    this.loading = false;
                    }
                },

            onModalOK() {
                //   this.isDisable=true;
                // this.OK();
                    // "143934"
                    
                let data;
                let arr = [];
                if(this.ammeter.typeList == 3) {
                    arr.push(this.ammeter.id);
                }else if(this.ammeter.typeList == 1) {
                    arr.push(this.ammeter.id);
                    // this.ammeter.stationcode
                }else if(this.ammeter.typeList == 2) {
                    let data = this.$refs.listTable1.getSelection();
                    console.log(data, "onModalOK(data) {");
                    // let arrId = [];
                    data.forEach(res => {
                        arr.push(res.id)
                    })
                    console.log(arr, "onModalOK(arr) {");
                }else{
                    arr = [];
                }
                data = {
                    "stationcode": this.ammeter.stationcode,
                    "applyArgument": this.ammeter.applyReason, //申请理由
                    "fj": null, //附件
                    "meterIdList": arr, //电表id列表
                    "type": this.ammeter.typeList,
                }
                console.log(data, "onModalOK() {data");
                // data = {
                //     "stationcode": "JZ20191217152106088",
                //     "applyArgument": "申请加入白名单", //申请理由
                //     "fj": null, //附件
                //     "meterIdList": [
                //     "143934"
                //     ], //电表id列表
                //     "type": "2" //白名单类型 1:一表多站 2:一站多表 3:单价
                //     }
                    
                    let flag = false;
                    this.$refs.ammeter1.validate((valid) => {
                    if (valid) {
                        flag = true;
                    }
                    });
                    this.$refs.ammeter2.validate((valid) => {
                    if (valid) {
                        flag = true;
                    }
                    });
                    this.$refs.ammeter3.validate((valid) => {
                    if (valid) {
                        flag = true;
                    }
                    });
                    if(flag) {
                        whiteInsertAdd(data)
                            .then(res => {
                                // debugger
                                console.log(res, "whiteInsertAdd(data)白名单新增res");
                                if(res.data.code == 500) {
                                this.loading = false;
                                this.$Message.error(res.data.msg);
                                this.showModel = false;
                                }
                                else{
                                this.$Message.success("保存成功");
                                this.loading = false;
                                this.id1 = res.data.id;
                                this.showModel = false;
                                }
                            })
                    }
                    else {
                    this.$Message.error("验证没通过");
                    this.loading = false;
                    }

            },
        selectChange(v) {
            console.log(v, "vvvvvvvvv");
            // let arr = Array.from(new Set(this.typeList));
        },
        cancelWL() {

        }, 
        saveWL() {

        }, 
        submitWL() {

        },
        getInfoJZ(data) {
            console.log(data, "局站getInfoJZ(data) {");
            this.ammeter.stationcode = data[0].stationcode;
            this.ammeter.stationtypename = data[0].stationtypename;
            this.ammeter.stationName = data[0].stationname;
            let params = {
                stationId: data[0].id,
                whitelistType: this.valueTL.typeList
            }
            this.whiteFindByStationId(params);
        },
        getElecMeter(data) {
            console.log(data, "电表getElecMeter(data)");
            this.ammeter = {};
            this.ammeter.typeList = this.valueTL.typeList;
            this.selectValue = data;
            this.ammeter.projectname = data[0].projectname;
            this.ammeter.ammetername = data[0].ammetername;
            this.ammeter.companyName = data[0].companyName;
            this.ammeter.countryName = data[0].countryName;
            this.ammeter.electrotypename = data[0].electrotypename;
            this.ammeter.directsupplyflag = data[0].directsupplyflag;
            this.ammeter.stationName = data[0].stationName;
            this.ammeter.stationcode = data[0].stationcode;
            this.ammeter.id = data[0].id;
            // this.fileParam.busiId = "" || data[0].id;
            this.ammeter.price = data[0].price;
            let params = {
                stationId: data[0].id,
                whitelistType: this.valueTL.typeList
            }
            this.whiteFindByStationId(params);
        },
        refresh(){
            let obj = this
            setTimeout(function () {
            //    obj.getAccountMessages()
            },200);
        },
        getDataFromModal(data) {
            this.ammeter.country = data.id;
            this.ammeter.countryName = data.name;
            //选择所属部门结束
        },
        
        errorTips(str) {
        this.$Notice.error({
            title: "提示",
            desc: str,
            duration: 10,
        });
        },
        chooseOne() {
            // this.getElecmeter();
            let companyId = this.getQueryParams.company;
            let country = this.getQueryParams.country;
            if(companyId != null && country != null){
                let obj = {
                    company:companyId,
                    country:country,
                    accountno:this.getQueryParams.accountno,
                    accountType:'1',
                    accountestype:1
                }
                this.$refs.elecMeter.getElecmeterList(obj);
        // that.fileParam.busiId = this.selectValue[0].id;
        // console.log(that.fileParam.busiId, "that.fileParam.busiId");
            }else{
                this.errorTips('请选择分公司和部门')
            }
        },
        chooseJZ() {
            console.log(this.getQueryParams, "this.getQueryParams");
            let companyId = this.getQueryParams.company;
            let country = this.getQueryParams.country;
            if(companyId != null && country != null){
                let obj = {
                    company:companyId,
                    country:country,
                    accountno:this.getQueryParams.accountno,
                    accountType:'1',
                    accountestype:1
                }
                this.$refs.infoJZ.getElecmeterList(obj);
        // that.fileParam.busiId = this.selectValue[0].id;
        // console.log(that.fileParam.busiId, "that.fileParam.busiId");
            }else{
                this.errorTips('请选择分公司和部门')
            }
        },
        choose(pcid) {
            this.imgUrl = null;
            this.show = true
            this.param.busiId = pcid + "";
            this.$refs.attachList.query();
            let that = this;
            setTimeout(function () {
                if (that.$refs.attachList.insideData && that.$refs.attachList.insideData.length > 0)
                    that.showPic(that.$refs.attachList.insideData[0])
            }, 200);
        },
        cancle() {
            this.show = false
        }, 
        add() {
            this.display = true
            this.attach.fileForm = {}
        },
        remove(row) {
            this.$Modal.confirm({
                title: "确认删除",
                content: "<p>确定删除吗？</p>",
                onOk: () => {
                    axios.request({
                        url: '/common/attachments/remove',
                        method: 'post',
                        params: {ids: row.id}
                    }).then(() => {
                        this.$refs.attachList.query()
                        this.$emit("onchange",1)
                    })
                },
                onCancel: () => {
                }
            })
        },
        reload() {
            this.$refs.attachList.query()
        },
        removeAttach(){
        removeAttach({ids:this.removeIds.join()}).then(() => {

        });
        },
        setAttachData(data){
        console.log(data, "setAttachData(data)");
        this.multiFiles = data.data;
        this.removeIds = data.ids;
        if(this.removeIds.length!= 0 && data.type == 'remove'){
            this.removeAttach();
        }else{
            this.upload();
        }
        },
        upload(){
        if (this.attachData.length != 0 && this.multiFiles.length != 0){

            // this.$Message.info("提示:上传文件过大可能导致上传失败！");
            this.loading = true;
            axios.request({
            url: '/common/attachments/uploadMultiFile',
            method: 'post',
            data: this.multiFiles
            }).then((res) => {
            // debugger
            console.log(res, "axios.request({");
            if(res.data.code != 0){
                this.loading = false;
            }
            // else {
                let that = this;
                // if(that.fileParam.busiId == "") {
                //     that.errorTips('busiId为空');
                //     that.attachData = [];
                //     return;
                // }else {
                    // attchList = common/attachments/list;
                attchList({busiId:that.fileParam.busiId}).then(res => {
                    console.log(res, "attchList");
                    that.attachData = Object.assign([], res.data.rows);
                });
                // }
            // }
            
            })
        }
        // else {
            
        //         that.errorTips('multiFiles.length为0');
        // }
        },
            // upload() {
            //     if (!this.attach.fileForm.file) {
            //         this.$Message.info({content: '请选择要上传的文件！'})
            //         this.$refs.attach.buttonLoading = false
            //         return
            //     }
            //     axios.request({
            //         url: '/common/attachments/uploadMultiFile',
            //         method: 'post',
            //         data: Object.assign({}, this.param, this.attach.fileForm)
            //     }).then(() => {
            //         this.$refs.attach.buttonLoading = false
            //         this.display = false
            //         this.$refs.attachList.query()
            //         this.$emit("onchange",1)
            //     })
            // },
            download(row) {
                axios.file({
                    url: '/common/attachments/download',
                    method: 'get',
                    params: {
                        id: row.id,
                        shardKey: row.shardKey,
                        thumbnail: ''
                    }
                }).then((res) => {
                    const content = res
                    const blob = new Blob([content])
                    const fileName = row.fileName
                    if ('download' in document.createElement('a')) { // 非IE下载
                        const elink = document.createElement('a')
                        elink.download = fileName
                        elink.style.display = 'none'
                        elink.href = URL.createObjectURL(blob)
                        document.body.appendChild(elink)
                        elink.click()
                        URL.revokeObjectURL(elink.href) // 释放URL 对象
                        document.body.removeChild(elink)
                    } else { // IE10+下载
                        navigator.msSaveBlob(blob, fileName)
                    }
                })
            }, 
            showPic(row) {
                if (row.litimgUrl) {
                    this.imgUrl = row.litimgUrl;
                } else {
                    let sub = row.fileName.split(".");
                    let key = row.mongodbFileId + "." + sub[sub.length - 1];
                    this.loading = true;
                    axios.request({
                        url: '/common/attachments/share/' + key,
                        method: 'get',
                    }).then((res) => {
                        row.litimgUrl = res.data.url;
                        this.imgUrl = res.data.url;
                        let bgImg = new Image()
                        bgImg.src = this.imgUrl // 获取背景图片的url
                        bgImg.onerror = () => {
                            row.litimgUrl = null;
                            this.$Message.error("图片加载失败！！！");
                            this.loading = false;
                        }
                        bgImg.onload = () => { // 等背景图片加载成功后 去除loading
                            this.loading = false;
                        }
                        // this.imgUrl = "http://objects.objs.paas.sc.ctc.com/s/28aa0721445452647828ccd2e3a0602a.jpg?token=65794a68624763694f694a49557a49314e694973496e523563434936496b705856434a392e65794a6964574e725a5851694f694a7a5932356f4c575a70624755694c434a6c654841694f6a45314e6a67334d4445334e6a5173496d3969616d566a64434936496a4934595745774e7a49784e4451314e4455794e6a51334f44493459324e6b4d6d557a595441324d444a684c6d70775a794a392e625765664946333557794e723236554d3239394b314b6c634f6763646b516f54356f72557238484f794e6b";
                    })
                }
            },
            
            /*初始化*/
            initAmmeter() {
                editAmmeter('', 0).then(res => {
                console.log(res, "initAmmeter(id)");
                    // that.setAmmeter(Object.assign({}, res.data));
                    this.fileParam.busiId = res.data.id;
                    // that.getUser();
                });
                // getClassification().then(res => {//用电类型
                //     that.classificationData = res.data;
                // });
            },
            // upload() {
            //     if (!this.attach.fileForm.file) {
            //         this.$Message.info({content: '请选择要上传的文件！'})
            //         this.$refs.attach.buttonLoading = false
            //         return
            //     }
            //     let fileName = this.attach.fileForm.file.name;
            //     let fileSize = this.attach.fileForm.file.size;
            //     let count = 0;
            //     this.attachData.forEach(item => {
            //         if (item.fileName === fileName) {//判断相同名称只能添加一次
            //             count++;
            //         }
            //     });
            //     if(count != 0){
            //         this.$Message.info({content: '已有相同文件，请先删除'})
            //         this.$refs.attach.buttonLoading = false
            //         this.display = false;
            //         return
            //     }
            //     //新增数据到列表
            //     this.files.push(this.attach.fileForm.file);
            //     let data = this.attachData;
            //     data.push({fileName:fileName,creatorName:this.userName,createTime:new Date().format("yyyy-MM-dd hh:mm:ss"),fileSize:fileSize});
            //     this.attachData = data;

            //     this.$refs.attach.buttonLoading = false;
            //     this.display = false;
            //     this.setData('upload');
            // },
            onSelectionChange(selection) {
            this.selected = selection.map(item => item.id);
            }
        }, 
        mounted() {
            // this.handleSelectAll();
            console.log(this.$route.query.taskName, "mounted(this.$route.query.taskName) {");
            if(this.$route.query.taskName.indexOf("一表多站") > -1) {
                this.valueTL.typeList = "1";
            }else if(this.$route.query.taskName.indexOf("一站多表") > -1) {
                this.valueTL.typeList = "2";
            }else if(this.$route.query.taskName.indexOf("单价合理性") > -1) {
                this.valueTL.typeList = "3";
            }else {
                this.valueTL.typeList = ""; 
            }
            // whiteDetail(
            //     this.valueTL.typeList == "2"?this.$route.query.id:this.valueTL.typeList == "1"?this.$route.query.taskId
            //     :this.valueTL.typeList == "3"?this.$route.query.taskId:""
            //     )
            whiteDetail(this.$route.query.id)
            .then(res => {
          console.log(res, "whiteDetail(this.$route.query.id)");
        //   this.ammeter.applyReason = res.data.applyArgument;
        //   this.ammeter.stationcode = res.data.stationcode;
        //   this.ammeter.stationtypename = res.data.stationtypeName;
        //   this.ammeter.stationName = res.data.stationname;
        //   this.ammeter.typeList = res.data.whitelistType;
        //   this.ammeter.projectname = res.data.projectname;
        //   this.ammeter.ammetername = res.data.ammetername;
        //   this.ammeter.companyName = res.data.companyName;
        //   this.ammeter.countryName = res.data.countryName;
        //   this.ammeter.electrotypename = res.data.electrotypename;
        //   this.ammeter.directsupplyflag = res.data.directsupplyflag;
        //   this.ammeter.id = res.data.id;
        //   this.ammeter.price = res.data.price;
        this.ammeter.applyReason = res.data.applyArgument;
          this.ammeter.stationcode = res.data.stationcode;
          this.ammeter.stationtypename = res.data.stationtypeName;
          this.ammeter.stationName = 
          // res.data.meterList[0].stationName;
          this.valueTL.typeList== "2"?res.data.stationname:
          this.valueTL.typeList == "1"?res.data.meterList[0].stationName:
          this.valueTL.typeList == "3"?res.data.meterList[0].stationName:"";
          this.ammeter.typeList = res.data.whitelistType;
          this.ammeter.projectname = res.data.meterList[0].projectname;
          // this.tableName == "2"?res.data.projectname:
          // this.tableName == "1"?res.data.meterList[0].projectname:
          // this.tableName == "3"?res.data.meterList[0].projectname:"-";
          this.ammeter.ammetername =  res.data.meterList[0].ammetername;
          // this.tableName == "2"?res.data.ammetername:
          // this.tableName == "1"?res.data.meterList[0].ammetername:
          // this.tableName == "3"?res.data.meterList[0].ammetername:"-";
          this.ammeter.ammeternameId =  res.data.meterList[0].id;
          // this.tableName == "1"?res.data.ammetername:
          // this.tableName == "1"?res.data.meterList[0].id:
          // this.tableName == "3"?res.data.meterList[0].id:"-";
          this.ammeter.companyName = res.data.companyName;
          this.ammeter.countryName = res.data.countryName;
          this.ammeter.electrotypename = res.data.meterList[0].electrotypeName;
          // this.tableName == "2"?res.data.electrotypename:
          // this.tableName == "1"?res.data.meterList[0].electrotypeName:
          // this.tableName == "3"?res.data.meterList[0].electrotypeName:"-";
          this.ammeter.directsupplyflag = res.data.meterList[0].directsupplyflagName;
          // this.tableName == "2"?res.data.directsupplyflagName:
          // this.tableName == "1"?res.data.meterList[0].directsupplyflagName:
          // this.tableName == "3"?res.data.meterList[0].directsupplyflagName:"-";
          this.ammeter.id = res.data.id;
          this.ammeter.billStatus = res.data.billStatus; 
          this.ammeter.procInstId = res.data.procInstId; 
          this.ammeter.price = res.data.meterList[0].price;

          if(this.valueTL.typeList == "1") {
            whiteOneTableMultiStationListInfo({stationCodes: res.data.meterList[0].stationCodes})
              .then(res => {
              console.log(res, "whiteOneTableMultiStationListInfo(res.data.stationCodes)");
              res.data.forEach((item, index) => {
                item.index = index + 1;
                item.checked = true;
                // this.$refs.applyAddWhiteList.$refs.listTable1.objData[index+1]._isChecked=true;
              })
              this.jz.insideData2 = res.data;
          })
          }
          
        //   res.data.meterList.forEach((item, index) => {
        //     item.index = index + 1;
        //     item.checked = true;
        //     // this.$refs.applyAddWhiteList.$refs.listTable1.objData[index+1]._isChecked=true;
        //   })
        //   this.$refs.applyAddWhiteList.jz.insideData1 = res.data.meterList;

          res.data.meterList.forEach((item, index) => {
            item.index = index + 1;
            item.checked = true;
            // this.$refs.listTable1.objData[index+1]._isChecked=true;
          })
            res.data.meterList.forEach(item => {
                console.log(item.category, "whiteFindByStationId(item.category) {");
                if(item.category == "1") {
                    item.ammetername1 = item.ammetername;
                }else {
                    item.ammetername1 = item.protocolname;
                } 
            })
          this.jz.insideData1 = res.data.meterList;
          whiteGetFileListByFileBusiId(res.data.fileBusiId).then(res => {
                // whiteGetFileListByFileBusiId(res1.data.id).then(res => {
                    console.log(res, "whiteGetFileListByFileBusiId({busiId:this.param.busiId}).then(res => { ");
                    this.attachData= res.data;
                });




        })

            this.initAmmeter(this.$route.query.id);
            this.configVersion = this.$config.version;
            getClassificationId(this.ammeter.electrotype).then(res => {
                    this.ammeter.classifications = res.data;
                });
                // this.$refs.attachFile.downloadOnly = true;
        },
    }
</script>

<style>
    .margin-right-width {
        margin-right: 10px;
    }

    .testaa .ivu-row {
        margin-left: 5px;
        margin-right: 5px;
    }

    .testaa .requireStar .ivu-form-item-label:before {
        content: '*';
        display: inline-block;
        margin-right: 4px;
        line-height: 1;
        font-family: SimSun;
        font-size: 12px;
        color: #ed4014;
    }
</style>
