package com.enrising.ctsc.discharge.api.vo;


import lombok.Data;

import java.math.BigDecimal;

/**
 * 碳排放趋势数据vo
 *
 * <AUTHOR>
 */
@Data
public class DischargeEmissionTrendVo {

	/**
	 * 月份或者季度
	 * */
	private Long companyId;

	/**
	* 月份或者季度
	* */
	private String dataTime;

	/**
	 * 月份排放或者季度排放 / 碳排总量
	 * */
	private BigDecimal carbonTotal;

	/**
	 * 能源消耗总量
	 */
	private BigDecimal energyTotal;

	/**
	 * 今年
	 * */
	private BigDecimal nowYearCarbon;

	/**
	 * 今年
	 * */
	private BigDecimal preYearCarbon;

	/**
	 * 增长率
	 * */
	private String upRate;

	/**
	 * 部门名称
	 */
	private String companyName;

	/**
	 * 排名
	 */
	private String rankNum;

	/**
	 * 使用率
	 */
	private String useRate = "0";

	/**
	 * 碳排放量总额在定额值80%以下保持底色，80%-100%用橙色，超过100%用红色。
	 */
	private String state = "1";

	/**
	 * 区划编码
	 */
	private String cityCode;

	/**
	 * 碳排放强度
	 */
	private BigDecimal carbonIntensity = new BigDecimal(0);

	/**
	 * 业务碳排放强度
	 */
	private BigDecimal busCarbonIntensity = new BigDecimal(0);
}
