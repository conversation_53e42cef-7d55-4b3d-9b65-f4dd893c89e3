package com.enrising.ctsc.discharge.api.query;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.vo.GreenEnergyManagementVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 绿电管理查询
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2024-09-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GreenEnergyManagementQuery extends Page<GreenEnergyManagementVo> {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 交易年月-开始
     */
    private String transactionMonthStart;

    /**
     * 交易年月-结束
     */
    private String transactionMonthEnd;

    /**
     * 能源类型
     */
    private String energyType;

    /**
     * 审核结果
     */
    private String auditResult;

    /**
     * 所属分公司
     */
    private Long companies;

    /**
     * 所属部门
     */
    private Long companyBranch;

    public String getKeyword() {
        if (StrUtil.isNotBlank(keyword)) {
            return StrUtil.format("%{}%", keyword);
        }
        return keyword;
    }
}
