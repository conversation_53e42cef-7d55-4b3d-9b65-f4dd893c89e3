package com.enrising.ctsc.carbon.common.entity;

import cn.zhxu.bs.bean.SearchBean;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 用户角色关联表
 */
@Data
@TableName(value = "rmp.sys_r_users_roles")
@SearchBean(dataSource = "rmpDs", tables = "sys_r_users_roles")
public class UserRoles {

    /**
     * id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 角色id
     */
    private Long roleId;

}
