<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.enrising.ctsc.discharge.mapper.DischargeReportMapper">

	<!-- 表字段 -->
	<sql id="baseColumns">
			t.id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.year,
            t.report_name,
            t.content,
            t.is_notification,
            t.reporter,
            to_char(t.report_time,'yyyy-MM-dd') as report_time,
            t.del_flag
	</sql>

	<!-- 查询列表 -->
	<select id="findList" resultType="com.enrising.ctsc.discharge.api.vo.DischargeReportVo">
		SELECT
		<include refid="baseColumns"/>
		FROM discharge_report t
		${ew.customSqlSegment}
	</select>

	<!-- 查询详情 -->
	<select id="detail" resultType="com.enrising.ctsc.discharge.api.vo.DischargeReportVo">
		SELECT
		<include refid="baseColumns"/>
		FROM discharge_report t
		${ew.customSqlSegment}
		limit 1
	</select>
</mapper>