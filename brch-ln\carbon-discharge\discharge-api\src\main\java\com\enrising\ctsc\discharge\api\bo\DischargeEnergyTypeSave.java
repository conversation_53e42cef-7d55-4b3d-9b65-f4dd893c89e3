package com.enrising.ctsc.discharge.api.bo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.awt.*;
import java.io.Serializable;
import java.util.ArrayList;

/**
 * 碳排放能源类型表
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-1-4
 */
@Data
public class DischargeEnergyTypeSave implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 考核类型列表
	 */
	@NotEmpty(message = "不能提交空数据")
	private ArrayList<DischargeEnergyTypeBo> list;

}
