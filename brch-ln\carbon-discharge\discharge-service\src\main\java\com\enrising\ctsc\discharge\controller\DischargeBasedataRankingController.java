package com.enrising.ctsc.discharge.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enrising.ctsc.discharge.api.bo.DischargeBasedataRankingBo;
import com.enrising.ctsc.discharge.api.entity.DischargeBasedataRanking;
import com.enrising.ctsc.discharge.api.query.DischargeBasedataRankingQuery;
import com.enrising.ctsc.carbon.common.utils.R;
import com.enrising.ctsc.carbon.common.utils.TableDataInfo;
import com.enrising.ctsc.discharge.api.vo.DischargeBasedataRankingVo;
import com.enrising.ctsc.discharge.service.DischargeBasedataRankingService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 集团排名情况
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-10-13
 */
@Slf4j
@RestController
@RequestMapping("/baseRanking")
@AllArgsConstructor
public class DischargeBasedataRankingController {
    private final DischargeBasedataRankingService dischargeBasedataRankingService;

    @GetMapping("/list")
    public TableDataInfo<DischargeBasedataRankingVo> page(Page<DischargeBasedataRankingVo> page, DischargeBasedataRankingQuery query) {
        return dischargeBasedataRankingService.findList(page, query);
    }

    @GetMapping("/detail")
    public R<DischargeBasedataRankingVo> get(DischargeBasedataRankingQuery query) {
        DischargeBasedataRankingVo detail = dischargeBasedataRankingService.detail(query);
        return R.success(detail, "查询成功");
    }

    @PostMapping(value = "/save")
    public R<String> save(@RequestBody @Valid DischargeBasedataRankingBo bo) {
        dischargeBasedataRankingService.add(bo);
        return R.success("保存成功");
    }

    @PostMapping(value = "/update")
    public R<String> update(@RequestBody @Valid DischargeBasedataRankingBo bo) {
        dischargeBasedataRankingService.edit(bo);
        return R.success("修改成功");
    }

    @PostMapping(value = "/setDisplayState")
    public R<String> setDisplayState(@RequestBody DischargeBasedataRankingBo bo) {
        dischargeBasedataRankingService.setDisplayState(bo);
        return R.success("修改成功");
    }

    @PostMapping(value = "/delete/{id}")
    public R<String> delete(@PathVariable Long id) {
        dischargeBasedataRankingService.del(id);
        return R.success("删除成功");
    }

    /**
     * 获取展示的对象
     */
    @GetMapping(value = "/getShowObj")
    public R<DischargeBasedataRanking> getShowObj() {
        DischargeBasedataRanking showObj = dischargeBasedataRankingService.getShowObj();
        return R.success(showObj, "集团排名情况");
    }
}
