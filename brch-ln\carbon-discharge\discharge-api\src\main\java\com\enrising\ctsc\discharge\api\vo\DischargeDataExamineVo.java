package com.enrising.ctsc.discharge.api.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;

/**
 *  碳盘查数据表
 *
 * <AUTHOR>
 * @since 3/13
 */

@Data
@TableName("discharge_data_examine")
public class DischargeDataExamineVo extends Model<DischargeDataExamineVo> {

	/**
	 * 主键id,采用雪花id
	 */
		@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 能源指标类型名称
	 */
		@Excel(name = "类型", width = 20,orderNum = "1")
	private String examineName;

	/**
	 * 单位字典value
	 */
		private String unit;

	/**
	 * 单位字典字母简称
	 */
		private String unitDescription;

	/**
	 * 简称
	 */
		@Excel(name = "单位", width = 20,orderNum = "3")
	private String unitName;

	/**
	 * 排序字段
	 */
		private String sort;

	/**
	 * 公司id
	 */
		private Long companyId;

	/**
	 * 使用量
	 */
		@Excel(name = "使用量", width = 20,orderNum = "2")
	private BigDecimal useTotal = new BigDecimal(0);

	/**
	 * 碳排放因子id
	 */
		private Long factorId;

	/**
	 * 填报时间
	 */
		private String reportTime;

	/**
	 * 能源类型：energy_type：1-水、2-电、3-气、4-油、5-热力
	 */
		private String energyType;

	/**
	 * 转换因子
	 */
		private BigDecimal factor;

	/**
	 * 碳排放量
	 */
		@Excel(name = "碳排放量（tCO₂）", width = 20,orderNum = "4")
	private BigDecimal carbonTotal;


}
