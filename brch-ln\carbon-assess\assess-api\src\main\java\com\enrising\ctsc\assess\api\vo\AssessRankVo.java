package com.enrising.ctsc.assess.api.vo;

import com.enrising.ctsc.carbon.common.utils.MathUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * 考核排名列表vo
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2022-12-07
 */
@Data
public class AssessRankVo {

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 模板总分
     */
    private double totalScore = 0;

    /**
     * 考核得分
     */
    private double assessScore = 0;

    /**
     * 得分率
     */
    private String assessRate;

    /**
     * 考核排名
     */
    private String assessRank;

    /**
     * 是否达标
     */
    private String isReachingStand;

    /**
     * 指标得分列表
     */
    private List<Double> targetScoreList;

    /**
     * 指标名称列表
     */
    private List<String> targetNameList;

    /**
     * 达标分数线
     */
    private double standScore = 0;

    public String getAssessRate() {
        return MathUtils.d2dRatio(new BigDecimal(assessScore), new BigDecimal(totalScore));
    }

    public String getIsReachingStand() {
        return assessScore < standScore ? "否" : "是";
    }
}
